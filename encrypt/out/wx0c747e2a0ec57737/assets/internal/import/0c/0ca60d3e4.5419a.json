[1, ["609xlc7CpF67qUiVX2DoHQ", "a3zQCfCrBCDZJ4uf2rk5u8", "81Dpbk5FZEaJtZ9OjzlzLA", "c2chXYaDVLaL+7verGEAwE", "1cAq5vRJJJFbj4dJKjseTN"], ["_effectAsset"], [["cc.EffectAsset", ["_name", "shaders", "techniques", "combinations"], -1], ["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.Material", ["_name", "_states", "_defines", "_props"], 0, 12]], [[1, 0, 1, 2, 3, 5], [0, 0, 1, 2, 4], [0, 0, 3, 1, 2, 5], [2, 0, 1, 2, 3, 4]], [[[[1, "internal/builtin-graphics", [{"hash": 586721616, "name": "internal/builtin-graphics|vs:vert|fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_color", "format": 44, "location": 1, "defines": []}, {"name": "a_dist", "format": 11, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCLocal {\n  highp mat4 cc_matWorld;\n  highp mat4 cc_matWorldIT;\n  highp vec4 cc_lightingMapUVParam;\n  highp vec4 cc_localShadowBias;\n  highp vec4 cc_reflectionProbeData1;\n  highp vec4 cc_reflectionProbeData2;\n  highp vec4 cc_reflectionProbeBlendData1;\n  highp vec4 cc_reflectionProbeBlendData2;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\nin float a_dist;\nout float v_dist;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_color = a_color;\n  v_dist = a_dist;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nin vec4 v_color;\nin float v_dist;\nvec4 frag () {\n  vec4 o = v_color;\n    float aa = fwidth(v_dist);\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matViewProj;\nuniform highp mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\nattribute float a_dist;\nvarying float v_dist;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_color = a_color;\n  v_dist = a_dist;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\n#ifdef GL_OES_standard_derivatives\n#extension GL_OES_standard_derivatives: enable\n#endif\nprecision highp float;\nvarying vec4 v_color;\nvarying float v_dist;\nvec4 frag () {\n  vec4 o = v_color;\n    #ifdef GL_OES_standard_derivatives\n      float aa = fwidth(v_dist);\n    #else\n      float aa = 0.05;\n    #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": []}], [{"passes": [{"program": "internal/builtin-graphics|vs:vert|fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 4, "blendSrcAlpha": 1, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[0, "ui-alpha-test-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_ALPHA_TEST": true, "USE_TEXTURE": true, "IS_GRAY": false, "CC_USE_EMBEDDED_ALPHA": false}]]], 0, 0, [0], [0], [0]], [[[2, "for2d/builtin-sprite", [{}], [{"hash": 2249878161, "name": "for2d/builtin-sprite|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if USE_LOCAL\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\n#if SAMPLE_FROM_RT\n  #define QUATER_PI         0.78539816340\n  #define HALF_PI           1.57079632679\n  #define PI                3.14159265359\n  #define PI2               6.28318530718\n  #define PI4               12.5663706144\n  #define INV_QUATER_PI     1.27323954474\n  #define INV_HALF_PI       0.63661977237\n  #define INV_PI            0.31830988618\n  #define INV_PI2           0.15915494309\n  #define INV_PI4           0.07957747155\n  #define EPSILON           1e-6\n  #define EPSILON_LOWP      1e-4\n  #define LOG2              1.442695\n  #define EXP_VALUE         2.71828183\n  #define FP_MAX            65504.0\n  #define FP_SCALE          0.0009765625\n  #define FP_SCALE_INV      1024.0\n  #define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n      #define LIGHT_MAP_TYPE_DISABLED 0\n  #define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n  #define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n  #define REFLECTION_PROBE_TYPE_NONE 0\n  #define REFLECTION_PROBE_TYPE_CUBE 1\n  #define REFLECTION_PROBE_TYPE_PLANAR 2\n  #define REFLECTION_PROBE_TYPE_BLEND 3\n  #define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n      #define LIGHT_TYPE_DIRECTIONAL 0.0\n  #define LIGHT_TYPE_SPHERE 1.0\n  #define LIGHT_TYPE_SPOT 2.0\n  #define LIGHT_TYPE_POINT 3.0\n  #define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n  #define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n  #define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n  #define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n  #define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n  #define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n  #define TONE_MAPPING_ACES 0\n  #define TONE_MAPPING_LINEAR 1\n  #define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n  #ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n    #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n  #endif\n  #ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n    #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n  #endif\n#endif\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nout vec4 color;\nout vec2 uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n  uv0 = a_texCoord;\n  #if SAMPLE_FROM_RT\n    uv0 = cc_cameraPos.w > 1.0 ? vec2(uv0.x, 1.0 - uv0.y) : uv0;\n  #endif\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture(tex, uv).rgb, texture(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture(tex, uv);\n#endif\n}\n#if USE_ALPHA_TEST\n  layout(std140) uniform ALPHA_TEST_DATA {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 color;\n#if USE_TEXTURE\n  in vec2 uv0;\n  uniform sampler2D cc_spriteTexture;\n#endif\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n    o *= CCSampleWithAlphaSeparated(cc_spriteTexture, uv0);\n    #if IS_GRAY\n      float gray  = 0.2126 * o.r + 0.7152 * o.g + 0.0722 * o.b;\n      o.r = o.g = o.b = gray;\n    #endif\n  #endif\n  o *= color;\n  ALPHA_TEST(o);\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp mat4 cc_matViewProj;\n  uniform highp vec4 cc_cameraPos;\n#if USE_LOCAL\n  uniform highp mat4 cc_matWorld;\n#endif\n#if SAMPLE_FROM_RT\n  #define QUATER_PI         0.78539816340\n  #define HALF_PI           1.57079632679\n  #define PI                3.14159265359\n  #define PI2               6.28318530718\n  #define PI4               12.5663706144\n  #define INV_QUATER_PI     1.27323954474\n  #define INV_HALF_PI       0.63661977237\n  #define INV_PI            0.31830988618\n  #define INV_PI2           0.15915494309\n  #define INV_PI4           0.07957747155\n  #define EPSILON           1e-6\n  #define EPSILON_LOWP      1e-4\n  #define LOG2              1.442695\n  #define EXP_VALUE         2.71828183\n  #define FP_MAX            65504.0\n  #define FP_SCALE          0.0009765625\n  #define FP_SCALE_INV      1024.0\n  #define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n      #define LIGHT_MAP_TYPE_DISABLED 0\n  #define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n  #define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n  #define REFLECTION_PROBE_TYPE_NONE 0\n  #define REFLECTION_PROBE_TYPE_CUBE 1\n  #define REFLECTION_PROBE_TYPE_PLANAR 2\n  #define REFLECTION_PROBE_TYPE_BLEND 3\n  #define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n      #define LIGHT_TYPE_DIRECTIONAL 0.0\n  #define LIGHT_TYPE_SPHERE 1.0\n  #define LIGHT_TYPE_SPOT 2.0\n  #define LIGHT_TYPE_POINT 3.0\n  #define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n  #define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n  #define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n  #define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n  #define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n  #define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n  #define TONE_MAPPING_ACES 0\n  #define TONE_MAPPING_LINEAR 1\n  #define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n  #ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n    #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n  #endif\n  #ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n    #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n  #endif\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec4 color;\nvarying vec2 uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n  uv0 = a_texCoord;\n  #if SAMPLE_FROM_RT\n    uv0 = cc_cameraPos.w > 1.0 ? vec2(uv0.x, 1.0 - uv0.y) : uv0;\n  #endif\n  color = a_color;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture2D(tex, uv).rgb, texture2D(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture2D(tex, uv);\n#endif\n}\n#if USE_ALPHA_TEST\n      uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 color;\n#if USE_TEXTURE\n  varying vec2 uv0;\n  uniform sampler2D cc_spriteTexture;\n#endif\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n    o *= CCSampleWithAlphaSeparated(cc_spriteTexture, uv0);\n    #if IS_GRAY\n      float gray  = 0.2126 * o.r + 0.7152 * o.g + 0.0722 * o.b;\n      o.r = o.g = o.b = gray;\n    #endif\n  #endif\n  o *= color;\n  ALPHA_TEST(o);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": ["USE_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "SAMPLE_FROM_RT", "type": "boolean"}, {"name": "USE_PIXEL_ALIGNMENT", "type": "boolean"}, {"name": "CC_USE_EMBEDDED_ALPHA", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "USE_TEXTURE", "type": "boolean"}, {"name": "IS_GRAY", "type": "boolean"}]}], [{"passes": [{"program": "for2d/builtin-sprite|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []], [[[2, "internal/builtin-clear-stencil", [{}], [{"hash": 3507038093, "name": "internal/builtin-clear-stencil|sprite-vs:vert|sprite-fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nin vec3 a_position;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 frag () {\n  vec4 o = vec4(1.0);\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nattribute vec3 a_position;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 frag () {\n  vec4 o = vec4(1.0);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 0, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 0}}, "defines": []}], [{"passes": [{"program": "internal/builtin-clear-stencil|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[1, "util/profiler", [{"hash": 394204838, "name": "util/profiler|profiler-vs:vert|profiler-fs:frag", "blocks": [{"name": "Constants", "stageFlags": 1, "binding": 0, "members": [{"name": "offset", "type": 16, "count": 1}], "defines": []}, {"name": "PerFrameInfo", "stageFlags": 1, "binding": 1, "members": [{"name": "digits", "type": 16, "count": 22}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_color", "format": 44, "location": 1, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "stageFlags": 1, "binding": 0, "members": [{"name": "offset", "type": 16, "count": 1}], "defines": []}, {"name": "PerFrameInfo", "stageFlags": 1, "binding": 1, "members": [{"name": "digits", "type": 16, "count": 22}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec2 v_uv;\nlayout(std140) uniform Constants {\n  vec4 offset;\n};\nlayout(std140) uniform PerFrameInfo {\n  vec4 digits[8 * 11 / 4];\n};\nfloat getComponent(vec4 v, float i) {\n  if (i < 1.0) { return v.x; }\n  else if (i < 2.0) { return v.y; }\n  else if (i < 3.0) { return v.z; }\n  else { return v.w; }\n}\nvec4 vert () {\n  mat2 proj = mat2(cc_matProj[0].xy, cc_matProj[1].xy);\n  proj /= abs(proj[1].x + proj[1].y);\n  vec2 position = proj * a_position.xy + offset.xy;\n  v_uv = a_color.xy;\n  if (a_color.z >= 0.0) {\n    float n = getComponent(digits[int(a_color.z)], a_color.w);\n    v_uv += vec2(offset.z * n, 0.0);\n  }\n  return vec4(position, 0.0, 1.0);\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nin vec2 v_uv;\nuniform sampler2D mainTexture;\nvec4 frag () {\n  return CCFragOutput(texture(mainTexture, v_uv));\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nuniform highp mat4 cc_matProj;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec2 v_uv;\n   uniform vec4 offset;\n    uniform vec4 digits[22];\nfloat getComponent(vec4 v, float i) {\n  if (i < 1.0) { return v.x; }\n  else if (i < 2.0) { return v.y; }\n  else if (i < 3.0) { return v.z; }\n  else { return v.w; }\n}\nvec4 vert () {\n  mat2 proj = mat2(cc_matProj[0].xy, cc_matProj[1].xy);\n  proj /= abs(proj[1].x + proj[1].y);\n  vec2 position = proj * a_position.xy + offset.xy;\n  v_uv = a_color.xy;\n  if (a_color.z >= 0.0) {\n    float n = getComponent(digits[int(a_color.z)], a_color.w);\n    v_uv += vec2(offset.z * n, 0.0);\n  }\n  return vec4(position, 0.0, 1.0);\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying vec2 v_uv;\nuniform sampler2D mainTexture;\nvec4 frag () {\n  return CCFragOutput(texture2D(mainTexture, v_uv));\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 65, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": []}], [{"passes": [{"program": "util/profiler|profiler-vs:vert|profiler-fs:frag", "priority": 255, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[0, "default-clear-stencil", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": false}]]], 0, 0, [0], [0], [2]], [[[1, "util/splash-screen", [{"hash": 3189094080, "name": "util/splash-screen|splash-screen-vs:vert|splash-screen-fs:frag", "blocks": [{"name": "Constant", "stageFlags": 1, "binding": 0, "members": [{"name": "u_buffer0", "type": 16, "count": 1}, {"name": "u_buffer1", "type": 16, "count": 1}, {"name": "u_projection", "type": 25, "count": 1}], "defines": []}, {"name": "Factor", "stageFlags": 16, "binding": 1, "members": [{"name": "u_percent", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 21, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constant", "stageFlags": 1, "binding": 0, "members": [{"name": "u_buffer0", "type": 16, "count": 1}, {"name": "u_buffer1", "type": 16, "count": 1}, {"name": "u_projection", "type": 25, "count": 1}], "defines": []}, {"name": "Factor", "stageFlags": 16, "binding": 1, "members": [{"name": "u_percent", "type": 13, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision mediump float;\nin vec2 a_position;\nin vec2 a_texCoord;\nout vec2 v_uv;\nlayout(std140) uniform Constant {\n  vec4 u_buffer0;\n  vec4 u_buffer1;\n  mat4 u_projection;\n};\nvec4 vert () {\n  vec2 worldPos = a_position * u_buffer1.xy + u_buffer1.zw;\n  vec2 clipSpace = worldPos / u_buffer0.xy * 2.0 - 1.0;\n  vec4 screenPos = u_projection * vec4(clipSpace, 0.0, 1.0);\n  v_uv = a_texCoord;\n  return screenPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nin vec2 v_uv;\nlayout(std140) uniform Factor {\n  float u_percent;\n};\nuniform sampler2D mainTexture;\nvec4 frag () {\n  vec4 color = texture(mainTexture, v_uv);\n  float percent = clamp(u_percent, 0.0, 1.0);\n  color.xyz *= percent;\n  return color;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision mediump float;\nattribute vec2 a_position;\nattribute vec2 a_texCoord;\nvarying vec2 v_uv;\n  uniform vec4 u_buffer0;\n  uniform vec4 u_buffer1;\n  uniform mat4 u_projection;\nvec4 vert () {\n  vec2 worldPos = a_position * u_buffer1.xy + u_buffer1.zw;\n  vec2 clipSpace = worldPos / u_buffer0.xy * 2.0 - 1.0;\n  vec4 screenPos = u_projection * vec4(clipSpace, 0.0, 1.0);\n  v_uv = a_texCoord;\n  return screenPos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nvarying vec2 v_uv;\n  uniform float u_percent;\nuniform sampler2D mainTexture;\nvec4 frag () {\n  vec4 color = texture2D(mainTexture, v_uv);\n  float percent = clamp(u_percent, 0.0, 1.0);\n  color.xyz *= percent;\n  return color;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 6, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": []}], [{"name": "default", "passes": [{"program": "util/splash-screen|splash-screen-vs:vert|splash-screen-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "resolution": {"type": 14, "value": [640, 960], "handleInfo": ["u_buffer0", 0, 14]}, "percent": {"type": 13, "value": [0.5], "handleInfo": ["u_percent", 0, 13]}, "scale": {"type": 14, "value": [200, 500], "handleInfo": ["u_buffer1", 0, 14]}, "translate": {"type": 14, "value": [320, 480], "handleInfo": ["u_buffer1", 2, 14]}, "u_buffer0": {"type": 16, "value": [640, 960, 0, 0]}, "u_percent": {"type": 13, "value": [0.5]}, "u_buffer1": {"type": 16, "value": [200, 500, 320, 480]}}}]}]]], 0, 0, [], [], []], [[[1, "builtin-unlit", [{"hash": 340555192, "name": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "blocks": [{"name": "TexCoords", "stageFlags": 1, "binding": 0, "members": [{"name": "tilingOffset", "type": 16, "count": 1}], "defines": ["USE_TEXTURE"]}, {"name": "Constant", "stageFlags": 16, "binding": 1, "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "colorScaleAndCutoff", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}, {"name": "a_color", "format": 44, "location": 17, "defines": ["USE_VERTEX_COLOR"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "TexCoords", "stageFlags": 1, "binding": 0, "members": [{"name": "tilingOffset", "type": 16, "count": 1}], "defines": ["USE_TEXTURE"]}, {"name": "Constant", "stageFlags": 16, "binding": 1, "members": [{"name": "mainColor", "type": 16, "count": 1}, {"name": "colorScaleAndCutoff", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "mainTexture", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  layout(std140) uniform CCMorph {\n    vec4 cc_displacementWeights[15];\n    vec4 cc_displacementTextureInfo;\n  };\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int pixelIndex) {\n        ivec2 texSize = textureSize(tex, 0);\n        return texelFetch(tex, ivec2(pixelIndex % texSize.x, pixelIndex / texSize.x), 0);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture(tex, x)),\n        decode32(texture(tex, y)),\n        decode32(texture(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    layout(std140) uniform CCSkinningTexture {\n      highp vec4 cc_jointTextureInfo;\n    };\n    layout(std140) uniform CCSkinningAnimation {\n      highp vec4 cc_jointAnimInfo;\n    };\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      layout(std140) uniform CCSkinning {\n        highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n      };\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if !USE_INSTANCING\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nvoid CCGetWorldMatrix(out mat4 matWorld)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n  #endif\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n#if !CC_USE_ACCURATE_FOG\nout mediump float v_fog_factor;\n#endif\nvoid CC_TRANSFER_FOG(vec4 pos) {\n#if !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(pos, v_fog_factor);\n#endif\n}\n#if USE_VERTEX_COLOR\n  in lowp vec4 a_color;\n  out lowp vec4 v_color;\n#endif\n#if USE_TEXTURE\n  out vec2 v_uv;\n  layout(std140) uniform TexCoords {\n    vec4 tilingOffset;\n  };\n#endif\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld;\n  CCGetWorldMatrix(matWorld);\n  #if USE_TEXTURE\n    v_uv = a_texCoord * tilingOffset.xy + tilingOffset.zw;\n    #if SAMPLE_FROM_RT\n      v_uv = cc_cameraPos.w > 1.0 ? vec2(v_uv.x, 1.0 - v_uv.y) : v_uv;\n    #endif\n  #endif\n  #if USE_VERTEX_COLOR\n    v_color = a_color;\n  #endif\n  CC_TRANSFER_FOG(matWorld * position);\n  return cc_matProj * (cc_matView * matWorld) * position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec4 CCFragOutput (vec4 color) {\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #elif !CC_USE_FLOAT_OUTPUT\n    #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\n#if !CC_USE_ACCURATE_FOG\nin mediump float v_fog_factor;\n#endif\nvoid CC_APPLY_FOG(inout vec4 color) {\n#if !CC_USE_ACCURATE_FOG\n    CC_APPLY_FOG_BASE(color, v_fog_factor);\n#endif\n}\nvoid CC_APPLY_FOG(inout vec4 color, vec3 worldPos) {\n#if CC_USE_ACCURATE_FOG\n    float factor;\n    CC_TRANSFER_FOG_BASE(vec4(worldPos, 1.0), factor);\n#else\n    float factor = v_fog_factor;\n#endif\n    CC_APPLY_FOG_BASE(color, factor);\n}\n#if USE_ALPHA_TEST\n#endif\n#if USE_TEXTURE\n  in vec2 v_uv;\n  uniform sampler2D mainTexture;\n#endif\nlayout(std140) uniform Constant {\n  vec4 mainColor;\n  vec4 colorScaleAndCutoff;\n};\n#if USE_VERTEX_COLOR\n  in lowp vec4 v_color;\n#endif\nvec4 frag () {\n  vec4 o = mainColor;\n  o.rgb *= colorScaleAndCutoff.xyz;\n  #if USE_VERTEX_COLOR\n    o.rgb *= SRGBToLinear(v_color.rgb);\n    o.a *= v_color.a;\n  #endif\n  #if USE_TEXTURE\n    vec4 texColor = texture(mainTexture, v_uv);\n    texColor.rgb = SRGBToLinear(texColor.rgb);\n    o *= texColor;\n  #endif\n  #if USE_ALPHA_TEST\n    if (o.ALPHA_TEST_CHANNEL < colorScaleAndCutoff.w) discard;\n  #endif\n  CC_APPLY_FOG(o);\n  return CCFragOutput(o);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n#if !USE_INSTANCING\n  uniform highp mat4 cc_matWorld;\n#endif\nvoid CCGetWorldMatrix(out mat4 matWorld)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n  #endif\n}\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\n#if !CC_USE_ACCURATE_FOG\nvarying mediump float v_fog_factor;\n#endif\nvoid CC_TRANSFER_FOG(vec4 pos) {\n#if !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(pos, v_fog_factor);\n#endif\n}\n#if USE_VERTEX_COLOR\n  attribute lowp vec4 a_color;\n  varying lowp vec4 v_color;\n#endif\n#if USE_TEXTURE\n  varying vec2 v_uv;\n      uniform vec4 tilingOffset;\n#endif\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld;\n  CCGetWorldMatrix(matWorld);\n  #if USE_TEXTURE\n    v_uv = a_texCoord * tilingOffset.xy + tilingOffset.zw;\n    #if SAMPLE_FROM_RT\n      v_uv = cc_cameraPos.w > 1.0 ? vec2(v_uv.x, 1.0 - v_uv.y) : v_uv;\n    #endif\n  #endif\n  #if USE_VERTEX_COLOR\n    v_color = a_color;\n  #endif\n  CC_TRANSFER_FOG(matWorld * position);\n  return cc_matProj * (cc_matView * matWorld) * position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec4 CCFragOutput (vec4 color) {\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #elif !CC_USE_FLOAT_OUTPUT\n    #if CC_USE_HDR && CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\nuniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_fogColor;\n  uniform mediump vec4 cc_fogBase;\n  uniform mediump vec4 cc_fogAdd;\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\n#if !CC_USE_ACCURATE_FOG\nvarying mediump float v_fog_factor;\n#endif\nvoid CC_APPLY_FOG(inout vec4 color) {\n#if !CC_USE_ACCURATE_FOG\n    CC_APPLY_FOG_BASE(color, v_fog_factor);\n#endif\n}\nvoid CC_APPLY_FOG(inout vec4 color, vec3 worldPos) {\n#if CC_USE_ACCURATE_FOG\n    float factor;\n    CC_TRANSFER_FOG_BASE(vec4(worldPos, 1.0), factor);\n#else\n    float factor = v_fog_factor;\n#endif\n    CC_APPLY_FOG_BASE(color, factor);\n}\n#if USE_ALPHA_TEST\n#endif\n#if USE_TEXTURE\n  varying vec2 v_uv;\n  uniform sampler2D mainTexture;\n#endif\n   uniform vec4 mainColor;\n   uniform vec4 colorScaleAndCutoff;\n#if USE_VERTEX_COLOR\n  varying lowp vec4 v_color;\n#endif\nvec4 frag () {\n  vec4 o = mainColor;\n  o.rgb *= colorScaleAndCutoff.xyz;\n  #if USE_VERTEX_COLOR\n    o.rgb *= SRGBToLinear(v_color.rgb);\n    o.a *= v_color.a;\n  #endif\n  #if USE_TEXTURE\n    vec4 texColor = texture2D(mainTexture, v_uv);\n    texColor.rgb = SRGBToLinear(texColor.rgb);\n    o *= texColor;\n  #endif\n  #if USE_ALPHA_TEST\n    if (o.ALPHA_TEST_CHANNEL < colorScaleAndCutoff.w) discard;\n  #endif\n  CC_APPLY_FOG(o);\n  return CCFragOutput(o);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 75, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 44}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}, {"name": "CC_USE_FOG", "type": "number", "range": [0, 4]}, {"name": "CC_USE_ACCURATE_FOG", "type": "boolean"}, {"name": "USE_VERTEX_COLOR", "type": "boolean"}, {"name": "USE_TEXTURE", "type": "boolean"}, {"name": "SAMPLE_FROM_RT", "type": "boolean"}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "range": [0, 3]}, {"name": "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC", "type": "boolean"}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean"}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean"}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean"}, {"name": "CC_USE_HDR", "type": "boolean"}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "ALPHA_TEST_CHANNEL", "type": "string", "options": ["a", "r", "g", "b"]}]}, {"hash": 3680218420, "name": "builtin-unlit|planar-shadow-vs:vert|planar-shadow-fs:frag", "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCShadow", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  layout(std140) uniform CCMorph {\n    vec4 cc_displacementWeights[15];\n    vec4 cc_displacementTextureInfo;\n  };\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int pixelIndex) {\n        ivec2 texSize = textureSize(tex, 0);\n        return texelFetch(tex, ivec2(pixelIndex % texSize.x, pixelIndex / texSize.x), 0);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture(tex, x)),\n        decode32(texture(tex, y)),\n        decode32(texture(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    layout(std140) uniform CCSkinningTexture {\n      highp vec4 cc_jointTextureInfo;\n    };\n    layout(std140) uniform CCSkinningAnimation {\n      highp vec4 cc_jointAnimInfo;\n    };\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      layout(std140) uniform CCSkinning {\n        highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n      };\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if !USE_INSTANCING\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nvoid CCGetWorldMatrixFull(out mat4 matWorld, out mat4 matWorldIT)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n    vec3 scale = 1.0 / vec3(length(a_matWorld0.xyz), length(a_matWorld1.xyz), length(a_matWorld2.xyz));\n    vec3 scale2 = scale * scale;\n    matWorldIT = mat4(\n      vec4(a_matWorld0.xyz * scale2.x, 0.0),\n      vec4(a_matWorld1.xyz * scale2.y, 0.0),\n      vec4(a_matWorld2.xyz * scale2.z, 0.0),\n      vec4(0.0, 0.0, 0.0, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n    matWorldIT = cc_matWorldIT;\n  #endif\n}\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\nvec4 CalculatePlanarShadowPos(vec3 meshWorldPos, vec3 cameraPos, vec3 lightDir, vec4 plane) {\n  vec3 P = meshWorldPos;\n  vec3 L = lightDir;\n  vec3 N = plane.xyz;\n  float d = plane.w + EPSILON_LOWP;\n  float dist = (-d - dot(P, N)) / (dot(L, N) + EPSILON_LOWP);\n  vec3 shadowPos = P + L * dist;\n  return vec4(shadowPos, dist);\n}\nvec4 CalculatePlanarShadowClipPos(vec4 shadowPos, vec3 cameraPos, mat4 matView, mat4 matProj, vec4 nearFar, float bias) {\n  vec4 camPos = matView * vec4(shadowPos.xyz, 1.0);\n  float lerpCoef = saturate((nearFar.z < 0.0 ? -camPos.z : camPos.z) / (nearFar.y - nearFar.x));\n  camPos.z += mix(nearFar.x * 0.01, nearFar.y * EPSILON_LOWP * bias, lerpCoef);\n  return matProj * camPos;\n}\nout float v_dist;\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld, matWorldIT;\n  CCGetWorldMatrixFull(matWorld, matWorldIT);\n  vec3 worldPos = (matWorld * position).xyz;\n  vec4 shadowPos = CalculatePlanarShadowPos(worldPos, cc_cameraPos.xyz, cc_mainLitDir.xyz, cc_planarNDInfo);\n  position = CalculatePlanarShadowClipPos(shadowPos, cc_cameraPos.xyz, cc_matView, cc_matProj, cc_nearFar, cc_shadowWHPBInfo.w);\n  v_dist = shadowPos.w;\n  return position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nin float v_dist;\nvec4 frag () {\n  if(v_dist < 0.0)\n    discard;\n  return CCFragOutput(cc_shadowColor);\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nattribute vec3 a_position;\nattribute vec3 a_normal;\nattribute vec2 a_texCoord;\nattribute vec4 a_tangent;\n#if CC_USE_SKINNING\n    attribute vec4 a_joints;\n  attribute vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    attribute highp vec4 a_jointAnimInfo;\n  #endif\n  attribute vec4 a_matWorld0;\n  attribute vec4 a_matWorld1;\n  attribute vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    attribute vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    attribute vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    attribute vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    attribute vec4 a_sh_linear_const_r;\n    attribute vec4 a_sh_linear_const_g;\n    attribute vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    attribute float a_vertexId;\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if CC_USE_MORPH\n  uniform vec4 cc_displacementWeights[15];\n  uniform vec4 cc_displacementTextureInfo;\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n        int pixelIndex = elementIndex;\n        vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n        vec2 uv = getPixelCoordFromLocation(location, cc_displacementTextureInfo.xy);\n        return texture2D(tex, uv);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture2D(tex, x)),\n        decode32(texture2D(tex, y)),\n        decode32(texture2D(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    uniform highp vec4 cc_jointTextureInfo;\n    uniform highp vec4 cc_jointAnimInfo;\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      uniform highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture2D(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture2D(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture2D(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture2D(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\nvoid CCVertInput(inout vec4 In)\n{\n    In = vec4(a_position, 1.0);\n  #if CC_USE_MORPH\n    applyMorph(In);\n  #endif\n  #if CC_USE_SKINNING\n    CCSkin(In);\n  #endif\n}\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp vec4 cc_cameraPos;\n  uniform mediump vec4 cc_mainLitDir;\n  uniform mediump vec4 cc_nearFar;\n#if !USE_INSTANCING\n  uniform highp mat4 cc_matWorld;\n  uniform highp mat4 cc_matWorldIT;\n#endif\nvoid CCGetWorldMatrixFull(out mat4 matWorld, out mat4 matWorldIT)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n    vec3 scale = 1.0 / vec3(length(a_matWorld0.xyz), length(a_matWorld1.xyz), length(a_matWorld2.xyz));\n    vec3 scale2 = scale * scale;\n    matWorldIT = mat4(\n      vec4(a_matWorld0.xyz * scale2.x, 0.0),\n      vec4(a_matWorld1.xyz * scale2.y, 0.0),\n      vec4(a_matWorld2.xyz * scale2.z, 0.0),\n      vec4(0.0, 0.0, 0.0, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n    matWorldIT = cc_matWorldIT;\n  #endif\n}\nuniform mediump vec4 cc_shadowWHPBInfo;\n  uniform mediump vec4 cc_planarNDInfo;\nvec4 CalculatePlanarShadowPos(vec3 meshWorldPos, vec3 cameraPos, vec3 lightDir, vec4 plane) {\n  vec3 P = meshWorldPos;\n  vec3 L = lightDir;\n  vec3 N = plane.xyz;\n  float d = plane.w + EPSILON_LOWP;\n  float dist = (-d - dot(P, N)) / (dot(L, N) + EPSILON_LOWP);\n  vec3 shadowPos = P + L * dist;\n  return vec4(shadowPos, dist);\n}\nvec4 CalculatePlanarShadowClipPos(vec4 shadowPos, vec3 cameraPos, mat4 matView, mat4 matProj, vec4 nearFar, float bias) {\n  vec4 camPos = matView * vec4(shadowPos.xyz, 1.0);\n  float lerpCoef = saturate((nearFar.z < 0.0 ? -camPos.z : camPos.z) / (nearFar.y - nearFar.x));\n  camPos.z += mix(nearFar.x * 0.01, nearFar.y * EPSILON_LOWP * bias, lerpCoef);\n  return matProj * camPos;\n}\nvarying float v_dist;\nvec4 vert () {\n  vec4 position;\n  CCVertInput(position);\n  mat4 matWorld, matWorldIT;\n  CCGetWorldMatrixFull(matWorld, matWorldIT);\n  vec3 worldPos = (matWorld * position).xyz;\n  vec4 shadowPos = CalculatePlanarShadowPos(worldPos, cc_cameraPos.xyz, cc_mainLitDir.xyz, cc_planarNDInfo);\n  position = CalculatePlanarShadowClipPos(shadowPos, cc_cameraPos.xyz, cc_matView, cc_matProj, cc_nearFar, cc_shadowWHPBInfo.w);\n  v_dist = shadowPos.w;\n  return position;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nuniform lowp vec4 cc_shadowColor;\nvec4 CCFragOutput (vec4 color) {\n  return color;\n}\nvarying float v_dist;\nvec4 frag () {\n  if(v_dist < 0.0)\n    discard;\n  return CCFragOutput(cc_shadowColor);\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCLocal", "defines": ["!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 90, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 58}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}]}], [{"name": "opaque", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "planar-shadow", "propertyIndex": 0, "program": "builtin-unlit|planar-shadow-vs:vert|planar-shadow-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false, "stencilTestFront": true, "stencilFuncFront": 5, "stencilPassOpFront": 2, "stencilRefBack": 128, "stencilRefFront": 128, "stencilReadMaskBack": 128, "stencilReadMaskFront": 128, "stencilWriteMaskBack": 128, "stencilWriteMaskFront": 128}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag"}]}, {"name": "transparent", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "planar-shadow", "propertyIndex": 0, "program": "builtin-unlit|planar-shadow-vs:vert|planar-shadow-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false, "stencilTestFront": true, "stencilFuncFront": 5, "stencilPassOpFront": 2, "stencilRefBack": 128, "stencilRefFront": 128, "stencilReadMaskBack": 128, "stencilReadMaskFront": 128, "stencilWriteMaskBack": 128, "stencilWriteMaskFront": 128}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "add", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 1, "blendSrcAlpha": 2, "blendDstAlpha": 1}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}, {"name": "alpha-blend", "passes": [{"program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendSrcAlpha": 2, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28}, "tilingOffset": {"type": 16, "value": [1, 1, 0, 0]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1]}, "colorScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["colorScaleAndCutoff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["colorScaleAndCutoff", 3, 13]}, "color": {"linear": true, "type": 16, "handleInfo": ["mainColor", 0, 16]}, "colorScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}}}, {"phase": "deferred-forward", "propertyIndex": 0, "program": "builtin-unlit|unlit-vs:vert|unlit-fs:frag", "rasterizerState": {"cullMode": 0}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendSrcAlpha": 2, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}]]], 0, 0, [], [], []], [[[0, "default-spine-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": false, "IS_GRAY": false}]]], 0, 0, [0], [0], [3]], [[[3, "missing-effect-material", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_COLOR": true}], [[[{}, "mainColor", 8, [4, 4278255615]]], 11]]], 0, 0, [0], [0], [1]], [[[1, "for2d/builtin-spine", [{"hash": 3152403458, "name": "for2d/builtin-spine|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}, {"name": "a_color2", "format": 44, "location": 3, "defines": ["TWO_COLORED"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if USE_LOCAL\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nout vec4 v_light;\nout vec2 uv0;\n#if TWO_COLORED\n  in vec4 a_color2;\n  out vec4 v_dark;\n#endif\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  pos = cc_matViewProj * pos;\n  uv0 = a_texCoord;\n  v_light = a_color;\n  #if TWO_COLORED\n    v_dark = a_color2;\n  #endif\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n  layout(std140) uniform ALPHA_TEST_DATA {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_light;\n#if TWO_COLORED\n  in vec4 v_dark;\n#endif\nin vec2 uv0;\nuniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if TWO_COLORED\n    vec4 texColor = vec4(1, 1, 1, 1);\n    texColor *= texture(cc_spriteTexture, uv0);\n     o.a = texColor.a * v_light.a;\n    o.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;\n  #else\n    o *= texture(cc_spriteTexture, uv0);\n    o *= v_light;\n  #endif\n  ALPHA_TEST(o);\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matViewProj;\n#if USE_LOCAL\n  uniform highp mat4 cc_matWorld;\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec4 v_light;\nvarying vec2 uv0;\n#if TWO_COLORED\n  attribute vec4 a_color2;\n  varying vec4 v_dark;\n#endif\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  pos = cc_matViewProj * pos;\n  uv0 = a_texCoord;\n  v_light = a_color;\n  #if TWO_COLORED\n    v_dark = a_color2;\n  #endif\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\n#if USE_ALPHA_TEST\n      uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_light;\n#if TWO_COLORED\n  varying vec4 v_dark;\n#endif\nvarying vec2 uv0;\nuniform sampler2D cc_spriteTexture;\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if TWO_COLORED\n    vec4 texColor = vec4(1, 1, 1, 1);\n    texColor *= texture2D(cc_spriteTexture, uv0);\n     o.a = texColor.a * v_light.a;\n    o.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;\n  #else\n    o *= texture2D(cc_spriteTexture, uv0);\n    o *= v_light;\n  #endif\n  ALPHA_TEST(o);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [{"name": "cc_spriteTexture", "defines": []}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "TWO_COLORED", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}]}], [{"passes": [{"program": "for2d/builtin-spine|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []], [[[3, "missing-material", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_COLOR": true}], [[[{}, "mainColor", 8, [4, 4294902015]]], 11]]], 0, 0, [0], [0], [1]], [[[0, "ui-sprite-gray-alpha-sep-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": true, "IS_GRAY": true}]]], 0, 0, [0], [0], [0]], [[[0, "ui-base-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": false}]]], 0, 0, [0], [0], [0]], [[[0, "ui-sprite-gray-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": false, "IS_GRAY": true}]]], 0, 0, [0], [0], [0]], [[[0, "ui-graphics-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{}]]], 0, 0, [0], [0], [4]], [[[0, "ui-sprite-alpha-sep-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "CC_USE_EMBEDDED_ALPHA": true, "IS_GRAY": false}]]], 0, 0, [0], [0], [0]], [[[0, "ui-sprite-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true, "IS_GRAY": false, "CC_USE_EMBEDDED_ALPHA": false}]]], 0, 0, [0], [0], [0]]]]