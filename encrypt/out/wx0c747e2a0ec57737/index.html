<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小游戏 - wx0c747e2a0ec57737</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }

        #loading {
            color: white;
            font-family: Arial, sans-serif;
            text-align: center;
        }
    </style>
</head>

<body>
    <div id="loading">
        <p>正在加载游戏...</p>
        <div id="progress">0%</div>
    </div>
    <canvas id="gameCanvas" style="display: none;"></canvas>

    <script>
        // 模拟GameGlobal对象
        window.GameGlobal = {
            manager: null,
            scene: null,
            requestAnimationFrame: function (callback) {
                requestAnimationFrame(callback);
            },
            __isAdapterInjected: true
        };

        // 定义 __devtoolssubcontext 来跳过适配器注入
        window.__devtoolssubcontext = true;
        // 模拟canvas相关API
        const canvas = document.getElementById('gameCanvas');
        window.canvas = canvas;

        // 虚拟文件系统模块系统
        const modules = new Map();           // 存储模块: moduleId -> { factory, loaded }
        const moduleCache = new Map();       // 模块缓存: absolutePath -> exports
        let loadedModules = 0;
        let totalModules = 0;
        let currentModulePath = '/';         // 当前执行模块的路径

        // 解析相对路径为绝对路径
        function resolvePath(currentPath, requestPath) {
            // 如果请求路径不以 ./ 或 ../ 开头，直接返回（当作绝对路径）
            if (!requestPath.startsWith('./') && !requestPath.startsWith('../')) {
                return requestPath;
            }

            // 获取当前模块的目录路径
            const currentDir = currentPath.includes('/')
                ? currentPath.substring(0, currentPath.lastIndexOf('/') + 1)
                : '/';

            // 处理相对路径
            let resolved = currentDir + requestPath;

            // 标准化路径：处理 ../ 和 ./
            const parts = resolved.split('/');
            const stack = [];

            for (const part of parts) {
                if (part === '' || part === '.') {
                    continue;
                } else if (part === '..') {
                    stack.pop();
                } else {
                    stack.push(part);
                }
            }

            return stack.join('/');
        }

        // 全局define函数
        window.define = function (id, factory) {
            // console.log(`[Define] 注册模块: ${id}`);
            const moduleData = { factory, loaded: false };

            // 存储原始ID
            modules.set(id, moduleData);

            // 如果ID以.js结尾，同时存储不带.js的版本
            if (id.endsWith('.js')) {
                const withoutJs = id.slice(0, -3);
                modules.set(withoutJs, moduleData);
                // console.log(`[Define] 同时注册: ${withoutJs}`);
            }
            // 如果ID不以.js结尾，同时存储带.js的版本
            else {
                const withJs = id + '.js';
                modules.set(withJs, moduleData);
                // console.log(`[Define] 同时注册: ${withJs}`);
            }

            totalModules++;
            updateProgress();
        };

        // 全局require函数
        window.require = function (requestPath) {
            // 解析绝对路径
            const absolutePath = resolvePath(currentModulePath, requestPath);

            // 检查缓存
            if (moduleCache.has(absolutePath)) {
                return moduleCache.get(absolutePath);
            }

            // 查找模块定义
            const moduleDef = modules.get(absolutePath);
            if (!moduleDef) {
                console.warn(`模块未找到: ${requestPath}`);
                console.log(`当前模块路径: ${currentModulePath}`);
                console.log(`解析后路径: ${absolutePath}`);
                console.log(`可用模块:`, Array.from(modules.keys()).slice(0, 5));
                return {};
            }

            if (absolutePath !== requestPath) {
                // console.log(`[Require] 路径解析: "${requestPath}" -> "${absolutePath}"`);
            } else {
                // console.log(`[Require] 加载模块: ${absolutePath}`);
            }

            // 保存当前路径，设置新的执行上下文
            const previousPath = currentModulePath;
            currentModulePath = absolutePath;

            const module = { id: absolutePath, exports: {} };
            const exports = module.exports;

            try {
                moduleDef.factory.call(exports, window.require, module, exports);
                moduleCache.set(absolutePath, module.exports);
                moduleDef.loaded = true;
                loadedModules++;
                updateProgress();
                return module.exports;
            } catch (error) {
                console.error(`模块执行失败: ${absolutePath}`, error);
                return {};
            } finally {
                // 恢复之前的路径上下文
                currentModulePath = previousPath;
            }
        };

        // 更新加载进度
        function updateProgress() {
            if (totalModules > 0) {
                const progress = Math.round((loadedModules / totalModules) * 100);
                document.getElementById('progress').textContent = progress + '%';

                if (progress >= 100) {
                    setTimeout(() => {
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('gameCanvas').style.display = 'block';
                        console.log('🎮 游戏加载完成！');
                    }, 500);
                }
            }
        }

        // 加载游戏脚本
        function loadGameScript() {
            const script = document.createElement('script');
            script.src = 'game.js';
            script.onload = function () {
                console.log('游戏脚本加载完成');
            };
            script.onerror = function () {
                console.error('游戏脚本加载失败');
                document.getElementById('loading').innerHTML = '<p style="color: red;">游戏加载失败</p>';
            };
            document.head.appendChild(script);
        }

        // 启动游戏
        console.log('🎮 启动微信小游戏环境...');
        loadGameScript();
    </script>
</body>

</html>

<script>
    window.wx = {
        getSystemInfoSync: function () {
            return {
                platform: 'devtools',
                version: '8.0.5',
                SDKVersion: '2.19.4',
                system: 'macOS 10.15.7',
                model: 'mac',
                pixelRatio: 2,
                windowWidth: 375,
                windowHeight: 667,
                language: 'zh_CN',
                statusBarHeight: 20,
                safeArea: {
                    left: 0,
                    right: 375,
                    top: 20,
                    bottom: 667,
                    width: 375,
                    height: 647
                }
            };
        },

        onShow: function (callback) {
            console.log('[wx] 注册onShow回调');
            setTimeout(callback, 100);
        },

        onHide: function (callback) {
            console.log('[wx] 注册onHide回调');
        },

        request: function (options) {
            console.log('[wx] 网络请求:', options.url);
            setTimeout(() => {
                options.success && options.success({ data: 'mock data' });
            }, 100);
        },

        getStorageSync: function (key) {
            return localStorage.getItem(key);
        },

        setStorageSync: function (key, value) {
            localStorage.setItem(key, value);
        },

        showToast: function (options) {
            console.log('[wx] 显示toast:', options.title);
        },

        getLaunchOptionsSync: function () {
            return {}
        },

        createImage: function () {
            return new Image()
        },
        createCanvas: function () {
            return window.canvas
        },
        onTouchStart: function () {

        },
        onTouchMove: function () {

        },
        onTouchEnd: function () {

        },
        onTouchCancel: function () {

        },

        onDeviceOrientationChange: function () {
        },

        getFileSystemManager: function () {
            return {
                readFileSync: function (filePath, encoding = 'utf8') {
                }
            }
        },


    };
</script>