[1, ["61NXJOyyZNU6ZGh0FJkMv2", "3eaRF8a1JADZfX3Qj84+Oo@03348", "3eaRF8a1JADZfX3Qj84+Oo@86470", "3eaRF8a1JADZfX3Qj84+Oo@9242e", "3eaRF8a1JADZfX3Qj84+Oo@fe56c", "2enw6dg1JAa5xhWIDgaiWv@ebf5f", "02YI/3HBZKjozk+EPWJc3l@6c48a", "94FGSZedBCE46Kdw4aaHkw@f9941", "65nnJ3SlJOoYRwNWpKye8V@f9941", "2enw6dg1JAa5xhWIDgaiWv@46320", "2enw6dg1JAa5xhWIDgaiWv@50f42", "2enw6dg1JAa5xhWIDgaiWv@c4250", "2enw6dg1JAa5xhWIDgaiWv@2daac", "3eaRF8a1JADZfX3Qj84+Oo@1157a", "2enw6dg1JAa5xhWIDgaiWv@d75f2", "3eaRF8a1JADZfX3Qj84+Oo@3e84d", "3eaRF8a1JADZfX3Qj84+Oo@b0545", "3eaRF8a1JADZfX3Qj84+Oo@e64fb", "3eaRF8a1JADZfX3Qj84+Oo@ed403", "3eaRF8a1JADZfX3Qj84+Oo@a6fb9", "3eaRF8a1JADZfX3Qj84+Oo@43060", "2enw6dg1JAa5xhWIDgaiWv@11722", "ffuIqPr2JI9I8dPLYGRDpD@f9941", "2enw6dg1JAa5xhWIDgaiWv@4bada", "2enw6dg1JAa5xhWIDgaiWv@12b33", "2enw6dg1JAa5xhWIDgaiWv@80081", "faQO1SSTFOvo4Bd/0qdjpc@8c199", "faQO1SSTFOvo4Bd/0qdjpc@993b1", "2enw6dg1JAa5xhWIDgaiWv@46629", "afxHkx8GZGsJC+n+YfITQo@f9941"], ["node", "_spriteFrame", "_font", "_target", "_parent", "_textureSource", "root", "btnDelRole", "yinsiNode", "btnYaoQing", "btnKefuNode", "btnExchange", "btnLogout", "node1", "hurt", "onlyShowMe", "showSkillEffect", "showNick", "showGuildName", "showChenghao", "soundEffect", "bgMusic", "txtVer", "txtID", "txtName", "subNode", "data", "_scrollView", "_normalSprite"], [["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "__prefab", "_spriteFrame", "_color"], 0, 1, 4, 6, 5], ["cc.Node", ["_name", "_active", "_layer", "_components", "_prefab", "_children", "_parent", "_lpos"], 0, 9, 4, 2, 1, 5], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_lpos", "_children"], 0, 12, 4, 1, 5, 2], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_enableOutline", "_enableWrapText", "_cacheMode", "_fontFamily", "_isBold", "_outlineWidth", "_horizontalAlign", "node", "__prefab", "_font", "_color"], -9, 1, 4, 6, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_alignMode", "node", "__prefab"], -1, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "clickEvents", "_normalSprite"], 2, 1, 4, 5, 1, 9, 6], "cc.SpriteFrame", ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingLeft", "_spacingX", "_affectedByScale", "_isAlign", "node", "__prefab"], -3, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "vertical", "node", "__prefab", "_content", "_verticalScrollBar"], -1, 1, 4, 1, 1], ["cc.Toggle", ["_isChecked", "node", "__prefab", "_normalColor", "_target", "checkEvents", "_checkMark", "clickEvents"], 2, 1, 4, 5, 1, 9, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["87ba4CPkYZO/ZEr98HBe/ke", ["node", "__prefab", "txtName", "txtID", "txtVer", "bgMusic", "soundEffect", "showChenghao", "showGuildName", "showNick", "showSkillEffect", "onlyShowMe", "hurt", "node1", "btnLogout", "btnExchange", "btnKefuNode", "btnYaoQing", "yinsiNode", "btnDelRole"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["5503dCBHmhOQak0pG91aMay", ["node", "__prefab", "subNode"], 3, 1, 4, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_maxWidth", "_fontFamily", "_cacheMode", "node", "__prefab"], -3, 1, 4], ["<PERSON>.<PERSON>", ["_direction", "node", "__prefab", "_handle"], 2, 1, 4, 1]], [[13, 0, 2], [15, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 1], [1, 0, 6, 5, 3, 4, 7, 2], [19, 0, 1, 1], [9, 0, 1, 3, 3], [1, 0, 6, 3, 4, 7, 2], [7, 0, 1, 2, 3, 1], [5, 0, 1, 2, 5, 3, 4, 2], [3, 0, 1, 2, 3, 6, 4, 7, 5, 12, 13, 14, 9], [0, 0, 3, 4, 5, 2], [1, 0, 6, 3, 4, 2], [0, 0, 1, 3, 4, 5, 3], [0, 0, 1, 3, 4, 3], [9, 0, 1, 2, 3, 4], [2, 0, 7, 3, 4, 6, 2], [2, 0, 5, 3, 4, 2], [3, 0, 1, 2, 3, 4, 7, 5, 12, 13, 14, 8], [11, 1, 2, 7, 3, 4, 5, 6, 1], [0, 3, 4, 5, 1], [4, 0, 1, 2, 4, 5, 4], [0, 2, 0, 3, 4, 5, 3], [2, 0, 5, 3, 4, 6, 2], [8, 0, 1, 2, 3, 6, 7, 5], [20, 0, 1, 1], [1, 0, 5, 3, 4, 2], [1, 0, 1, 6, 5, 3, 4, 7, 3], [2, 0, 5, 7, 3, 4, 6, 2], [17, 0, 1, 1], [18, 0, 1, 2, 1], [3, 0, 11, 1, 2, 3, 6, 4, 5, 12, 13, 9], [12, 0, 2], [1, 0, 1, 6, 5, 3, 4, 3], [1, 0, 5, 3, 4, 7, 2], [1, 0, 6, 5, 3, 4, 2], [1, 0, 2, 6, 5, 3, 4, 3], [2, 0, 2, 1, 7, 3, 4, 6, 4], [2, 0, 2, 1, 5, 3, 4, 4], [2, 0, 1, 5, 3, 4, 6, 3], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1], [4, 0, 2, 3, 4, 5, 4], [4, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 3, 4, 5, 4], [0, 0, 3, 4, 6, 5, 2], [0, 2, 0, 3, 4, 6, 5, 3], [0, 2, 0, 1, 3, 4, 6, 5, 4], [0, 1, 3, 4, 5, 2], [0, 2, 0, 3, 4, 6, 3], [16, 0, 1, 2, 1], [8, 0, 1, 4, 5, 6, 7, 5], [5, 1, 2, 3, 4, 1], [5, 0, 1, 2, 3, 4, 2], [5, 0, 1, 2, 5, 3, 4, 6, 2], [3, 0, 1, 2, 3, 6, 4, 5, 12, 13, 14, 8], [3, 0, 1, 2, 8, 3, 6, 9, 5, 12, 13, 9], [3, 0, 1, 2, 3, 6, 4, 7, 5, 10, 12, 13, 14, 10], [3, 0, 1, 2, 3, 6, 4, 12, 13, 15, 7], [10, 0, 1, 2, 4, 5, 6, 4], [10, 0, 1, 3, 4, 5, 6, 7, 4], [21, 0, 1, 2, 3, 4, 5, 6, 7, 7], [11, 0, 1, 2, 3, 4, 5, 6, 2], [22, 0, 1, 2, 3, 2]], [[[[31, "SetDlgUI"], [25, "SetDlgUI", [-23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45], [[2, -2, [0, "5czi08k4hOBpQfwtDz60Qu"], [5, 750, 1334]], [39, -21, [0, "ff7YOCNm1ARamVlmZzKG/t"], -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3], [20, 45, 750, 1334, -22, [0, "4evPsTLVZApp6u4ibqTdov"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [32, "yinsiNode", false, 1, [-48, -49, -50, -51, -52], [[2, -46, [0, "ccrMq1Z8ZIQrltHiKJ3Q4u"], [5, 750, 1334]], [20, 45, 1, 1, -47, [0, "91eMbdt7JPy4o0SqPb40xg"]]], [1, "5eUqx0Lv5NrZSoqocUBZAO", null, null, null, 1, 0]], [27, "onlyShowMe", 1, [-58, -59], [[[2, -53, [0, "aeexoMehJPzJHace9Hz83u"], [5, 30, 30]], [12, 2, false, -54, [0, "fdvGbfsFhKTbA9SAf2VhNX"], 29], -55, [48, -57, [0, "38ZibAmzdKTYiCz478De5D"], -56]], 4, 4, 1, 4], [1, "93gYoa7L9DN5HMEXnbCS20", null, null, null, 1, 0], [1, -193.706, -136.12199999999996, 0]], [33, "node2", [-62, -63, -64, -65], [[7, -60, [0, "835E5aMRtHTYepg4yEp5EY"], [5, 668, 100], [0, 0, 0.5]], [23, 1, 1, 10, 10, -61, [0, "2eQSA/N3tCQopIiaM8LDHv"]]], [1, "8fYMIdPWFARKimFkJtWa4W", null, null, null, 1, 0], [1, -257.16700000000003, 0, 0]], [3, "node0", 1, [-68, -69, -70, -71], [[2, -66, [0, "b1LSSOW85FOYPigH/XSGaB"], [5, 456, 100]], [23, 1, 1, 2, 50, -67, [0, "7cYPSFswVATbEnSN7MmP7Q"]]], [1, "8diCbPyEFDiJDfRZXMFS76", null, null, null, 1, 0], [1, 0, -211.997, 0]], [3, "btnKefu", 5, [-76], [[2, -72, [0, "cceC+jF2BOeLFtVow/Gz08"], [5, 76, 74]], [10, 2, -73, [0, "bchP76SoxJ9L5iq+fGPVKq"], 35], [8, 3, -75, [0, "a9cjeNzd1OD5m66/BQvhQF"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -74]], [1, "8fuEBUC7xFIoarfHqbdlso", null, null, null, 1, 0], [1, 64, 0, 0]], [3, "node1", 1, [-79, -80, -81], [[2, -77, [0, "82UDvoCYVBJ5XRXrAHI6wH"], [5, 290, 100]], [23, 1, 1, 2, 30, -78, [0, "28CfhYZLhOFYGdxZ3XC3mE"]]], [1, "d3VTjvvEpIaq+m337KwRfR", null, null, null, 1, 0], [1, 0, -301.442, 0]], [3, "btnDelRole", 7, [-86], [[2, -82, [0, "882NjyLLxHv4smNhfL7VMt"], [5, 76, 74]], [10, 0, -83, [0, "b6rlw6r8hLbqk4VX3wL+wL"], 43], [8, 3, -85, [0, "6bAmPOq/VAj6LlwQ1MzI2h"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -84]], [1, "98beryKEBJCo7lSaP1/Vjs", null, null, null, 1, 0], [1, 107, 0, 0]], [3, "btnLogout", 4, [-91], [[2, -87, [0, "26T6GBE6ZGl5Z6L6Dfz9H+"], [5, 157, 60]], [10, 2, -88, [0, "34ZqDUClRBCLLdH/ajoK8G"], 48], [8, 3, -90, [0, "83JJWpllpGYL3z0jWpDlvc"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -89]], [1, "72QsaHt/5Jr7iqbFQV+8i1", null, null, null, 1, 0], [1, 255.5, 0, 0]], [3, "btnExchange", 4, [-96], [[2, -92, [0, "0fmHXYMKFF7p88MJTWtT1b"], [5, 157, 60]], [19, -93, [0, "f5tSNH71FKdoxWOwavSM/z"], 50], [8, 3, -95, [0, "c9FuKTccJNwbV5u7IOtBCR"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -94]], [1, "4d8jqD0yRNQbveEW1xqB+M", null, null, null, 1, 0], [1, 422.5, 0, 0]], [3, "btnYaoQing", 4, [-101], [[2, -97, [0, "5f06p2cv1CNIxyjB68H0vu"], [5, 157, 60]], [19, -98, [0, "8f8/A+IS5M1J8n/8HZD+H6"], 52], [8, 3, -100, [0, "30bWh2Pk5FBamVxda/meWH"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -99]], [1, "816sGhSiFGbZqORFAa+5qA", null, null, null, 1, 0], [1, 589.5, 0, 0]], [11, "bgClose", 1, [[2, -102, [0, "74QYk6VdFA3K0LJuN503f5"], [5, 750, 1334]], [43, 0, -103, [0, "073Z31T3xIe5j86SzZKs3D"], [4, 3019898880], 0], [50, -105, [0, "faCo4kgHpOPrRn/y+XgrFy"], [4, 4292269782], -104], [20, 45, 39, 39, -106, [0, "83uHL9utRGGZUCzVlVGwA3"]]], [1, "01V6nbITBPS5R+3AWj4iTg", null, null, null, 1, 0]], [15, "Toggle", [-110], [[[2, -107, [0, "6ed0O+kgBJx6H4bfes7P3V"], [5, 122, 41]], [12, 2, false, -108, [0, "32AeRNUYhKObw9y9qHC+LD"], 12], -109], 4, 4, 1], [1, "61qYobCPdNRbiKBLnjL2FS", null, null, null, 1, 0], [1, 121.49400000000003, -2.146000000000072, 0]], [15, "Toggle", [-114], [[[2, -111, [0, "b3g6G/9xFLgJf29NkZgA53"], [5, 122, 41]], [12, 2, false, -112, [0, "9cyhLfMCRLWLBQd4TyqmLK"], 14], -113], 4, 4, 1], [1, "b1QWVm2TNI/JgNgRVRy6fR", null, null, null, 1, 0], [1, 121.49400000000003, -2.146000000000072, 0]], [15, "Toggle", [-118], [[[2, -115, [0, "68/ut3a9RCMK/2wWaaizxX"], [5, 122, 41]], [12, 2, false, -116, [0, "0cU1kQwz1FibHMbpDE6w8L"], 16], -117], 4, 4, 1], [1, "5ePwjesI1AN49NuOAIWoUH", null, null, null, 1, 0], [1, 121.49400000000003, -2.146000000000072, 0]], [15, "Toggle", [-122], [[[2, -119, [0, "648r37StVHL5VlsZ9QwkyH"], [5, 122, 41]], [12, 2, false, -120, [0, "49d1XzXpdIkYj4ZO/U4/5U"], 18], -121], 4, 4, 1], [1, "19/fUR/Y9BVKxVZrsnnnK+", null, null, null, 1, 0], [1, 113.92200000000008, -0.7450000000000045, 0]], [15, "Toggle", [-126], [[[2, -123, [0, "9bgB+gL/BKWLgTnSoNdAhz"], [5, 122, 41]], [12, 2, false, -124, [0, "f23OBeJZ1Erovy4jZcq2b9"], 20], -125], 4, 4, 1], [1, "00JJClTfNECYO2AiwnAffr", null, null, null, 1, 0], [1, 113.92200000000008, -0.7450000000000045, 0]], [15, "Toggle", [-130], [[[2, -127, [0, "95eHEf3xVMxYepTrlaylA8"], [5, 122, 41]], [12, 2, false, -128, [0, "c3yKW8B6tGabCFreXPt5sP"], 22], -129], 4, 4, 1], [1, "ealuyIlaBChbQstwD6sY/2", null, null, null, 1, 0], [1, 113.92200000000008, -0.7450000000000045, 0]], [15, "Toggle", [-134], [[[2, -131, [0, "53a5o5T5pI6bhxzXzrKODN"], [5, 122, 41]], [12, 2, false, -132, [0, "ccqRyidSJJsJLqngDmrhMA"], 24], -133], 4, 4, 1], [1, "26UzGn7QtDcbEYStqe2BGy", null, null, null, 1, 0], [1, 113.92200000000008, -0.7450000000000045, 0]], [3, "btnChangeRole", 5, [-139], [[2, -135, [0, "1eT1W2d+lLaJDKGanDTALm"], [5, 76, 74]], [10, 2, -136, [0, "51SYAIN4FAPJrPadQxQXhf"], 31], [8, 3, -138, [0, "15CSmOBB5GErthYbYNbFzE"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -137]], [1, "caj6hc6I5GdL7pOgY+f1ZG", null, null, null, 1, 0], [1, -188, 0, 0]], [3, "btnKasi", 5, [-144], [[2, -140, [0, "36wZoplcVILJZfSlqJLVcI"], [5, 76, 74]], [10, 2, -141, [0, "687e9qMytN4qnL5LGUoV/y"], 33], [8, 3, -143, [0, "eeKRCJlcdBkrjLKFxkdDh8"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -142]], [1, "782Se+py5CTYBlc3uljgyO", null, null, null, 1, 0], [1, -62, 0, 0]], [3, "btnYijian", 5, [-149], [[2, -145, [0, "09+Dp3xIBO/KS+RvQAswHM"], [5, 76, 74]], [10, 2, -146, [0, "18UfuCzwRA+6y//A7VZvHX"], 37], [8, 3, -148, [0, "fdt8y9UapBCoicIkWZFWqp"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -147]], [1, "bawkphEpdCToM1AvSHYqiF", null, null, null, 1, 0], [1, 190, 0, 0]], [3, "btnYinSi", 7, [-154], [[2, -150, [0, "2dLuAZk4pOVLFlewEGu7x9"], [5, 76, 74]], [10, 0, -151, [0, "30dQQhPVhIhpbSW2JZutaG"], 39], [8, 3, -153, [0, "c0ik2rtW1Ixam+MnxxuuRW"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -152]], [1, "71C9RgYvJBU7ZugQ7u5H+9", null, null, null, 1, 0], [1, -105, 0, 0]], [3, "btnXieYi", 7, [-159], [[2, -155, [0, "97iRbGBhFAXJBEoNhueiaI"], [5, 76, 74]], [10, 0, -156, [0, "afGv2B71JPxpYZC7FPjm2m"], 41], [8, 3, -158, [0, "1cyqzUaAJMpIxiBLFq2gQ1"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -157]], [1, "e45E7Q/YpIvq8G0wOQe66m", null, null, null, 1, 0], [1, 1, 0, 0]], [36, "scrollBar", false, 33554432, [-164], [[[7, -160, [0, "4eFMH3kHJHiJbAYW0sZngQ"], [5, 12, 250], [0, 1, 0.5]], [44, 1, 0, -161, [0, "21nBuKXQJPPYdCgOEE28oH"], [4, 16777215], 44], [40, 37, 250, 1, -162, [0, "c5qK/5X5RNS7hCOpoWeAa0"]], -163], 4, 4, 4, 1], [1, "d5mi1lN/FIFZhRFtlL+oQh", null, null, null, 1, 0], [1, 120, 0, 0]], [3, "btnServer", 4, [-169], [[2, -165, [0, "5ak3QdclVAcI4E+tbJPWCa"], [5, 157, 60]], [10, 2, -166, [0, "2988Da7QZEI6KwWCNpZ7oA"], 46], [8, 3, -168, [0, "c2v3ogN0xCXJBN1fZ8KmQ9"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -167]], [1, "afhtLS+U5NPbHk5fDYW5Vw", null, null, null, 1, 0], [1, 88.5, 0, 0]], [25, "view", [-174], [[7, -170, [0, "83vFapUVxO0YdJXGkN2v0l"], [5, 600, 770], [0, 0.5, 1]], [28, -171, [0, "b5FMoIfjBDLJfAgwxx/t5v"]], [29, -172, [0, "84OETAMnxO3YfV034MjpT5"], [4, 16777215]], [41, 45, 240, 250, 0, -173, [0, "b1iC6fpzZISIcLXFwZjPnR"]]], [1, "c3RkaY599FIoZcFg0dJl4m", null, null, null, 1, 0]], [34, "content", 27, [-178], [[7, -175, [0, "89fbvOJcNBWanxuHV3Jni2"], [5, 600, 37.8], [0, 0.5, 1]], [42, 41, 220, 0, -176, [0, "c3eLPRDrFHb5Pk9YQ0QFkr"]], [49, 1, 2, true, true, -177, [0, "c25C7ZCvlP7b+b1u/t2eEO"]]], [1, "57cyXdFKlJ14lahix5Twps", null, null, null, 1, 0]], [6, "btnClose", 1, [[2, -179, [0, "10CXxZqbRLtIOYAcKtmHFO"], [5, 58, 57]], [21, 1, 2, -180, [0, "33oVGDhmlKCJKNmeOFj0GV"], 2], [51, 3, -182, [0, "0e9iCsCKtM/7c5qSPzhkMA"], [4, 4292269782], -181]], [1, "95q2WjWj5HPJuGotrhzVIO", null, null, null, 1, 0], [1, 262.031, 328.661, 0]], [3, "name", 1, [-185, -186], [[2, -183, [0, "efTIDqHXJJI7CbGcwwdgkG"], [5, 268, 38]], [21, 1, 0, -184, [0, "5dY8bFuOpJPYJ5Jih7duac"], 7]], [1, "eeW7mExMlHC6AgrykmQP3J", null, null, null, 1, 0], [1, -2.3829999999999814, 222.11800000000005, 0]], [6, "btnRename", 30, [[2, -187, [0, "780vXU9ZdLNJ6MNpSE6sm0"], [5, 38, 38]], [10, 2, -188, [0, "20xtCunBFN9IshqPvWsBQo"], 5], [52, 3, -190, [0, "a6WsgwziZJs7HmIJIVviK3"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -189, 6]], [1, "98UiH/R/1PPblzXCiZ8xpC", null, null, null, 1, 0], [1, 121.142, 0, 0]], [6, "btnCopy", 1, [[2, -191, [0, "02Wg4kuS5OM5JCr+4tiGx9"], [5, 38, 38]], [10, 2, -192, [0, "5befNhXlVAV7W1kFbp33vC"], 9], [8, 3, -194, [0, "e80S3jLEVJ/K3c4h0UtXWz"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -193]], [1, "2cLDd8RbhK3q8Sl2xiPl2K", null, null, null, 1, 0], [1, 139.293, 171.83000000000004, 0]], [3, "txt_music", 1, [13], [[7, -195, [0, "31r9AR34ZKIrZ7xUuyxs33"], [5, 44, 29.2], [0, 0, 0.5]], [17, "音乐", 20, 20, 20, false, 1, true, -196, [0, "0bhOLPc5hE64F35dnL0J1k"], 13], [4, -197, [0, "0eVB2n1B9ItZDN26aZhf3A"]]], [1, "3eae+7/z5AppwBI5SwUnw8", null, null, null, 1, 0], [1, -218.25, 62.59400000000005, 0]], [3, "txt_chenhao", 1, [14], [[7, -198, [0, "efm3DPQphPSoK9ryj1igrA"], [5, 44.05998229980469, 29.2], [0, 0, 0.5]], [17, "称号", 20, 20, 20, false, 1, true, -199, [0, "89Mq9vjj9C3r94vDc05Uzy"], 15], [4, -200, [0, "76QzNzNIFM0KhkumJnDTwv"]]], [1, "06dtvkgZVDpatLoR+8AZ2t", null, null, null, 1, 0], [1, -218.25, -4.326000000000022, 0]], [3, "txt_nick", 1, [15], [[7, -201, [0, "70O/LPxDxEqqkS/wgJnGD/"], [5, 44.1199951171875, 29.2], [0, 0, 0.5]], [17, "昵称", 20, 20, 20, false, 1, true, -202, [0, "82SDWb5PZEUZ2YqX0oGuTs"], 17], [4, -203, [0, "98lFyq4x9L9a5VCh5vKDns"]]], [1, "177Xk+Ny5JrLV34J6ikxRm", null, null, null, 1, 0], [1, -218.25, -72.68399999999997, 0]], [3, "txt_effect", 1, [16], [[7, -204, [0, "e4QMBss5hCe4L1X9i6ENFj"], [5, 43.93998718261719, 29.2], [0, 0, 0.5]], [17, "音效", 20, 20, 20, false, 1, true, -205, [0, "03y/3cMWNPj6TT0YPaFrEz"], 19], [4, -206, [0, "71P7jCUolKOLXbp+H/2Q+0"]]], [1, "61lc/g2y5EzppThGubOIov", null, null, null, 1, 0], [1, 42.66699999999997, 61.73000000000002, 0]], [3, "txt_guild", 1, [17], [[7, -207, [0, "12btrVNeRK+6Y/mtKVv5AD"], [5, 44.019989013671875, 29.2], [0, 0, 0.5]], [17, "公会", 20, 20, 20, false, 1, true, -208, [0, "04PfbRkA9N6Yk3CQG3RuzS"], 21], [4, -209, [0, "de+Fsrr4FCqLBrxrG0Uz4m"]]], [1, "0f1aHmWjBIk5agFekE710c", null, null, null, 1, 0], [1, 42.66699999999997, -7.347999999999956, 0]], [3, "txt_hurt", 1, [18], [[7, -210, [0, "c2SAGl7yhFio7PIem5d/Y9"], [5, 44.019989013671875, 29.2], [0, 0, 0.5]], [17, "伤害", 20, 20, 20, false, 1, true, -211, [0, "92jgd7A/pKJLIc1rbUcCVz"], 23], [4, -212, [0, "0eghhjkgxBvLELJpC0eF4l"]]], [1, "05TvkffPBBaYw3VkhtphTX", null, null, null, 1, 0], [1, 42.66699999999997, -74.34799999999996, 0]], [26, "txt_skillEffect", false, 1, [19], [[7, -213, [0, "29dQDvzENMarfCBm77IE9G"], [5, 43.93998718261719, 29.2], [0, 0, 0.5]], [17, "特效", 20, 20, 20, false, 1, true, -214, [0, "7aJFVuXGBGwagnAlwVpI7/"], 25], [4, -215, [0, "06B5yP4kpBHovSA8vBnSce"]]], [1, "99VI3RcHJJtrtkBF28iErY", null, null, null, 1, 0], [1, 42.66699999999997, -75.42700000000002, 0]], [6, "FontLabel", 3, [[7, -216, [0, "c95eep95lA2LBbgvv5voYF"], [5, 113.97799682617188, 31.72], [0, 0, 0.5]], [53, "仅显示自己", 22, 22, 22, false, false, true, -217, [0, "1cInYLWPpBGqbWHs6ntsTZ"], 28], [4, -218, [0, "9azttA0GFLAoS1/bQV+GpJ"]]], [1, "faVgMUyJ1LxL2gmg4nN0+w", null, null, null, 1, 0], [1, 22.15, 0.361, 0]], [27, "scrollView", 1, [25, -221], [[[2, -219, [0, "bfKD8E9BBKzYb0oY5N+hfR"], [5, 660, 100]], -220], 4, 1], [1, "c9yr3rDW9F8KSYuHJCXeTG", null, null, null, 1, 0], [1, -2.61099999999999, -396.067, 0]], [35, "view", 33554432, 41, [4], [[2, -222, [0, "a9aathhbhIWaLICvzwnm+H"], [5, 520, 100]], [28, -223, [0, "9e8St1t4ZCprD3dyiKfX5I"]], [29, -224, [0, "0dYyElINxFX5MdxueIjD34"], [4, 16777215]]], [1, "2ewaHgpsxLULLWghwKAyLQ", null, null, null, 1, 0]], [11, "Sprite", 2, [[2, -225, [0, "32MHtoFVRIip/5NgeNeVMf"], [5, 750, 1334]], [45, 1, 0, false, -226, [0, "64rLm7q/ZPALdob5bbs2T3"], [4, 3019898880], 53], [20, 45, 2, 2, -227, [0, "83qBXTXJpOzKiItp47NeVy"]], [24, -228, [0, "20Tx3DOR9Iwq+3rl03dOkX"]]], [1, "a4kcX52dtOyJstgV3Brzn2", null, null, null, 1, 0]], [6, "close<PERSON><PERSON><PERSON>", 2, [[2, -229, [0, "dcUqGoNcpArqsWxrMtaENT"], [5, 58, 57]], [46, false, -230, [0, "99QoEeYbpJW4X+0t0Xa0PN"], 55], [8, 3, -232, [0, "598HK9RThJKIEbe+B1ieQW"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "onClick", 1]], [4, 4292269782], -231]], [1, "e8lZlQOhtER4I07Rq122TJ", null, null, null, 1, 0], [1, 300.8229999999999, 372.145, 0]], [6, "bg", 1, [[2, -233, [0, "6fFr17FFdCNJJLuVkUmUIC"], [5, 546, 795.666]], [21, 1, 0, -234, [0, "a64WDbripFW7PL4QFmQfQc"], 1], [24, -235, [0, "a5t2X6SRJCDr5lzRTBsOkG"]]], [1, "81O23U6sxDE4aXttdZ0iMh", null, null, null, 1, 0], [1, 0, -64.333, 0]], [3, "pub_frame_bottom_9", 1, [-238], [[2, -236, [0, "43mCvDAUJExpfgvZTvGFTb"], [5, 511, 46]], [19, -237, [0, "e5J8Z92PlJp5ai49x0ZlS9"], 4]], [1, "80Q3bumW9EyLzheBufK4TE", null, null, null, 1, 0], [1, 0, 292, 0]], [22, "txtName", 30, [[[7, -239, [0, "64IVWN0dpIz5FvfY5Iswzm"], [5, 84.1199951171875, 29.2], [0, 0, 0.5]], -240, [4, -241, [0, "5csib6BAxGsJfQCMlBs0aq"]]], 4, 1, 4], [1, "d8RTEVlENHg5O6WbSbkoUZ", null, null, null, 1, 0], [1, -123.57400000000001, 0, 0]], [3, "shezhi_frame_01", 1, [-244], [[2, -242, [0, "bfyDS2tntKWZQiZnwQcqIe"], [5, 500, 25]], [19, -243, [0, "12/C2VZHxAWICDLmuzRgWp"], 11]], [1, "96ik7NatBIuqRZKNyykgri", null, null, null, 1, 0], [1, 0, 117.92399999999998, 0]], [26, "shezhi_frame_02", false, 1, [-247], [[2, -245, [0, "26cDrt8qFPHLVrQzGyxCGV"], [5, 500, 25]], [19, -246, [0, "13FdhITHpK9LUbCo0W2Taq"], 27]], [1, "27uvBNy8hJ5oe+wArEMkev", null, null, null, 1, 0], [1, 0, -4, 0]], [6, "btn_text", 20, [[2, -248, [0, "bfgwEpSgpH44xV7FWKl8oy"], [5, 83.93998718261719, 29.2]], [9, "选择角色", 20, 20, 20, false, false, 1, true, -249, [0, "a0jHrxvG9DuI+CCOMhhTNb"], 30], [4, -250, [0, "8aGzfgf4JMUqWH4Id9dswE"]]], [1, "d3IOekVxNA4YCWxXCGisdF", null, null, null, 1, 0], [1, 0, -31.753000000000043, 0]], [6, "btn_text", 21, [[2, -251, [0, "45fjlNnzhPgaw1WHgO/LYV"], [5, 84, 29.2]], [9, "角色卡死", 20, 20, 20, false, false, 1, true, -252, [0, "b2Mgxr+iJDM6qI6F6Fsjwz"], 32], [4, -253, [0, "43q8+kmRFIYb0yZxdejbK8"]]], [1, "18gLVCgOhMi5YeAnqZmHHZ", null, null, null, 1, 0], [1, 0, -31.753000000000043, 0]], [6, "btn_text", 6, [[2, -254, [0, "cfTYBIicxI0al2o6Q1sTDG"], [5, 44, 29.2]], [9, "客服", 20, 20, 20, false, false, 1, true, -255, [0, "58UJZc3dlIOY1z57vjO0TV"], 34], [4, -256, [0, "84OfQtiXRDP4mn2jQYAHEf"]]], [1, "a7SWhhfX1J3ZwvQx+aOzN6", null, null, null, 1, 0], [1, 0, -31.753000000000043, 0]], [6, "btn_text", 22, [[2, -257, [0, "2embNLjZBBCa8EFHS5hL6/"], [5, 83.95997619628906, 29.2]], [9, "反馈意见", 20, 20, 20, false, false, 1, true, -258, [0, "58jQ3MlaBGNLLAd9ulKzBu"], 36], [4, -259, [0, "f9pokipXhB3oeEXBQzjxVW"]]], [1, "deYi3cTsBDH6vSmjnUlbHq", null, null, null, 1, 0], [1, 0, -31.753000000000043, 0]], [6, "txt", 23, [[2, -260, [0, "436IN0HXNHXYBmy8rYcXCD"], [5, 84, 29.2]], [9, "隐私政策", 20, 20, 20, false, false, 1, true, -261, [0, "fbCH+PrFJAGbysVhLaiLol"], 38], [4, -262, [0, "e6Z1tZDVZKpLFj0KLLqZV6"]]], [1, "10RfU9EdBI3Lg9NZ5fFXkm", null, null, null, 1, 0], [1, 0, -38, 0]], [6, "txt", 24, [[2, -263, [0, "39mMEOV0VPRrVZD6OIu/6V"], [5, 84.01998901367188, 29.2]], [9, "用户协议", 20, 20, 20, false, false, 1, true, -264, [0, "89zt87mLZJEpOi7UCZ9ocs"], 40], [4, -265, [0, "9dFqmBrnJKZoe+PyPuseOv"]]], [1, "e5N+X6++lLWqzH/KRbto3m", null, null, null, 1, 0], [1, 0, -38, 0]], [6, "txt", 8, [[2, -266, [0, "e4b/v5OZRHGLy8d/ybAWNL"], [5, 83.93998718261719, 29.2]], [9, "注销账号", 20, 20, 20, false, false, 1, true, -267, [0, "7ewP2fbMJGeIa6dDqRdijj"], 42], [4, -268, [0, "27pI/XLihNf54gv/onbkxz"]]], [1, "6cYnpxhOxPvowtob/SRVmN", null, null, null, 1, 0], [1, 0, -38, 0]], [22, "txtID", 1, [[[7, -269, [0, "92lh22nsBAiZmRsjiOCJqe"], [5, 134.5599365234375, 29.2], [0, 1, 0.5]], -270, [4, -271, [0, "10hrZD4ABEYp9d+wSRdwi+"]]], 4, 1, 4], [1, "ebt/FxwtNC5qj2fBoR/vkI", null, null, null, 1, 0], [1, 112.35399999999998, 170.71400000000006, 0]], [22, "txtVer", 1, [[[2, -272, [0, "2bYZySUGJBCbZqg4DawKRF"], [5, 50.12995910644531, 25.2]], -273, [4, -274, [0, "bb0Txa7wREpJBZDYe/nKT0"]]], 4, 1, 4], [1, "a1XNl+f9ZMc6SY7Vv5oq0Z", null, null, null, 1, 0], [1, 0, -437.89599999999996, 0]], [11, "btn_text", 26, [[2, -275, [0, "8dTPX6OwNF0JykS6nI+D9W"], [5, 40, 29.2]], [9, "选服", 18, 18, 20, false, false, 1, true, -276, [0, "34deTjHPNHppjg1laa9lX3"], 45], [4, -277, [0, "3c8ZYXsIVKE7Ko7sMZB4nB"]]], [1, "a0YzxuhYpLgYvgUZN0hSAh", null, null, null, 1, 0]], [11, "btn_text", 9, [[2, -278, [0, "d8kRzsSOFMSIWzVHeUIo79"], [5, 40, 29.2]], [9, "注销", 18, 18, 20, false, false, 1, true, -279, [0, "1ciRhKCHhM67xrZyJ28YKV"], 47], [4, -280, [0, "61Gj1iQDxIRr9smzxe8IuB"]]], [1, "57T20DHOVFUYrd8ASoQ3ss", null, null, null, 1, 0]], [11, "Label", 10, [[2, -281, [0, "27IAuWVHhGOoCsA1LH61Cl"], [5, 58, 29.2]], [9, "兑换码", 18, 18, 20, false, false, 1, true, -282, [0, "07fd992cVCwLZHpvfDFSKr"], 49], [4, -283, [0, "29gFdK76VBerULnD46ee9C"]]], [1, "f3BAjwzCJBQYpzQp3zabQj", null, null, null, 1, 0]], [11, "Label", 11, [[2, -284, [0, "90VwEUeVxMY7bi5FglwZJT"], [5, 58, 29.2]], [9, "邀请码", 18, 18, 20, false, false, 1, true, -285, [0, "6d2q91DDxD3b98noMFJT4s"], 51], [4, -286, [0, "8eyhQW6bRNpZ2LwQ/QC06m"]]], [1, "d97WXPRdhODLKVS1LHNAV8", null, null, null, 1, 0]], [6, "bg", 2, [[2, -287, [0, "95b7PRP4tJm4rIy4j4GTcI"], [5, 650, 850]], [21, 1, 0, -288, [0, "366D23nS1Mt4AvBCxp0bz4"], 54], [24, -289, [0, "903mMDyq1MiIRzfKpiPWMk"]]], [1, "996x0cjPZMJL6cK/GvfJxb", null, null, null, 1, 0], [1, 0, -47, 0]], [6, "title", 2, [[2, -290, [0, "1dmdsBfYhBH5boFq2RMlsH"], [5, 146.734375, 48.1]], [54, "用户协议", 35, 35, "黑体", 35, false, true, true, -291, [0, "4ep1o4IjtJkqYY0uJxWHZl"]], [4, -292, [0, "c7lc5jPbZHCqtxy+WkpPdh"]]], [1, "a10fvC119GcphKkolsqRvt", null, null, null, 1, 0], [1, -5.684341886080802e-14, 349.981, 0]], [3, "list", 2, [27], [[7, -293, [0, "59yLs1o1JOgKSsE0A5q1Rj"], [5, 600, 770], [0, 0.5, 1]], [57, 0.23, 0.75, false, -294, [0, "1cVd7yCZZBcYafdZT7sIgJ"], 28]], [1, "9fvTNBTmVAzYxNWyF3W551", null, null, null, 1, 0], [1, -2.486000000000047, 323.79099999999994, 0]], [11, "OutlineLabel", 46, [[2, -295, [0, "695H5RzzVEW6jtlxvnbXGD"], [5, 54, 36.24]], [55, "设置", 24, 24, 24, false, false, 1, true, 3, -296, [0, "14dE3AsbtDipueUJFZIWcd"], 3]], [1, "8frCeHcrNJY4F6+c8nL6aw", null, null, null, 1, 0]], [6, "pub_line_long_1", 1, [[2, -297, [0, "5aNGV2PGFODr4DGQaHZXb5"], [5, 317, 2]], [10, 0, -298, [0, "21QXNhO45CV7CYR7KiGhoU"], 8]], [1, "18pGF/8aNPI5UPZz9Bp+Ue", null, null, null, 1, 0], [1, 0, 152.745, 0]], [11, "OutlineLabel", 48, [[2, -299, [0, "23ybVw54RH+5CuHnD2s4o4"], [5, 92.02198791503906, 31.72]], [9, "基础设置", 22, 22, 22, false, false, 1, true, -300, [0, "1062tbsOJJOLpaevDOClKD"], 10]], [1, "1dbKj9LKlIkIftKVs3D2Ca", null, null, null, 1, 0]], [16, "Checkmark", 13, [[[2, -301, [0, "8aqR8RwA1BvZ41j7h+xeYN"], [5, 121, 41]], -302], 4, 1], [1, "05HJJsCF9O7ov3FNM355M8", null, null, null, 1, 0]], [16, "Checkmark", 14, [[[2, -303, [0, "506ta3IPNKaYI+1Ak1h9Bp"], [5, 121, 41]], -304], 4, 1], [1, "ec5NptTeBKvrwciLdTGVz6", null, null, null, 1, 0]], [16, "Checkmark", 15, [[[2, -305, [0, "14lciMJoNFU7xFNre1owM7"], [5, 121, 41]], -306], 4, 1], [1, "ee7NkwReNKLLZcw7FvBQte", null, null, null, 1, 0]], [16, "Checkmark", 16, [[[2, -307, [0, "76wjAKzQ5I6J99DKn6+QAJ"], [5, 121, 41]], -308], 4, 1], [1, "ef1OH/42tBR4OLgYyfhAxH", null, null, null, 1, 0]], [16, "Checkmark", 17, [[[2, -309, [0, "36KVcOneVH8ot+y9arAQpa"], [5, 121, 41]], -310], 4, 1], [1, "b6T0CJXHlDxJLeCOAtGhmr", null, null, null, 1, 0]], [16, "Checkmark", 18, [[[2, -311, [0, "9elcoHmW5Ck5mcGL4ZJcC9"], [5, 121, 41]], -312], 4, 1], [1, "ede6EJCipPsY39H6IkNVTB", null, null, null, 1, 0]], [16, "Checkmark", 19, [[[2, -313, [0, "f68uoPdsREtI6Nzy4fQtG7"], [5, 121, 41]], -314], 4, 1], [1, "11uosEe5pOAIx/5uSph0T0", null, null, null, 1, 0]], [11, "OutlineLabel", 49, [[2, -315, [0, "52IUEfHKFLMZaOhyZ9ssqq"], [5, 84, 29.2]], [9, "相关设置", 20, 20, 20, false, false, 1, true, -316, [0, "ecWTvX1+RLjKW+jyTcqyYg"], 26]], [1, "486r17uKBDuKq/ihxYcRIH", null, null, null, 1, 0]], [37, "Checkmark", false, 33554432, 3, [[[2, -317, [0, "4ak/GCPpBD3J5IbH+1JGMH"], [5, 40, 33]], -318], 4, 1], [1, "4ehwiykU5Pu7gkfbcHPyRd", null, null, null, 1, 0]], [38, "bar", 33554432, 25, [[[7, -319, [0, "e7W6Xal7VHa6WpXiIJUFI1"], [5, 10, 190], [0, 0, 0]], -320], 4, 1], [1, "dc+M2U8RlG6Ys6GAoK0Qqc", null, null, null, 1, 0], [1, -11, -65, 0]], [6, "rich", 28, [[2, -321, [0, "57j2IYEf5OmLUavpdgNWM3"], [5, 590, 37.8]], [59, 30, "", 25, 590, "黑体", 1, -322, [0, "a7z8FcB99HHbPqWpdVoq3C"]]], [1, "445nok3mpKz4bcJcMh0JHi", null, null, null, 1, 0], [1, 0, -18.9, 0]], [30, "玩家名称", 0, 20, 20, 20, false, false, true, 47, [0, "9elgnEZ2dGqZwxMyL12gpg"]], [13, 2, false, 69, [0, "e6E7bpI8lOH4d+Coaai+mG"]], [18, 13, [0, "6frDjf0KhOaLKpgj0ml1CL"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "playClickSound", 1]], [4, 4292269782], 13, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "music", 1]], 81], [13, 2, false, 70, [0, "305QKbWbZJa6odJnz3Xqbl"]], [18, 14, [0, "88UmKe5G1DCJ38Wuyzd0bq"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "playClickSound", 1]], [4, 4292269782], 14, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "cheng<PERSON>", 1]], 83], [13, 2, false, 71, [0, "b1EzGh+atHnYncc/UkNa6m"]], [18, 15, [0, "c4+1RG5R5APoTLo9UXnszn"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "playClickSound", 1]], [4, 4292269782], 15, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "nick", 1]], 85], [13, 2, false, 72, [0, "11fnBIf+hPEJ1ujjcHBp4v"]], [18, 16, [0, "e3UVRF5HtM8JtO5TJq0Cl1"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "playClickSound", 1]], [4, 4292269782], 16, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "sound", 1]], 87], [13, 2, false, 73, [0, "64+0xzH3VMs4fqLYJ08+ws"]], [18, 17, [0, "e0+7csxLNFf4IpcH1V30L1"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "playClickSound", 1]], [4, 4292269782], 17, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "guildName", 1]], 89], [13, 2, false, 74, [0, "594Fsv21xGtIlsXwnsp/zb"]], [18, 18, [0, "937DOx7JNCwZyql+mF4oaV"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "playClickSound", 1]], [4, 4292269782], 18, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "hurt", 1]], 91], [13, 2, false, 75, [0, "12EeZl8PlEuLLPXGZz7Qpx"]], [18, 19, [0, "a3a8gOPXNKq7pxhbzfSH4B"], [[5, "87ba4CPkYZO/ZEr98HBe/ke", "playClickSound", 1]], [4, 4292269782], 19, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "skillEffect", 1]], 93], [13, 2, false, 77, [0, "d64D4epLNBc5CSSYaPHJtK"]], [60, false, 3, [0, "8a/rEXeuRDgoAwqaYIKmgi"], [4, 4292269782], 3, [[14, "87ba4CPkYZO/ZEr98HBe/ke", "onCheckEvents", "onlyShowMe", 1]], 95], [30, "ID：345464864", 2, 20, 20, 20, false, false, true, 57, [0, "e3anTybwFKNZCVGkggmlFI"]], [56, "v1.0.0", 18, 18, 20, false, false, 58, [0, "9ekKsL9UFD7aV7HfO1QeuQ"], [4, 4280495954]], [47, 1, 0, 78, [0, "989sZwQgtBL7gysBJQlLXZ"], [4, 16777215]], [61, 1, 25, [0, "7fZ5/golpH5ZEgd+PaSzhs"], 99], [58, 0.23, 0.75, false, 41, [0, "7ccYE6hHZJTJJ1HC93zNT8"], 4, 100]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 8, 0, 8, 2, 0, 9, 11, 0, 10, 6, 0, 11, 10, 0, 12, 9, 0, 13, 7, 0, 14, 92, 0, 15, 96, 0, 16, 94, 0, 17, 86, 0, 18, 90, 0, 19, 84, 0, 20, 88, 0, 21, 82, 0, 22, 98, 0, 23, 97, 0, 24, 80, 0, 0, 1, 0, 0, 1, 0, -1, 12, 0, -2, 45, 0, -3, 29, 0, -4, 46, 0, -5, 30, 0, -6, 67, 0, -7, 32, 0, -8, 48, 0, -9, 33, 0, -10, 34, 0, -11, 35, 0, -12, 36, 0, -13, 37, 0, -14, 38, 0, -15, 39, 0, -16, 49, 0, -17, 3, 0, -18, 5, 0, -19, 7, 0, -20, 57, 0, -21, 58, 0, -22, 41, 0, -23, 2, 0, 0, 2, 0, 0, 2, 0, -1, 43, 0, -2, 63, 0, -3, 64, 0, -4, 44, 0, -5, 65, 0, 0, 3, 0, 0, 3, 0, -3, 96, 0, 25, 40, 0, 0, 3, 0, -1, 77, 0, -2, 40, 0, 0, 4, 0, 0, 4, 0, -1, 26, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, 0, 5, 0, 0, 5, 0, -1, 20, 0, -2, 21, 0, -3, 6, 0, -4, 22, 0, 0, 6, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, -1, 52, 0, 0, 7, 0, 0, 7, 0, -1, 23, 0, -2, 24, 0, -3, 8, 0, 0, 8, 0, 0, 8, 0, 3, 8, 0, 0, 8, 0, -1, 56, 0, 0, 9, 0, 0, 9, 0, 3, 9, 0, 0, 9, 0, -1, 60, 0, 0, 10, 0, 0, 10, 0, 3, 10, 0, 0, 10, 0, -1, 61, 0, 0, 11, 0, 0, 11, 0, 3, 11, 0, 0, 11, 0, -1, 62, 0, 0, 12, 0, 0, 12, 0, 3, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -3, 82, 0, -1, 69, 0, 0, 14, 0, 0, 14, 0, -3, 84, 0, -1, 70, 0, 0, 15, 0, 0, 15, 0, -3, 86, 0, -1, 71, 0, 0, 16, 0, 0, 16, 0, -3, 88, 0, -1, 72, 0, 0, 17, 0, 0, 17, 0, -3, 90, 0, -1, 73, 0, 0, 18, 0, 0, 18, 0, -3, 92, 0, -1, 74, 0, 0, 19, 0, 0, 19, 0, -3, 94, 0, -1, 75, 0, 0, 20, 0, 0, 20, 0, 3, 20, 0, 0, 20, 0, -1, 50, 0, 0, 21, 0, 0, 21, 0, 3, 21, 0, 0, 21, 0, -1, 51, 0, 0, 22, 0, 0, 22, 0, 3, 22, 0, 0, 22, 0, -1, 53, 0, 0, 23, 0, 0, 23, 0, 3, 23, 0, 0, 23, 0, -1, 54, 0, 0, 24, 0, 0, 24, 0, 3, 24, 0, 0, 24, 0, -1, 55, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, -4, 100, 0, -1, 78, 0, 0, 26, 0, 0, 26, 0, 3, 26, 0, 0, 26, 0, -1, 59, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, -1, 28, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, -1, 79, 0, 0, 29, 0, 0, 29, 0, 3, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, -1, 31, 0, -2, 47, 0, 0, 31, 0, 0, 31, 0, 3, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 3, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, -2, 101, 0, -2, 42, 0, 0, 42, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 3, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, -1, 66, 0, 0, 47, 0, -2, 80, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, -1, 68, 0, 0, 49, 0, 0, 49, 0, -1, 76, 0, 0, 50, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, -2, 97, 0, 0, 57, 0, 0, 58, 0, -2, 98, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, -2, 81, 0, 0, 70, 0, -2, 83, 0, 0, 71, 0, -2, 85, 0, 0, 72, 0, -2, 87, 0, 0, 73, 0, -2, 89, 0, 0, 74, 0, -2, 91, 0, 0, 75, 0, -2, 93, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, -2, 95, 0, 0, 78, 0, -2, 99, 0, 0, 79, 0, 0, 79, 0, 26, 1, 4, 4, 42, 13, 4, 33, 14, 4, 34, 15, 4, 35, 16, 4, 36, 17, 4, 37, 18, 4, 38, 19, 4, 39, 25, 4, 41, 27, 4, 65, 100, 27, 101, 322], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 80, 81, 83, 85, 87, 89, 91, 93, 95, 97, 98, 99], [1, 1, 1, 2, 1, 1, 28, 1, 1, 1, 2, 1, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 1, 2, 1, 2, 1, 2, 1, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1], [7, 8, 9, 0, 10, 3, 3, 11, 12, 13, 0, 4, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 4, 0, 14, 0, 15, 0, 16, 0, 17, 0, 18, 0, 19, 0, 20, 0, 21, 22, 0, 5, 0, 5, 0, 23, 0, 24, 25, 26, 27, 0, 2, 2, 2, 2, 2, 2, 2, 28, 0, 0, 29]], [[{"name": "zonesvr_bg1", "rect": {"x": 576, "y": 2, "width": 447, "height": 604}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 447, "height": 604}, "rotated": false, "capInsets": [0, 150, 0, 245], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [5], [6]], [[{"name": "zonesvr10", "rect": {"x": 918, "y": 803, "width": 58, "height": 57}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 58, "height": 57}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [6], 0, [0], [5], [6]]]]