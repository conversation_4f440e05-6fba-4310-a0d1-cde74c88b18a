[1, ["ad2fyqhHVFIZF7feMHuBxN"], ["_effectAsset"], [["cc.Material", ["_name", "_props", "_states", "_defines"], -1], ["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[0, 0, 1, 2, 3, 5], [1, 0, 1, 2, 4]], [[[[0, "Mult-material", [{}], [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_TEXTURE": true}]]], 0, 0, [0], [0], [0]], [[[1, "../resources/multTextures/Mult-effect", [{"hash": 1692582351, "name": "../resources/multTextures/Mult-effect|sprite-vs:vert|sprite-fs:frag", "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [{"name": "texture0", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": ["USE_TEXTURE"]}, {"name": "texture1", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": ["USE_TEXTURE"]}, {"name": "texture2", "type": 28, "count": 1, "stageFlags": 16, "binding": 3, "defines": ["USE_TEXTURE"]}, {"name": "texture3", "type": 28, "count": 1, "stageFlags": 16, "binding": 4, "defines": ["USE_TEXTURE"]}, {"name": "texture4", "type": 28, "count": 1, "stageFlags": 16, "binding": 5, "defines": ["USE_TEXTURE"]}, {"name": "texture5", "type": 28, "count": 1, "stageFlags": 16, "binding": 6, "defines": ["USE_TEXTURE"]}, {"name": "texture6", "type": 28, "count": 1, "stageFlags": 16, "binding": 7, "defines": ["USE_TEXTURE"]}, {"name": "texture7", "type": 28, "count": 1, "stageFlags": 16, "binding": 8, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 1, "defines": []}, {"name": "a_color", "format": 44, "location": 2, "defines": []}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["USE_LOCAL"]}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "ALPHA_TEST_DATA", "stageFlags": 16, "binding": 0, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplerTextures": [{"name": "texture0", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": ["USE_TEXTURE"]}, {"name": "texture1", "type": 28, "count": 1, "stageFlags": 16, "binding": 2, "defines": ["USE_TEXTURE"]}, {"name": "texture2", "type": 28, "count": 1, "stageFlags": 16, "binding": 3, "defines": ["USE_TEXTURE"]}, {"name": "texture3", "type": 28, "count": 1, "stageFlags": 16, "binding": 4, "defines": ["USE_TEXTURE"]}, {"name": "texture4", "type": 28, "count": 1, "stageFlags": 16, "binding": 5, "defines": ["USE_TEXTURE"]}, {"name": "texture5", "type": 28, "count": 1, "stageFlags": 16, "binding": 6, "defines": ["USE_TEXTURE"]}, {"name": "texture6", "type": 28, "count": 1, "stageFlags": 16, "binding": 7, "defines": ["USE_TEXTURE"]}, {"name": "texture7", "type": 28, "count": 1, "stageFlags": 16, "binding": 8, "defines": ["USE_TEXTURE"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 1, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#if USE_LOCAL\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\n#if SAMPLE_FROM_RT\n  #define QUATER_PI         0.78539816340\n  #define HALF_PI           1.57079632679\n  #define PI                3.14159265359\n  #define PI2               6.28318530718\n  #define PI4               12.5663706144\n  #define INV_QUATER_PI     1.27323954474\n  #define INV_HALF_PI       0.63661977237\n  #define INV_PI            0.31830988618\n  #define INV_PI2           0.15915494309\n  #define INV_PI4           0.07957747155\n  #define EPSILON           1e-6\n  #define EPSILON_LOWP      1e-4\n  #define LOG2              1.442695\n  #define EXP_VALUE         2.71828183\n  #define FP_MAX            65504.0\n  #define FP_SCALE          0.0009765625\n  #define FP_SCALE_INV      1024.0\n  #define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n      #define LIGHT_MAP_TYPE_DISABLED 0\n  #define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n  #define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n  #define REFLECTION_PROBE_TYPE_NONE 0\n  #define REFLECTION_PROBE_TYPE_CUBE 1\n  #define REFLECTION_PROBE_TYPE_PLANAR 2\n  #define REFLECTION_PROBE_TYPE_BLEND 3\n  #define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n      #define LIGHT_TYPE_DIRECTIONAL 0.0\n  #define LIGHT_TYPE_SPHERE 1.0\n  #define LIGHT_TYPE_SPOT 2.0\n  #define LIGHT_TYPE_POINT 3.0\n  #define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n  #define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n  #define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n  #define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n  #define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n  #define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n  #define TONE_MAPPING_ACES 0\n  #define TONE_MAPPING_LINEAR 1\n  #define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n  #ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n    #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n  #endif\n  #ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n    #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n  #endif\n#endif\nin vec3 a_position;\nin vec2 a_texCoord;\nin vec4 a_color;\nout vec4 color;\nout vec3 uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n   color = a_color;\n   uv0.xy = a_texCoord;\n   uv0.z = mod(color.r,10.0);\n   color.r = ( color.r -  uv0.z)*0.000001;\n  #if SAMPLE_FROM_RT\n    uv0.xy = cc_cameraPos.w > 1.0 ? vec2(uv0.xy.x, 1.0 - uv0.xy.y) : uv0.xy;\n  #endif\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture(tex, uv).rgb, texture(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture(tex, uv);\n#endif\n}\n#if USE_ALPHA_TEST\n  layout(std140) uniform ALPHA_TEST_DATA {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 color;\n#if USE_TEXTURE\n  in vec3 uv0;\n  uniform sampler2D texture0;\n  uniform sampler2D texture1;\n  uniform sampler2D texture2;\n  uniform sampler2D texture3;\n  uniform sampler2D texture4;\n  uniform sampler2D texture5;\n  uniform sampler2D texture6;\n  uniform sampler2D texture7;\n#endif\nvec4 ONE = vec4(1.0, 1.0, 1.0, 1.0);\nvec4 HALF = vec4(0.5, 0.5, 0.5, 0.5);\nvec4 ZERO = vec4(0.0, 0.0, 0.0, 0.0);\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n    vec4 p0 = vec4(uv0.z) - vec4(0.0, 1.0, 2.0, 3.0);\n    vec4 p1 = vec4(uv0.z) - vec4(4.0, 5.0, 6.0, 7.0);\n    vec4 w0 = step(p0*p0,HALF);\n    vec4 w1 = step(p1*p1,HALF);\n    vec4 sampledColor = ZERO;\n    if(uv0.z < 1.5){\n      sampledColor += w0.x * CCSampleWithAlphaSeparated(texture0, uv0.xy*w0.x);\n      sampledColor += w0.y * CCSampleWithAlphaSeparated(texture1, uv0.xy*w0.y);\n    }else if(uv0.z < 3.5){\n      sampledColor += w0.z * CCSampleWithAlphaSeparated(texture2, uv0.xy*w0.z);\n      sampledColor += w0.w * CCSampleWithAlphaSeparated(texture3, uv0.xy*w0.w);\n    }else if(uv0.z < 5.5){\n      sampledColor += w1.x * CCSampleWithAlphaSeparated(texture4, uv0.xy*w1.x);\n      sampledColor += w1.y * CCSampleWithAlphaSeparated(texture5, uv0.xy*w1.y);\n    }else{\n      sampledColor += w1.z * CCSampleWithAlphaSeparated(texture6, uv0.xy*w1.z);\n      sampledColor += w1.w * CCSampleWithAlphaSeparated(texture7, uv0.xy*w1.w);\n    }\n    o*=sampledColor;\n    #if IS_GRAY\n      float gray  = 0.2126 * o.r + 0.7152 * o.g + 0.0722 * o.b;\n      o.r = o.g = o.b = gray;\n    #endif\n  #endif\n  o *= color;\n  ALPHA_TEST(o);\n  return o;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "glsl1": {"vert": "\nprecision highp float;\nuniform highp mat4 cc_matView;\n  uniform highp mat4 cc_matProj;\n  uniform highp mat4 cc_matViewProj;\n  uniform highp vec4 cc_cameraPos;\n#if USE_LOCAL\n  uniform highp mat4 cc_matWorld;\n#endif\n#if SAMPLE_FROM_RT\n  #define QUATER_PI         0.78539816340\n  #define HALF_PI           1.57079632679\n  #define PI                3.14159265359\n  #define PI2               6.28318530718\n  #define PI4               12.5663706144\n  #define INV_QUATER_PI     1.27323954474\n  #define INV_HALF_PI       0.63661977237\n  #define INV_PI            0.31830988618\n  #define INV_PI2           0.15915494309\n  #define INV_PI4           0.07957747155\n  #define EPSILON           1e-6\n  #define EPSILON_LOWP      1e-4\n  #define LOG2              1.442695\n  #define EXP_VALUE         2.71828183\n  #define FP_MAX            65504.0\n  #define FP_SCALE          0.0009765625\n  #define FP_SCALE_INV      1024.0\n  #define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n      #define LIGHT_MAP_TYPE_DISABLED 0\n  #define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n  #define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n  #define REFLECTION_PROBE_TYPE_NONE 0\n  #define REFLECTION_PROBE_TYPE_CUBE 1\n  #define REFLECTION_PROBE_TYPE_PLANAR 2\n  #define REFLECTION_PROBE_TYPE_BLEND 3\n  #define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n      #define LIGHT_TYPE_DIRECTIONAL 0.0\n  #define LIGHT_TYPE_SPHERE 1.0\n  #define LIGHT_TYPE_SPOT 2.0\n  #define LIGHT_TYPE_POINT 3.0\n  #define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n  #define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n  #define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n  #define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n  #define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n  #define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n  #define TONE_MAPPING_ACES 0\n  #define TONE_MAPPING_LINEAR 1\n  #define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n  #ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n    #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n  #endif\n  #ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n    #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n  #endif\n#endif\nattribute vec3 a_position;\nattribute vec2 a_texCoord;\nattribute vec4 a_color;\nvarying vec4 color;\nvarying vec3 uv0;\nvec4 vert () {\n  vec4 pos = vec4(a_position, 1);\n  #if USE_LOCAL\n    pos = cc_matWorld * pos;\n  #endif\n  #if USE_PIXEL_ALIGNMENT\n    pos = cc_matView * pos;\n    pos.xyz = floor(pos.xyz);\n    pos = cc_matProj * pos;\n  #else\n    pos = cc_matViewProj * pos;\n  #endif\n   color = a_color;\n   uv0.xy = a_texCoord;\n   uv0.z = mod(color.r,10.0);\n   color.r = ( color.r -  uv0.z)*0.000001;\n  #if SAMPLE_FROM_RT\n    uv0.xy = cc_cameraPos.w > 1.0 ? vec2(uv0.xy.x, 1.0 - uv0.xy.y) : uv0.xy;\n  #endif\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision highp float;\nvec4 CCSampleWithAlphaSeparated(sampler2D tex, vec2 uv) {\n#if CC_USE_EMBEDDED_ALPHA\n  return vec4(texture2D(tex, uv).rgb, texture2D(tex, uv + vec2(0.0, 0.5)).r);\n#else\n  return texture2D(tex, uv);\n#endif\n}\n#if USE_ALPHA_TEST\n      uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n    if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n    if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 color;\n#if USE_TEXTURE\n  varying vec3 uv0;\n  uniform sampler2D texture0;\n  uniform sampler2D texture1;\n  uniform sampler2D texture2;\n  uniform sampler2D texture3;\n  uniform sampler2D texture4;\n  uniform sampler2D texture5;\n  uniform sampler2D texture6;\n  uniform sampler2D texture7;\n#endif\nvec4 ONE = vec4(1.0, 1.0, 1.0, 1.0);\nvec4 HALF = vec4(0.5, 0.5, 0.5, 0.5);\nvec4 ZERO = vec4(0.0, 0.0, 0.0, 0.0);\nvec4 frag () {\n  vec4 o = vec4(1, 1, 1, 1);\n  #if USE_TEXTURE\n    vec4 p0 = vec4(uv0.z) - vec4(0.0, 1.0, 2.0, 3.0);\n    vec4 p1 = vec4(uv0.z) - vec4(4.0, 5.0, 6.0, 7.0);\n    vec4 w0 = step(p0*p0,HALF);\n    vec4 w1 = step(p1*p1,HALF);\n    vec4 sampledColor = ZERO;\n    if(uv0.z < 1.5){\n      sampledColor += w0.x * CCSampleWithAlphaSeparated(texture0, uv0.xy*w0.x);\n      sampledColor += w0.y * CCSampleWithAlphaSeparated(texture1, uv0.xy*w0.y);\n    }else if(uv0.z < 3.5){\n      sampledColor += w0.z * CCSampleWithAlphaSeparated(texture2, uv0.xy*w0.z);\n      sampledColor += w0.w * CCSampleWithAlphaSeparated(texture3, uv0.xy*w0.w);\n    }else if(uv0.z < 5.5){\n      sampledColor += w1.x * CCSampleWithAlphaSeparated(texture4, uv0.xy*w1.x);\n      sampledColor += w1.y * CCSampleWithAlphaSeparated(texture5, uv0.xy*w1.y);\n    }else{\n      sampledColor += w1.z * CCSampleWithAlphaSeparated(texture6, uv0.xy*w1.z);\n      sampledColor += w1.w * CCSampleWithAlphaSeparated(texture7, uv0.xy*w1.w);\n    }\n    o*=sampledColor;\n    #if IS_GRAY\n      float gray  = 0.2126 * o.r + 0.7152 * o.g + 0.0722 * o.b;\n      o.r = o.g = o.b = gray;\n    #endif\n  #endif\n  o *= color;\n  ALPHA_TEST(o);\n  return o;\n}\nvoid main() { gl_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["USE_LOCAL"]}], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 56, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 1}}, "defines": [{"name": "USE_LOCAL", "type": "boolean"}, {"name": "SAMPLE_FROM_RT", "type": "boolean"}, {"name": "USE_PIXEL_ALIGNMENT", "type": "boolean"}, {"name": "CC_USE_EMBEDDED_ALPHA", "type": "boolean"}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "USE_TEXTURE", "type": "boolean"}, {"name": "IS_GRAY", "type": "boolean"}]}], [{"passes": [{"program": "../resources/multTextures/Mult-effect|sprite-vs:vert|sprite-fs:frag", "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": false, "depthWrite": false}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []]]]