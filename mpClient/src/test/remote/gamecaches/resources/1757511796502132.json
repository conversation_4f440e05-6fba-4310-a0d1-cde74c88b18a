[1, ["42YlG8C+VFQIQhi/uTnF/K"], ["node", "_font", "root", "labTimer", "wait<PERSON><PERSON>", "data"], [["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab"], 2, 2, 9, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["79625nJPs5FErtP6kcjZWHi", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "__prefab"], -2, 1, 4], ["1a878+gkdZGtJ7PW70oWiNZ", ["node", "__prefab", "wait<PERSON><PERSON>", "labTimer"], 3, 1, 4, 1, 1]], [[4, 0, 2], [6, 0, 1, 2, 3, 4, 5, 5], [1, 0, 1, 1], [2, 0, 2], [3, 0, 1, 2, 3, 2], [0, 0, 2, 3, 4, 2], [0, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 1], [5, 0, 1, 1], [7, 0, 1, 2, 3, 4, 5, 6, 6], [8, 0, 1, 2, 3, 1]], [[3, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [4, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", [-6, -7], [[2, -2, [0, "2bsgpwGKVM9pxVPr+5gStF"]], [10, -5, [0, "08nLXCm05DWawEvlfzvvJS"], -4, -3]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [5, "<PERSON>inghun<PERSON>z<PERSON>", 1, [[[2, -8, [0, "aaDb/935hCQLj1Upt4meVL"]], -9], 4, 1], [1, "dcMH5oEGBGDqQ6sW5P41VU", null, null, null, 1, 0]], [6, "Label", 1, 1, [[[7, -10, [0, "74wjC4dR9PVY+bpPILd7m3"], [5, 14.67, 24]], -11], 4, 1], [1, "a4gOsQa0lCCJWpKlaJdG8T", null, null, null, 1, 0], [1, 0, 162.178, 0]], [8, 2, [0, "a9kX7CPGtGEYoj6KV8MopC"]], [9, "9", 22, 22, 24, false, 3, [0, "86fc7ajjND8ZGOmcioym6B"]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 5, 0, 4, 4, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, -2, 4, 0, 0, 3, 0, -2, 5, 0, 5, 1, 11], [5], [1], [0]]