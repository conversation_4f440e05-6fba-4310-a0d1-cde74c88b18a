[1, ["61NXJOyyZNU6ZGh0FJkMv2", "02RQoCPDdB2oQptKDc3OAK", "78fanDWc5E0biRuSEU/8IL@dab90", "78fanDWc5E0biRuSEU/8IL@a398f", "54DKo7/p1AJ6l1t7elinpS", "a0jsqMxRJBHqiXq7n31HbP@70240", "16mojGSc1Nvrb/YTBQYkJg@f9941", "67Q1UFWaZIJpo5vSsyRjYY@6c48a", "003XsrKclKsL0AX0p7cbeY@f9941", "78fanDWc5E0biRuSEU/8IL@fb647", "2enw6dg1JAa5xhWIDgaiWv@da132", "67Q1UFWaZIJpo5vSsyRjYY@f9941", "b7MFJ8MjNBwqr3fNq1j5dJ@f9941", "78fanDWc5E0biRuSEU/8IL@acbf5", "62Hrci6TNDrq5pYKIyRrhH", "2enw6dg1JAa5xhWIDgaiWv@8c16d", "2enw6dg1JAa5xhWIDgaiWv@87b40", "2enw6dg1JAa5xhWIDgaiWv", "89FnbzNR9DvKjyDKgpIEsk@1212f", "b3bxJLynhLTIRUgtwPgfnn", "78fanDWc5E0biRuSEU/8IL@5c24f", "78fanDWc5E0biRuSEU/8IL@81f83", "78fanDWc5E0biRuSEU/8IL", "0d2Rss5lBIELZgdjqjQQeK", "35aggRUKFMsbyUU0o9ZFlf@f9941", "1eUO2ZVahI3rjY1B9HHC3d", "78fanDWc5E0biRuSEU/8IL@a49db", "2enw6dg1JAa5xhWIDgaiWv@81fd7", "9ay0LqY7BFQ47GkDh7qy9n", "efAluyZGVF/qz8eVF/sVje@00685", "33k5R3N2hJ85WdS3pbVh2f@2262d", "2enw6dg1JAa5xhWIDgaiWv@46320", "094JsxQidCgpFwjex9tQg7"], ["node", "targetInfo", "_spriteFrame", "_font", "root", "asset", "value", "_parent", "target", "source", "_atlas", "_textureSource", "lbServer", "severDlg", "txt_wbm", "maskNode", "rankList", "emptyTips", "IbMyPoint", "myRankLabel", "pageContent", "rolePage", "list", "_content", "_target", "data", "pageItemPrefab"], [["cc.Node", ["_name", "_active", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale"], -2, 4, 9, 1, 5, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_lineHeight", "_cacheMode", "_outlineWidth", "_overflow", "node", "__prefab", "_font", "_color"], -7, 1, 4, 6, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "__prefab", "_spriteFrame", "_atlas"], -1, 1, 4, 6, 6], ["cc.Layout", ["_layoutType", "_resizeMode", "_paddingTop", "_spacingY", "_spacingX", "_paddingBottom", "node", "__prefab"], -3, 1, 4], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_lpos", "_children"], 1, 1, 12, 4, 5, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "clickEvents", "_normalColor", "_target"], 2, 1, 4, 9, 5, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["96925NpxVhGX5GHR06EhbFH", ["_virtual", "node", "__prefab", "tmpNode", "pageChangeEvent"], 2, 1, 4, 1, 4], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["d0e50ROX2dBpamWNlOHpv28", ["node", "__prefab", "rolePage", "pageContent", "myRankLabel", "IbMyPoint", "emptyTips", "rankList", "maskNode", "txt_wbm", "pageDain", "severDlg", "lbServer", "pageItemPrefab"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["a9538i77kVF3oagPzx5PJyk", ["node", "__prefab", "list"], 3, 1, 4, 1], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 4], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["<PERSON>.<PERSON>", ["_scrollThreshold", "node", "__prefab", "_content"], 2, 1, 4, 1]], [[11, 0, 2], [15, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [26, 0, 1, 2, 2], [18, 0, 2], [25, 0, 1, 2, 3], [0, 0, 7, 6, 5, 8, 2], [2, 4, 5, 6, 1], [23, 0, 1, 1], [0, 3, 4, 7, 5, 3], [16, 0, 1, 2, 3, 4, 5, 4], [24, 0, 1, 2, 2], [5, 0, 1, 2, 3, 1], [0, 0, 7, 9, 6, 5, 8, 2], [0, 0, 3, 7, 6, 5, 8, 3], [2, 0, 4, 5, 6, 2], [7, 0, 1, 2, 3], [1, 0, 1, 2, 6, 3, 4, 5, 10, 11, 12, 8], [0, 0, 7, 9, 6, 5, 2], [0, 0, 1, 2, 7, 6, 5, 8, 4], [0, 0, 2, 7, 6, 5, 8, 3], [4, 0, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3, 4, 4], [6, 0, 1, 2, 3, 2], [21, 0, 1, 1], [22, 0, 1, 2, 1], [27, 0, 1, 2, 2], [0, 0, 9, 6, 5, 8, 2], [0, 0, 2, 7, 9, 6, 5, 8, 3], [0, 0, 1, 7, 6, 5, 8, 3], [4, 0, 2, 6, 3, 4, 5, 2], [17, 0, 1, 2, 3, 4, 2], [3, 1, 0, 6, 7, 3], [2, 1, 4, 5, 6, 2], [2, 3, 2, 0, 4, 5, 6, 4], [7, 1], [20, 0, 1, 2, 3, 4, 5, 4], [1, 0, 1, 2, 6, 3, 4, 5, 10, 11, 13, 8], [28, 0, 1, 2, 2], [30, 0, 1, 2, 3], [10, 0, 2], [0, 0, 9, 6, 5, 2], [0, 0, 1, 7, 9, 6, 5, 3], [0, 0, 2, 9, 6, 5, 3], [0, 0, 3, 7, 9, 6, 5, 8, 3], [0, 0, 1, 7, 9, 6, 5, 8, 3], [0, 0, 3, 1, 7, 6, 5, 8, 4], [0, 0, 7, 6, 5, 2], [0, 0, 7, 6, 5, 8, 10, 2], [4, 0, 1, 2, 6, 3, 4, 5, 3], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1], [14, 0, 1, 2, 3, 4, 5, 3], [19, 0, 1, 2, 1], [3, 1, 0, 2, 5, 3, 6, 7, 6], [3, 1, 0, 2, 4, 3, 6, 7, 6], [3, 0, 4, 6, 7, 3], [2, 2, 0, 4, 5, 6, 3], [2, 0, 1, 4, 5, 6, 7, 3], [2, 4, 5, 6, 7, 1], [6, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 4, 7, 5, 8, 10, 11, 13, 12, 9], [1, 0, 1, 2, 6, 3, 4, 7, 10, 11, 13, 12, 8], [1, 0, 1, 2, 3, 4, 7, 5, 8, 10, 11, 12, 9], [1, 0, 1, 2, 6, 3, 4, 7, 5, 10, 11, 13, 12, 9], [1, 0, 1, 2, 6, 3, 4, 7, 5, 10, 11, 12, 9], [1, 0, 1, 2, 6, 3, 4, 5, 10, 11, 13, 12, 8], [1, 0, 1, 2, 6, 9, 3, 4, 5, 10, 11, 13, 12, 9], [1, 0, 1, 2, 6, 3, 4, 7, 5, 8, 10, 11, 12, 10], [1, 0, 1, 2, 6, 3, 4, 5, 10, 11, 8], [29, 0, 1, 2, 2], [31, 0, 1, 2, 2], [32, 0, 1, 1], [33, 0, 1, 2, 3, 2], [8, 1, 2, 3, 4, 1], [8, 0, 1, 2, 3, 4, 2]], [[[{"name": "jingjichang_bg_01", "rect": {"x": 0, "y": 0, "width": 720, "height": 401}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 720, "height": 401}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-360, -200.5, 0, 360, -200.5, 0, -360, 200.5, 0, 360, 200.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 401, 720, 401, 0, 0, 720, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -360, "y": -200.5, "z": 0}, "maxPos": {"x": 360, "y": 200.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [9], 0, [0], [11], [7]], [[[40, "BattlefrontRankDlg"], [41, "BattlefrontRankDlg", [-34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46], [[2, -18, [0, "bf5h1GMQZLfIXLyxf6HmaF"], [5, 750, 1334]], [22, 45, 100, 1334, -19, [0, "3cFBYgG5VIya7RKNx4CH3+"]], [50, -33, [0, "caUqelVQBIq6fMvu6XkFDW"], -32, -31, -30, -29, -28, -27, -26, -25, [-22, -23, -24], -21, -20, 53]], [51, "a5Vd+kHClEtKIcKU9vUc6q", null, -17, 0, [[31, ["handle"], -14, [4, ["2dQ2wXCkFJzY5oPQvWqcNX"]], -13, [4, ["09JETm349J/q+uedF63ohs"]]], [31, ["redNode"], -16, [4, ["8fN27e90VKwrOm0UkkXWci"]], -15, [4, ["75lOaODvdAG7/FKF89g08B"]]]], [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12]]], [42, "serverDlg", false, 1, [-51, -52, -53, -54, -55, -56], [[2, -47, [0, "86jQI3gFBOKa5C6M6UYVGb"], [5, 750, 1334]], [22, 45, 750, 1334, -48, [0, "532P2pXy5InoCyV4SEF2Wo"]], [52, -50, [0, "d6P8a03phErpIca8PCODgl"], -49]], [1, "729CXXr8lKkqv4k+p7eJNU", null, null, null, 1, 0]], [43, "content", 33554432, [-59, -60, -61, -62, -63], [[12, -57, [0, "44X0ykQmdPqLeqP+7gkOkl"], [5, 3584.205, 400], [0, 0, 0.5]], [32, 1, 1, -58, [0, "94jLgS+2BB8pEwSxNANSt2"]]], [1, "865YTjTK9Hw5A5b//QGWug", null, null, null, 1, 0]], [27, "rankItem", [-65, -66, -67, -68, -69, -70, -71], [[2, -64, [0, "c8ry+Ncq5CY7DMjn3UIjCd"], [5, 713, 149]]], [1, "f5T2Os9JNG6JsWxqHe5bkf", null, null, null, 1, 0], [1, 0, -84.5, 0]], [13, "topBg", 1, [-74, -75, -76, -77, -78, -79], [[2, -72, [0, "a1N/QMKYpAupzjpo7p1s2g"], [5, 720, 401]], [33, false, -73, [0, "8cv45cWyVGTrzpXnsyMnbA"], 9]], [1, "53HaPaXM5MeYBpif2ZjZ6j", null, null, null, 1, 0], [1, 0, 302, 0]], [28, "Indicator", 33554432, 1, [-81, -82, -83, -84, -85, -86], [[2, -80, [0, "c1ZVbeTjhGxYo0w8XDYBmv"], [5, 431.043, 62.227]]], [1, "003noNEKtGebW7hI5RwEdj", null, null, null, 1, 0], [1, 4.430000000000007, 117.05899999999997, 0]], [44, "zhezhao", 512, 5, [-88, -89, -90, -91], [[2, -87, [0, "852sVEEBBH4Yf/K8PApjd6"], [5, 750, 74]]], [1, "dfak+RN01D/4szzz3cPYBS", null, null, null, 1, 0], [1, 0, -236.008, 0]], [27, "listIItme", [-93, -94, -95, -96, -97], [[2, -92, [0, "97BatByftKC6MO2SrNrmOU"], [5, 167.313, 138.423]]], [1, "8bypltTSJBKI8CJ/iYrzwi", null, null, null, 1, 0], [1, -180.4255, -0.44650000000000034, 0]], [30, "rankList", 1, [-102], [[[12, -98, [0, "ebW0gD62tIOrLGuUoR9gWZ"], [5, 720, 460], [0, 0.5, 1]], -99, -100, [56, 1, 0, -101, [0, "bcjkoIV5JL9qmud8mVEbLX"], 37]], 4, 1, 1, 4], [1, "47+8uxCW5LEZx+3i1S00Rx", null, null, null, 1, 0], [1, 0, 28, 0]], [13, "button_award", 1, [-106, -107], [[2, -103, [0, "90cBhBArxA/qumczGUvOyf"], [5, 66, 64]], [7, -104, [0, "18PIiIBntJCItDdXxsLBf1"], 43], [23, 3, -105, [0, "a2lfZ7VZVOQ4OcgdWz6Gfm"], [[16, "d0e50ROX2dBpamWNlOHpv28", "onShowRankReward", 1]]]], [1, "5a2IJR6ZVFOK7QTjjUbdPP", null, null, null, 1, 0], [1, 312.236, -473.653, 0]], [49, "rankList", false, 2, [-113], [[[12, -108, [0, "f71wtzOtxMYY9DvYbqz/8E"], [5, 680, 170], [0, 0.5, 1]], [36, 0.23, 0.75, false, -110, [0, "5fbipxXdRKgafQ1Pkrq+Ie"], -109], -111, [34, false, 1, 0, -112, [0, "87gTiQLYJGIIXw2ZPAs/KQ"], 52]], 4, 4, 1, 4], [1, "e7IpQkGRNMAbx3kznRclUH", null, null, null, 1, 0], [1, 0, 57.54600000000005, 0]], [18, "view", 11, [-118], [[12, -114, [0, "8cSfbsEsZO75aHJfmNEpiT"], [5, 680, 170], [0, 0.5, 1]], [24, -115, [0, "319CYm1iRFpoJZ08LndUfm"]], [25, -116, [0, "2325TSHVVGZpbLhDEJbxXP"], [4, 16777215]], [22, 45, 589, 615, -117, [0, "51GX0Tjs5FpqNbYPWmVgU9"]]], [1, "8afovB33ROoZ/fonG/wBMi", null, null, null, 1, 0]], [29, "txt_wbm", false, 5, [[2, -119, [0, "bdSrHrzmNI24PxG3qMbZzV"], [5, 230.97499084472656, 56.4]], [60, "本角色未报名竞技场", 25, 25, false, false, 1, true, 3, -120, [0, "1aMaxnVoNOLqOPUJ2T4yav"], [4, 4285881087], 7], [8, -121, [0, "c5GNRqzudBWpFatXLitjA6"]]], [1, "48YEGNPbxEnoKUMXw64TkA", null, null, null, 1, 0], [1, -20.07499999999999, -237.62900000000002, 0]], [30, "rolePage", 1, [-125], [[[2, -122, [0, "99Qz+4kMdFHKZL2CcOt58h"], [5, 715.985, 400.36800000000005]], [34, false, 1, 0, -123, [0, "e3+lJUO4NOIpOrsTak2Abl"], 15], -124], 4, 4, 1], [1, "eeNolNPcZE3J1Vjg2naTW0", null, null, null, 1, 0], [1, 0, 301.684, 0]], [28, "view", 33554432, 14, [3], [[12, -126, [0, "e2Ib3xWcxIfaICgPbbHJ7+"], [5, 715.0889999999999, 400.36799999999994], [0, 0, 0.5]], [24, -127, [0, "6dPtbZGB5IIbSK70AjhZRo"]], [25, -128, [0, "79qcHo/41BeZYJqHtpjMvm"], [4, 16777215]]], [1, "64urbnJyFByqPmKnhOBgKk", null, null, null, 1, 0], [1, -358.535, 0, 0]], [13, "view", 9, [-132], [[12, -129, [0, "1cJFuwiAtKu6KnCZ82K20G"], [5, 720, 455], [0, 0.5, 1]], [24, -130, [0, "8beOo/ssdPwYZi/laozfpV"]], [25, -131, [0, "d3+Sn6zzdEoad0ivfAHJQU"], [4, 16777215]]], [1, "b5d/JcTVdLB6Y7biLzWx0M", null, null, null, 1, 0], [1, -6.087999999999999, 0, 0]], [18, "content", 16, [4], [[12, -133, [0, "05UJ8S0opAeY5yIOi9epHg"], [5, 720, 169], [0, 0.5, 1]], [53, 1, 2, 10, 10, 10, -134, [0, "70WntIBVRCZLPrIBe+FUbZ"]]], [1, "f4kd3MqC9KHoRHl/kOZeDS", null, null, null, 1, 0]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [18, "Node", 8, [-137, -138], [[2, -135, [0, "f6Wt45Jk5NzpzXf/w4vgTP"], [5, 96.03996276855469, 100]], [32, 1, 1, -136, [0, "ebUqmrVP1GhK8qgeseC3Tx"]]], [1, "1d4rFUOeRGebrn7I4Eez+Q", null, null, null, 1, 0]], [4, ["f3OdqDoPhD0aP0W/ojHTpT"]], [4, ["e09XPeHD1GrqZ2upEW3Deu"]], [9, 0, {}, 1, [10, "48QgggtnpApZxwpAfGYRjg", null, null, -146, [11, "5cZpjL7FlPrrU6r97/nyVf", 1, [[5, "MyScrollBar", ["_name"], -139], [3, ["_lpos"], -140, [1, 355.985, -160.529, 0]], [3, ["_lrot"], -141, [3, 0, 0, 0, 1]], [3, ["_euler"], -142, [1, 0, 0, 0]], [26, ["_contentSize"], [4, ["ae8XjF0uhCJI6F8w5MXn7m"]], [5, 6, 378]], [38, ["scrollView"], -144, -143], [5, false, ["autoHide"], -145]]], 38]], [45, "emptyTips", false, 1, [-149], [[2, -147, [0, "e8A/IOBLZA+49Ingvwf7ui"], [5, 191.9759979248047, 25.2]], [61, "暂无排行榜的记录", 24, 24, 20, false, false, 1, -148, [0, "97EHzVgD1BBaJXQ4bH+nsy"], [4, 4281219932], 40]], [1, "007wU1N0FJsIMU6z9965VW", null, null, null, 1, 0], [1, 11.884999999999991, -335.974, 0]], [9, 0, {}, 10, [10, "c46/YsCPVOJYA4mWEpNYRx", null, null, -157, [11, "8drl0JRd1CUK0/DkQLXdvr", 1, [[5, "RedDot", ["_name"], -150], [3, ["_lpos"], -151, [1, 27.30600000000004, 17.34699999999998, 0]], [3, ["_lrot"], -152, [3, 0, 0, 0, 1]], [3, ["_euler"], -153, [1, 0, 0, 0]], [5, 129, ["redPointIdList", "0"], -154], [5, 1, ["checkType"], -155], [5, false, ["_active"], -156]]], 42]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [6, "btnClose", 2, [[2, -158, [0, "a6oviAAONM1JFN9PkyLAiz"], [5, 58, 57]], [33, false, -159, [0, "4c6KA8VslHlqLTfCV7Mvdi"], 49], [59, 3, -161, [0, "41eDr3hxFCzrc0IVpgrnrm"], [[16, "d0e50ROX2dBpamWNlOHpv28", "onShowServer", 1]], [4, 4292269782], -160]], [1, "b1W59zMkBNSYc00lE72UZt", null, null, null, 1, 0], [1, 346.21299999999997, 108.15499999999997, 0]], [13, "content", 12, [-164], [[12, -162, [0, "92qY8tiw1IaJiYk6gdnjiq"], [5, 650, 42.24], [0, 0.5, 1]], [54, 1, 3, 8, 60, 40, -163, [0, "b53iYZQJNK37hEAj41rEve"]]], [1, "79l3g5M1FA4qIv01//Jlnn", null, null, null, 1, 0], [1, 0, -34.696000000000026, 0]], [6, "title", 27, [[2, -165, [0, "bcXjjATs1HeZYTegP0gAgx"], [5, 161.6799774169922, 34.24]], [17, "燃烧远征124服", 24, 24, 24, false, false, true, -166, [0, "617k5hgatBv7IkNJZKVZAL"], 51], [8, -167, [0, "0chJ5vZ3tP9JAX4RmoqNBF"]]], [1, "981JYKWWlDNqSR0QqTc/UH", null, null, null, 1, 0], [1, -244.1600112915039, -25.12, 0]], [4, ["49+PNfEAdKea7Nn8OqLKwZ"]], [14, "titleTxt", 512, 5, [[2, -168, [0, "34IQraNWJBW5rj6tnjRca1"], [5, 149.89199829101562, 56.4]], [62, "积分排名", 36, 36, false, false, 1, true, 3, -169, [0, "ab8XG9r91GtYM0PmhQtCXk"], 4], [8, -170, [0, "e0Y02AKolI3Kc8iEPXcfRu"]]], [1, "13FQmhOTtCj5QmNt5/Tf45", null, null, null, 1, 0], [1, -3.9850000000000136, 235.87300000000005, 0]], [14, "myRankText", 512, 7, [[2, -171, [0, "40cUoLyKlChYOLEC6hnGVn"], [5, 103, 35.5]], [63, "我的排名：", 22, 22, 25, false, false, 1, true, -172, [0, "2dLWlishBCw6EHJ4/rx0ji"], [4, 4283957233], 5], [8, -173, [0, "d7i35xhaFEsprgNhKZ0zzW"]]], [1, "6av4X0tt1NE5l8LZ1YzQHL", null, null, null, 1, 0], [1, -287.075, 0, 0]], [21, "myRankNum", 7, [[[12, -174, [0, "603nizXbJJrIqHpJFWsGZn"], [5, 19.267990112304688, 35.5], [0, 0, 0.5]], -175, [8, -176, [0, "7aRXNqm1pCrIpxFEQUTJ6Z"]]], 4, 1, 4], [1, "86axs4IKZIq4GKxVyre30x", null, null, null, 1, 0], [1, -233.2, 0, 0]], [21, "lbMyPoint", 7, [[[12, -177, [0, "fcI/xNAnxGxKhLGu9LlF52"], [5, 19.267990112304688, 35.5], [0, 0, 0.5]], -178, [8, -179, [0, "746sqbJOpGnaC6bHSqsCni"]]], 4, 1, 4], [1, "55LwInDHhBnJnf1NbEESXh", null, null, null, 1, 0], [1, -5.6129999999999995, 0, 0]], [46, "tips", 512, false, 5, [[2, -180, [0, "a9b8TcgMBBBpg+V/Y32HUP"], [5, 157.9119873046875, 35.5]], [64, "排行榜定时刷新", 22, 22, 25, false, false, 1, true, -181, [0, "b9oK1o0GJPopF/oCswes3s"], 8], [8, -182, [0, "ed0YUbfThIAYVhA8wdlPlQ"]]], [1, "aaQZcngGZLQo/3xIV5iwZS", null, null, null, 1, 0], [1, 278.707, -236.00800000000004, 0]], [4, ["cc+NVKm5tBW61CeFJEsJ71"]], [4, ["cc+NVKm5tBW61CeFJEsJ71"]], [4, ["cc+NVKm5tBW61CeFJEsJ71"]], [4, ["cc+NVKm5tBW61CeFJEsJ71"]], [4, ["cc+NVKm5tBW61CeFJEsJ71"]], [19, "chuanshuo_dot1", false, 33554432, 6, [[2, -183, [0, "d39ClUq7RDyrGbCCnEyyJu"], [5, 24, 24]], [7, -184, [0, "f4PEZAdrFBa4ysFQWjhPI0"], 17]], [1, "69HrjmoyVFbo+KXBGK4D38", null, null, null, 1, 0], [1, -35.99799999999999, 0, 0]], [19, "chuanshuo_dot4", false, 33554432, 6, [[2, -185, [0, "ebgppCByVFfI8intJAkmxn"], [5, 24, 24]], [7, -186, [0, "eaPvdJwddPDoBSooqvlXy0"], 19]], [1, "a5lh5zgF5O+52GU+NUdzV9", null, null, null, 1, 0], [1, -1.8000000000000114, 0, 0]], [19, "chuanshuo_dot6", false, 33554432, 6, [[2, -187, [0, "df8zedAoFBo7lSof47P8T7"], [5, 24, 24]], [7, -188, [0, "0cUYWn9JxH7ZCXi4qPAfqg"], 21]], [1, "02L9fU9LBDsJMBN+Ylr2hq", null, null, null, 1, 0], [1, 34.19900000000001, 0, 0]], [13, "playerRankList", 4, [8], [[2, -189, [0, "3eE5yQoOtLybP5/ISydwXv"], [5, 528.164, 136.523]], [55, 1, 12, -190, [0, "ddaCIr9AFJZ6pBJeLbJ1ut"]]], [1, "11WkoUpENAhqipwEaDQtdT", null, null, null, 1, 0], [1, 2.039999999999999, 1.4105000000000008, 0]], [6, "strengthValue", 19, [[12, -191, [0, "119ScceXJFOKBbBJbmRTS3"], [5, 63.03996276855469, 29.2], [0, 0, 0.5]], [17, "1000", 24, 24, 20, false, false, true, -192, [0, "dbTWikLJxCQ5ovPSfaZiby"], 28], [8, -193, [0, "7f/zn1yypHH61OVZwNMBHL"]]], [1, "cfXh+YOQFFf7Kqt/hndD75", null, null, null, 1, 0], [1, -15.019981384277344, -53.178, 0]], [6, "level", 8, [[2, -194, [0, "9enEZg0TRGMZvrYeE2ZfKz"], [5, 53.64396667480469, 29.2]], [65, "Lv  60", 18, 18, 20, false, false, true, -195, [0, "a3L5d/s7lCeLKIpQUdK0Lb"], [4, 4279627566], 29], [8, -196, [0, "ff2sMHfZpFp53E0yRsacOJ"]]], [1, "08rywxyGFBQ4JMvHLzjjYA", null, null, null, 1, 0], [1, 13.113, 11.598999999999933, 0]], [6, "rankNum", 4, [[2, -197, [0, "72pAP/VQFPWa4jB9QbEuNJ"], [5, 13.827987670898438, 29.2]], [17, "1", 26, 26, 20, false, false, true, -198, [0, "b5Onr3BydNFaVfrh6gZk78"], 35], [8, -199, [0, "adP06fOMZOuYFbODsMtoRa"]]], [1, "41qSRqbWlNGoNgjdbcgDJO", null, null, null, 1, 0], [1, -308.042, 0.5190000000000055, 0]], [6, "lbPoint", 4, [[2, -200, [0, "6e2SEgUBFKhZ4IdyytLVY1"], [5, 61.48597717285156, 35.5]], [66, "12.5w", 22, 22, 25, 2, false, false, true, -201, [0, "77ELqpoE5BbIsnzBYu0nlr"], [4, 4279627566], 36], [8, -202, [0, "f1UXL9lwJN77skXlT0u6SJ"]]], [1, "1ancXwZd1HzLBthgc2WyWt", null, null, null, 1, 0], [1, 308.521, -11.789999999999964, 0]], [4, ["48QgggtnpApZxwpAfGYRjg"]], [6, "txt", 10, [[2, -203, [0, "0acOBX8KFA25nBn6RrbhpJ"], [5, 50, 36.24]], [67, "奖励", 22, 22, 24, false, false, 1, true, 3, -204, [0, "b9Uh6QlChDeq3L8fm0fVtb"], 41], [8, -205, [0, "35aE7APGBBF4a75qNn5mZi"]]], [1, "1e7tiAV9dIloJKzCsDgJEJ", null, null, null, 1, 0], [1, 0, -24, 0]], [29, "button_help", false, 1, [[2, -206, [0, "cbr8farUlOE6sD61MD6eJb"], [5, 52, 52]], [7, -207, [0, "1d7nRei49KgouvFTNjhX7V"], 44], [23, 3, -208, [0, "8fi1sxe0tMOJzksJYb8bZU"], [[16, "ef9e5sZYqFP3ZCrLe4sx9vX", "onBtnClick", 1]]]], [1, "80sELvtP1IF4UnJk5Nq+/a", null, null, null, 1, 0], [1, -334.556, 475.86799999999994, 0]], [4, ["dbo4r8ifZCIIo1hZPsLoZe"]], [6, "button_server", 1, [[2, -209, [0, "85Hip70PdKCJDFQbp0PYCn"], [5, 131, 24]], [7, -210, [0, "c6BxHUCWpKMKJrWMGiBnkX"], 46], [23, 3, -211, [0, "e57Q6EO7pJhZ/y6yrIX0C1"], [[16, "d0e50ROX2dBpamWNlOHpv28", "onShowServer", 1]]]], [1, "2e96LmZxNMD7asKhp3mUlK", null, null, null, 1, 0], [1, 297.092, 61.833999999999946, 0]], [9, 0, {}, 2, [10, "49+PNfEAdKea7Nn8OqLKwZ", null, null, -222, [11, "92SLahnUVIPKHu/D+th5qA", 1, [[5, "bgClose", ["_name"], -212], [3, ["_lpos"], -213, [1, 0, 0, 0]], [3, ["_lrot"], -214, [3, 0, 0, 0, 1]], [3, ["_euler"], -215, [1, 0, 0, 0]], [26, ["_contentSize"], [4, ["73TTyP4IJKVZWnh2kKl7c1"]], [5, 750, 1334]], [5, 0, ["_left"], -216], [5, 0, ["_right"], -217], [5, 1, ["clickEvents", "length"], -218], [69, ["clickEvents", "0"], -219, [16, "d0e50ROX2dBpamWNlOHpv28", "onShowServer", 1]], [38, ["_target"], -221, -220]]], 47]], [4, ["49+PNfEAdKea7Nn8OqLKwZ"]], [47, "bg", 2, [[2, -223, [0, "f1pi3ChVJGl7E1ZOn1AVZT"], [5, 719, 257]], [15, 0, -224, [0, "a6MqtlbptCBau4HATpy+Nn"], 48], [71, -225, [0, "5atvgN0d5ElI1N01d5Ejlp"]]], [1, "ff0WcOHzdPZK0MXJWWxbk9", null, null, null, 1, 0]], [6, "title", 2, [[2, -226, [0, "f86hf/42hOwr7AGG4aptPQ"], [5, 124, 34.24]], [17, "参赛服务器", 24, 24, 24, false, false, true, -227, [0, "c9t+nyH3VFK7XJf80b/8D9"], 50], [8, -228, [0, "3dzz5an4NCz6Ba3BMMe65M"]]], [1, "15ePq1F5xC+oQPBUYuY2Lu", null, null, null, 1, 0], [1, 0, 94.13499999999999, 0]], [21, "lbServer", 2, [[[2, -229, [0, "ceUCNCmNRCGqNQq5vXOyUL"], [5, 76, 34.24]], -230, [8, -231, [0, "2doxnipRtCr7BSOWOn+/j0"]]], 4, 1, 4], [1, "14Gty0CBxJjbeZ+6jE8Dai", null, null, null, 1, 0], [1, 0, -1.5049999999999955, 0]], [9, 0, {}, 1, [10, "49+PNfEAdKea7Nn8OqLKwZ", null, null, -232, [11, "25PSmOZrhG44d6jQJPolRj", 1, [[5, "bgClose", ["_name"], 29], [3, ["_lpos"], 29, [1, 0, 0, 0]], [3, ["_lrot"], 29, [3, 0, 0, 0, 1]], [3, ["_euler"], 29, [1, 0, 0, 0]]]], 0]], [6, "bg", 1, [[2, -233, [0, "91J+OU0MBNWra3c6ORwHrD"], [5, 750, 1105]], [15, 0, -234, [0, "86sS2SOXlGaIAdHi3u/LHS"], 1]], [1, "42zt3QPMBEd5bv6DtXKAAk", null, null, null, 1, 0], [1, 0, -10, 0]], [14, "Sprite", 512, 5, [[2, -235, [0, "0e5PWlFIhMnZtn2dvAb2mO"], [5, 750, 74]], [7, -236, [0, "7dyM1MSEVKcqmf2CrnZ+c0"], 2]], [1, "88c9FWm+FOSJT5PzLojIHe", null, null, null, 1, 0], [1, 0, -235, 0]], [14, "titleBg", 512, 5, [[2, -237, [0, "cfbClm1kJBRo+dz5DBYf+A"], [5, 610, 108]], [7, -238, [0, "64BrZlWpRLqJoxDFg7gLLy"], 3]], [1, "ea1tqgZD5KeIbDYOYvnpij", null, null, null, 1, 0], [1, 0, 226.87300000000005, 0]], [14, "spIcon", 512, 7, [[2, -239, [0, "39/FtgzJ9GSrHyDK1OtMmR"], [5, 80, 80]], [15, 2, -240, [0, "2byfT3JY5H6ILkWqXZHuaF"], 6]], [1, "96GZAknwNK3bU7MXeUX/51", null, null, null, 1, 0], [1, -37.17500000000001, 0, 0]], [9, 0, {}, 3, [10, "cc+NVKm5tBW61CeFJEsJ71", null, null, -241, [11, "d1ZE03yVRKfY9uX2YyeMpS", 1, [[5, "BattlefrontRoleInfoNode", ["_name"], 35], [3, ["_lpos"], 35, [1, 0, 0, 0]], [3, ["_lrot"], 35, [3, 0, 0, 0, 1]], [3, ["_euler"], 35, [1, 0, 0, 0]], [39, false, ["_active"], [4, ["96kzvSqQdPhr3lGkI+0grf", "f3OdqDoPhD0aP0W/ojHTpT"]]]]], 10]], [9, 0, {}, 3, [10, "cc+NVKm5tBW61CeFJEsJ71", null, null, -242, [11, "c4/XGqEzxAz5KPzIzWFHCJ", 1, [[5, "BattlefrontRoleInfoNode-001", ["_name"], 36], [3, ["_lpos"], 36, [1, 716.841, 0, 0]], [3, ["_lrot"], 36, [3, 0, 0, 0, 1]], [3, ["_euler"], 36, [1, 0, 0, 0]]]], 11]], [9, 0, {}, 3, [10, "cc+NVKm5tBW61CeFJEsJ71", null, null, -243, [11, "e8jjV5w6tEbb5YleJHzIOe", 1, [[5, "BattlefrontRoleInfoNode-002", ["_name"], 37], [3, ["_lpos"], 37, [1, 1433.682, 0, 0]], [3, ["_lrot"], 37, [3, 0, 0, 0, 1]], [3, ["_euler"], 37, [1, 0, 0, 0]]]], 12]], [9, 0, {}, 3, [10, "cc+NVKm5tBW61CeFJEsJ71", null, null, -244, [11, "12B7XfNjpIAKJ5e9HhR9BA", 1, [[5, "BattlefrontRoleInfoNode-003", ["_name"], 38], [3, ["_lpos"], 38, [1, 2150.523, 0, 0]], [3, ["_lrot"], 38, [3, 0, 0, 0, 1]], [3, ["_euler"], 38, [1, 0, 0, 0]]]], 13]], [9, 0, {}, 3, [10, "cc+NVKm5tBW61CeFJEsJ71", null, null, -245, [11, "0b2WN873JDKpGvO6g300Zq", 1, [[5, "BattlefrontRoleInfoNode-004", ["_name"], 39], [3, ["_lpos"], 39, [1, 2867.364, 0, 0]], [3, ["_lrot"], 39, [3, 0, 0, 0, 1]], [3, ["_euler"], 39, [1, 0, 0, 0]]]], 14]], [20, "chuanshuo_dot2", 33554432, 6, [[2, -246, [0, "9fpeOTGblBI4GZuQeUOGr0"], [5, 24, 24]], [7, -247, [0, "0dqWtE3NhPjoUmyzhfGlP+"], 16]], [1, "e3VIVzDH5OtIx4wCv4GFQ5", null, null, null, 1, 0], [1, -35.99799999999999, 0, 0]], [20, "chuanshuo_dot3", 33554432, 6, [[2, -248, [0, "6cYt9e+gBHEIhy70/PdA3j"], [5, 24, 24]], [7, -249, [0, "5a/oyhIVNAuIfixTTgQurq"], 18]], [1, "02VBzwWrVMBqfQC7J99mw5", null, null, null, 1, 0], [1, -1.8000000000000114, 0, 0]], [20, "chuanshuo_dot5", 33554432, 6, [[2, -250, [0, "0a4FJiDRFKLKPDdrpIvc2g"], [5, 24, 24]], [7, -251, [0, "748Dgs9nNDLrUQPx3d9eNe"], 20]], [1, "f7wlSihS1F+ZA3WHE37irP", null, null, null, 1, 0], [1, 34.19900000000001, 0, 0]], [6, "itemBgBtn", 4, [[2, -252, [0, "a93D60Nj1HXbSdTF1uBLNI"], [5, 713, 149]], [15, 0, -253, [0, "ddQkmDpvRAv6G9kzbQ6OND"], 22]], [1, "41P6YbTDdGZrt2RaSo6HYt", null, null, null, 1, 0], [1, 5, 0, 0]], [9, 0, {}, 8, [10, "c46/YsCPVOJYA4mWEpNYRx", null, null, -254, [11, "25MyEUXVdKNbh8DpS2fI7C", 1, [[5, "UIPlayerHead", ["_name"], 18], [3, ["_lpos"], 18, [1, 0, 35.174, 0]], [3, ["_lrot"], 18, [3, 0, 0, 0, 1]], [3, ["_euler"], 18, [1, 0, 0, 0]], [26, ["_contentSize"], [4, ["6dV2Ske+5MYbBtsQ9bfHmo"]], [5, 65, 65]], [70, ["_spriteFrame"], [4, ["e3lZ7YCexMoYPzNGCZ0et6"]], 24], [5, true, ["_active"], 18]]], 23]], [48, "vocationIcon", 8, [[2, -255, [0, "22idhZ8UJJlrD44TwK/FXG"], [5, 45, 45]], [57, 0, false, -256, [0, "29S4GJ1mpI3IF1AhpxWVV1"], 25, 26]], [1, "bd0WzYj+ZBIalri+o6164+", null, null, null, 1, 0], [1, -28.060000000000002, 11.598999999999933, 0], [1, 0.6, 0.6, 1]], [6, "fight", 19, [[2, -257, [0, "c4pSwmgetDaoNlus+t4OwA"], [5, 33, 31]], [7, -258, [0, "07BfZdI9xMP6OmaLwjUDwZ"], 27]], [1, "93GmvNx95Iqr60LXz7I/jq", null, null, null, 1, 0], [1, -31.519981384277344, -52.09299999999996, 0]], [9, 0, {}, 8, [10, "f3OdqDoPhD0aP0W/ojHTpT", null, null, -265, [11, "83Bqp1uY9AKoS6kSh/kdO/", 1, [[5, "lay_name", ["_name"], 20], [3, ["_lpos"], 20, [1, 0, -15.807, 0]], [3, ["_lrot"], 20, [3, 0, 0, 0, 1]], [3, ["_euler"], 20, [1, 0, 0, 0]], [3, ["_contentSize"], -259, [5, 160, 30]], [3, ["_anchorPoint"], -260, [0, 0.5, 0.5]], [5, "rich", ["_name"], -261], [39, 30, ["speed"], [4, ["948QzsdbBF8YbB+3RAdngW"]]], [5, 1, ["_horizontalAlign"], 21], [5, 1, ["_verticalAlign"], 21], [3, ["_lpos"], -262, [1, 0, 0, 0]], [3, ["_anchorPoint"], -263, [0, 0.5, 0.5]], [5, true, ["_active"], 20], [3, ["_contentSize"], -264, [5, 97.49998474121094, 32.76]], [5, "五个字...", ["_string"], 21], [5, 26, ["_fontSize"], 21], [5, 26, ["_lineHeight"], 21]]], 30]], [6, "jing<PERSON>chang_frame_26", 4, [[2, -266, [0, "66lCpzxuNBKbEMllAcfS45"], [5, 75, 143]], [7, -267, [0, "58iKA5oW9DoLgxpF4GiSLJ"], 31]], [1, "05d5/kJKNPbqUio8iO971+", null, null, null, 1, 0], [1, 308.63500000000005, 4.649000000000001, 0]], [6, "rankIcon", 4, [[2, -268, [0, "57NG005MREX7Wq7wUziy+q"], [5, 61, 47]], [58, -269, [0, "95w7N4a1JAboi6IhuxpilV"], 32, 33]], [1, "3cwqTGU1BM66uAP0cVFwO0", null, null, null, 1, 0], [1, -307.465, 3.5370000000000346, 0]], [6, "spIcon", 4, [[2, -270, [0, "c2jZ/+YDZNvaQ2345VtP7g"], [5, 80, 80]], [15, 2, -271, [0, "0b2ViTqhdNw4FAgrc4cVL5"], 34]], [1, "508rKmVqJATbV5aUWmrnqQ", null, null, null, 1, 0], [1, 309.858, 38.738000000000056, 0]], [6, "nothing_bg", 23, [[2, -272, [0, "2f6o83HGJNYrtdLSs3ukBd"], [5, 214, 298]], [7, -273, [0, "fcuwd4hShPaLsDslHaljtd"], 39]], [1, "d9vA/up8lEXr+sK6T21PL2", null, null, null, 1, 0], [1, -11.884999999999991, 174.789, 0]], [9, 0, {}, 1, [10, "dbo4r8ifZCIIo1hZPsLoZe", null, null, -274, [11, "ceNey39ZtDyI2EPgGMvtAz", 1, [[5, "btnClose", ["_name"], 51], [3, ["_lpos"], 51, [1, 346.119, 504.4549999999999, 0]], [3, ["_lrot"], 51, [3, 0, 0, 0, 1]], [3, ["_euler"], 51, [1, 0, 0, 0]]]], 45]], [4, ["e8h56hlr9Dh7YuJ+x7hhi9"]], [37, "0", 22, 22, 25, false, false, true, 32, [0, "24ll3R0dxG+bvYFF2GH0Hz"], [4, 4283957233]], [37, "0", 22, 22, 25, false, false, true, 33, [0, "cbsCQgqN9K1Js6vZkizXEw"], [4, 4279627566]], [72, 0.3, 14, [0, "baFesfYixBL6nJ+iy98IsQ"], 3], [4, ["a3SifehbVO+JKTEXbDDYAn"]], [4, ["9fzr2fmzZNh5jUFBouaNNe"]], [4, ["c9aXSE1RhPgoKqYoSOPblc"]], [36, 0.23, 0.75, false, 9, [0, "912zXmtY5OAr8U4fU/41c2"], 17], [73, 9, [0, "4aJlOjPe9ATaApDQa2TZPC"], 4, [35]], [4, ["2dQ2wXCkFJzY5oPQvWqcNX"]], [4, ["8fN27e90VKwrOm0UkkXWci"]], [4, ["8e9dcKKG5Na4U2YorfR3IR"]], [74, false, 11, [0, "feYGg9JpxL77iBbFaZw6tg"], 28, [35]], [68, "全平台", 24, 24, 24, false, false, true, 57, [0, "a34LMKLMtED5VeUOQc7/mO"]]], 0, [0, -1, 53, 0, -2, 80, 0, -3, 24, 0, -4, 22, 0, -5, 75, 0, -6, 72, 0, -7, 67, 0, -8, 66, 0, -9, 65, 0, -10, 64, 0, -11, 63, 0, -12, 58, 0, 8, 22, 0, 9, 22, 0, 8, 24, 0, 9, 24, 0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 12, 94, 0, 13, 2, 0, -1, 40, 0, -2, 41, 0, -3, 42, 0, 14, 13, 0, 15, 7, 0, 16, 89, 0, 17, 23, 0, 18, 83, 0, 19, 82, 0, 20, 3, 0, 21, 84, 0, 0, 1, 0, -1, 58, 0, -2, 59, 0, -3, 5, 0, -4, 14, 0, -5, 6, 0, -6, 9, 0, -7, 22, 0, -8, 23, 0, -9, 10, 0, -10, 50, 0, -11, 80, 0, -12, 52, 0, -13, 2, 0, 0, 2, 0, 0, 2, 0, 22, 93, 0, 0, 2, 0, -1, 53, 0, -2, 55, 0, -3, 26, 0, -4, 56, 0, -5, 11, 0, -6, 57, 0, 0, 3, 0, 0, 3, 0, -1, 63, 0, -2, 64, 0, -3, 65, 0, -4, 66, 0, -5, 67, 0, 0, 4, 0, -1, 71, 0, -2, 43, 0, -3, 76, 0, -4, 77, 0, -5, 78, 0, -6, 46, 0, -7, 47, 0, 0, 5, 0, 0, 5, 0, -1, 60, 0, -2, 61, 0, -3, 30, 0, -4, 7, 0, -5, 13, 0, -6, 34, 0, 0, 6, 0, -1, 68, 0, -2, 40, 0, -3, 69, 0, -4, 41, 0, -5, 70, 0, -6, 42, 0, 0, 7, 0, -1, 31, 0, -2, 32, 0, -3, 33, 0, -4, 62, 0, 0, 8, 0, -1, 72, 0, -2, 73, 0, -3, 19, 0, -4, 45, 0, -5, 75, 0, 0, 9, 0, -2, 88, 0, -3, 89, 0, 0, 9, 0, -1, 16, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, -1, 49, 0, -2, 24, 0, 0, 11, 0, 23, 27, 0, 0, 11, 0, -3, 93, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -1, 27, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, -3, 84, 0, -1, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 17, 0, 0, 17, 0, 0, 17, 0, 0, 19, 0, 0, 19, 0, -1, 74, 0, -2, 44, 0, 1, 48, 0, 1, 48, 0, 1, 48, 0, 1, 48, 0, 6, 88, 0, 1, 90, 0, 1, 90, 0, 4, 22, 0, 0, 23, 0, 0, 23, 0, -1, 79, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 91, 0, 1, 91, 0, 1, 25, 0, 4, 24, 0, 0, 26, 0, 0, 26, 0, 24, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, -1, 28, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, -2, 82, 0, 0, 32, 0, 0, 33, 0, -2, 83, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 49, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 50, 0, 0, 52, 0, 0, 52, 0, 0, 52, 0, 1, 54, 0, 1, 54, 0, 1, 54, 0, 1, 54, 0, 1, 92, 0, 1, 92, 0, 1, 81, 0, 1, 81, 0, 6, 53, 0, 1, 81, 0, 4, 53, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, -2, 94, 0, 0, 57, 0, 4, 58, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 62, 0, 4, 63, 0, 4, 64, 0, 4, 65, 0, 4, 66, 0, 4, 67, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 4, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 1, 85, 0, 1, 85, 0, 1, 86, 0, 1, 86, 0, 1, 87, 0, 1, 87, 0, 4, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 4, 80, 0, 25, 1, 3, 7, 15, 4, 7, 17, 8, 7, 43, 274], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 82, 83, 94], [5, 2, 2, 2, 3, 3, 2, 3, 3, 2, 5, 5, 5, 5, 5, 2, 2, 2, 2, 2, 2, 2, 2, 5, 6, 2, 10, 2, 3, 3, 5, 2, 2, 10, 2, 3, 3, 2, 5, 2, 3, 3, 5, 2, 2, 5, 2, 5, 2, 2, 3, 3, 2, 26, 3, 3, 3], [4, 8, 9, 10, 0, 0, 5, 0, 0, 11, 1, 1, 1, 1, 1, 12, 2, 3, 2, 3, 2, 3, 13, 14, 15, 16, 17, 18, 0, 0, 19, 20, 21, 22, 5, 0, 0, 6, 23, 24, 0, 0, 25, 26, 27, 28, 29, 4, 30, 31, 0, 0, 6, 32, 0, 0, 0]]]]