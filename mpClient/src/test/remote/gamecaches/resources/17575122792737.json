[1, ["61NXJOyyZNU6ZGh0FJkMv2", "541WLRVAlDBJZU4D6y5CRZ@6c48a", "2enw6dg1JAa5xhWIDgaiWv@1100d", "2enw6dg1JAa5xhWIDgaiWv@a0708", "c1rLmu/GhMz7i/DEoDVHz7", "d8ikyXL1JGQ6jVrMuXESDm", "89FnbzNR9DvKjyDKgpIEsk@47773", "89FnbzNR9DvKjyDKgpIEsk@819c8", "89FnbzNR9DvKjyDKgpIEsk@1eaa7", "9eKd+t+RFBqYWj7VNXzJHe@f9941", "03xxrCZWdGQrcWs4309XRb@6c48a", "23qCr1KNtPi6iUBDDYV/FB@6c48a", "d5HsmWyhhLk7pOcjEKfLW7@f9941", "003XsrKclKsL0AX0p7cbeY@f9941", "2enw6dg1JAa5xhWIDgaiWv@da132", "ffuIqPr2JI9I8dPLYGRDpD@f9941", "2enw6dg1JAa5xhWIDgaiWv@ab328", "89FnbzNR9DvKjyDKgpIEsk@4677b", "89FnbzNR9DvKjyDKgpIEsk@9f348", "89FnbzNR9DvKjyDKgpIEsk@bedd7", "2enw6dg1JAa5xhWIDgaiWv@6e447", "89FnbzNR9DvKjyDKgpIEsk@1212f", "62Hrci6TNDrq5pYKIyRrhH", "16mojGSc1Nvrb/YTBQYkJg@f9941", "0d2Rss5lBIELZgdjqjQQeK", "2enw6dg1JAa5xhWIDgaiWv@a6399", "40mmaZK8VNXIQfrnyjJ4ZA", "dfQgPUFp9BzacaD8ACVBbL@f9941", "b4BBvsrqlKha7mAOXIACtN@f9941", "03xxrCZWdGQrcWs4309XRb@f9941", "23qCr1KNtPi6iUBDDYV/FB@f9941", "b0MdOrSJtDf5jMDg9ncaiZ@f9941", "bee4xeDpBGEZqv8H7gOzyD@f9941", "afxHkx8GZGsJC+n+YfITQo@f9941", "89FnbzNR9DvKjyDKgpIEsk", "b0MdOrSJtDf5jMDg9ncaiZ@6c48a", "b4BBvsrqlKha7mAOXIACtN@6c48a", "bee4xeDpBGEZqv8H7gOzyD@6c48a", "dfQgPUFp9BzacaD8ACVBbL@6c48a"], ["node", "targetInfo", "_spriteFrame", "_font", "_textureSource", "root", "asset", "_target", "target", "source", "_checkMark", "_parent", "value", "data", "myRankNum", "topBg", "rankList", "rankTab", "rankNum", "rankIcon", "strengthValue", "level", "nick<PERSON><PERSON>", "talentIcon", "junxianIcon", "_scrollView", "_atlas"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lscale"], -2, 4, 12, 1, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_alignMode", "_bottom", "node", "__prefab"], -2, 1, 4], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_cacheMode", "_enableOutline", "_lineHeight", "_outlineWidth", "node", "__prefab", "_font", "_color"], -6, 1, 4, 6, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingY", "_spacingX", "_isAlign", "_paddingTop", "node", "__prefab"], -3, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo", "sourceInfo"], 2, 1, 1, 4, 4], ["cc.Toggle", ["_isChecked", "node", "__prefab", "_normalColor", "_target", "_checkMark"], 2, 1, 4, 5, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "clickEvents"], 2, 1, 4, 5, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["bounceDuration", "brake", "vertical", "horizontal", "node", "__prefab", "_content", "_verticalScrollBar"], -1, 1, 4, 1, 1], "cc.SpriteAtlas", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "nestedPrefabInstanceRoots", "root", "instance", "targetOverrides", "asset"], 1, 1, 4, 9, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.ToggleContainer", ["node", "__prefab"], 3, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 1], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["c5e70b/tg1Ln5w/WYKCjOaJ", ["node", "__prefab", "vocationImgs", "emptyTips", "spChengHao"], 3, 1, 4, 3, 1, 2], ["bbeb5s3M2FNf7p5RcxpQJso", ["node", "__prefab"], 3, 1, 4], ["<PERSON>.<PERSON>", ["_direction", "node", "__prefab", "_handle"], 2, 1, 4, 1], ["107a3Ucyc5CMoNb5CYJ1tFl", ["node", "__prefab"], 3, 1, 4], ["96925NpxVhGX5GHR06EhbFH", ["node", "__prefab", "tmpNode", "pageChangeEvent"], 3, 1, 4, 1, 4]], [[15, 0, 2], [17, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 1], [27, 0, 1, 2, 2], [20, 0, 2], [26, 0, 1, 2, 3], [3, 0, 5, 3, 4, 7, 2], [5, 0, 1, 2, 7, 3, 4, 5, 6, 9, 10, 11, 9], [3, 0, 5, 6, 3, 4, 7, 2], [2, 3, 4, 5, 1], [1, 3, 4, 7, 5, 3], [7, 0, 1, 2, 3, 1], [25, 0, 1, 2, 2], [18, 0, 1, 2, 3, 4, 5, 4], [2, 1, 0, 3, 4, 5, 3], [32, 0, 1, 1], [2, 0, 3, 4, 2], [1, 0, 7, 8, 6, 5, 9, 2], [1, 0, 1, 7, 8, 6, 5, 3], [1, 0, 7, 6, 5, 9, 2], [9, 0, 1, 2, 3, 4, 5, 2], [29, 0, 1, 2, 2], [8, 0, 1, 2, 3, 2], [28, 0, 1, 2, 3], [5, 0, 1, 2, 7, 3, 4, 6, 9, 10, 8], [2, 0, 3, 4, 5, 2], [2, 2, 3, 4, 2], [1, 0, 8, 6, 5, 9, 2], [1, 0, 2], [3, 0, 5, 6, 3, 4, 2], [3, 0, 5, 3, 4, 2], [4, 0, 1, 2, 5, 6, 4], [8, 0, 1, 4, 2, 3, 2], [2, 0, 2, 3, 4, 3], [23, 0, 1, 1], [24, 0, 1, 2, 1], [14, 0, 2], [1, 0, 8, 6, 5, 2], [1, 0, 1, 2, 8, 6, 5, 9, 4], [1, 0, 7, 8, 6, 5, 2], [1, 0, 2, 7, 6, 5, 9, 3], [1, 0, 7, 6, 5, 9, 10, 2], [1, 0, 7, 6, 5, 2], [3, 0, 1, 6, 3, 4, 7, 3], [3, 0, 1, 5, 6, 3, 4, 3], [3, 0, 2, 5, 3, 4, 7, 3], [4, 0, 1, 5, 6, 3], [4, 0, 2, 3, 5, 6, 4], [4, 0, 4, 5, 6, 3], [16, 0, 1, 2, 3, 4, 5, 3], [19, 0, 1, 2, 3, 4, 5, 3], [21, 0, 1, 1], [6, 0, 1, 3, 2, 4, 6, 7, 6], [6, 0, 1, 6, 7, 3], [6, 0, 1, 5, 2, 6, 7, 5], [2, 1, 0, 3, 4, 3], [2, 3, 4, 1], [9, 1, 2, 3, 4, 5, 1], [10, 0, 1, 2, 3, 4, 2], [10, 1, 2, 5, 1], [22, 0, 1, 1], [30, 0, 1, 2, 2], [31, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 6, 8, 9, 10, 11, 9], [5, 0, 1, 2, 7, 3, 4, 5, 9, 10, 12, 11, 8], [11, 0, 1, 2, 3], [11, 1], [33, 0, 1, 2, 3, 4, 1], [34, 0, 1, 1], [35, 0, 1, 2, 3, 2], [12, 0, 1, 2, 4, 5, 6, 7, 4], [12, 0, 1, 3, 4, 5, 6, 4], [36, 0, 1, 1], [37, 0, 1, 2, 3, 1]], [[[{"name": "p<PERSON><PERSON><PERSON>_zhiye_daozei_bg", "rect": {"x": 0, "y": 0, "width": 719, "height": 364}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 719, "height": 364}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-359.5, -182, 0, 359.5, -182, 0, -359.5, 182, 0, 359.5, 182, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 364, 719, 364, 0, 0, 719, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -359.5, "y": -182, "z": 0}, "maxPos": {"x": 359.5, "y": 182, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [10]], [[{"name": "pai<PERSON><PERSON>_zhiye_lieren_bg", "rect": {"x": 0, "y": 0, "width": 719, "height": 364}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 719, "height": 364}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-359.5, -182, 0, 359.5, -182, 0, -359.5, 182, 0, 359.5, 182, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 364, 719, 364, 0, 0, 719, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -359.5, "y": -182, "z": 0}, "maxPos": {"x": 359.5, "y": 182, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [11]], [[[36, "RankDlgUI"], [37, "RankDlgUI", [-25, -26, -27, -28], [[[2, -22, [0, "fd9xMOlmZLnJnAoBBH+Y2g"], [5, 750, 1334]], [31, 45, 750, 1334, -23, [0, "42H5BAb3ZC8ZXmAEDlvZgx"]], -24], 4, 4, 1], [49, "c46/YsCPVOJYA4mWEpNYRx", null, -21, 0, [[22, ["userModel1"], -12, -11, [4, ["d51ZN24RRESpuoHZqYMWmi"]]], [22, ["userModel2"], -14, -13, [4, ["d51ZN24RRESpuoHZqYMWmi"]]], [22, ["userModel3"], -16, -15, [4, ["d51ZN24RRESpuoHZqYMWmi"]]], [22, ["playerHead"], -18, -17, [4, ["54BQwDKY9FlJPSQ6/WOXeX"]]], [22, ["nick<PERSON><PERSON>"], -20, -19, [4, ["edJWUpoHFEIYwtOPt+zzCQ"]]]], [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10]]], [27, "RankStrengthItem", [-31, -32, -33, -34, -35, -36, -37, -38, -39, -40], [[[2, -29, [0, "abAEXEwyhBzriLgYGheOj+"], [5, 682, 124]], -30], 4, 1], [1, "e2z7AQTp9FsrsTo9f2SGlZ", null, null, null, 1, 0], [1, 0, -72, 0]], [29, "Node", 1, [-43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53], [[2, -41, [0, "a6YREZgQdKGq3Jl84a4RCh"], [5, 750, 1334]], [46, 44, 750, -42, [0, "b5sN/910tNNaB1ibcMcBBh"]]], [1, "5fK5+9qqtMHbN+oPRyk+Bd", null, null, null, 1, 0]], [27, "rankTab", [-58, -59, -60, -61, -62, -63, -64], [[[51, -54, [0, "28sboH5QtEvLMwZXrSjo/E"]], [11, -55, [0, "5cpN+VHQlD4Ki9hvjN8pv4"], [5, 942.8, 100], [0, 0, 0.5]], [52, 1, 1, 4.3, 2, true, -56, [0, "58EHxT+rBAQpnIDeNZdN5n"]], -57], 4, 4, 4, 1], [1, "4diBXeUPhJaZECCvZKRXaK", null, null, null, 1, 0], [1, 0, 4.542999999999893, 0]], [17, "topBg", 3, [-67, -68, -69, -70, -71, -72, -73], [[[2, -65, [0, "13fSxbYxdGuJgZp2nfaOfC"], [5, 719, 465]], -66], 4, 1], [1, "e4ovcrj05Cmr719SFfn9Pf", null, null, null, 1, 0], [1, 0, 172.585, 0]], [8, "Toggle1", 4, [-79, -80], [[2, -74, [0, "c9yO7ebM5JWK2Lukm+qeEV"], [5, 131, 51]], [14, 1, 0, -75, [0, "1csAwBN5NGwqdA+Z3bDvqs"], 7], [57, -78, [0, "d2skvAv3REX4naKMClxkO3"], [4, 4292269782], -77, -76]], [1, "a7054VVy1Mfow5TSmHrwe7", null, null, null, 1, 0], [1, 65.5, 0, 0]], [8, "Toggle2", 4, [-86, -87], [[2, -81, [0, "28kNJzQ09FHZc6jqm8sLsC"], [5, 131, 51]], [14, 1, 0, -82, [0, "a1L3ABaEZLfaNfDnM9yywZ"], 10], [20, false, -85, [0, "c2jlL1lX5CSKOCayEHX6PP"], [4, 4292269782], -84, -83]], [1, "d5oSayPv5EHri6u9cXhqbt", null, null, null, 1, 0], [1, 200.8, 0, 0]], [8, "Toggle3", 4, [-93, -94], [[2, -88, [0, "7532Hh6HxG1bU9pDuxTgrv"], [5, 131, 51]], [14, 1, 0, -89, [0, "b5p/LIfcFKXpn5uDHmUYDW"], 13], [20, false, -92, [0, "1a0/sZrgZPnJ9hv6/5XBjt"], [4, 4292269782], -91, -90]], [1, "0aRfLeUV9AHrzEc5SWrgdY", null, null, null, 1, 0], [1, 336.1, 0, 0]], [8, "Toggle4", 4, [-100, -101], [[2, -95, [0, "9eh1LHA/tEyJpuNBgPrWRz"], [5, 131, 51]], [14, 1, 0, -96, [0, "4fuuJrfOdEvabBD0x57hjp"], 16], [20, false, -99, [0, "1f1b+bTxdHW7uYIV8xIvsh"], [4, 4292269782], -98, -97]], [1, "e42dlCsJRHIYOGirzZiYD+", null, null, null, 1, 0], [1, 471.40000000000003, 0, 0]], [8, "Toggle5", 4, [-107, -108], [[2, -102, [0, "37f8coSvhG+aDVyMaLGQIg"], [5, 131, 51]], [14, 1, 0, -103, [0, "a4RsnpLehKErl4uQK8/7k9"], 19], [20, false, -106, [0, "0cLHUb6zdOPY9LQz8jxNN4"], [4, 4292269782], -105, -104]], [1, "f3I8HRnmdGPYX/pMYoiYg4", null, null, null, 1, 0], [1, 606.7, 0, 0]], [8, "Toggle6", 4, [-114, -115], [[2, -109, [0, "70LDt/nZVG+ZnP44Izibfh"], [5, 131, 51]], [14, 1, 0, -110, [0, "a0xB0zZXFJMpj9IZC5ELak"], 22], [20, false, -113, [0, "23VjpfojlBIq9QAvkMHy/p"], [4, 4292269782], -112, -111]], [1, "d3YV95t0lGZZe5nT0noH5t", null, null, null, 1, 0], [1, 742, 0, 0]], [8, "Toggle7", 4, [-121, -122], [[2, -116, [0, "24L7MRpQlHAZN+Txku6xp2"], [5, 131, 51]], [14, 1, 0, -117, [0, "f5PWUPvG5Loo3CUDKS367u"], 25], [20, false, -120, [0, "32xeYko1hBd5nCIazCMZCH"], [4, 4292269782], -119, -118]], [1, "b4uADmRXNAPYILVptU35fV", null, null, null, 1, 0], [1, 877.3, 0, 0]], [38, "scrollBar", false, 33554432, [-127], [[[11, -123, [0, "64Q3gG3ZZAPrjTJlTfbVtz"], [5, 12, 80], [0, 1, 0.5]], [14, 1, 0, -124, [0, "dfdJqIRKRDsadjQyhwi1XQ"], 4], [47, 37, 250, 1, -125, [0, "3bmvAysMpKNrlqLm9D7BAl"]], -126], 4, 4, 4, 1], [1, "4b8Ovhx4BPXYqWXqr75eK3", null, null, null, 1, 0], [1, 360, 0, 0]], [8, "zhezhao", 5, [-130, -131, -132], [[2, -128, [0, "89hWPZg4ZNG6Pbvw9ql8kX"], [5, 719, 40]], [25, 0, -129, [0, "5dm4jY2fpIop7a59mwnv5B"], 28]], [1, "e4ivuh6xtLlK7mHCLr6Z4v", null, null, null, 1, 0], [1, 0, -215.36, 0]], [4, ["edJWUpoHFEIYwtOPt+zzCQ"]], [4, ["edJWUpoHFEIYwtOPt+zzCQ"]], [4, ["edJWUpoHFEIYwtOPt+zzCQ"]], [17, "rankList", 3, [-137], [[[11, -133, [0, "e2R0kgWBpF+7d0JIppOUAh"], [5, 720, 450], [0, 0.5, 1]], -134, -135, [14, 1, 0, -136, [0, "d1xIOT73VOtqLlAjadoEkS"], 48]], 4, 1, 1, 4], [1, "92ZA/vt3dI6r754zJTG+n7", null, null, null, 1, 0], [1, 0, -72.239, 0]], [4, ["edJWUpoHFEIYwtOPt+zzCQ"]], [6, "btnClose", 1, [[2, -138, [0, "beR4XxtudJQqWyc7irHx7o"], [5, 112, 85]], [25, 2, -139, [0, "daD8oA495MMZR4GOz/0vqt"], 51], [58, 3, -141, [0, "a4ULpRpHdBNZVrWN9FQ57S"], [4, 4292269782], -140], [48, 12, 12.062000000000012, -142, [0, "b2EiLe+wVMWZ0ofNiaNiec"]]], [1, "c1U73OVd5Pn4WIq6eXVqQb", null, null, null, 1, 0], [1, -319, -612.438, 0]], [30, "bg", 1, [[2, -143, [0, "d6QQGxRhxN0bLBddeWDM7j"], [5, 750, 1334]], [25, 0, -144, [0, "03h/JUSc9Nm6W4KauWmE1h"], 0], [60, -145, [0, "e3knOOIGpDFaOTYJ7uj9ur"]], [31, 45, 750, 1334, -146, [0, "6aLN8iWmROM5HYBym8YrGg"]]], [1, "82G6Qmsx5LD4yFiUgz7ba6", null, null, null, 1, 0]], [17, "scrollViewToggle", 3, [13, -149], [[[2, -147, [0, "490RTHN8FHqrbZ6KNKekNa"], [5, 720, 80]], -148], 4, 1], [1, "4ehUcxLwZNro6+HLuU7XHD", null, null, null, 1, 0], [1, 0, 438.193, 0]], [43, "content", 33554432, [4], [[11, -150, [0, "f0oR3ScNxMbpiXewHe5mtl"], [5, 942.8, 51], [0, 0, 0.5]], [53, 1, 1, -151, [0, "eaadd+wCVNBakPi8xaNtoh"]]], [1, "20iezy55hLtbVtM1x1acWZ", null, null, null, 1, 0], [1, -360, 0, 0]], [44, "view", 33554432, 22, [23], [[2, -152, [0, "b3ViUw0GFEJqukmWi278Mj"], [5, 720, 80]], [34, -153, [0, "9famd+OeVGWqGwvpAKW1yq"]], [35, -154, [0, "744NGMIFpAkpQVjTGr14dy"], [4, 16777215]]], [1, "2frP8ziQBFDahk7q1dvI2c", null, null, null, 1, 0]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [17, "spChengHao1", 3, [-157, -158], [[[2, -155, [0, "84iN9vCrtNvZxcivUkjoVW"], [5, 50, 20]], -156], 4, 1], [1, "a4ZV2PXqpF8I4GwESBnBfh", null, null, null, 1, 0], [1, 0, 375, 0]], [4, ["f3OdqDoPhD0aP0W/ojHTpT"]], [17, "spChengHao2", 3, [-161, -162], [[[2, -159, [0, "11Cqaf43JBWZjvkFLhqE39"], [5, 50, 20]], -160], 4, 1], [1, "ea8fCzcbFHxKxQQPiwWQYq", null, null, null, 1, 0], [1, -230.775, 365, 0]], [4, ["f3OdqDoPhD0aP0W/ojHTpT"]], [17, "spChengHao3", 3, [-165, -166], [[[2, -163, [0, "e1sBDjZYRIkLVPQzKhD2Z1"], [5, 50, 20]], -164], 4, 1], [1, "f68eICl8pP1LtDVldK4sbU", null, null, null, 1, 0], [1, 228.018, 365, 0]], [4, ["f3OdqDoPhD0aP0W/ojHTpT"]], [8, "view", 18, [-170], [[11, -167, [0, "1aU/5oA/BKkb2fRD0PFiZv"], [5, 720, 450], [0, 0.5, 1]], [34, -168, [0, "e4JJEj07ZKI6N6/M819keS"]], [35, -169, [0, "cb1VLYkWZOM6daa4oawGD6"], [4, 16777215]]], [1, "abavuwXNtL8KEK/fNgRtDS", null, null, null, 1, 0], [1, -6.087999999999999, 0, 0]], [29, "content", 34, [2], [[11, -171, [0, "4f1IxACxhAupfm2lI/+g2f"], [5, 720, 134], [0, 0.5, 1]], [54, 1, 2, 10, 10, -172, [0, "6aAwYhTwBITIGC6oZXLP6G"]]], [1, "50H53dTDZIF42pILSA5afw", null, null, null, 1, 0]], [10, 0, {}, 2, [13, "f3OdqDoPhD0aP0W/ojHTpT", null, null, -184, [12, "67FRKTC1xGGIzQHAwJe69V", 1, [[5, "lay_name", ["_name"], -173], [3, ["_lpos"], -174, [1, -50, -0.16599999999993997, 0]], [3, ["_lrot"], -175, [3, 0, 0, 0, 1]], [3, ["_euler"], -176, [1, 0, 0, 0]], [3, ["_contentSize"], -177, [5, 140, 36]], [5, true, ["_active"], -178], [5, 24, ["_fontSize"], 19], [3, ["_contentSize"], -179, [5, 124.07199096679688, 36.76]], [5, 24, ["_actualFontSize"], 19], [5, true, ["_enableOutline"], 19], [3, ["_anchorPoint"], -180, [0, 0.5, 0.5]], [3, ["_anchorPoint"], -181, [0, 0.5, 0.5]], [3, ["_lpos"], -182, [1, 5.684341886080802e-14, 0, 0]], [23, 50, ["speed"], [4, ["50honeHvFFa4nnnP2rT90g"]]], [5, 26, ["_lineHeight"], 19], [5, "五字角色名", ["_string"], 19], [5, "nick<PERSON><PERSON>", ["_name"], -183], [5, false, ["_enableWrapText"], 19]]], 45]], [4, ["f3OdqDoPhD0aP0W/ojHTpT"]], [6, "titleTxt", 3, [[2, -185, [0, "7b67zzAZZBEJ5lv6Jilw4o"], [5, 113.9639892578125, 56.4]], [63, "排行榜", 36, 36, false, false, 1, true, 3, -186, [0, "efl6f/QPlOf4upXcbRgIya"], 3], [15, -187, [0, "60O9AIpZVIwb4j+/kt+zME"]]], [1, "eabqsPcMBAoog5pfa8tJQO", null, null, null, 1, 0], [1, -1.9850000000000136, 537.873, 0]], [39, "Checkmark", 6, [-190], [[[2, -188, [0, "343eZDl0hMXbKXiGHHR2YM"], [5, 131, 51]], -189], 4, 1], [1, "6fvuwdpZlHjKf2I8ruePZ9", null, null, null, 1, 0]], [18, "Checkmark", false, 7, [-193], [[[2, -191, [0, "0cyXuPb6RARK5X/uf6knPx"], [5, 131, 51]], -192], 4, 1], [1, "976xT+WAJPO7FIaE90Y70q", null, null, null, 1, 0]], [18, "Checkmark", false, 8, [-196], [[[2, -194, [0, "d1x/CF0wFF2qRv8zyRb+VL"], [5, 131, 51]], -195], 4, 1], [1, "29T1cWZslGWJMcE+yGWk/Y", null, null, null, 1, 0]], [18, "Checkmark", false, 9, [-199], [[[2, -197, [0, "ac6/yiJ0pGr5Udn8de1T7e"], [5, 131, 51]], -198], 4, 1], [1, "2at8tMqxlKWojc1hJkvPWV", null, null, null, 1, 0]], [18, "Checkmark", false, 10, [-202], [[[2, -200, [0, "a9YXn81bRCDY1gp8kSQkE7"], [5, 131, 51]], -201], 4, 1], [1, "73H0eqZEdN97MpKw4ZG+iU", null, null, null, 1, 0]], [18, "Checkmark", false, 11, [-205], [[[2, -203, [0, "75RWBLirBLu5rmSOoSWQc2"], [5, 131, 51]], -204], 4, 1], [1, "dbC0HZMotJvoZ9YIBqEvd6", null, null, null, 1, 0]], [18, "Checkmark", false, 12, [-208], [[[2, -206, [0, "90XyBSeWhLkr/RTAJBqoI+"], [5, 131, 51]], -207], 4, 1], [1, "7amtD4DxlDCY1swD+kR/WQ", null, null, null, 1, 0]], [6, "myRankText", 14, [[2, -209, [0, "71ONmYydVHsZQzJxK04/sF"], [5, 103, 35.5]], [7, "我的排名：", 22, 22, 25, false, false, 1, true, -210, [0, "dfowlWiDhAja23XIh2DEou"], 26], [15, -211, [0, "bfKG8uWyNE157qK060o/ay"]]], [1, "b5xopQsLtF0IoVtYGXqIF6", null, null, null, 1, 0], [1, -287.075, 0, 0]], [6, "tips", 14, [[2, -212, [0, "28UuRGrhxBNLnaJF1X/HWT"], [5, 157.9119873046875, 35.5]], [7, "排行榜定时刷新", 22, 22, 25, false, false, 1, true, -213, [0, "54TjbkTyFKd7DSVgTxA4s8"], 27], [15, -214, [0, "74n9EUJ9hGv4TwYeH/p1u7"]]], [1, "8aPWxiIvVNAr6hodCfFvAz", null, null, null, 1, 0], [1, 252.823, 0, 0]], [19, "myRankNum", 14, [[[11, -215, [0, "685Lq5841OyIAKSrSv1o5o"], [5, 19.267990112304688, 35.5], [0, 0, 0.5]], -216, [15, -217, [0, "8cMaFPIQhEZqt9egkRReRV"]]], 4, 1, 4], [1, "b38nThNWRAu5ptvKbCnZzz", null, null, null, 1, 0], [1, -233.2, 0, 0]], [8, "dizuo_3", 5, [-220], [[2, -218, [0, "9dH3qQQYZCZLA9JF3WCBm9"], [5, 163, 103]], [9, -219, [0, "979++cfZtNxKZnBfCu1U7O"], 30]], [1, "6ew+DLfLpEIKOiP9M2SIbf", null, null, null, 1, 0], [1, 228.01800000000003, -136.2, 0]], [8, "dizuo_2", 5, [-223], [[2, -221, [0, "3c3Wc+1exFvadiLHI9tMVc"], [5, 163, 103]], [9, -222, [0, "c61P69lXxOiYBtieZ9JD9h"], 32]], [1, "bdWJkCmxtEep6K3f4HjHx0", null, null, null, 1, 0], [1, -230.775, -136.7, 0]], [8, "dizuo_1", 5, [-226], [[2, -224, [0, "ddyZ1LSz5G3q13umO/5eVZ"], [5, 212, 143]], [9, -225, [0, "7bxl5RCvBFGK6lmq5PHOBJ"], 34]], [1, "59mbwCpEFEhbe57tkoIAtb", null, null, null, 1, 0], [1, 0, -129.59, 0]], [10, 0, {}, 5, [13, "c46/YsCPVOJYA4mWEpNYRx", null, null, -227, [12, "7bfxzF9OlPmbFJ/N3X0/xq", 1, [[5, "userModel3", ["_name"], 25], [3, ["_lpos"], 25, [1, 228.018, -108, 0]], [3, ["_lrot"], 25, [3, 0, 0, 0, 1]], [3, ["_euler"], 25, [1, 0, 0, 0]], [3, ["_lscale"], 25, [1, 0.5, 0.5, 1]]]], 35]], [10, 0, {}, 5, [13, "c46/YsCPVOJYA4mWEpNYRx", null, null, -228, [12, "99KkSf0aNDBZYSqKfcRk4D", 1, [[5, "userModel2", ["_name"], 26], [3, ["_lpos"], 26, [1, -230.775, -108, 0]], [3, ["_lrot"], 26, [3, 0, 0, 0, 1]], [3, ["_euler"], 26, [1, 0, 0, 0]], [3, ["_lscale"], 26, [1, 0.5, 0.5, 1]]]], 36]], [10, 0, {}, 5, [13, "c46/YsCPVOJYA4mWEpNYRx", null, null, -229, [12, "bfQG3OQ/5DXqVWiAXC7c87", 1, [[5, "userModel1", ["_name"], 27], [3, ["_lpos"], 27, [1, 0, -110, 0]], [3, ["_lrot"], 27, [3, 0, 0, 0, 1]], [3, ["_euler"], 27, [1, 0, 0, 0]], [3, ["_lscale"], 27, [1, 0.5, 0.5, 1]]]], 37]], [30, "itemBgBtn", 2, [[2, -230, [0, "59P701aHRJwI4I+PCkkuV4"], [5, 682, 124]], [25, 2, -231, [0, "64n3eDLA9HHLdUsLce1BOK"], 44], [59, -232, [0, "ecgAU3DPJN6pZtBM7T6abI"], [[65, "bbeb5s3M2FNf7p5RcxpQJso", "onItemBtnClick", 2]]]], [1, "10FIO8PcRLdavddS3miBo2", null, null, null, 1, 0]], [19, "level", 2, [[[11, -233, [0, "bcswUuj/9NMIAkLdjBcmZQ"], [5, 71.43997192382812, 29.2], [0, 0, 0.5]], -234, [15, -235, [0, "23l7DFtmVERa0vMRkXudit"]]], 4, 1, 4], [1, "9ajLe5J0lFpKdNsk0tzmaQ", null, null, null, 1, 0], [1, 92.261, 3.121, 0]], [19, "strengthValue", 2, [[[11, -236, [0, "b4KXVnu3hNpbg+6pQE/GgP"], [5, 63.03996276855469, 29.2], [0, 0, 0.5]], -237, [15, -238, [0, "bbGS2iYHZK7q/rJe7wia1d"]]], 4, 1, 4], [1, "0fPtzVMI9J/ariQ82Y4nqq", null, null, null, 1, 0], [1, 236.048, 0, 0]], [19, "rankNum", 2, [[[2, -239, [0, "4fS1zE9CBGd5pf0T0jVi3+"], [5, 13.827987670898438, 29.2]], -240, [15, -241, [0, "d41OAuP9NIwZAG+iQY5PhM"]]], 4, 1, 4], [1, "28T4LZLgVLpYh+w3iyXtC4", null, null, null, 1, 0], [1, -286, 0, 0]], [10, 0, {}, 2, [13, "c46/YsCPVOJYA4mWEpNYRx", null, null, -246, [12, "448pgoUYBIl43SQYjtnGrk", 1, [[5, "UIPlayerHead", ["_name"], -242], [3, ["_lpos"], -243, [1, -192.61, 0, 0]], [3, ["_lrot"], -244, [3, 0, 0, 0, 1]], [3, ["_euler"], -245, [1, 0, 0, 0]]]], 47]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [4, ["48QgggtnpApZxwpAfGYRjg"]], [45, "emptyTips", false, 3, [[2, -247, [0, "94ZTIRAOlGR6jmZN4Xdsat"], [5, 191.9759979248047, 25.2]], [64, "暂无排行榜的记录", 24, 24, 20, false, false, 1, -248, [0, "c3t+X9appJF4CGU7aEHj6t"], [4, 4281219932], 50]], [1, "2eRwd7eiZFh76AetPEgGWX", null, null, null, 1, 0], [1, 0, -244.85900000000004, 0]], [10, 0, {}, 1, [50, "c46/YsCPVOJYA4mWEpNYRx", null, -254, [12, "ebLawlWp1PrLXQvSe/ae1x", 1, [[23, "MoneyTop", ["_name"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]]], [21, ["_lpos"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0, 667, 0]], [21, ["_lrot"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [3, 0, 0, 0, 1]], [21, ["_euler"], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0, 0, 0]], [61, ["_target"], [4, ["ddihhl6PBHtqLiNxmV/xVz"]], -253], [21, ["_lpos"], [4, ["1cr7+5n01NiZXt1g9RZn1n"]], [1, 0, 0, 0]], [21, ["_contentSize"], [4, ["bd6qLlulFD0YdEI3cBk5Ko"]], [5, 517.242, 72]]]], [[32, ["redNode"], -250, [4, ["8fN27e90VKwrOm0UkkXWci"]], -249, [4, ["75lOaODvdAG7/FKF89g08B"]]], [32, ["redNode"], -252, [4, ["8fN27e90VKwrOm0UkkXWci"]], -251, [4, ["75lOaODvdAG7/FKF89g08B"]]]], 52]], [67, 1, [0, "1a0QgQzrFIlrn9CSagcyNO"], [53, 54, 55, 56, 57, 58, 59], 62, [-255, -256, -257]], [6, "bg1", 3, [[2, -258, [0, "779ijzldFCiZOmDzx7b6+o"], [5, 750, 1090]], [9, -259, [0, "5cmjJ36s1J2ohJQMv+Z62V"], 1]], [1, "e84N63Lu5J3Zmt1zQJ1DxC", null, null, null, 1, 0], [1, 0, -22, 0]], [6, "titleBg", 3, [[2, -260, [0, "44Ka+Qi9JDILn0zGNaT9od"], [5, 610, 108]], [9, -261, [0, "d9TsAxdeRCc60/P18wgaEn"], 2]], [1, "59FEXGqhlHSb78+uEzL2/s", null, null, null, 1, 0], [1, 0, 528.873, 0]], [40, "bar", 33554432, 13, [[[11, -262, [0, "edwxCk8jZBmYGW/x2VvDu1"], [5, 10, 156.25], [0, 0, 0]], -263], 4, 1], [1, "d4Ua5YvVJImJ8QphMpb4fv", null, null, null, 1, 0], [1, -11, -31.25, 0]], [6, "txt", 6, [[2, -264, [0, "6a5uHW6BRMGK4HB2AyZz9i"], [5, 52.02398681640625, 35.5]], [7, "战士", 24, 24, 25, false, false, 1, true, -265, [0, "233PhsMXhJN6j557R+IV6J"], 5]], [1, "94ZR+yCrBEJ4ZnThVhXAGa", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 39, [[2, -266, [0, "99w8uCHxNDoqIcx+694tJ/"], [5, 52.02398681640625, 35.5]], [7, "战士", 24, 24, 25, false, false, 1, true, -267, [0, "85U+kCTvlAbI+vbCMMgwTM"], 6]], [1, "0f7C34dm9G8q3xgYOceI+t", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 7, [[2, -268, [0, "08Kuoc2e9Hq4TtELVMsa0k"], [5, 52, 35.5]], [7, "法师", 24, 24, 25, false, false, 1, true, -269, [0, "1cMUOEMD9Pcrbfk25uetTA"], 8]], [1, "80fFAW69ZGL6VMCBoHP1PC", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 40, [[2, -270, [0, "0aSXH5PuJLsLKbufKKPkCV"], [5, 52, 35.5]], [7, "法师", 24, 24, 25, false, false, 1, true, -271, [0, "db6zXdPhNIobILIDe1Sr3v"], 9]], [1, "e4idlDSnVLF5XRjaiUST5p", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 8, [[2, -272, [0, "8fKZT7bXRDEK/GAKnwb587"], [5, 52.**************, 35.5]], [7, "牧师", 24, 24, 25, false, false, 1, true, -273, [0, "97QBFLvolI7YbvGmsFr6HE"], 11]], [1, "60eIez2MFONIXlL7nMOy/H", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 41, [[2, -274, [0, "92+sunNelIiLMzc/46SAAZ"], [5, 52.**************, 35.5]], [7, "牧师", 24, 24, 25, false, false, 1, true, -275, [0, "17iUT1LydObp8f/iisQckQ"], 12]], [1, "ce7xqlSOJL1KJw7IUlklT1", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 9, [[2, -276, [0, "22EFiFxNFKFoZGxznklPse"], [5, 52, 35.5]], [7, "盗贼", 24, 24, 25, false, false, 1, true, -277, [0, "3d99s6Z4FOpZwbjIpvGesW"], 14]], [1, "c6ArTTt9lPdarheYxUXbK6", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 42, [[2, -278, [0, "f7VN0h80xJLJVD4JBvRZAU"], [5, 52, 35.5]], [7, "盗贼", 24, 24, 25, false, false, 1, true, -279, [0, "d6053wV3FA4Ks4XB7mqQtL"], 15]], [1, "abwPDunOJDibVw62wYhkF0", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 10, [[2, -280, [0, "3bg1d8QgNEFIOkCrGu6v3b"], [5, 51.**************, 35.5]], [7, "猎人", 24, 24, 25, false, false, 1, true, -281, [0, "a5pW6evYxJrIu24lBAnKLs"], 17]], [1, "feW6Uuh/FFIa2YhQ9yydOP", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 43, [[2, -282, [0, "1aVYEaM3BE1Z7zc/m1H/mJ"], [5, 51.**************, 35.5]], [7, "猎人", 24, 24, 25, false, false, 1, true, -283, [0, "dcy+0OmmFDxYVNFieEsFrT"], 18]], [1, "00ipB/cuFIuq7traOsFh99", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 11, [[2, -284, [0, "84Rp6kUz9PhaFhVpJkxQTV"], [5, 75.**************, 35.5]], [7, "圣骑士", 24, 24, 25, false, false, 1, true, -285, [0, "4eKjsXkzlP6Z/kSyYAY6yp"], 20]], [1, "62aTnydDtNyp7I0KvZIrf3", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 44, [[2, -286, [0, "7fnkqCv8pPI7TuvOkVQOH8"], [5, 51.**************, 35.5]], [7, "圣骑士", 24, 24, 25, false, false, 1, true, -287, [0, "39lm3emihMAqnxKIhLBD2r"], 21]], [1, "7dKqo7VUVO5YTfNJg5rYa8", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 12, [[2, -288, [0, "cdq0Drxw1A25lQyuJn1wFX"], [5, 52.**************, 35.5]], [7, "萨满", 24, 24, 25, false, false, 1, true, -289, [0, "44ui5TnHNB54sVS1eKf2s6"], 23]], [1, "4bS7fvUoZJX4t77nYfZMiY", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "txt", 45, [[2, -290, [0, "acKOY09lRM3q17Ymp1d7Pg"], [5, 51.**************, 35.5]], [7, "萨满", 24, 24, 25, false, false, 1, true, -291, [0, "0anWKwN/lEaaxUKmOrnwSv"], 24]], [1, "136GJi6iRLQJxXar6hYwXr", null, null, null, 1, 0], [1, 0, 1.641, 0]], [6, "flag", 49, [[2, -292, [0, "78Zv8hqcNCvaiuSwcOhfOX"], [5, 36, 44]], [9, -293, [0, "85juGYFMxF3Y9i4AmtOGZq"], 29]], [1, "e0gGyYQgpPbb6H/lIpk65K", null, null, null, 1, 0], [1, 0, -31, 0]], [6, "flag", 50, [[2, -294, [0, "baZ6ccBlZBX6uwyHI6p3J9"], [5, 51, 43]], [9, -295, [0, "b0T8FqgEJPnb+lyGbsG5Ef"], 31]], [1, "e1aWd/6FlBh50T3AWYC4xl", null, null, null, 1, 0], [1, 0, -31, 0]], [6, "flag", 51, [[2, -296, [0, "63Edf3jnxMhqCxramIt89M"], [5, 61, 47]], [9, -297, [0, "aei/QCM9lMRq0x9E82mld4"], 33]], [1, "32QnCbdcdAt4YQaUVRQ6kB", null, null, null, 1, 0], [1, 0, -47, 0]], [6, "paihangbang_taizi_04", 28, [[2, -298, [0, "26ng0Isa1AVbtuj8H9tRRO"], [5, 191, 30]], [9, -299, [0, "d9Y3GBQeBM6ryIqGvyZRjC"], 38]], [1, "2b6NgUtbVKYKquyW1ovzR6", null, null, null, 1, 0], [1, 0, -48, 0]], [10, 0, {}, 28, [13, "f3OdqDoPhD0aP0W/ojHTpT", null, null, -306, [12, "dd/xj3jvxKSJ5R5vYKwq7v", 1, [[5, "lay_name", ["_name"], 29], [3, ["_lpos"], 29, [1, 0, -48, 0]], [3, ["_lrot"], 29, [3, 0, 0, 0, 1]], [3, ["_euler"], 29, [1, 0, 0, 0]], [3, ["_contentSize"], -300, [5, 128, 28]], [5, true, ["_active"], 29], [5, 22, ["_fontSize"], 15], [3, ["_contentSize"], -301, [5, 4, 35.5]], [5, 22, ["_actualFontSize"], 15], [5, true, ["_enableOutline"], 15], [3, ["_anchorPoint"], -302, [0, 0.5, 0.5]], [3, ["_anchorPoint"], -303, [0, 0.5, 0.5]], [3, ["_lpos"], -304, [1, 0, 0, 0]], [23, 50, ["speed"], [4, ["50honeHvFFa4nnnP2rT90g"]]], [5, 25, ["_lineHeight"], 15], [5, "", ["_string"], 15], [5, "lbName", ["_name"], -305], [5, false, ["_enableWrapText"], 15]]], 39]], [6, "paihangbang_taizi_04", 30, [[2, -307, [0, "e99/2uj35Kb4uYbzgnwQ2l"], [5, 191, 30]], [9, -308, [0, "d9TK4l5cBHZZv47pA4gvXF"], 40]], [1, "22eTKgD71ILIkwHXwON68r", null, null, null, 1, 0], [1, 0, -52, 0]], [10, 0, {}, 30, [13, "f3OdqDoPhD0aP0W/ojHTpT", null, null, -315, [12, "ccd/04TDBKHaLIXoR3jyh+", 1, [[5, "lay_name", ["_name"], 31], [3, ["_lpos"], 31, [1, 0, -51.38199999999995, 0]], [3, ["_lrot"], 31, [3, 0, 0, 0, 1]], [3, ["_euler"], 31, [1, 0, 0, 0]], [3, ["_contentSize"], -309, [5, 128, 28]], [5, true, ["_active"], 31], [5, 22, ["_fontSize"], 16], [3, ["_contentSize"], -310, [5, 4, 35.5]], [5, 22, ["_actualFontSize"], 16], [5, true, ["_enableOutline"], 16], [3, ["_anchorPoint"], -311, [0, 0.5, 0.5]], [3, ["_anchorPoint"], -312, [0, 0.5, 0.5]], [3, ["_lpos"], -313, [1, 0, 0, 0]], [23, 50, ["speed"], [4, ["50honeHvFFa4nnnP2rT90g"]]], [5, 25, ["_lineHeight"], 16], [5, "", ["_string"], 16], [5, "lbName", ["_name"], -314], [5, false, ["_enableWrapText"], 16]]], 41]], [6, "paihangbang_taizi_04", 32, [[2, -316, [0, "8bfreuv5ZK3rG+keFVRJeD"], [5, 191, 30]], [9, -317, [0, "e9xubpMXpH4roZWeYR+7EN"], 42]], [1, "ccw/EZxwBHJ4nz4F4l0xAg", null, null, null, 1, 0], [1, 0, -52, 0]], [10, 0, {}, 32, [13, "f3OdqDoPhD0aP0W/ojHTpT", null, null, -324, [12, "7fRwA6WY1CvK0izlJ30YTA", 1, [[5, "lay_name", ["_name"], 33], [3, ["_lpos"], 33, [1, 0, -52.04399999999998, 0]], [3, ["_lrot"], 33, [3, 0, 0, 0, 1]], [3, ["_euler"], 33, [1, 0, 0, 0]], [3, ["_contentSize"], -318, [5, 128, 28]], [5, true, ["_active"], 33], [5, 22, ["_fontSize"], 17], [3, ["_contentSize"], -319, [5, 4, 35.5]], [5, 22, ["_actualFontSize"], 17], [5, true, ["_enableOutline"], 17], [3, ["_anchorPoint"], -320, [0, 0.5, 0.5]], [3, ["_anchorPoint"], -321, [0, 0.5, 0.5]], [3, ["_lpos"], -322, [1, 0, 0, 0]], [23, 50, ["speed"], [4, ["50honeHvFFa4nnnP2rT90g"]]], [5, 25, ["_lineHeight"], 17], [5, "", ["_string"], 17], [5, "lbName", ["_name"], -323], [5, false, ["_enableWrapText"], 17]]], 43]], [19, "talentIcon", 2, [[[2, -325, [0, "9fGXIb8vhAm7XrEa/sp+Tu"], [5, 45, 45]], -326], 4, 1], [1, "1bP+sHLFBHorYY3BMVsJfa", null, null, null, 1, 0], [1, 50.967, 3.0205, 0]], [41, "junxianIcon", 2, [[[2, -327, [0, "cbiqQgUl1LaJqmET6eON20"], [5, 45, 45]], -328], 4, 1], [1, "170eGqLVFBf5oYBTg1hT5J", null, null, null, 1, 0], [1, -138.94799999999998, 0, 0], [1, 0.5, 0.5, 1]], [6, "fight", 2, [[2, -329, [0, "32zWH/WM1HobxM/hLlPbyJ"], [5, 33, 31]], [9, -330, [0, "edgRPDlVhJDJawh0/A8pqs"], 46]], [1, "5fdd1dpv1DW7RBMKJyQcXR", null, null, null, 1, 0], [1, 209.798, 0, 0]], [19, "rankIcon", 2, [[[2, -331, [0, "a5vlgPB0ZGuZkI9uzqxqt1"], [5, 61, 47]], -332], 4, 1], [1, "1cUe8jeZ5JLJgwlSUxJlEW", null, null, null, 1, 0], [1, -286, 0, 0]], [68, 2, [0, "742k6oAHBAkYYU9K1jS65H"]], [42, "nick<PERSON><PERSON>", 36, [[[2, -333, [0, "e14Nw3xydFfZs/veAC2hiw"], [5, 124.07199096679688, 36.76]], -334, [15, -335, [0, "3e7CdSkyVF/r9YO2NKM1CF"]]], 4, 1, 4], [1, "c9GxCfzpVPgo+rXr5xuUkk", null, null, null, 1, 0]], [10, 0, {}, 3, [13, "48QgggtnpApZxwpAfGYRjg", null, null, -339, [12, "bauDma8sZCI7eUokkhHgiR", 1, [[5, "MyScrollBar", ["_name"], 61], [3, ["_lpos"], 61, [1, 355.985, -299, 0]], [3, ["_lrot"], 61, [3, 0, 0, 0, 1]], [3, ["_euler"], 61, [1, 0, 0, 0]], [21, ["_contentSize"], [4, ["ae8XjF0uhCJI6F8w5MXn7m"]], [5, 6, 450]], [62, ["scrollView"], -337, -336], [5, false, ["autoHide"], -338]]], 49]], [55, 1, 0, 67, [0, "6ddjjavtpLIqsI5K5ZPgJ7"]], [69, 1, 13, [0, "56/s7xBudFBpPQCjEht3qg"], 98], [70, 0.23, 0.75, false, 22, [0, "19DI/Ms4pAcJ8y6A+zR0el"], 23, 99], [16, 0, 39, [0, "e8JlzQpxdPMI/f21YxfUyW"]], [16, 2, 40, [0, "eeS5Bs7ctJbqZ64zNyHhhL"]], [16, 2, 41, [0, "951vpOzYpKfr6+6IifmFkM"]], [16, 2, 42, [0, "e9nB9DXIZLgIRIgdy2WKt6"]], [16, 2, 43, [0, "0c3SBpU1FAvKW1H92PXa9F"]], [16, 2, 44, [0, "a1HEzWjfdPbbp2uBnCcP9x"]], [16, 2, 45, [0, "4c7N9IsFxOxoxNBs4LaL25"]], [72, 4, [0, "0atuQoS89GWr2sCwjhBtrM"]], [24, "0", 22, 22, 25, false, false, true, 48, [0, "34TTg/AwlNEoShI6EzvYqb"]], [33, 0, false, 5, [0, "e2ofmUqnJDWJB4Kz2g1tEi"]], [4, ["a3SifehbVO+JKTEXbDDYAn"]], [4, ["e14Nw3xydFfZs/veAC2hiw"]], [4, ["c9GxCfzpVPgo+rXr5xuUkk"]], [26, false, 28, [0, "af9U4Yc4RL4rR85VoucRNS"]], [4, ["a3SifehbVO+JKTEXbDDYAn"]], [4, ["e14Nw3xydFfZs/veAC2hiw"]], [4, ["c9GxCfzpVPgo+rXr5xuUkk"]], [26, false, 30, [0, "97BDXCORxJ5bGrFsD4tZkC"]], [4, ["a3SifehbVO+JKTEXbDDYAn"]], [4, ["e14Nw3xydFfZs/veAC2hiw"]], [4, ["c9GxCfzpVPgo+rXr5xuUkk"]], [26, false, 32, [0, "3fk3UFmU5KdZ0wvkN+k9Sh"]], [33, 0, false, 91, [0, "30Whh00zJL24u6IuJQuexm"]], [26, false, 92, [0, "d5knkkU2dHvpcIkhL0HznA"]], [4, ["a3SifehbVO+JKTEXbDDYAn"]], [4, ["e14Nw3xydFfZs/veAC2hiw"]], [4, ["c9GxCfzpVPgo+rXr5xuUkk"]], [24, "Lv  20", 24, 24, 20, false, false, true, 56, [0, "97v8eHRP1OtbdhtiEJCSaP"]], [24, "1000", 24, 24, 20, false, false, true, 57, [0, "32VmbBZnVOT4dI9rjdtHUS"]], [56, 94, [0, "5ae46BAStEQaxOdmyeis8O"]], [24, "1", 26, 26, 20, false, false, true, 58, [0, "2b0/v9YHpBYL2HU1n8ZtUe"]], [24, "五字角色名", 24, 24, 26, false, false, true, 96, [0, "edJWUpoHFEIYwtOPt+zzCQ"]], [71, 0.23, 0.75, false, 18, [0, "28AT0VDZ1NBZ2A9bsd06ke"], 35], [73, 18, [0, "c5YPp6b2lJjoC6VApdqGM5"], 2, [66]], [4, ["2dQ2wXCkFJzY5oPQvWqcNX"]], [28, "New Node"], [28, "New Node"]], 0, [0, -1, 63, 0, -2, 97, 0, -3, 59, 0, -4, 36, 0, -5, 90, 0, -6, 88, 0, -7, 86, 0, -8, 54, 0, -9, 53, 0, -10, 52, 0, 8, 54, 0, 9, 64, 0, 8, 53, 0, 9, 64, 0, 8, 52, 0, 9, 64, 0, 8, 59, 0, 9, 95, 0, 8, 36, 0, 9, 95, 0, 5, 1, 0, 0, 1, 0, 0, 1, 0, -3, 64, 0, -1, 21, 0, -2, 3, 0, -3, 20, 0, -4, 63, 0, 0, 2, 0, -2, 95, 0, -1, 55, 0, -2, 91, 0, -3, 92, 0, -4, 36, 0, -5, 56, 0, -6, 93, 0, -7, 57, 0, -8, 94, 0, -9, 58, 0, -10, 59, 0, 0, 3, 0, 0, 3, 0, -1, 65, 0, -2, 66, 0, -3, 38, 0, -4, 22, 0, -5, 5, 0, -6, 28, 0, -7, 30, 0, -8, 32, 0, -9, 18, 0, -10, 97, 0, -11, 62, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -4, 108, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, 0, 5, 0, -2, 110, 0, -1, 14, 0, -2, 49, 0, -3, 50, 0, -4, 51, 0, -5, 52, 0, -6, 53, 0, -7, 54, 0, 0, 6, 0, 0, 6, 0, 10, 101, 0, 7, 6, 0, 0, 6, 0, -1, 68, 0, -2, 39, 0, 0, 7, 0, 0, 7, 0, 10, 102, 0, 7, 7, 0, 0, 7, 0, -1, 70, 0, -2, 40, 0, 0, 8, 0, 0, 8, 0, 10, 103, 0, 7, 8, 0, 0, 8, 0, -1, 72, 0, -2, 41, 0, 0, 9, 0, 0, 9, 0, 10, 104, 0, 7, 9, 0, 0, 9, 0, -1, 74, 0, -2, 42, 0, 0, 10, 0, 0, 10, 0, 10, 105, 0, 7, 10, 0, 0, 10, 0, -1, 76, 0, -2, 43, 0, 0, 11, 0, 0, 11, 0, 10, 106, 0, 7, 11, 0, 0, 11, 0, -1, 78, 0, -2, 44, 0, 0, 12, 0, 0, 12, 0, 10, 107, 0, 7, 12, 0, 0, 12, 0, -1, 80, 0, -2, 45, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -4, 99, 0, -1, 67, 0, 0, 14, 0, 0, 14, 0, -1, 46, 0, -2, 47, 0, -3, 48, 0, 0, 18, 0, -2, 133, 0, -3, 134, 0, 0, 18, 0, -1, 34, 0, 0, 20, 0, 0, 20, 0, 7, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -2, 100, 0, -2, 24, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 28, 0, -2, 114, 0, -1, 85, 0, -2, 86, 0, 0, 30, 0, -2, 118, 0, -1, 87, 0, -2, 88, 0, 0, 32, 0, -2, 122, 0, -1, 89, 0, -2, 90, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, -1, 35, 0, 0, 35, 0, 0, 35, 0, 1, 37, 0, 1, 37, 0, 1, 37, 0, 1, 37, 0, 1, 125, 0, 1, 37, 0, 1, 126, 0, 1, 125, 0, 1, 126, 0, 1, 127, 0, 1, 127, 0, 5, 36, 0, 0, 38, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, -2, 101, 0, -1, 69, 0, 0, 40, 0, -2, 102, 0, -1, 71, 0, 0, 41, 0, -2, 103, 0, -1, 73, 0, 0, 42, 0, -2, 104, 0, -1, 75, 0, 0, 43, 0, -2, 105, 0, -1, 77, 0, 0, 44, 0, -2, 106, 0, -1, 79, 0, 0, 45, 0, -2, 107, 0, -1, 81, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, -2, 109, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, -1, 82, 0, 0, 50, 0, 0, 50, 0, -1, 83, 0, 0, 51, 0, 0, 51, 0, -1, 84, 0, 5, 52, 0, 5, 53, 0, 5, 54, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, -2, 128, 0, 0, 56, 0, 0, 57, 0, -2, 129, 0, 0, 57, 0, 0, 58, 0, -2, 131, 0, 0, 58, 0, 1, 60, 0, 1, 60, 0, 1, 60, 0, 1, 60, 0, 5, 59, 0, 0, 62, 0, 0, 62, 0, 8, 136, 0, 9, 136, 0, 8, 137, 0, 9, 137, 0, 12, 63, 0, 5, 63, 0, -1, 114, 0, -2, 118, 0, -3, 122, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, -2, 98, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 1, 111, 0, 1, 112, 0, 1, 111, 0, 1, 112, 0, 1, 113, 0, 1, 113, 0, 5, 86, 0, 0, 87, 0, 0, 87, 0, 1, 115, 0, 1, 116, 0, 1, 115, 0, 1, 116, 0, 1, 117, 0, 1, 117, 0, 5, 88, 0, 0, 89, 0, 0, 89, 0, 1, 119, 0, 1, 120, 0, 1, 119, 0, 1, 120, 0, 1, 121, 0, 1, 121, 0, 5, 90, 0, 0, 91, 0, -2, 123, 0, 0, 92, 0, -2, 124, 0, 0, 93, 0, 0, 93, 0, 0, 94, 0, -2, 130, 0, 0, 96, 0, -2, 132, 0, 0, 96, 0, 12, 133, 0, 1, 135, 0, 1, 135, 0, 5, 97, 0, 13, 1, 2, 11, 35, 4, 11, 23, 13, 11, 22, 23, 11, 24, 64, 14, 109, 64, 15, 110, 64, 16, 134, 64, 17, 108, 95, 18, 131, 95, 19, 130, 95, 20, 129, 95, 21, 128, 95, 22, 132, 95, 23, 123, 95, 24, 124, 99, 25, 100, 339], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 98, 101, 102, 103, 104, 105, 106, 107, 109, 110, 128, 129, 130, 130, 131, 132], [2, 2, 2, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 3, 3, 2, 2, 2, 2, 2, 2, 2, 6, 6, 6, 2, 6, 2, 6, 2, 6, 2, 6, 2, 6, 2, 6, 3, 2, 6, -1, -2, -3, -4, -5, -6, -7, 2, 2, 2, 2, 2, 2, 2, 2, 3, 2, 3, 3, 2, 26, 3, 3], [12, 13, 14, 0, 15, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 16, 17, 7, 18, 7, 8, 19, 5, 5, 5, 6, 4, 6, 4, 6, 4, 20, 4, 21, 22, 23, 24, 0, 25, 26, 9, 27, 28, 29, 30, 31, 32, 33, 3, 3, 3, 3, 3, 3, 3, 0, 9, 0, 0, 8, 34, 0, 0]], [[{"name": "rank", "spriteFrames": ["paihangbang_paiming_01", "89FnbzNR9DvKjyDKgpIEsk@1eaa7", "paihangbang_paiming_02", "89FnbzNR9DvKjyDKgpIEsk@9f348", "paihangbang_paiming_03", "89FnbzNR9DvKjyDKgpIEsk@4677b", "paihangbang_taizi_01", "89FnbzNR9DvKjyDKgpIEsk@bedd7", "paihangbang_taizi_02", "89FnbzNR9DvKjyDKgpIEsk@819c8", "paihangbang_taizi_03", "89FnbzNR9DvKjyDKgpIEsk@b941b", "paihangbang_taizi_04", "89FnbzNR9DvKjyDKgpIEsk@47773", "paihangbang_zhanli_icon_01", "89FnbzNR9DvKjyDKgpIEsk@1212f"]}], [13], 0, [], [], []], [[{"name": "paihangbang_zhanli_icon_01", "rect": {"x": 168, "y": 313, "width": 33, "height": 31}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 33, "height": 31}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "paihangbang_paiming_01", "rect": {"x": 168, "y": 202, "width": 61, "height": 47}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 61, "height": 47}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "paihangbang_paiming_03", "rect": {"x": 168, "y": 266, "width": 36, "height": 44}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 36, "height": 44}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "paihangbang_taizi_04", "rect": {"x": 217, "y": 2, "width": 191, "height": 30}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 191, "height": 30}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "paihangbang_taizi_02", "rect": {"x": 2, "y": 148, "width": 163, "height": 103}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 163, "height": 103}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "paihangbang_paiming_02", "rect": {"x": 168, "y": 148, "width": 51, "height": 43}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 51, "height": 43}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "paihangbang_taizi_03", "rect": {"x": 2, "y": 254, "width": 163, "height": 103}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 163, "height": 103}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "paihangbang_taizi_01", "rect": {"x": 2, "y": 2, "width": 212, "height": 143}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 212, "height": 143}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "p<PERSON><PERSON><PERSON>_zhi<PERSON>_sheng<PERSON><PERSON>i_bg", "rect": {"x": 0, "y": 0, "width": 719, "height": 364}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 719, "height": 364}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-359.5, -182, 0, 359.5, -182, 0, -359.5, 182, 0, 359.5, 182, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 364, 719, 364, 0, 0, 719, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -359.5, "y": -182, "z": 0}, "maxPos": {"x": 359.5, "y": 182, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [35]], [[{"name": "p<PERSON><PERSON><PERSON>_zhiye_mushi_bg", "rect": {"x": 0, "y": 0, "width": 719, "height": 364}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 719, "height": 364}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-359.5, -182, 0, 359.5, -182, 0, -359.5, 182, 0, 359.5, 182, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 364, 719, 364, 0, 0, 719, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -359.5, "y": -182, "z": 0}, "maxPos": {"x": 359.5, "y": 182, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [36]], [[{"name": "pai<PERSON><PERSON>_zhiye_saman_bg", "rect": {"x": 0, "y": 0, "width": 719, "height": 364}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 719, "height": 364}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-359.5, -182, 0, 359.5, -182, 0, -359.5, 182, 0, 359.5, 182, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 364, 719, 364, 0, 0, 719, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -359.5, "y": -182, "z": 0}, "maxPos": {"x": 359.5, "y": 182, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [37]], [[{"name": "pai<PERSON><PERSON>_zhiye_fashi_bg", "rect": {"x": 0, "y": 0, "width": 719, "height": 364}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 719, "height": 364}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-359.5, -182, 0, 359.5, -182, 0, -359.5, 182, 0, 359.5, 182, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 364, 719, 364, 0, 0, 719, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -359.5, "y": -182, "z": 0}, "maxPos": {"x": 359.5, "y": 182, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [38]]]]