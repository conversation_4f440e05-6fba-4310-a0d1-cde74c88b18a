[1, ["b5fXDcze5FXrMfVmVDTG7q@6c48a", "42YlG8C+VFQIQhi/uTnF/K", "61NXJOyyZNU6ZGh0FJkMv2", "2enw6dg1JAa5xhWIDgaiWv", "2enw6dg1JAa5xhWIDgaiWv@0c02c", "8fOvUyIEhI+YYSThw1sq2H", "2enw6dg1JAa5xhWIDgaiWv@f3339", "2enw6dg1JAa5xhWIDgaiWv@c70d0", "41Cf1vi2BOm4J8zvJo4tGH", "41Cf1vi2BOm4J8zvJo4tGH@b6f21", "2enw6dg1JAa5xhWIDgaiWv@4a0f4", "2enw6dg1JAa5xhWIDgaiWv@a0708", "65nnJ3SlJOoYRwNWpKye8V@f9941", "2enw6dg1JAa5xhWIDgaiWv@60b8b"], ["_textureSource", "node", "_font", "_spriteFrame", "targetInfo", "_target", "_parent", "_userDefinedFont", "root", "_atlas", "target", "source", "data", "asset", "_imageAtlas"], ["cc.SpriteFrame", ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingY", "_paddingTop", "_paddingBottom", "_spacingX", "_affectedByScale", "_isAlign", "node", "__prefab"], -5, 1, 4], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_cacheMode", "_outlineWidth", "_verticalAlign", "_horizontalAlign", "node", "__prefab", "_font", "_color"], -8, 1, 4, 6, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_atlas"], 0, 1, 4, 6, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_lpos", "_parent", "_children"], 1, 12, 4, 5, 1, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_isSystemFontUsed", "_maxWidth", "_handleTouchEvent", "_verticalAlign", "node", "__prefab", "_font", "_userDefinedFont", "_imageAtlas"], -4, 1, 4, 6, 6, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_parent"], 0, 4, 2, 2, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "clickEvents", "_normalColor", "_target"], 2, 1, 4, 9, 5, 1], "cc.SpriteAtlas", ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo"], 2, 1, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["2b852xrmS9HTZ4ufS6ZJDUB", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["06927HtDK9NpZs1itvSz4RY", ["viewType", "showType", "node", "__prefab"], 1, 1, 4], ["e2003Z4Up1O1r6nEUSwTtry", ["node", "__prefab", "txt_xhlabel", "icon_btn", "img_icon", "item_num", "iconTipsComp"], 3, 1, 4, 1, 1, 1, 1, 1], ["126fa/qenlHdIPcGna3h2uY", ["node", "__prefab", "txtName", "useLv", "have<PERSON>um", "descNode", "txtDesc", "btnNode", "panelUITr", "mainType", "btnPreview", "node_cang", "cang_star", "itemcost", "fuWenNode"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[17, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [6, 0, 1, 2, 1], [20, 0, 1, 1], [6, 0, 1, 2, 3, 1], [2, 0, 4, 5, 2, 3, 6, 2], [2, 0, 4, 2, 3, 6, 2], [5, 0, 5, 2, 3, 4, 2], [3, 0, 9, 1, 2, 3, 4, 5, 6, 11, 12, 14, 9], [3, 0, 1, 2, 3, 4, 5, 7, 6, 11, 12, 13, 9], [16, 0, 2], [9, 0, 1, 2, 3, 4, 5, 2], [19, 0, 1, 2, 3], [24, 0, 1, 2, 2], [2, 0, 4, 2, 3, 2], [5, 0, 1, 5, 2, 3, 4, 3], [1, 0, 1, 8, 9, 3], [4, 3, 4, 5, 1], [4, 0, 2, 3, 4, 5, 6, 3], [4, 0, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 7, 6, 8, 11, 12, 13, 10], [11, 0, 2], [8, 0, 4, 5, 3, 2], [8, 1, 2, 6, 3, 3], [2, 0, 5, 2, 3, 6, 2], [2, 0, 4, 5, 2, 3, 2], [2, 0, 1, 4, 2, 3, 6, 3], [2, 0, 5, 2, 3, 2], [5, 0, 6, 2, 3, 4, 2], [12, 0, 1, 2, 3, 4, 5, 3], [14, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 3, 2], [6, 0, 1, 1], [1, 0, 1, 3, 4, 2, 8, 9, 6], [1, 0, 1, 5, 6, 8, 9, 5], [1, 0, 1, 2, 6, 8, 9, 5], [1, 0, 1, 2, 8, 9, 4], [1, 0, 1, 5, 8, 9, 4], [1, 0, 1, 3, 4, 8, 9, 5], [1, 0, 1, 2, 7, 8, 9, 5], [4, 1, 0, 3, 4, 5, 3], [4, 1, 0, 3, 4, 3], [18, 0, 1, 1], [9, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 6, 8, 11, 12, 14, 13, 9], [3, 0, 10, 9, 1, 2, 3, 4, 5, 6, 11, 12, 14, 13, 10], [3, 0, 1, 2, 3, 4, 5, 7, 6, 11, 12, 9], [21, 0, 1, 1], [22, 0, 1, 2, 2], [23, 0, 1, 2, 3], [25, 0, 1, 2, 3], [7, 0, 1, 6, 2, 4, 3, 5, 7, 8, 9, 10, 11, 8], [7, 0, 1, 2, 3, 7, 8, 5], [7, 0, 1, 2, 4, 3, 5, 7, 8, 7], [26, 0, 1, 2, 3, 3], [27, 0, 1, 2, 3, 4, 5, 6, 1], [28, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1]], [[[[21, "ItemTipsUI"], [22, "ItemTipsUI", [-7], [-5, -6], [29, "c46/YsCPVOJYA4mWEpNYRx", null, -4, 0, [[31, ["itemCell"], -3, -2, [10, ["1f5RzKwa5EaIr2SaOeKvoI"]]]], [-1]]], [24, "itemInfo", [-9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19], [[2, -8, [0, "13DMAqkNpMsLWcQS1Q3AMt"], [5, 400, 124.542]]], [1, "e1WqbbcNpKwY506e6A1Gnl", null, null, null, 1, 0], [1, 0, 91.71999999999998, 0]], [25, "node", 1, [2, -24, -25], [[2, -20, [0, "28XXvC+ZBDpKpCPRshPPLv"], [5, 447, 347.98199999999997]], [33, 1, 2, 20, 20, 5, -21, [0, "79PODI76hBLqmlcRtFV6Ij"]], [40, 1, 0, -22, [0, "6cPzOUmT5OFKuJUvljCMxh"], 20], [42, -23, [0, "00icL9OkdF+Lmvw2a4bQeE"]]], [1, "50dWVwEqxCZbvavX+rsnEI", null, null, null, 1, 0]], [28, "ItemConsume", [-29, -30, -31], [[[2, -26, [0, "29rcQFI9lJoKExotG4qm/M"], [5, 39, 17.64]], [34, 1, 1, 5, true, -27, [0, "92Ps+WJsBDSZ+P8jZpbVVz"]], -28], 4, 4, 1], [1, "a30XEvwz9Gv52gpXYSSBIH", null, null, null, 1, 0], [1, -241.18, -12.083999999999946, 0]], [5, "descNode", 3, [-34, -35, -36], [[4, -32, [0, "f5JNZnRMVOApAzlmufGK8T"], [5, 395, 93.44], [0, 0.5, 1]], [35, 1, 2, 10, true, -33, [0, "5dux1VkJVGTKrHt+1QvxbX"]]], [1, "36v0/svUJFCZ++3V6jrHYr", null, null, null, 1, 0], [1, 0, 24.448999999999984, 0]], [6, "btn_preview", 2, [[2, -37, [0, "5fUhwxILpH66tkFZJRpzlF"], [5, 29, 29]], [17, -38, [0, "caCyolBvhGTatzFdYqH0H5"], 5], [11, 3, -40, [0, "14dLjfDANP7quTsxtUBzoA"], [[12, "126fa/qenlHdIPcGna3h2uY", "onClick", 1]], [4, 4292269782], -39]], [1, "dbw0a95SZM3Z2OqSKJPZSR", null, null, null, 1, 0], [1, 191, -157.44500000000005, 0]], [5, "node_cang", 2, [-42, -43, 4], [[2, -41, [0, "f4Ih5yVyVI560D9l7gyAcJ"], [5, 85.01799011230469, 30]]], [1, "92cvWVgmhCwqMGwrTmHHem", null, null, null, 1, 0], [1, 92.18, -31.776000000000067, 0]], [5, "fuWen", 5, [-46, -47], [[2, -44, [0, "edzu5boSlM9ZqpwsBr/eDV"], [5, 395, 55.72]], [36, 1, 2, 6, -45, [0, "c8CfLrJttPdJelvXEcsPD3"]]], [1, "21XmFkO/RFNrZv4gDnaUxk", null, null, null, 1, 0], [1, 0, -65.58, 0]], [5, "btnNode", 3, [-50, -51], [[2, -48, [0, "9125oZNEdNk7GO9hPn8ZFP"], [5, 322, 80]], [37, 1, 1, 60, -49, [0, "05lSbTu4VJF58oqRbKPRBj"]]], [1, "5fgEECsP5MVotV8IRyud7o", null, null, null, 1, 0], [1, 0, -113.99100000000001, 0]], [5, "btn0", 9, [-56], [[2, -52, [0, "2aUZKgf8hDmo2SxZKRvBH7"], [5, 131, 51]], [18, 2, false, -53, [0, "8eCZZs9phB/4/NHF6J981r"], 15, 16], [11, 3, -55, [0, "2cveEjsOdDobwk8RKrnpOg"], [[12, "126fa/qenlHdIPcGna3h2uY", "onClick", 1]], [4, 4292269782], -54]], [1, "d0nO1vp7NAgZKhSRweHSEE", null, null, null, 1, 0], [1, -95.5, 0, 0]], [5, "btn1", 9, [-61], [[2, -57, [0, "1e6wadOjtAXa2tVSort/pO"], [5, 131, 51]], [18, 2, false, -58, [0, "94n480X1RE354h+p6PJ6wa"], 18, 19], [11, 3, -60, [0, "2deVWd5yVAS7vO+ThYb2Us"], [[12, "126fa/qenlHdIPcGna3h2uY", "onClick", 1]], [4, 4292269782], -59]], [1, "c47ENs72ZPdZB3jb5L0U6u", null, null, null, 1, 0], [1, 95.5, 0, 0]], [5, "node_box", 7, [-64, -65], [[4, -62, [0, "3bm7ekwt9P57Vg3IFphZD1"], [5, 38.25, 29.2], [0, 0, 0.5]], [16, 1, 1, -63, [0, "661eMub0hIIah0te+3574q"]]], [1, "fbyO+g4VlByaJwou3sq9c6", null, null, null, 1, 0], [1, 42, 0, 0]], [15, "img_icon", false, 4, [[[2, -66, [0, "f5x24V76hNDpATAlba6sz4"], [5, 42, 42]], -67, -68, -69], 4, 1, 1, 1], [1, "16boCSSqtMlYxQPiH4dPtf", null, null, null, 1, 0], [1, -22, 0, 0]], [26, "txt_time", false, 5, [[2, -70, [0, "b1m+NEWRRHGJQ1snqzzLNS"], [5, 0, 20]], [44, "", 18, 18, 20, false, false, true, 1, -71, [0, "01k43T23dLq4QrrTDoIqYa"], [4, 4278190335], 8], [3, -72, [0, "edhKMIsRJDOptGI97evlE1"]], [47, -73, [0, "10QWDI+p9H9InBc2p1PuYR"]]], [1, "95iF2j55RMSJUsJGUrV5aW", null, null, null, 1, 0], [1, 0, -32, 0]], [27, "spBg", [-77], [[2, -74, [0, "f8tFNUbzpJZoj/MVZGY+VH"], [5, 390, 29.72]], [19, 0, -75, [0, "08No2deopKuaL0YXKwVacP"], 13], [38, 1, 2, -6, 8, -76, [0, "41uaT9g0VOa4jJcDoeyAdV"]]], [1, "63ek3EVFpICqXewqwy49de", null, null, null, 1, 0]], [6, "useLvcom", 2, [[4, -78, [0, "94F0E/UmdNwKpx7+omv5XP"], [5, 84.98197937011719, 29.2], [0, 0, 0.5]], [9, "使用等级：", 18, 18, 20, false, false, 1, true, -79, [0, "88VdhxQd5GPbmfvVcB4TuS"], 1], [3, -80, [0, "55LeoMkPNLbLRItTFNiUGw"]]], [1, "00jnhtIp1MNojeoMR0jt6m", null, null, null, 1, 0], [1, -92.458, -3.387, 0]], [6, "haveNumCom", 2, [[4, -81, [0, "7aX3ZC4rRCyoAyMpMGHvXc"], [5, 85.01799011230469, 29.2], [0, 0, 0.5]], [9, "拥有数量：", 18, 18, 20, false, false, 1, true, -82, [0, "07OyU4OFZKe7dYUodfL3hv"], 2], [3, -83, [0, "53urpUIa1CeYALchUlT8lN"]]], [1, "3fm26d26tAap6wN/YsI6Nb", null, null, null, 1, 0], [1, -92.458, -32.176, 0]], [6, "labtype", 2, [[4, -84, [0, "beSyekSlpPPr22/OBYGTo4"], [5, 85, 29.2], [0, 0, 0.5]], [9, "物品类型：", 18, 18, 20, false, false, 1, true, -85, [0, "b7MqVv0+RJI4Pw1xoNwcPn"], 3], [3, -86, [0, "51m3JIsfFKr5GNB/3/qhHh"]]], [1, "d9H2ELKgRIIJ3h92pZQm9O", null, null, null, 1, 0], [1, 49.181, -4.815, 0]], [23, 0, {}, 2, [30, "c46/YsCPVOJYA4mWEpNYRx", null, null, -91, [48, "23S5abp41DnZ89ZoJ0zhpM", 1, [[49, "ItemCell", ["_name"], -87], [13, ["_lpos"], -88, [1, -151.347, 6, 0]], [13, ["_lrot"], -89, [3, 0, 0, 0, 1]], [13, ["_euler"], -90, [1, 0, 0, 0]], [50, 0, ["touchType"], [10, ["63Xidp4fNMJZ024S1Wuvtk"]]]]], 4]], [10, ["c46/YsCPVOJYA4mWEpNYRx"]], [7, "txtName", 2, [[[4, -92, [0, "adz73oag1JwYweE7Pw3for"], [5, 105, 25], [0, 0, 0.5]], -93, [3, -94, [0, "0cSRQMerZFPKjlRbiQHMZf"]]], 4, 1, 4], [1, "a0JG3p0gZLTpP/qgYgBLOS", null, null, null, 1, 0], [1, -92.458, 27.249, 0]], [7, "useLv", 2, [[[4, -95, [0, "47BNGh2NpDk4tX6ttSGwSE"], [5, 14.25, 20], [0, 0, 0.5]], -96, [3, -97, [0, "04+YEkaPhGpI1UGpbIny0z"]]], 4, 1, 4], [1, "b8/yrphWlISa/NYWkRerw6", null, null, null, 1, 0], [1, -4.739, -2.387, 0]], [7, "have<PERSON>um", 2, [[[4, -98, [0, "367LCYWRdDWpLUUrmQQUzr"], [5, 14.25, 20], [0, 0, 0.5]], -99, [3, -100, [0, "1d4GtHikFKMKaRvDA04b2C"]]], 4, 1, 4], [1, "26KG8NW2tDRI3lngIglJBM", null, null, null, 1, 0], [1, -4.739, -31.176000000000002, 0]], [7, "mainType", 2, [[[4, -101, [0, "67hO/GxRhAaaab/na+V4A2"], [5, 0, 20], [0, 0, 0.5]], -102, [3, -103, [0, "3bYyNUIgRNmIMNaDTUxduv"]]], 4, 1, 4], [1, "d8qWkcECZM8pZtJqJM7oue", null, null, null, 1, 0], [1, 130.666, -3.8150000000000004, 0]], [6, "cang_txt", 7, [[4, -104, [0, "efrCeYC1RHrIMkP5J0tGzA"], [5, 85, 29.2], [0, 0, 0.5]], [9, "藏品星级：", 18, 18, 20, false, false, 1, true, -105, [0, "47hoh3QdVGAYnFKtsYv3A8"], 6], [3, -106, [0, "b0punkn65AAq8uEKoVLmLz"]]], [1, "17zHqgoKhAjJdlPAx43KDX", null, null, null, 1, 0], [1, -42.508995056152344, -0.24700000000007094, 0]], [7, "cang_star", 12, [[[4, -107, [0, "5ccKvw0nNHN6BgjxImG32i"], [5, 14.25, 20], [0, 0, 0.5]], -108, [3, -109, [0, "e4JkPurhhArLRU8eq3JSkA"]]], 4, 1, 4], [1, "4a7NMStDxFZ7N87hnuyLME", null, null, null, 1, 0], [1, 0, 0.6000000000000227, 0]], [15, "txt_xhlabel", false, 4, [[[2, -110, [0, "14SN6mdgdNipw9bVSuYF7d"], [5, 48.98199462890625, 29.2]], -111, [3, -112, [0, "82eAS8SnNLH7g4FGlj+mIN"]]], 4, 1, 4], [1, "e10NKvKedPkJLeEfKQYa0j", null, null, null, 1, 0], [1, -36.99198913574219, 0, 0]], [7, "item_num", 4, [[[2, -113, [0, "8d+qvXqnVHIZwz+CDHNk/0"], [5, 39, 22.68]], -114, [3, -115, [0, "f9u/U0EpFKP51JOrAtgO0B"]]], 4, 1, 4], [1, "92bUCi3ERKzoa0dyJHw28A", null, null, null, 1, 0], [1, 0, 3, 0]], [7, "rich", 5, [[[2, -116, [0, "d8vZAihHtAeq8KgXItrbmS"], [5, 390, 27.72]], -117, [3, -118, [0, "fcEFd97cBIa56UlvT1XEUO"]]], 4, 1, 4], [1, "72wXNzZBRO9IrCe4PPG46d", null, null, null, 1, 0], [1, 0, -13.86, 0]], [5, "content", 8, [-121], [[4, -119, [0, "534awkde1Jx5NEBs3JXuhP"], [5, 395, 29.72], [0, 0.5, 1]], [39, 1, 2, 8, true, -120, [0, "4afAslIzJKh5BdwbobDghk"]]], [1, "49TrCkoolBurnOngVdqfmk", null, null, null, 1, 0], [1, 0, 1.8599999999999994, 0]], [5, "item", 30, [15], [[2, -122, [0, "40Uw+//iRDFIixJuSeIDjn"], [5, 386.273, 29.72]], [16, 1, 2, -123, [0, "0eWiJhoKdIALHqMqe0K9jt"]]], [1, "76JROGVQpIppyXrDKPyDN5", null, null, null, 1, 0], [1, 0, -14.86, 0]], [6, "rich", 15, [[2, -124, [0, "14HNLYeZtKU7BUmn6/tch9"], [5, 380, 27.72]], [51, 22, "333333", 1, 18, 380, false, false, -125, [0, "81Gm+Q43FC+qy8gP2BQx+e"], 10, 11, 12], [3, -126, [0, "f32QjRopJOYKi/hhrRFqcU"]]], [1, "7bGoG9YyBEG5oO/WOavkc3", null, null, null, 1, 0], [1, 0, 7, 0]], [14, "txt", 10, [[2, -127, [0, "492cPYm0pD8LY3twnW4RP+"], [5, 59.95198059082031, 37.5]], [20, "使 用", 24, 24, 25, false, false, 1, true, 3, -128, [0, "8aoPQAL9BOv6vIe242eMG/"], 14], [3, -129, [0, "007VwJ2c9O8J3Zo+pflXbz"]]], [1, "8b5V+lXIBDe5E0aO41IPbB", null, null, null, 1, 0]], [14, "txt", 11, [[2, -130, [0, "1d6sxN2nREAo6etnH5nNSj"], [5, 101.87997436523438, 37.5]], [20, "批量使用", 24, 24, 25, false, false, 1, true, 3, -131, [0, "51evB8HRxFaaXhJ5fzWSpI"], 17], [3, -132, [0, "53wrjvFk9I95tS5UtsKM/i"]]], [1, "11kOP8SGtDLrgRmcYhJVyT", null, null, null, 1, 0]], [6, "pub_line", 2, [[2, -133, [0, "d1d9yzwDVNf7rksFFPT3e/"], [5, 399, 5]], [19, 2, -134, [0, "77dDkkTCVM6L1DgEYD9ktd"], 0]], [1, "bazjwaQodOcL+dDwrP1ok9", null, null, null, 1, 0], [1, 0, -59.05, 0]], [6, "star", 12, [[2, -135, [0, "acPVu4x+ZIDJMMVCTJVQet"], [5, 24, 24]], [17, -136, [0, "60MTeDNS5PPqFuLJN6eb3/"], 7]], [1, "78nym7KhBFUYI4q1GHdxCq", null, null, null, 1, 0], [1, 26.25, 0, 0]], [6, "Label", 8, [[2, -137, [0, "b8f3TKFNdGp45YQnKJeg33"], [5, 136.67, 20]], [45, "符文效果预览:", 0, 0, 20, 20, 20, false, false, true, -138, [0, "a2FvuYHhdACaXJUJ8K5r62"], [4, 4285683961], 9]], [1, "f6BVnfxPJExIn5zigyuJxc", null, null, null, 1, 0], [1, -125.90199999999999, 17.86, 0]], [8, "物品名称", 0, 24, 24, 25, false, false, true, 21, [0, "0capz8czdJeLmnL448UkD6"], [4, 4294961035]], [8, "0", 0, 18, 18, 20, false, false, true, 22, [0, "a9y8JGwgJHm5NYaM+p4ts8"], [4, 4286085375]], [8, "0", 0, 18, 18, 20, false, false, true, 23, [0, "bd8ndMalRNBqeXvE1NL0wD"], [4, 4278255360]], [8, "", 0, 18, 18, 20, false, false, true, 24, [0, "0cphawfEZCqIQ0HLNuhjMn"], [4, 4278255360]], [8, "0", 0, 18, 18, 20, false, false, true, 26, [0, "38Gre/xxFMdLKFv7SMrsJk"], [4, 4278255360]], [46, "消耗：", 18, 18, 20, false, false, 1, true, 27, [0, "daF9hQaTBDpKgAxhHXhiJt"]], [41, 1, 0, 13, [0, "56vF/t6blM9KM1QT6Hz1oE"]], [43, 13, [0, "6aXlaGJZBJS5S3eGCYmyAB"]], [54, 1, 1, 13, [0, "04r4C+2pFBRZqFe84Rku4S"]], [52, 18, "<color=#ff0000>0<color=#ffffff>/0</color></color>", 18, false, 28, [0, "a4bUk7Y+5Fv4oxwLfChJmA"]], [55, 4, [0, "746StkRJ9I9oZY9snClZIj"], 43, 45, 44, 47, 46], [53, 22, "", 18, 390, false, false, 29, [0, "baU9aX27NNQJIqDtU+wNAv"]], [32, 1, [0, "68dlPzUqpM4bwcv1lbeSva"]], [56, 1, [0, "a1ipC3l9xFKZ5ylatZiFWM"], 38, 39, 40, 5, 49, 9, 50, 41, 6, 7, 42, 48, 8]], 0, [0, -1, 19, 0, 10, 19, 0, 11, 51, 0, 8, 1, 0, -1, 50, 0, -2, 51, 0, -1, 3, 0, 1, 2, 0, -1, 35, 0, -2, 16, 0, -3, 17, 0, -4, 18, 0, -5, 19, 0, -6, 21, 0, -7, 22, 0, -8, 23, 0, -9, 24, 0, -10, 6, 0, -11, 7, 0, 1, 3, 0, 1, 3, 0, 1, 3, 0, 1, 3, 0, -2, 5, 0, -3, 9, 0, 1, 4, 0, 1, 4, 0, -3, 48, 0, -1, 27, 0, -2, 13, 0, -3, 28, 0, 1, 5, 0, 1, 5, 0, -1, 29, 0, -2, 14, 0, -3, 8, 0, 1, 6, 0, 1, 6, 0, 5, 6, 0, 1, 6, 0, 1, 7, 0, -1, 25, 0, -2, 12, 0, 1, 8, 0, 1, 8, 0, -1, 37, 0, -2, 30, 0, 1, 9, 0, 1, 9, 0, -1, 10, 0, -2, 11, 0, 1, 10, 0, 1, 10, 0, 5, 10, 0, 1, 10, 0, -1, 33, 0, 1, 11, 0, 1, 11, 0, 5, 11, 0, 1, 11, 0, -1, 34, 0, 1, 12, 0, 1, 12, 0, -1, 26, 0, -2, 36, 0, 1, 13, 0, -2, 44, 0, -3, 45, 0, -4, 46, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 1, 14, 0, 1, 15, 0, 1, 15, 0, 1, 15, 0, -1, 32, 0, 1, 16, 0, 1, 16, 0, 1, 16, 0, 1, 17, 0, 1, 17, 0, 1, 17, 0, 1, 18, 0, 1, 18, 0, 1, 18, 0, 4, 20, 0, 4, 20, 0, 4, 20, 0, 4, 20, 0, 8, 19, 0, 1, 21, 0, -2, 38, 0, 1, 21, 0, 1, 22, 0, -2, 39, 0, 1, 22, 0, 1, 23, 0, -2, 40, 0, 1, 23, 0, 1, 24, 0, -2, 41, 0, 1, 24, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 26, 0, -2, 42, 0, 1, 26, 0, 1, 27, 0, -2, 43, 0, 1, 27, 0, 1, 28, 0, -2, 47, 0, 1, 28, 0, 1, 29, 0, -2, 49, 0, 1, 29, 0, 1, 30, 0, 1, 30, 0, -1, 31, 0, 1, 31, 0, 1, 31, 0, 1, 32, 0, 1, 32, 0, 1, 32, 0, 1, 33, 0, 1, 33, 0, 1, 33, 0, 1, 34, 0, 1, 34, 0, 1, 34, 0, 1, 35, 0, 1, 35, 0, 1, 36, 0, 1, 36, 0, 1, 37, 0, 1, 37, 0, 12, 1, 2, 6, 3, 4, 6, 7, 15, 6, 31, 138], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 39, 40, 41, 42, 43, 44, 47, 47, 49, 49], [3, 2, 2, 2, 13, 3, 2, 3, 2, 2, 2, 7, 14, 3, 2, 3, 9, 2, 3, 9, 3, 2, 2, 2, 2, 2, 2, 3, 2, 7, 2, 7], [4, 2, 2, 2, 5, 6, 2, 7, 1, 1, 1, 1, 8, 9, 2, 10, 3, 2, 11, 3, 12, 1, 1, 1, 1, 1, 2, 13, 1, 1, 1, 1]], [[{"name": "fuwen", "spriteFrames": ["aprilFools_icon_02", "41Cf1vi2BOm4J8zvJo4tGH@2138e", "fuwen_add1", "41Cf1vi2BOm4J8zvJo4tGH@f3659", "fuwen_add2", "41Cf1vi2BOm4J8zvJo4tGH@8c514", "fuwen_add3", "41Cf1vi2BOm4J8zvJo4tGH@bc827", "fuwen_add4", "41Cf1vi2BOm4J8zvJo4tGH@7e8a7", "fuwen_add5", "41Cf1vi2BOm4J8zvJo4tGH@4da80", "fuwen_add6", "41Cf1vi2BOm4J8zvJo4tGH@680de", "fuwen_add7", "41Cf1vi2BOm4J8zvJo4tGH@fb621", "fuwen_add8", "41Cf1vi2BOm4J8zvJo4tGH@a80b1", "fuwen_add9", "41Cf1vi2BOm4J8zvJo4tGH@a6d66", "fuwen_bg_02", "41Cf1vi2BOm4J8zvJo4tGH@4d26b", "fuwen_bg_03", "41Cf1vi2BOm4J8zvJo4tGH@83bbc", "fuwen_bg_04", "41Cf1vi2BOm4J8zvJo4tGH@02c46", "fuwen_bg_05", "41Cf1vi2BOm4J8zvJo4tGH@26701", "fuwen_bg_06", "41Cf1vi2BOm4J8zvJo4tGH@906e5", "fuwen_bg_07", "41Cf1vi2BOm4J8zvJo4tGH@a983b", "fuwen_bg_08", "41Cf1vi2BOm4J8zvJo4tGH@62b22", "fuwen_bg_13", "41Cf1vi2BOm4J8zvJo4tGH@e44b5", "fuwen_btn_01", "41Cf1vi2BOm4J8zvJo4tGH@8bf75", "fuwen_btn_02", "41Cf1vi2BOm4J8zvJo4tGH@4519c", "fuwen_btn_03", "41Cf1vi2BOm4J8zvJo4tGH@ff59e", "fuwen_btn_04", "41Cf1vi2BOm4J8zvJo4tGH@096c6", "fuwen_btn_05", "41Cf1vi2BOm4J8zvJo4tGH@ae697", "fuwen_daozei", "41Cf1vi2BOm4J8zvJo4tGH@f0b17", "fuwen_di", "41Cf1vi2BOm4J8zvJo4tGH@91069", "fuwen_fashi", "41Cf1vi2BOm4J8zvJo4tGH@ffa53", "fuwen_frame_01", "41Cf1vi2BOm4J8zvJo4tGH@b6f21", "fuwen_frame_02", "41Cf1vi2BOm4J8zvJo4tGH@36f31", "fuwen_frame_03", "41Cf1vi2BOm4J8zvJo4tGH@6bcfe", "fuwen_frame_04", "41Cf1vi2BOm4J8zvJo4tGH@6dcdf", "fuwen_frame_05", "41Cf1vi2BOm4J8zvJo4tGH@680dc", "fuwen_frame_06", "41Cf1vi2BOm4J8zvJo4tGH@8c73e", "fuwen_frame_07", "41Cf1vi2BOm4J8zvJo4tGH@bbc2d", "fuwen_frame_08", "41Cf1vi2BOm4J8zvJo4tGH@679df", "fuwen_icon_01", "41Cf1vi2BOm4J8zvJo4tGH@b1fc3", "fuwen_icon_02", "41Cf1vi2BOm4J8zvJo4tGH@8ec9c", "fuwen_icon_03", "41Cf1vi2BOm4J8zvJo4tGH@112c2", "fuwen_icon_04", "41Cf1vi2BOm4J8zvJo4tGH@b9481", "fuwen_icon_05", "41Cf1vi2BOm4J8zvJo4tGH@06b86", "fuwen_icon_06", "41Cf1vi2BOm4J8zvJo4tGH@5394c", "fuwen_icon_07", "41Cf1vi2BOm4J8zvJo4tGH@5136a", "fuwen_icon_08", "41Cf1vi2BOm4J8zvJo4tGH@fff41", "fuwen_icon_09", "41Cf1vi2BOm4J8zvJo4tGH@1a85b", "fuwen_icon_10", "41Cf1vi2BOm4J8zvJo4tGH@053c5", "fuwen_icon_11", "41Cf1vi2BOm4J8zvJo4tGH@0900f", "fuwen_icon_12", "41Cf1vi2BOm4J8zvJo4tGH@57f71", "fuwen_icon_13", "41Cf1vi2BOm4J8zvJo4tGH@9ae04", "fuwen_icon_14", "41Cf1vi2BOm4J8zvJo4tGH@3b103", "fuwen_icon_15", "41Cf1vi2BOm4J8zvJo4tGH@fb4d3", "fuwen_icon_16", "41Cf1vi2BOm4J8zvJo4tGH@26225", "fuwen_icon_17", "41Cf1vi2BOm4J8zvJo4tGH@c21fa", "fuwen_icon_18", "41Cf1vi2BOm4J8zvJo4tGH@ea386", "fuwen_icon_19", "41Cf1vi2BOm4J8zvJo4tGH@15fec", "fuwen_icon_20", "41Cf1vi2BOm4J8zvJo4tGH@f20c4", "fuwen_icon_21", "41Cf1vi2BOm4J8zvJo4tGH@21dc7", "fuwen_icon_22", "41Cf1vi2BOm4J8zvJo4tGH@89e62", "fuwen_icon_9", "41Cf1vi2BOm4J8zvJo4tGH@f1c67", "fuwen_lieren", "41Cf1vi2BOm4J8zvJo4tGH@ba313", "fuwen_line_01", "41Cf1vi2BOm4J8zvJo4tGH@65f92", "fuwen_line_02", "41Cf1vi2BOm4J8zvJo4tGH@d1c46", "fuwen_line_03", "41Cf1vi2BOm4J8zvJo4tGH@1c182", "fuwen_line_04", "41Cf1vi2BOm4J8zvJo4tGH@20fc3", "fuwen_line_05", "41Cf1vi2BOm4J8zvJo4tGH@64bf5", "fuwen_line_06", "41Cf1vi2BOm4J8zvJo4tGH@16373", "fuwen_mushi", "41Cf1vi2BOm4J8zvJo4tGH@30793", "fuwen_role_25", "41Cf1vi2BOm4J8zvJo4tGH@ef69b", "fu<PERSON>_she<PERSON><PERSON><PERSON>", "41Cf1vi2BOm4J8zvJo4tGH@23786", "fuwen_skill_btn_auto_01", "41Cf1vi2BOm4J8zvJo4tGH@4b50e", "fuwen_skill_btn_auto_02", "41Cf1vi2BOm4J8zvJo4tGH@1179c", "fuwen_tishi", "41Cf1vi2BOm4J8zvJo4tGH@122f2", "fuwen_zhanshi", "41Cf1vi2BOm4J8zvJo4tGH@2813e", "fuwenbtn1", "41Cf1vi2BOm4J8zvJo4tGH@6b56f", "fuwencion3", "41Cf1vi2BOm4J8zvJo4tGH@b0225", "fuwenicon1", "41Cf1vi2BOm4J8zvJo4tGH@6f34a", "fuwenicon2", "41Cf1vi2BOm4J8zvJo4tGH@0625e", "fuwenlibao_btn_01", "41Cf1vi2BOm4J8zvJo4tGH@a2204", "fuwenlibao_xinxikuang_01", "41Cf1vi2BOm4J8zvJo4tGH@fdefb", "fw_1", "41Cf1vi2BOm4J8zvJo4tGH@c54bd", "fw_2", "41Cf1vi2BOm4J8zvJo4tGH@280fd", "fw_3", "41Cf1vi2BOm4J8zvJo4tGH@71aa1", "fw_4", "41Cf1vi2BOm4J8zvJo4tGH@5d948", "fw_5", "41Cf1vi2BOm4J8zvJo4tGH@755e0", "fw_6", "41Cf1vi2BOm4J8zvJo4tGH@154ea", "fw_7", "41Cf1vi2BOm4J8zvJo4tGH@7be6c"]}], [10], 0, [], [], []], [[{"name": "fuwen_bg_04", "rect": {"x": 820, "y": 1461, "width": 50, "height": 50}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 50, "height": 50}, "rotated": false, "capInsets": [18, 18, 18, 15], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_10", "rect": {"x": 603, "y": 1494, "width": 22, "height": 28}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 22, "height": 28}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwenicon2", "rect": {"x": 941, "y": 1012, "width": 80, "height": 90}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 80, "height": 90}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_05", "rect": {"x": 69, "y": 1137, "width": 20, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 25}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_11", "rect": {"x": 628, "y": 1494, "width": 22, "height": 28}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 22, "height": 28}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_btn_04", "rect": {"x": 821, "y": 1123, "width": 132, "height": 43}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 132, "height": 43}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_03", "rect": {"x": 794, "y": 1315, "width": 66, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 66, "height": 25}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_skill_btn_auto_02", "rect": {"x": 927, "y": 1414, "width": 55, "height": 42}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 55, "height": 42}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_tishi", "rect": {"x": 985, "y": 1414, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fw_6", "rect": {"x": 566, "y": 526, "width": 254, "height": 279}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 254, "height": 279}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_19", "rect": {"x": 703, "y": 1395, "width": 57, "height": 73}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 57, "height": 73}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_line_06", "rect": {"x": 250, "y": 1496, "width": 224, "height": 24}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 224, "height": 24}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_09", "rect": {"x": 477, "y": 1502, "width": 20, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 25}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_line_03", "rect": {"x": 250, "y": 1469, "width": 224, "height": 24}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 224, "height": 24}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_line_04", "rect": {"x": 960, "y": 476, "width": 224, "height": 24}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 224, "height": 24}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "aprilFools_icon_02", "rect": {"x": 424, "y": 1049, "width": 126, "height": 95}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 126, "height": 95}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_21", "rect": {"x": 691, "y": 1455, "width": 57, "height": 73}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 57, "height": 73}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fu<PERSON>_she<PERSON><PERSON><PERSON>", "rect": {"x": 927, "y": 1249, "width": 88, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 88, "height": 93}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_16", "rect": {"x": 486, "y": 1442, "width": 22, "height": 28}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 22, "height": 28}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_bg_05", "rect": {"x": 823, "y": 476, "width": 213, "height": 134}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 213, "height": 134}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fw_2", "rect": {"x": 167, "y": 596, "width": 254, "height": 279}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 254, "height": 279}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_zhanshi", "rect": {"x": 836, "y": 1303, "width": 88, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 88, "height": 93}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_mushi", "rect": {"x": 836, "y": 1207, "width": 88, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 88, "height": 93}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_02", "rect": {"x": 560, "y": 438, "width": 191, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 191, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_14", "rect": {"x": 974, "y": 1459, "width": 48, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 48, "height": 40}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_btn_02", "rect": {"x": 707, "y": 1311, "width": 84, "height": 81}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 84, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_skill_btn_auto_01", "rect": {"x": 779, "y": 1395, "width": 55, "height": 42}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 55, "height": 42}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_bg_02", "rect": {"x": 767, "y": 1455, "width": 50, "height": 50}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 50, "height": 50}, "rotated": false, "capInsets": [14, 14, 13, 15], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add5", "rect": {"x": 755, "y": 381, "width": 266, "height": 92}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 266, "height": 92}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_07", "rect": {"x": 125, "y": 1137, "width": 20, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 25}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_06", "rect": {"x": 97, "y": 1137, "width": 20, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 25}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_12", "rect": {"x": 797, "y": 476, "width": 22, "height": 28}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 22, "height": 28}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fw_4", "rect": {"x": 2, "y": 1183, "width": 254, "height": 279}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 254, "height": 279}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_bg_08", "rect": {"x": 2, "y": 151, "width": 750, "height": 143}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 750, "height": 143}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_line_05", "rect": {"x": 987, "y": 476, "width": 224, "height": 24}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 224, "height": 24}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_line_01", "rect": {"x": 921, "y": 692, "width": 224, "height": 24}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 224, "height": 24}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_08", "rect": {"x": 2, "y": 297, "width": 750, "height": 138}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 750, "height": 138}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_05", "rect": {"x": 530, "y": 898, "width": 111, "height": 23}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 111, "height": 23}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add6", "rect": {"x": 633, "y": 1237, "width": 85, "height": 71}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 71}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwenbtn1", "rect": {"x": 553, "y": 1090, "width": 164, "height": 54}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 164, "height": 54}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_03", "rect": {"x": 2, "y": 596, "width": 584, "height": 64}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 584, "height": 64}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_04", "rect": {"x": 798, "y": 1215, "width": 97, "height": 35}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 97, "height": 35}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwenicon1", "rect": {"x": 941, "y": 919, "width": 80, "height": 90}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 80, "height": 90}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fw_3", "rect": {"x": 167, "y": 878, "width": 254, "height": 279}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 254, "height": 279}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fw_5", "rect": {"x": 259, "y": 1160, "width": 254, "height": 279}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 254, "height": 279}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fw_7", "rect": {"x": 566, "y": 808, "width": 254, "height": 279}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 254, "height": 279}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add4", "rect": {"x": 823, "y": 692, "width": 231, "height": 95}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 231, "height": 95}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_bg_03", "rect": {"x": 424, "y": 596, "width": 450, "height": 70}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 450, "height": 70}, "rotated": true, "capInsets": [62, 37, 55, 14], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_22", "rect": {"x": 530, "y": 1012, "width": 29, "height": 29}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 29, "height": 29}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_btn_01", "rect": {"x": 516, "y": 1418, "width": 84, "height": 81}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 84, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add2", "rect": {"x": 819, "y": 1169, "width": 126, "height": 35}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 126, "height": 35}, "rotated": false, "capInsets": [10, 10, 10, 10], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_06", "rect": {"x": 530, "y": 596, "width": 299, "height": 33}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 299, "height": 33}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_02", "rect": {"x": 948, "y": 889, "width": 65, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 65, "height": 25}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_bg_06", "rect": {"x": 823, "y": 926, "width": 194, "height": 115}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 194, "height": 115}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_di", "rect": {"x": 873, "y": 1461, "width": 50, "height": 50}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 50, "height": 50}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_13", "rect": {"x": 754, "y": 476, "width": 40, "height": 47}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 47}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwenlibao_btn_01", "rect": {"x": 720, "y": 1090, "width": 98, "height": 76}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 98, "height": 76}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add9", "rect": {"x": 516, "y": 1192, "width": 163, "height": 42}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 163, "height": 42}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add8", "rect": {"x": 516, "y": 1147, "width": 165, "height": 42}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 165, "height": 42}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_bg_07", "rect": {"x": 2, "y": 2, "width": 750, "height": 146}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 750, "height": 146}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_btn_05", "rect": {"x": 684, "y": 1169, "width": 132, "height": 43}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 132, "height": 43}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwencion3", "rect": {"x": 926, "y": 1459, "width": 45, "height": 50}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 45, "height": 50}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_01", "rect": {"x": 890, "y": 1399, "width": 40, "height": 34}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 40, "height": 34}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_01", "rect": {"x": 497, "y": 596, "width": 450, "height": 30}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 450, "height": 30}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_04", "rect": {"x": 663, "y": 1416, "width": 65, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 65, "height": 25}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_lieren", "rect": {"x": 707, "y": 1215, "width": 88, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 88, "height": 93}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_frame_07", "rect": {"x": 755, "y": 284, "width": 266, "height": 94}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 266, "height": 94}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add3", "rect": {"x": 2, "y": 1465, "width": 245, "height": 41}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 245, "height": 41}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_17", "rect": {"x": 927, "y": 1340, "width": 85, "height": 71}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 71}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fw_1", "rect": {"x": 755, "y": 2, "width": 254, "height": 279}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 254, "height": 279}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_line_02", "rect": {"x": 259, "y": 1442, "width": 224, "height": 24}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 224, "height": 24}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_bg_13", "rect": {"x": 69, "y": 596, "width": 538, "height": 95}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 538, "height": 95}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_18", "rect": {"x": 948, "y": 1189, "width": 57, "height": 73}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 57, "height": 73}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_daozei", "rect": {"x": 516, "y": 1322, "width": 88, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 88, "height": 93}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_9", "rect": {"x": 477, "y": 1473, "width": 22, "height": 28}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 22, "height": 28}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_20", "rect": {"x": 603, "y": 1418, "width": 57, "height": 73}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 57, "height": 73}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add1", "rect": {"x": 948, "y": 703, "width": 183, "height": 64}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 183, "height": 64}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_15", "rect": {"x": 956, "y": 1105, "width": 81, "height": 65}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 81, "height": 65}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_add7", "rect": {"x": 516, "y": 1237, "width": 114, "height": 82}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 114, "height": 82}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwenlibao_xinxikuang_01", "rect": {"x": 2, "y": 438, "width": 555, "height": 155}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 555, "height": 155}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_btn_03", "rect": {"x": 824, "y": 1399, "width": 63, "height": 59}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 63, "height": 59}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_fashi", "rect": {"x": 607, "y": 1325, "width": 88, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 88, "height": 93}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "fuwen_icon_08", "rect": {"x": 663, "y": 1484, "width": 20, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 25}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]]]