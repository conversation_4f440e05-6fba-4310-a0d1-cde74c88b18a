[1, ["61NXJOyyZNU6ZGh0FJkMv2", "41Cf1vi2BOm4J8zvJo4tGH", "2enw6dg1JAa5xhWIDgaiWv@345e8", "2enw6dg1JAa5xhWIDgaiWv@29896", "2enw6dg1JAa5xhWIDgaiWv", "2enw6dg1JAa5xhWIDgaiWv@4a0f4", "41Cf1vi2BOm4J8zvJo4tGH@ef69b", "41Cf1vi2BOm4J8zvJo4tGH@4b50e", "2enw6dg1JAa5xhWIDgaiWv@46320", "2enw6dg1JAa5xhWIDgaiWv@0c02c", "42YlG8C+VFQIQhi/uTnF/K", "94FGSZedBCE46Kdw4aaHkw@f9941", "2enw6dg1JAa5xhWIDgaiWv@cf4c0", "41Cf1vi2BOm4J8zvJo4tGH@fb4d3", "2enw6dg1JAa5xhWIDgaiWv@694c2", "2enw6dg1JAa5xhWIDgaiWv@f6c2e", "e3QXx3OSpOdrupO+Id3C8U", "41Cf1vi2BOm4J8zvJo4tGH@2813e", "41Cf1vi2BOm4J8zvJo4tGH@8ec9c", "2enw6dg1JAa5xhWIDgaiWv@6acb7", "41Cf1vi2BOm4J8zvJo4tGH@b6f21", "41Cf1vi2BOm4J8zvJo4tGH@8c73e", "41Cf1vi2BOm4J8zvJo4tGH@9ae04", "b7yA5VwgxLqpPr9XP5C2+t", "e6EcX/GGpOjLFEwbMZeekj", "2enw6dg1JAa5xhWIDgaiWv@f131f", "9ePHVvVY9ElIlHQ9J5J/AO@d34f8", "2enw6dg1JAa5xhWIDgaiWv@45885", "2enw6dg1JAa5xhWIDgaiWv@f9880", "65nnJ3SlJOoYRwNWpKye8V@f9941"], ["node", "_spriteFrame", "_font", "targetInfo", "_atlas", "_parent", "_userDefinedFont", "root", "asset", "target", "source", "uispine", "_skeletonData", "_imageAtlas", "_normalSprite", "_content", "_target", "data", "button_allUp", "spFuwenIcon", "lbFuWenUp", "fuwenAttrList", "upSp", "closeButton", "getLevel", "skillUpBtn", "backBtn", "downBtn", "upBtn", "upgradeBtn", "cdDes", "skillUseWay", "areaDes", "costName", "passiveList", "passiveDes", "activeDes", "noStudy", "skillLevel", "skillIcon", "handBtn", "autoBtn", "skillName"], [["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_enableWrapText", "_isSystemFontUsed", "_lineHeight", "_enableOutline", "_overflow", "_cacheMode", "_horizontalAlign", "_outlineWidth", "node", "__prefab", "_color", "_font"], -8, 1, 4, 5, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_parent", "_lpos", "_children", "_lscale"], 1, 9, 4, 1, 5, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_atlas", "_color"], 0, 1, 4, 6, 6, 5], ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children", "_lscale"], -1, 4, 12, 1, 5, 2, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "_paddingBottom", "_spacingY", "_spacingX", "_isAlign", "node", "__prefab"], -4, 1, 4], ["cc.Widget", ["_alignFlags", "_originalWidth", "_alignMode", "_originalHeight", "_bottom", "_left", "_right", "_top", "node", "__prefab"], -5, 1, 4], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "clickEvents"], 2, 1, 4, 5, 1, 9], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_maxWidth", "_isSystemFontUsed", "_handleTouchEvent", "_verticalAlign", "_horizontalAlign", "node", "__prefab", "_font", "_userDefinedFont", "_imageAtlas"], -5, 1, 4, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "_enabled", "node", "__prefab", "_content"], -1, 1, 4, 1], ["96925NpxVhGX5GHR06EhbFH", ["_virtual", "selectedMode", "node", "__prefab", "tmpNode", "pageChangeEvent"], 1, 1, 4, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "node", "__prefab", "_skeletonData"], 0, 1, 4, 6], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["79625nJPs5FErtP6kcjZWHi", ["node", "__prefab"], 3, 1, 4], ["b1133n1XT1P4LPH+vec6wCX", ["<PERSON><PERSON><PERSON>", "ani<PERSON><PERSON>", "node", "__prefab", "uispine"], 1, 1, 4, 1], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["ce6f4fRQZZL97fTZqM2aYl8", ["node", "__prefab", "upNode", "btnPanel", "maxLvSpr", "upCostPanel", "img_red", "img_red_up", "nodeFuWen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeFuWenHint1", "nodeFuWenHint2", "ToSLBtn"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo"], 2, 1, 1, 4]], [[13, 0, 2], [14, 0, 1, 2, 3, 4, 5, 5], [6, 0, 1, 2, 1], [6, 0, 1, 2, 3, 1], [17, 0, 1, 1], [1, 0, 4, 2, 3, 5, 2], [28, 0, 2], [1, 0, 4, 6, 2, 3, 5, 2], [3, 0, 6, 5, 4, 7, 2], [9, 0, 1, 2, 3], [7, 0, 1, 2, 5, 3, 4, 2], [1, 0, 4, 2, 3, 2], [2, 0, 3, 4, 5, 2], [2, 1, 0, 3, 4, 5, 3], [25, 0, 1, 2, 2], [26, 0, 1, 2, 2], [2, 3, 4, 5, 1], [2, 3, 4, 5, 6, 1], [0, 0, 1, 2, 7, 3, 4, 6, 11, 12, 14, 8], [3, 0, 6, 5, 4, 2], [3, 0, 6, 8, 5, 4, 7, 2], [1, 0, 4, 6, 2, 3, 2], [24, 0, 1, 2, 3], [1, 0, 6, 2, 3, 5, 2], [0, 0, 1, 2, 5, 3, 4, 6, 11, 12, 14, 8], [4, 0, 1, 7, 8, 3], [21, 0, 1, 1], [3, 0, 1, 6, 8, 5, 4, 7, 3], [1, 0, 1, 4, 6, 2, 3, 5, 3], [1, 0, 1, 4, 2, 3, 5, 3], [3, 2, 3, 6, 4, 3], [3, 0, 8, 5, 4, 7, 2], [1, 0, 4, 2, 3, 7, 2], [2, 0, 3, 4, 2], [2, 0, 2, 3, 4, 5, 3], [5, 0, 1, 3, 8, 9, 4], [15, 0, 1, 2, 3, 4, 5, 4], [0, 0, 1, 2, 5, 3, 4, 6, 11, 12, 8], [0, 0, 1, 2, 5, 3, 4, 11, 12, 7], [0, 0, 1, 2, 5, 3, 4, 11, 12, 13, 7], [0, 0, 1, 2, 5, 3, 4, 6, 11, 12, 13, 14, 8], [9, 1], [4, 0, 1, 5, 7, 8, 4], [19, 0, 1, 1], [20, 0, 1, 2, 1], [22, 0, 1, 2, 3, 4, 3], [23, 0, 1, 2, 2], [27, 0, 1, 2, 3], [31, 0, 1, 2, 3, 2], [12, 0, 2], [3, 0, 8, 5, 4, 2], [3, 0, 6, 5, 4, 7, 9, 2], [3, 0, 1, 6, 5, 4, 7, 3], [1, 0, 6, 2, 3, 2], [1, 0, 4, 2, 3, 5, 7, 2], [6, 0, 1, 1], [2, 0, 3, 4, 7, 5, 2], [2, 1, 0, 3, 4, 3], [2, 3, 4, 1], [2, 1, 0, 3, 4, 5, 6, 3], [7, 1, 2, 3, 4, 1], [7, 0, 1, 2, 5, 2], [5, 8, 9, 1], [5, 0, 5, 6, 7, 4, 1, 2, 8, 9, 8], [5, 0, 4, 1, 2, 8, 9, 5], [5, 0, 1, 3, 2, 8, 9, 5], [16, 0, 1, 2, 3, 4, 5, 3], [0, 0, 1, 2, 5, 3, 4, 6, 11, 12, 13, 8], [0, 0, 9, 1, 2, 5, 7, 3, 4, 6, 11, 12, 10], [0, 0, 1, 2, 5, 3, 4, 8, 6, 10, 11, 12, 13, 14, 10], [0, 0, 1, 2, 5, 3, 4, 8, 6, 11, 12, 13, 14, 9], [0, 0, 1, 2, 3, 4, 6, 11, 12, 13, 7], [18, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 2, 3, 4, 8, 9, 6], [8, 0, 1, 6, 2, 3, 4, 5, 8, 9, 10, 11, 12, 8], [8, 0, 1, 7, 2, 3, 4, 5, 8, 9, 8], [4, 0, 1, 3, 4, 7, 8, 5], [4, 0, 1, 5, 2, 6, 7, 8, 6], [4, 0, 1, 4, 2, 6, 7, 8, 6], [4, 0, 1, 3, 4, 2, 7, 8, 6], [4, 0, 1, 5, 6, 7, 8, 5], [4, 0, 1, 3, 2, 7, 8, 5], [10, 3, 0, 1, 2, 4, 5, 6, 5], [10, 0, 1, 2, 4, 5, 6, 4], [11, 2, 3, 4, 5, 1], [11, 0, 1, 2, 3, 4, 5, 3], [29, 0, 1, 1], [30, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1]], [[49, "SkillInfoDlgUI"], [50, "SkillInfoDlgUI", [-11, -12, -13], [[[2, -8, [0, "66pvF2C91DnKjoS/XOK17L"], [5, 750, 1334]], -9, [35, 45, 750, 1334, -10, [0, "21kyS9MopAUIC3KyS/uBhk"]]], 4, 1, 4], [66, "c46/YsCPVOJYA4mWEpNYRx", null, -7, 0, [[48, ["moneyConsume"], -4, -3, [6, ["f4kuyi9iVKF7T1eYAhBqGz"]]], [48, ["itemConsume"], -6, -5, [6, ["b5Lbk1jfZPWogfjBARWigS"]]]], [-1, -2]]], [23, "topPanel", [-15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29], [[3, -14, [0, "f0gSjnRoVAiZtBTbliFKJI"], [5, 447, 368.043], [0, 0.5, 1]]], [1, "eaaCMHcTFHd67OXfQLZXtC", null, null, null, 1, 0], [1, 0, 483.12149999999997, 0]], [21, "bg", 1, [2, -34, -35, -36, -37, -38], [[2, -30, [0, "65qUXK1AVA/b0KSQF9G6NV"], [5, 470, 966.2429999999999]], [13, 1, 0, -31, [0, "47CO2lWeJAkoDPkv7C0M2q"], 59], [86, -32, [0, "19GzcNR5BIpJxnUdaSO2jk"]], [81, 1, 2, 30, true, -33, [0, "4br+QkdKpPxoaaYcP2HP9T"]]], [1, "971eTbCPdAcIAkJvLmf+mE", null, null, null, 1, 0]], [53, "fuwenItem", [-40, -41, -42, -43, -44, -45, -46, -47, -48], [[2, -39, [0, "f0rTYt2a9N05WL3VFcJ/P4"], [5, 80, 80]]], [1, "7b/PH/As5FjozjcIh+QRVZ", null, null, null, 1, 0]], [23, "nodeFuWenAttrs", [-50, -51, -52, -53, -54, -55, -56], [[2, -49, [0, "fdWXGvYvVNvogNthrRtr2S"], [5, 447, 244]]], [1, "ed7XucajdFJpPP3KBiwWoP", null, null, null, 1, 0], [1, 0, -82.5, 0]], [7, "btnPanel", 3, [-59, -60, -61, -62], [[2, -57, [0, "8509wPYEVELbM9UgAIxGnM"], [5, 260, 100]], [42, 1, 1, 20, -58, [0, "d1ha8JW4REW7Negh1zVAKv"]]], [1, "8d9EzPbwREKYbqQmrLstkd", null, null, null, 1, 0], [1, 0, -403.1215, 0]], [20, "passiveList", 3, [-69], [[[3, -63, [0, "d5H+3xfhNPp5WrP8Z3RSz7"], [5, 376, 49.2], [0, 0.5, 1]], [57, 1, 0, -64, [0, "4e99pbfBRG462fMoG6CG10"]], [82, false, 0.23, 0.75, false, -66, [0, "b2KleumppPtZfz08UXvdoA"], -65], -67, [25, 1, 2, -68, [0, "57FO2jsU9E6ZusgnieQADw"]]], 4, 4, 4, 1, 4], [1, "17gta3HhhPXa8qUXJ3YLZl", null, null, null, 1, 0], [1, -10, 115.07849999999996, 0]], [7, "<PERSON><PERSON><PERSON><PERSON>", 3, [-72, -73, 5], [[2, -70, [0, "e7WOcUQPlDWoJcbMKmpsCM"], [5, 400, 419]], [79, 1, 2, 5, 10, true, -71, [0, "57NkrfFOJJYJ7X5RVmTlTx"]]], [1, "a7/+U0M99DGI0HUPfcZ2Xs", null, null, null, 1, 0], [1, 0, -143.62150000000003, 0]], [28, "upNode", false, 3, [-75, -76, -77, -78], [[2, -74, [0, "cdOfNmXgVPfLaiM7OQVrcc"], [5, 417, 140.926]]], [1, "d49jcVHd1Hf5H5vxwXm3Cn", null, null, null, 1, 0], [1, 0, -343.1215000000001, 0]], [31, "skillUpBtn", [-82, -83], [[[2, -79, [0, "21YdlQO01MGqmKw4mobi/R"], [5, 119, 46]], [13, 1, 0, -80, [0, "48ONEa0chFMbEOC2IKKMs2"], 47], -81], 4, 4, 1], [1, "c0Q3JAzAlI9r6exTeKvOno", null, null, null, 1, 0], [1, 79.5, 0, 0]], [20, "upgradeBtn", 6, [-87, -88], [[[2, -84, [0, "51e4d8otBHRKN5b5nLXpxy"], [5, 120, 46]], [13, 1, 0, -85, [0, "20ygei4YxDp4DjaC802s/O"], 55], -86], 4, 4, 1], [1, "96vgcEi7FI55qwx4W/zgc3", null, null, null, 1, 0], [1, 70, -13.394, 0]], [27, "fuwenUpBtn", false, 6, [-92, -93], [[[2, -89, [0, "fcx/N4HPFATLjH86e63LxM"], [5, 120, 46]], [13, 1, 0, -90, [0, "cerGB2qohLrKA1L60Hs95e"], 58], -91], 4, 4, 1], [1, "c704Mr7X9FrJxkXmpPQV2T", null, null, null, 1, 0], [1, 140, -13.394, 0]], [11, "bgClose", 1, [[2, -94, [0, "65L1csl9JGl6e5jEzEcAsb"], [5, 750, 1334]], [56, 0, -95, [0, "b2FR+bzmRIOajx6k6zG6rZ"], [4, 3019898880], 0], [60, -97, [0, "35JD05DvpCbLqGK/Nven1K"], [4, 4292269782], -96], [35, 45, 39, 39, -98, [0, "d6I9fl+ilKlpVKsrCqvm9/"]]], [1, "02iWejczJN0Y4W2/4TwoSS", null, null, null, 1, 0]], [27, "autoBtn", false, 2, [-102], [[[2, -99, [0, "54JEryoDVO2JKTNYT+veZT"], [5, 55, 42]], [34, 2, false, -100, [0, "92yNr/ditP1qkzL00pmb+J"], 9], -101], 4, 4, 1], [1, "86+UKKaE1KlqSCZ9/5Dfnc", null, null, null, 1, 0], [1, 170.117, -92.156, 0]], [8, "closeButton", 2, [[[2, -103, [0, "4dAlV7D7ZEaKIiCsVgBZ0g"], [5, 58, 57]], [13, 1, 2, -104, [0, "32CNd8HO9E34d2j4U1d9C7"], 10], -105, [62, -106, [0, "f2BfgMVF1NnLJd5+E4RyFc"]]], 4, 4, 1, 4], [1, "63bhn2NKJGq5l+PN4GxySH", null, null, null, 1, 0], [1, 206.98199999999997, -11.052000000000021, 0]], [21, "view", 7, [-111], [[3, -107, [0, "82uEf7fl9MB4/37EUGJJOh"], [5, 376, 49.2], [0, 0.5, 1]], [43, -108, [0, "62t6316kVOvp6PiVoDpHDG"]], [44, -109, [0, "ecelwVCtFMSKXxH2aoLvRL"], [4, 16777215]], [25, 1, 2, -110, [0, "cewFf4UAZIl70SfS52gVlK"]]], [1, "a4mMV28PpITo1H7bXpUtec", null, null, null, 1, 0]], [23, "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", [4], [[2, -112, [0, "bbvshsNO5HdrQ2k6enYRdj"], [5, 80, 100]], [63, 41, 10, 10, 2, 790, 120, 0, -113, [0, "ad4TSKwH9POb0uf6665NqI"]], [77, 1, 1, 22, true, true, -114, [0, "0d9d6MuxhI/4DtaKEZIB1b"]]], [1, "2fstyUPodNaZ1Rv2R8Aw//", null, null, null, 1, 0], [1, 0, 8, 0]], [23, "view", [-119], [[3, -115, [0, "987eG17lhLgLJAWuOhpj0L"], [5, 447, 160], [0, 0.5, 1]], [43, -116, [0, "419TRM1b5FOr1cLEGfgjNG"]], [44, -117, [0, "d9PpsTRWJEd4RxCNQT9Wyt"], [4, 16777215]], [65, 45, 240, 250, 0, -118, [0, "4daw1JutJPr4tQ6zEvYD92"]]], [1, "70XXCDxkVLp4phBW62ib19", null, null, null, 1, 0], [1, 0, 80, 0]], [21, "content", 18, [-123], [[3, -120, [0, "83iOFsBf5OcrR3BI52JtdI"], [5, 447, 27.72], [0, 0.5, 1]], [64, 41, 790, 120, 0, -121, [0, "b9jJ8922xPCYn0jugMmQaA"]], [78, 1, 2, 8, true, true, -122, [0, "52a1vP/9ZDuJQiBrcVDPy9"]]], [1, "c1MtNiZCNEAZB8T0GTeaxk", null, null, null, 1, 0]], [28, "lbHint2", false, 5, [-127], [[2, -124, [0, "05tnU3vkFKHr6RDwLzexAZ"], [5, 179.95596313476562, 31.72]], [70, "选择一枚符文启用", 22, 22, 22, false, false, 1, true, -125, [0, "551lo91rdE4J7aFf5t6x2l"], [4, 4283957233], 39], [4, -126, [0, "29YA8IiflJdIkoiXi/vVb4"]]], [1, "21BZ7s7bpODL4kGy973dN0", null, null, null, 1, 0], [1, 0, 20.222, 0]], [7, "upCostPanel", 9, [-130, -131], [[2, -128, [0, "fdUTqpq8ZPt6DmhmJxT3Io"], [5, 319.85, 50]], [80, 1, 1, 55.6, true, -129, [0, "31v65Nl5NFOoHH/W5q8qEh"]]], [1, "6fVfwXb4ZBzbHNh6kKEzpL", null, null, null, 1, 0], [1, 0, 8.553, 0]], [31, "backBtn", [-135], [[[2, -132, [0, "2a5wgWORpE0pbVO4Zd3lgC"], [5, 119, 46]], [13, 1, 0, -133, [0, "f5BjqoRUBD9qhVCX6Q3Jny"], 44], -134], 4, 4, 1], [1, "60WWwA6SxOmZlAFd34svo6", null, null, null, 1, 0], [1, -79.5, 0, 0]], [20, "upBtn", 6, [-139], [[[2, -136, [0, "e5kPuFoRNGE7PejT2aTtzd"], [5, 120, 46]], [13, 1, 0, -137, [0, "26qOgro6tG659/5nGIgIRC"], 50], -138], 4, 4, 1], [1, "61E/FI1dlFKbG1ZEtGhtrz", null, null, null, 1, 0], [1, -70, -13.394, 0]], [27, "downBtn", false, 6, [-143], [[[2, -140, [0, "61g3f7KyFBNonhd+qWb72q"], [5, 120, 46]], [13, 1, 0, -141, [0, "8dxq+/kjhAioy9idawnLoK"], 52], -142], 4, 4, 1], [1, "ccuo5f4nZFxKZiIBt9NAHc", null, null, null, 1, 0], [1, 0, -13.394, 0]], [8, "handBtn", 2, [[[2, -144, [0, "23zDvAv45CgraOH/vGUqJZ"], [5, 55, 42]], [34, 2, false, -145, [0, "23fhQ5FqpEUo7dtO6CY6Oa"], 6], -146], 4, 4, 1], [1, "dbNnj5u15L37JiiS/ojqsr", null, null, null, 1, 0], [1, 170.117, -92.156, 0]], [29, "pub_update", false, 2, [[2, -147, [0, "4baGCuUzJNxrpIlx6gkg75"], [5, 40, 38]], [16, -148, [0, "81ywx5l9tPJLEQMBU6juMN"], 7], [61, 3, -149, [0, "06p3Rq7PhOsb0DDV3a3T9b"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "ToShouLan", 1]]]], [1, "a7LgR/TzJAuKRPpenlqmWG", null, null, null, 1, 0], [1, 170.11699999999996, -150.24249999999995, 0]], [21, "content", 16, [-152], [[3, -150, [0, "5cRmOyXpdBwpOs86TBLRcL"], [5, 376, 49.2], [0, 0.5, 1]], [76, 1, 2, 20, 2, -151, [0, "8c5f9y/w5HHJeahCZDpwZG"]]], [1, "2bUw4gz2JP8bGeqyV/yCrp", null, null, null, 1, 0]], [19, "des", 27, [[[3, -153, [0, "47ifDHdm5J8KB1ijBwxf8c"], [5, 363, 29.2], [0, 0.5, 1]], -154, [4, -155, [0, "73sG2lWK1DVbx603DGt2Tq"]]], 4, 1, 4], [1, "b8r2qteCVMnbwR5j8D7bsk", null, null, null, 1, 0]], [20, "attrsList", 5, [18], [[[2, -156, [0, "bdIb8SDDdMpbKDwmnPGfZF"], [5, 447, 160]], [83, 0.23, 0.75, false, -157, [0, "88P8aXschIf7w25YfQegaV"], 19], -158], 4, 4, 1], [1, "dbx7SrOwhJcqwF+x5axD4m", null, null, null, 1, 0], [1, -2, 30, 0]], [7, "item", 19, [-161], [[2, -159, [0, "b0yFekC1FAca5BUd0ObULi"], [5, 447, 27.72]], [25, 1, 2, -160, [0, "48OlNLp1FDgrh8gtupjHfQ"]]], [1, "afp9eR6+lP9oh+1t+Zx8LC", null, null, null, 1, 0], [1, 0, -13.86, 0]], [21, "spBg", 30, [-165], [[2, -162, [0, "cceHrHyylA2bsfKOe04n1m"], [5, 447, 27.72]], [12, 0, -163, [0, "c7Pwd0jPlOD6rLQEpdo7hb"], 29], [25, 1, 2, -164, [0, "6aBtBep3NNjZkDVEYMHzfk"]]], [1, "213nIf+aVJaaZbibhv87EX", null, null, null, 1, 0]], [29, "lbHint1", false, 5, [[2, -166, [0, "84WrthxedGsKkpxzJymvFV"], [5, 242.02198791503906, 27.72]], [69, "请获得更多符文进行激活", 22, 22, 22, false, false, 1, true, 0, -167, [0, "09hPneKXVInb53hOE/lzzU"], [4, 4280495954], 36], [4, -168, [0, "85zlIOf7BF1qegxFaoMSnn"]]], [1, "61sQxPECxL7oU9uuJCHRAo", null, null, null, 1, 0], [1, 0, 30, 0]], [20, "getLevel", 9, [-172], [[[2, -169, [0, "f81TLk5A5D5b5+xYS9vyek"], [5, 10.803985595703125, 29.2]], -170, [4, -171, [0, "9bwJI8m+RLN7DQIRH5hFvs"]]], 4, 1, 4], [1, "2a/V7AQs5Bx6rYyT7GKSku", null, null, null, 1, 0], [1, 42, 45.838, 0]], [6, ["90tZ6Q1OhAL4rJ1kNTaJFT"]], [7, "btnNode", 9, [22, 10], [[2, -173, [0, "56oKN5AFhMrraFzzmrQ5up"], [5, 278, 46]], [42, 1, 1, 40, -174, [0, "49FsY2np9KNb5mu0v8eHzh"]]], [1, "7eLtDdIdhGo7eIu6hcBzea", null, null, null, 1, 0], [1, 0, -44.15300000000002, 0]], [7, "cellBg", 2, [-177], [[2, -175, [0, "c30yq76GJOrKvtOtLd2Q8b"], [5, 85, 85]], [12, 0, -176, [0, "4dlmIpq8ZJcZ7cIwA6Swkj"], 1]], [1, "f9zllk1tpFQbch2P692Teb", null, null, null, 1, 0], [1, -142.83, -116.751, 0]], [8, "skillName", 2, [[[2, -178, [0, "4bwGEE8zdBE6Zp6ySO0BJm"], [5, 99.97599792480469, 29.2]], -179, [4, -180, [0, "2f+BKit+1F1JKq9lN1lLem"]]], 4, 1, 4], [1, "87VaG3nJtCz78mDXge+s9c", null, null, null, 1, 0], [1, 0, -44.40399999999988, 0]], [28, "spFuwenBg", false, 2, [-183], [[2, -181, [0, "4fxfVTM29H35EiQ3BNiXLm"], [5, 81, 65]], [16, -182, [0, "b2/gY6suJFnYXYMG9dhPNe"], 2]], [1, "74+7v2n6RBY70fYdeD1Db1", null, null, null, 1, 0], [1, -143.636, -147.557, 0]], [7, "flag", 2, [-186], [[2, -184, [0, "1eGywx5xdAmaz9Qdy1gJNN"], [5, 28, 29]], [12, 0, -185, [0, "8aPSX53aVEqI8+fVuH6ABH"], 3]], [1, "d65GW6J7hDAKS7N2E0IBqJ", null, null, null, 1, 0], [1, -110.74, -149.064, 0]], [19, "skillLevel", 39, [[[2, -187, [0, "4e/6OQMTJMBrVuOukHZpGD"], [5, 21.1519775390625, 24.16]], -188, [4, -189, [0, "f2KjL+5GtKSaHcGyjxHlLw"]]], 4, 1, 4], [1, "64SuWQslZDgo0cKlFGYVwO", null, null, null, 1, 0]], [7, "bgTrim", 2, [-192], [[2, -190, [0, "b0H7KTqaZNeL0tE0VqHWQF"], [5, 177, 20]], [12, 2, -191, [0, "fdPNCngKhASbxdua97AE8u"], 4]], [1, "f5j0s88KJCtLUKZmuYeLTc", null, null, null, 1, 0], [1, -3.974, -85.68, 0]], [8, "costName", 41, [[[3, -193, [0, "dd8FWaC7pM37vC0T8EGBA3"], [5, 55.27796936035156, 25.2], [0, 0, 0.5]], -194, [4, -195, [0, "efsi7LCn1OCqIyh3aE/lby"]]], 4, 1, 4], [1, "62Llvq7qZLi7oPk8CC/hc8", null, null, null, 1, 0], [1, -73.81099999999998, -0.44500000000005, 0]], [8, "areaDes", 2, [[[3, -196, [0, "01THBbqF9JdL+2/MbdDsGX"], [5, 72.01799011230469, 25.2], [0, 0, 0.5]], -197, [4, -198, [0, "859EFUrRVNTaKvN0bDYUxD"]]], 4, 1, 4], [1, "e5sM58Q1dHF4MKIvYepwu+", null, null, null, 1, 0], [1, -78.921, -109.791, 0]], [7, "bgTrim2", 2, [-201], [[2, -199, [0, "dbMvGPEa1P5ajWzT3PpnXS"], [5, 177, 20]], [12, 2, -200, [0, "bfx5Gr7BtJbp2cWX65LsvY"], 5]], [1, "f1ZHlfsXNO/4l5Dmr2N3DD", null, null, null, 1, 0], [1, -3.974, -132.838, 0]], [8, "skillUseWay", 44, [[[3, -202, [0, "d1Nx0D+PZEj7n5mIfuSRr3"], [5, 36, 25.2], [0, 0, 0.5]], -203, [4, -204, [0, "64HVDJgVhBIaW7zo1Wn9nj"]]], 4, 1, 4], [1, "66m/gAQldDF76ZVfqkL8yz", null, null, null, 1, 0], [1, -71.45100000000002, -0.7849999999999682, 0]], [8, "cdDes", 2, [[[3, -205, [0, "e0DIjnC+lGY65xxBamLS2/"], [5, 99.64799499511719, 25.2], [0, 0, 0.5]], -206, [4, -207, [0, "660KSzxotBJK565pMb8aPi"]]], 4, 1, 4], [1, "d9PJPEV8RH7bAZH0rtAzrN", null, null, null, 1, 0], [1, -78.889, -156.558, 0]], [7, "bgTrim3", 2, [-210], [[3, -208, [0, "09KD5jeuVGn7qIGQ4iGz1r"], [5, 395, 30], [0, 0, 0.5]], [12, 2, -209, [0, "69If54PFxDS50HvHt/rYgT"], 12]], [1, "bdQ3TU3/NBwpGe/MFSDIQj", null, null, null, 1, 0], [1, -198.022, -197.942, 0]], [5, "activeTxt", 47, [[3, -211, [0, "38pftiYENOgryuUgOPPhBD"], [5, 91.82398986816406, 29.2], [0, 0, 0.5]], [24, "主动效果", 22, 22, 20, false, false, true, -212, [0, "b5q/TIRD9Na7ol+d+i+TQ8"], 11], [4, -213, [0, "cboUydQXJMY4DNHvsKzLvx"]]], [1, "a1ccbY/xJOTLpQU+3xii74", null, null, null, 1, 0], [1, 9.564999999999998, 0.39000000000010004, 0]], [7, "bgTrim4", 2, [-216], [[3, -214, [0, "cedbr7YKJLgYLtH5kcpaVI"], [5, 395, 30], [0, 0, 0.5]], [12, 2, -215, [0, "29ArIENAJH17VkBwhExMS+"], 14]], [1, "3b+N4t2/FNk6grSv1O8NdB", null, null, null, 1, 0], [1, -198.022, -346.62199999999996, 0]], [5, "passiveTxt", 49, [[3, -217, [0, "6f9hEIl+NBD41ZH5uHHpHL"], [5, 91.93399047851562, 29.2], [0, 0, 0.5]], [24, "被动效果", 22, 22, 20, false, false, true, -218, [0, "20i6lD06BOzLHX0ebdSxsU"], 13], [4, -219, [0, "2ehYK6Xc5KrK3LXiKkOb+f"]]], [1, "98Pn7O1s9LeKTR+0BaFQRj", null, null, null, 1, 0], [1, 9.575999999999993, 0.37399999999990996, 0]], [7, "nodeFuWenTitle", 8, [-222], [[3, -220, [0, "ed53tV4dBOEK/9QT6FMXEt"], [5, 395, 30], [0, 0, 0.5]], [12, 2, -221, [0, "3cp1jUfR1NuJQm9Q1AOs/z"], 16]], [1, "6chM96x55BiI2W9aCCKei/", null, null, null, 1, 0], [1, -198.022, 194.5, 0]], [5, "passiveTxt", 51, [[3, -223, [0, "fe4NmQ+RtFb7qNkxaAUGZI"], [5, 91.95599365234375, 29.2], [0, 0, 0.5]], [24, "技能符文", 22, 22, 20, false, false, true, -224, [0, "a8izZG6BZAN59ZOJ9NbSln"], 15], [4, -225, [0, "ba8lRwxJpH9pbfsWrAfZB0"]]], [1, "6drDuKflpOjapKkAnv2fQm", null, null, null, 1, 0], [1, 9.575999999999993, 0.37399999999990996, 0]], [19, "spQiyong", 4, [[[2, -226, [0, "ccbIiIhu5OwZXY0jAWNeiv"], [5, 29, 36]], -227, [45, "fuwen", "qiyong", -229, [0, "e26yQ2iNREG7nOpP8OYx05"], -228]], 4, 1, 4], [1, "46n6D6p+pPkaBm+IxpLtWz", null, null, null, 1, 0]], [8, "spUp", 4, [[[2, -230, [0, "b1RQcHlkpPGq/c3FQ1GuYd"], [5, 29, 36]], [45, "fuwen", "shengji_jt", -232, [0, "95gc4XdpRICZnm4piP6izC"], -231], -233], 4, 4, 1], [1, "ffRTSbAPVJyrEtewcxotUO", null, null, null, 1, 0], [1, 35.419, 8.022, 0]], [5, "lbLv", 4, [[2, -234, [0, "b6PAvTXoJNfbFsqH60Kxhv"], [5, 36.5799560546875, 26.68]], [24, "lv19", 18, 18, 18, false, false, true, -235, [0, "07rK+hHDZAJr4c64vfUxIM"], 19], [4, -236, [0, "c4X/lmaNJHxKESDD7GJ8eA"]]], [1, "4193P6Y1ZA5a8FyIsMh4xV", null, null, null, 1, 0], [1, 13.956999999999994, -32.918000000000006, 0]], [5, "lbName", 4, [[2, -237, [0, "2aoJpLzzFKErnhsD6t+zWF"], [5, 77.41996765136719, 29.2]], [40, "lbName", 20, 20, 20, false, false, true, -238, [0, "54KUjd8mVGLIDR6vqHD8zs"], [4, **********], 22], [4, -239, [0, "c6jdydheNA37P/zNkgADrt"]]], [1, "1amDkVQqBBx7NEmcxWGbUc", null, null, null, 1, 0], [1, 0, -55.14, 0]], [11, "rich", 31, [[2, -240, [0, "ccHqXP4OJBIJPBDaoB6y5f"], [5, 435, 27.72]], [74, 22, "333333", 1, 18, 435, false, false, -241, [0, "0fzcY2wm1IWJLN3sSFTFO0"], 26, 27, 28], [4, -242, [0, "54IM7QNIBO27RGcJ9/Mhdl"]]], [1, "603CTcY9FPRIIWNqgqF7A0", null, null, null, 1, 0]], [7, "spBg1", 5, [-245], [[2, -243, [0, "939p5oU8dHcqMUTpxCxsST"], [5, 299, 33]], [17, -244, [0, "87xRJERw9AipDsDHSIUZ3y"], 34, 35]], [1, "31bG6zqm1Oe4KBL9fkrvg4", null, null, null, 1, 0], [1, 0, -90.505, 0]], [8, "rich", 58, [[[2, -246, [0, "1dT8ZPmpJMPJTGU1GNm29Z"], [5, 330, 27.72]], -247, [4, -248, [0, "1eekUySuVH44tqnSMBE1Cn"]]], 4, 1, 4], [1, "8eqjuIIe9Bsb3dpg5PMTay", null, null, null, 1, 0], [1, 0, 3, 0]], [52, "noStudy", false, 3, [[[2, -249, [0, "27u+M9FApOLrdw95mi1rzb"], [5, 208.8399658203125, 54.4]], -250, [4, -251, [0, "b5kr03FbNCxr+12hOCJcs7"]]], 4, 1, 4], [1, "ccU+0fVPFAaKx2VKHkm49E", null, null, null, 1, 0], [1, 0, -343.6215, 0]], [5, "levelDes", 33, [[3, -252, [0, "f1RzH1TYdE37rmTRrz/CAi"], [5, 85.01799011230469, 29.2], [0, 1, 0.5]], [40, "等级要求：", 18, 18, 20, false, false, true, -253, [0, "90osLEnL5CEabuyIc6E1Qi"], [4, 4293195263], 40], [4, -254, [0, "0asEkAOA9F/KD/01SnzJpa"]]], [1, "806gRZQztMvp2i7Uf7zHM8", null, null, null, 1, 0], [1, -8.683, 0, 0]], [30, 0, {}, 21, [36, "90tZ6Q1OhAL4rJ1kNTaJFT", null, null, -257, [46, "41HQtQhOdBb4d+bX7vjeiF", 1, [[22, "moneyConsume", ["_name"], 34], [14, ["_lpos"], 34, [1, -86.55000000000001, 0, 0]], [14, ["_lrot"], 34, [3, 0, 0, 0, 1]], [14, ["_euler"], 34, [1, 0, 0, 0]], [22, false, ["_active"], -255], [14, ["_lpos"], -256, [1, -56.041969299316406, 0, 0]], [15, ["_lpos"], [6, ["1f1P/Fad9I3J64KBRIZlzx"]], [1, -52.375, 0, 0]], [15, ["_lpos"], [6, ["74/3cJZ0BJwLfbU/2PQ2iY"]], [1, 23.5, -2.2600000000000477, 0]], [15, ["_contentSize"], [6, ["d9hquB1jhA4Yd48Dt4PuY5"]], [5, 146.75, 30]], [15, ["_contentSize"], [6, ["72A1MqiM5J4oP1ChqgV/wC"]], [5, 99.75, 22.68]], [47, "<color=#ff0000>100<color=#ffffff>/1000</color></color>", ["_string"], [6, ["2eTrIr2/hNlLiCJSAJFt+D"]]], [22, true, ["_active"], 34]]], 41]], [30, 0, {}, 21, [36, "90tZ6Q1OhAL4rJ1kNTaJFT", null, null, -264, [46, "2eNsEqczZHnqkck1HMkxKY", 1, [[22, "itemConsume", ["_name"], -258], [14, ["_lpos"], -259, [1, 101.17499999999998, 0, 0]], [14, ["_lrot"], -260, [3, 0, 0, 0, 1]], [14, ["_euler"], -261, [1, 0, 0, 0]], [15, ["_contentSize"], [6, ["aeV5EW8XBOlq6/4kWyyQ2Z"]], [5, 70.5, 22.68]], [47, "<color=#ff0000>1<color=#ffffff>/1000</color></color>", ["_string"], [6, ["e7n7Nvz+lHSLLhehx04T3p"]]], [15, ["_lpos"], [6, ["1f1P/Fad9I3J64KBRIZlzx"]], [1, -37.75, 0, 0]], [15, ["_contentSize"], [6, ["d9hquB1jhA4Yd48Dt4PuY5"]], [5, 117.5, 30]], [22, false, ["_active"], -262], [14, ["_lpos"], -263, [1, -50.041969299316406, 0, 0]], [15, ["_lpos"], [6, ["dfBa3H9udDdYXQQsfoPfbV"]], [1, 23.5, -2.2600000000000477, 0]]]], 42]], [6, ["90tZ6Q1OhAL4rJ1kNTaJFT"]], [5, "img_redup", 10, [[2, -265, [0, "20qLLvOHJIDYyrxrsDmjOs"], [5, 29, 29]], [16, -266, [0, "ad2phKrVpKXYd56FYT8yeh"], 46]], [1, "52r72xzS5EHqJ6u85sUPPB", null, null, null, 1, 0], [1, 53.687, 14.445, 0]], [29, "maxLvSpr", false, 9, [[2, -267, [0, "f6bJut/XdJsb6cvTt2w1xW"], [5, 195, 48]], [16, -268, [0, "5e5MDMVTVMi4JeLs3ELHWY"], 48]], [1, "14d/T1eU5NJ51Vwo+OlPQs", null, null, null, 1, 0], [1, 0, 36.488, 0]], [5, "img_red", 11, [[2, -269, [0, "41J/tER+hCHI+Sv1mXA1QE"], [5, 29, 29]], [16, -270, [0, "3awxSwcRNBhIBD5SBSoq5r"], 54]], [1, "f9wikBknFDMY3Cigapfrn1", null, null, null, 1, 0], [1, 53.419, 18.555, 0]], [19, "skillIcon", 36, [[[2, -271, [0, "88920gYEVMB4AoNLLiFs5d"], [5, 85, 85]], -272], 4, 1], [1, "3fP0tgrvdJNb2egylTHHh/", null, null, null, 1, 0]], [51, "spFuwenIcon", 38, [[[2, -273, [0, "bcqpJ4cThLVY+Twi1guA29"], [5, 85, 85]], -274], 4, 1], [1, "4fXGfXJeZILY9hIJMe7oqO", null, null, null, 1, 0], [1, 0, -10, 0], [1, 0.55, 0.55, 1]], [32, "skillauto", 14, [[3, -275, [0, "0fprvvIQ9N671fcp5+bSPw"], [5, 113.4000015258789, 113.87999725341797], [0, 0.49744267273699144, 0.4996487785230529]], [72, "default", "animation", 0, -276, [0, "3aH96I5MBOvpIG9IFIrKC8"], 8]], [1, "9ewswqA45KkZSgkvp1iLFS", null, null, null, 1, 0], [1, 0.5, 0.5, 1]], [8, "activeDes", 2, [[[3, -277, [0, "d5lsmWaw5L37/Xe1nXp8h+"], [5, 363, 45.199999999999996], [0, 0.5, 1]], -278], 4, 1], [1, "57Fox/M09NMIciZleJkIVU", null, null, null, 1, 0], [1, -10, -221.258, 0]], [7, "Node", 8, [17], [[2, -279, [0, "2cf1nzYpJDJLCA99meldfI"], [5, 100, 120]]], [1, "2et5CLql1I25Jps+ZPOJLu", null, null, null, 1, 0], [1, 0, 109.5, 0]], [54, "spBg", 4, [[2, -280, [0, "ac+rTFYhlEjJxbrOW2iN1p"], [5, 88, 93]], [17, -281, [0, "8fgYqCQc5KFbHILLvuKB7z"], 17, 18]], [1, "faOSZawQNChJmykdA9r78r", null, null, null, 1, 0], [1, -2, -2, 0], [1, 1.25, 1.25, 1]], [32, "spItem", 4, [[2, -282, [0, "52TRQ57GxFYLAuOa4Iheua"], [5, 51, 61]], [58, -283, [0, "68Tcm5FNNMQJtgJPIplFMs"]]], [1, "a5nwe1UfhBbKuskuiip6Fh", null, null, null, 1, 0], [1, 1.2, 1.2, 1]], [5, "spHint", 4, [[2, -284, [0, "c3WZkJRZxDhZLavUkvGdxO"], [5, 65, 25]], [17, -285, [0, "2beXsMD+FMcaO5BUwNK77s"], 20, 21]], [1, "1cSbojiHpKT5amg8hipNk5", null, null, null, 1, 0], [1, -5.204000000000008, 30.336000000000013, 0]], [11, "spAnim", 4, [[2, -286, [0, "bbUuXZ2lpOO5yc4HxFm9AN"], [5, 29, 36]], [26, -287, [0, "7aXsXuaWNNToeCHE9Ek4Lh"]]], [1, "55NeYlguhM+ZUpScQNlOdg", null, null, null, 1, 0]], [5, "img_red", 4, [[2, -288, [0, "e6pANEwsBHAYjSkPAgnRgb"], [5, 29, 29]], [16, -289, [0, "f8v1u2p6JNj4Zp7Beo3tcP"], 23]], [1, "24ptd7BCNMv50Ski3nnHR3", null, null, null, 1, 0], [1, 19.274, 29.056, 0]], [5, "Sprite", 5, [[2, -290, [0, "66VVo7RrVN4YriQ8+ZTxbo"], [5, 447, 230]], [59, 1, 0, -291, [0, "76V0KAP7ZBeKJ0Z8eLolkY"], 24, 25]], [1, "d5ppMuEZNHjK3KFjMA5XT5", null, null, null, 1, 0], [1, -2.5, 0, 0]], [5, "spline1", 5, [[2, -292, [0, "a3phVHpg5BsIxWbonTQ9K5"], [5, 399, 5]], [17, -293, [0, "49dBpucvxMyKzje4QSo/AX"], 30, 31]], [1, "54UIbBktVPAog8y+YxdGJM", null, null, null, 1, 0], [1, 0, 120, 0]], [5, "spline2", 5, [[2, -294, [0, "41rKJFBcdB9pS1HepLF+BK"], [5, 399, 5]], [17, -295, [0, "9cPhYec8tLRJ3zHJ48OTY/"], 32, 33]], [1, "52FD8vwslEeaw4/+7xmImM", null, null, null, 1, 0], [1, 0, -122.345, 0]], [5, "spline3", 20, [[2, -296, [0, "42PNeeNgZByoUi2ctuayWZ"], [5, 40, 47]], [17, -297, [0, "2axD5BHylM27xqo04Abv5x"], 37, 38]], [1, "1bpqshuLJDHJqPg5VKxeEM", null, null, null, 1, 0], [1, 0, 43.499, 0]], [5, "Label", 22, [[2, -298, [0, "92w0V8UBVIUrOZ8t46X50d"], [5, 100, 40]], [18, "返回", 24, 24, 1, false, false, true, -299, [0, "1eAGHR/xZPW6zopSQFUgEV"], 43]], [1, "f7PEC1bPZKL5qcto6OFhBF", null, null, null, 1, 0], [1, -1.929, 0, 0]], [11, "Label", 10, [[2, -300, [0, "ffcRihRHJKpJq1XmRTel9/"], [5, 100, 40]], [18, "升级", 24, 24, 1, false, false, true, -301, [0, "cfGcNLeNdFW49Q7gjIWW9r"], 45]], [1, "35UyzT3rBMOLXk2UdEJEi8", null, null, null, 1, 0]], [11, "upLabel", 23, [[2, -302, [0, "cdcqtf29hJ8rmmedRpr/Z5"], [5, 100, 40]], [18, "装配", 24, 24, 1, false, false, true, -303, [0, "1dqgC1VM1CfYSEQgBPwFcJ"], 49]], [1, "f39oAQNLhKfY9pMnh6GwLC", null, null, null, 1, 0]], [11, "Label", 24, [[2, -304, [0, "61QFETPn5CN4KKijFSM03F"], [5, 100, 40]], [18, "卸下", 24, 24, 1, false, false, true, -305, [0, "6aHaFdWeNG6oIRxjTCeZSj"], 51]], [1, "fa5GXHEwtNaYBrdf94zi8C", null, null, null, 1, 0]], [11, "Label", 11, [[2, -306, [0, "e6TH5OAodLt7murubPLeKQ"], [5, 100, 40]], [18, "升级", 24, 24, 1, false, false, true, -307, [0, "04Ojdm+kVDKa0LebdCUHBM"], 53]], [1, "68Pb9LiQhGvoOYJ8q2ULV8", null, null, null, 1, 0]], [11, "Label", 12, [[2, -308, [0, "a8v26tcUNIcrX0UkCIfCOE"], [5, 100, 40]], [18, "符文升级", 24, 24, 1, false, false, true, -309, [0, "30Ub7CyoJLyKiHp8YdzTM1"], 56]], [1, "1fsrslcWVK3r57/9MEhSiM", null, null, null, 1, 0]], [5, "img_red", 12, [[2, -310, [0, "b3ydRrMeFEZ7Q8cKiN77CQ"], [5, 29, 29]], [16, -311, [0, "64Al3NJaVN7qoD4dNuHUzm"], 57]], [1, "93v/uPso5MMr23nx2UhiBa", null, null, null, 1, 0], [1, 53.419, 18.555, 0]], [19, "upSp", 1, [[[55, -312, [0, "6clxZMWsBOEpjEVZhJ/Gzf"]], -313], 4, 1], [1, "02sOE1wDtJMqY6c0Irp+mA", null, null, null, 1, 0]], [87, 1, [0, "559gi9ET5GX5dXBDHd70TN"], 9, 6, 66, 21, 67, 65, 8, 17, 32, 20, 26], [33, 0, 68, [0, "6azWcj0H9HWp4p1ln95nXp"]], [67, "英勇打击", 24, 24, 20, false, false, true, 37, [0, "abk+hpS+RPtrQrxmkOXjdb"], [4, 4278242302]], [33, 0, 69, [0, "8d1YjmhflFR51rvBtNY4JD"]], [37, "10", 16, 16, 16, false, false, true, 40, [0, "7bFyz7BcdMkoKtYnLF81ZV"]], [38, "10怒气", 18, 18, 20, false, false, 42, [0, "f8dpJdYtZGZIPLtqS57itW"]], [39, "近战范围", 18, 18, 20, false, false, 43, [0, "fatc0e3txEU5LLUMhKtpwD"], [4, 4280495954]], [38, "瞬发", 18, 18, 20, false, false, 45, [0, "c2N7Phtn1EjKJG57ERAALZ"]], [39, "5秒冷却时间", 18, 18, 20, false, false, 46, [0, "f8fHHcNUhD1bY4aHG2NFqX"], [4, 4280495954]], [10, 3, 25, [0, "95+v9bg7FNYotn07G8FPka"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onHandSkillBtnClick", 1]], [4, 4292269782], 25], [10, 3, 14, [0, "d7muBEmctJnaTHaFpTVxS/"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onAutoSkillBtnClick", 1]], [4, 4292269782], 14], [10, 3, 15, [0, "70N0iugn5Nq4g7xa+xEh86"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onClickCloseBtn", 1]], [4, 4292269782], 15], [73, 20, "<outline color=black width=2>给周围30码范围的友方单位施加效果【奥术智慧】，使其法术强度提高s1，持续600秒。</outline>", 18, 363, false, 71, [0, "dfx7oEMO1PqbLKEjiyJ8nO"]], [68, "攻击强度 +100", 0, 18, 18, 20, 3, false, false, true, 28, [0, "d5Vl4ja1lB0oDYvSdKdjZZ"]], [84, 7, [0, "bc94xYQfhDsbCONhGE0aR8"], 28, [41]], [26, 53, [0, "57/Y0zgUJDd4Zng0/y6ZFo"]], [26, 54, [0, "13malLIZ1LBJKy2BF2Oe1l"]], [85, false, 1, 29, [0, "b6c1EuNVNBH6T+gwoabHUC"], 30, [41]], [75, 22, "1阶 技能效果提升10%", 1, 18, 330, false, false, 59, [0, "abQoL1iD1C/4QG4oryn8og"]], [71, "角色等级达到1级自然习得", 18, 18, false, false, true, 60, [0, "5cM0UcFL9B75X9sgDJOL3J"], [4, 4286085375]], [37, "1", 18, 18, 20, false, false, true, 33, [0, "9cGFX9r4BOMoZ7B9drfZt2"]], [6, ["67p82bNlNGdJEXkHslWfNb"]], [6, ["67p82bNlNGdJEXkHslWfNb"]], [10, 3, 22, [0, "d0kCgiJ3JLAYzGRikpMbKb"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onBackBtnClick", 1]], [4, 4292269782], 22], [10, 3, 10, [0, "5aK2aFglRHVLFst7y67O6J"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onSkillUpBtnClick", 1]], [4, 4292269782], 10], [10, 3, 23, [0, "37F3X73tJAk6++W7jOCZEs"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onUpBtnClick", 1]], [4, 4292269782], 23], [10, 3, 24, [0, "70B+O9z5ZA6oQSfQ1uaF1a"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onDownBtnClick", 1]], [4, 4292269782], 24], [10, 3, 11, [0, "d0J6h89N9HCp63fmUliv5O"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onUpgradeBtnClick", 1]], [4, 4292269782], 11], [10, 3, 12, [0, "f3ntq3bU9NWYrv98iMj06g"], [[9, "ce6f4fRQZZL97fTZqM2aYl8", "onUpgradeFuWen", 1]], [4, 4292269782], 12], [26, 89, [0, "73wWV6fitHIrCQ2jEwBUAg"]]], 0, [0, -1, 63, 0, -2, 62, 0, 9, 62, 0, 10, 90, 0, 9, 63, 0, 10, 90, 0, 7, 1, 0, 0, 1, 0, -2, 90, 0, 0, 1, 0, -1, 13, 0, -2, 3, 0, -3, 89, 0, 0, 2, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -4, 39, 0, -5, 41, 0, -6, 43, 0, -7, 44, 0, -8, 46, 0, -9, 25, 0, -10, 26, 0, -11, 14, 0, -12, 15, 0, -13, 47, 0, -14, 71, 0, -15, 49, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -2, 7, 0, -3, 8, 0, -4, 60, 0, -5, 9, 0, -6, 6, 0, 0, 4, 0, -1, 53, 0, -2, 73, 0, -3, 74, 0, -4, 54, 0, -5, 55, 0, -6, 75, 0, -7, 56, 0, -8, 76, 0, -9, 77, 0, 0, 5, 0, -1, 78, 0, -2, 29, 0, -3, 79, 0, -4, 80, 0, -5, 58, 0, -6, 32, 0, -7, 20, 0, 0, 6, 0, 0, 6, 0, -1, 23, 0, -2, 24, 0, -3, 11, 0, -4, 12, 0, 0, 7, 0, 0, 7, 0, 15, 27, 0, 0, 7, 0, -4, 104, 0, 0, 7, 0, -1, 16, 0, 0, 8, 0, 0, 8, 0, -1, 51, 0, -2, 72, 0, 0, 9, 0, -1, 33, 0, -2, 21, 0, -3, 35, 0, -4, 66, 0, 0, 10, 0, 0, 10, 0, -3, 114, 0, -1, 83, 0, -2, 65, 0, 0, 11, 0, 0, 11, 0, -3, 117, 0, -1, 86, 0, -2, 67, 0, 0, 12, 0, 0, 12, 0, -3, 118, 0, -1, 87, 0, -2, 88, 0, 0, 13, 0, 0, 13, 0, 16, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, -3, 100, 0, -1, 70, 0, 0, 15, 0, 0, 15, 0, -3, 101, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 27, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, -1, 30, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, -1, 81, 0, 0, 21, 0, 0, 21, 0, -1, 62, 0, -2, 63, 0, 0, 22, 0, 0, 22, 0, -3, 113, 0, -1, 82, 0, 0, 23, 0, 0, 23, 0, -3, 115, 0, -1, 84, 0, 0, 24, 0, 0, 24, 0, -3, 116, 0, -1, 85, 0, 0, 25, 0, 0, 25, 0, -3, 99, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, -1, 28, 0, 0, 28, 0, -2, 103, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, -3, 107, 0, 0, 30, 0, 0, 30, 0, -1, 31, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, -1, 57, 0, 0, 32, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, -2, 110, 0, 0, 33, 0, -1, 61, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, -1, 68, 0, 0, 37, 0, -2, 92, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, -1, 69, 0, 0, 39, 0, 0, 39, 0, -1, 40, 0, 0, 40, 0, -2, 94, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, -1, 42, 0, 0, 42, 0, -2, 95, 0, 0, 42, 0, 0, 43, 0, -2, 96, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, -1, 45, 0, 0, 45, 0, -2, 97, 0, 0, 45, 0, 0, 46, 0, -2, 98, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, -1, 48, 0, 0, 48, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, -1, 50, 0, 0, 50, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, -1, 52, 0, 0, 52, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, -2, 105, 0, 11, 105, 0, 0, 53, 0, 0, 54, 0, 11, 106, 0, 0, 54, 0, -3, 106, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, -1, 59, 0, 0, 59, 0, -2, 108, 0, 0, 59, 0, 0, 60, 0, -2, 109, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 61, 0, 3, 111, 0, 3, 111, 0, 7, 62, 0, 3, 64, 0, 3, 64, 0, 3, 64, 0, 3, 64, 0, 3, 112, 0, 3, 112, 0, 7, 63, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, -2, 91, 0, 0, 69, 0, -2, 93, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, -2, 102, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, 0, 88, 0, 0, 89, 0, -2, 119, 0, 17, 1, 2, 5, 3, 4, 5, 17, 5, 5, 8, 10, 5, 35, 17, 5, 72, 18, 5, 29, 22, 5, 35, 90, 18, 118, 90, 19, 93, 90, 20, 108, 90, 21, 107, 90, 22, 119, 90, 23, 101, 90, 24, 110, 90, 25, 114, 90, 26, 113, 90, 27, 116, 90, 28, 115, 90, 29, 117, 90, 30, 98, 90, 31, 97, 90, 32, 96, 90, 33, 95, 90, 34, 104, 90, 35, 103, 90, 36, 102, 90, 37, 109, 90, 38, 94, 90, 39, 91, 90, 40, 99, 90, 41, 100, 90, 42, 92, 313], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 92, 94, 95, 96, 97, 98, 101, 102, 102, 103, 108, 108, 109, 110], [1, 1, 1, 1, 1, 1, 1, 1, 12, 1, 1, 2, 1, 2, 1, 2, 1, 1, 4, 2, 1, 4, 2, 1, 1, 4, 2, 6, 13, 1, 1, 4, 1, 4, 1, 4, 2, 1, 4, 2, 2, 8, 8, 2, 1, 2, 1, 1, 1, 2, 1, 2, 1, 2, 1, 1, 2, 1, 1, 1, 2, 2, 2, 2, 2, 2, 14, 2, 6, 2, 2, 6, 2, 2], [11, 12, 13, 14, 6, 6, 7, 15, 16, 7, 8, 0, 3, 0, 3, 0, 3, 17, 1, 0, 18, 1, 0, 2, 19, 4, 0, 0, 1, 20, 9, 4, 9, 4, 21, 1, 0, 22, 1, 0, 0, 23, 24, 0, 25, 0, 2, 5, 26, 0, 27, 0, 28, 0, 2, 5, 0, 2, 5, 29, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 10, 10, 0, 0]]