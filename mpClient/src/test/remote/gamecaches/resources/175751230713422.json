[1, ["61NXJOyyZNU6ZGh0FJkMv2", "e37aI6XhpCC4o1p98aJ2DI@6c48a", "2enw6dg1JAa5xhWIDgaiWv@41850", "54DKo7/p1AJ6l1t7elinpS", "fe2zYK53VN/KGNtvA+515K@05d3b", "fe2zYK53VN/KGNtvA+515K@1faec", "78fanDWc5E0biRuSEU/8IL@8b9a4", "2enw6dg1JAa5xhWIDgaiWv@5c9aa", "2enw6dg1JAa5xhWIDgaiWv@bbfb5", "2enw6dg1JAa5xhWIDgaiWv@0e5a4", "2enw6dg1JAa5xhWIDgaiWv@d3732", "2enw6dg1JAa5xhWIDgaiWv@a0708", "fe2zYK53VN/KGNtvA+515K@84211", "65nnJ3SlJOoYRwNWpKye8V@f9941", "2enw6dg1JAa5xhWIDgaiWv@46320", "2enw6dg1JAa5xhWIDgaiWv@d5b15", "2enw6dg1JAa5xhWIDgaiWv"], ["node", "_spriteFrame", "_font", "target", "source", "_textureSource", "root", "_parent", "btn_buy", "img_num", "img_icon", "<PERSON><PERSON><PERSON>", "_target", "data", "asset", "_backgroundImage", "_atlas"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lscale"], 0, 4, 9, 1, 2, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_cacheMode", "_outlineWidth", "_overflow", "_horizontalAlign", "node", "__prefab", "_color", "_font"], -8, 1, 4, 5, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_lpos", "_parent", "_children"], 1, 12, 4, 5, 1, 2], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], "cc.SpriteFrame", ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingTop", "_paddingBottom", "_spacingY", "_spacingX", "node", "__prefab"], -3, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "clickEvents"], 2, 1, 4, 5, 1, 9], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["e2a38Y70E1FC4wSqt/E6dYA", ["node", "__prefab", "<PERSON><PERSON><PERSON>", "img_icon", "img_num", "btn_buy"], 3, 1, 4, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["cc.EditBox", ["_string", "_inputMode", "_maxLength", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>"], 0, 1, 4, 1, 1], ["aca95a1mutKK7YfLD7G/yhk", ["showMax", "node", "__prefab", "btnAdd", "btnReduce", "btnMax", "btnMin", "editBox"], 2, 1, 4, 1, 1, 1, 1, 1]], [[9, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 2, 1], [16, 0, 2], [2, 0, 5, 2, 3, 4, 2], [3, 0, 2, 3, 4, 2], [0, 0, 5, 6, 4, 3, 7, 2], [0, 0, 5, 4, 3, 7, 2], [15, 0, 1, 2, 3, 4, 2], [19, 0, 1, 1], [6, 0, 1, 2, 3, 4, 2], [7, 0, 1, 2, 3, 1], [3, 1, 0, 2, 3, 4, 3], [3, 2, 3, 4, 1], [1, 0, 1, 2, 3, 4, 5, 7, 6, 8, 11, 12, 14, 10], [22, 0, 1, 2, 2], [0, 0, 6, 4, 3, 2], [8, 0, 2], [0, 0, 5, 6, 4, 3, 2], [0, 0, 5, 4, 3, 2], [0, 1, 2, 5, 3, 3], [0, 0, 5, 4, 3, 7, 8, 2], [2, 0, 6, 2, 3, 4, 2], [2, 0, 5, 6, 2, 3, 4, 2], [2, 0, 1, 5, 2, 3, 4, 3], [10, 0, 1, 2, 3, 4, 4], [11, 0, 1, 2, 3, 4, 5, 1], [12, 0, 1, 2, 3, 4, 5, 3], [14, 0, 1, 2, 3, 4, 5, 4], [5, 0, 1, 2, 3, 4, 6, 7, 6], [5, 0, 1, 6, 7, 3], [5, 0, 1, 5, 6, 7, 4], [3, 1, 0, 2, 3, 3], [17, 0, 1, 1], [6, 0, 1, 2, 2], [6, 0, 1, 2, 5, 3, 4, 2], [18, 0, 1, 2, 3], [1, 0, 1, 2, 3, 4, 5, 7, 6, 8, 11, 12, 13, 14, 10], [1, 0, 1, 2, 3, 9, 4, 5, 6, 11, 12, 9], [1, 0, 10, 1, 2, 3, 9, 4, 11, 12, 13, 8], [1, 0, 1, 2, 3, 4, 5, 7, 6, 11, 12, 9], [20, 0, 1, 2, 2], [21, 0, 1, 2, 3], [23, 0, 1, 2, 3, 4, 5, 6, 4], [24, 0, 1, 2, 3, 4, 5, 6, 7, 2]], [[[[17, "ArenaBuyCount"], [16, "ArenaBuyCount", [-20, -21, -22], [[2, -13, [0, "68dlPzUqpM4bwcv1lbeSva"], [5, 750, 1334]], [25, 45, 750, 1334, -14, [0, "22mQPOs79LALBADNwlmM5E"]], [26, -19, [0, "b2JvtlUaVGcKZ39uuUqWn/"], -18, -17, -16, -15]], [27, "c46/YsCPVOJYA4mWEpNYRx", null, -12, 0, [[8, ["btnAdd"], -3, [3, ["a2VQdXye9E+LYprmfhlwqn"]], -2, [3, ["416R/m4XhCyKRjgF1a1Yhq"]]], [8, ["btnReduce"], -5, [3, ["a2VQdXye9E+LYprmfhlwqn"]], -4, [3, ["47yvvSW3RDT5zapaRYhk3g"]]], [8, ["btnMax"], -7, [3, ["a2VQdXye9E+LYprmfhlwqn"]], -6, [3, ["faT5g4UP5BH6Gzuxj26szK"]]], [8, ["btnMin"], -9, [3, ["a2VQdXye9E+LYprmfhlwqn"]], -8, [3, ["cb73HMINFNkb2sm3aayyYi"]]], [8, ["editBox"], -11, [3, ["a2VQdXye9E+LYprmfhlwqn"]], -10, [3, ["f0EofCfZ1IzISFQi0DuX3+"]]]], [-1]]], [22, "NumberStepper", [-25, -26, -27, -28, -29], [[[2, -23, [0, "57wRPgccVIjIIVL/NAKM2J"], [5, 150, 40]], -24], 4, 1], [1, "e4fx1Xf6tCGI8LeKD6/Cm6", null, null, null, 1, 0], [1, 48.922, 43, 0]], [18, "node", 1, [-34, -35], [[2, -30, [0, "28XXvC+ZBDpKpCPRshPPLv"], [5, 490, 390.1]], [29, 1, 2, 5, 40.1, 5, -31, [0, "79PODI76hBLqmlcRtFV6Ij"]], [12, 1, 0, -32, [0, "6cPzOUmT5OFKuJUvljCMxh"], 15], [33, -33, [0, "00icL9OkdF+Lmvw2a4bQeE"]]], [1, "50dWVwEqxCZbvavX+rsnEI", null, null, null, 1, 0]], [6, "nodeBuy", 3, [2, -38, -39, -40], [[2, -36, [0, "375cN+3LFKRpqz+i1bgzqX"], [5, 460, 200]], [12, 1, 0, -37, [0, "65oCKzJ/lGNpWkgzAV3xrM"], 14]], [1, "d2u9u1+uhD8oXn4Iv4Yv1s", null, null, null, 1, 0], [1, -2, -54.94999999999999, 0]], [23, "EditBox", 2, [-44, -45], [[[2, -41, [0, "a8OklcGBZE9JvdrhGtj31v"], [5, 158, 38]], [12, 1, 0, -42, [0, "44hOxg3o1KVrwD495SX3rY"], 6], -43], 4, 4, 1], [1, "4cRjPDLkdIR7qIJBIIVxeU", null, null, null, 1, 0], [1, -16.082, 0, 0]], [6, "btn_buy", 4, [-49], [[2, -46, [0, "1dvL7R0TlFKb71DPo6p9Jg"], [5, 131, 51]], [13, -47, [0, "b6/gHU2odA3beOHnEQJsVr"], 13], [34, 3, -48, [0, "3aGuFJoMZC664FpOhAr6k3"]]], [1, "4fE7a/tFpFJZBn27EcZkec", null, null, null, 1, 0], [1, 0, -59.917, 0]], [6, "itemInfo", 3, [-51, -52, -53], [[2, -50, [0, "13DMAqkNpMsLWcQS1Q3AMt"], [5, 480, 140]]], [1, "e1WqbbcNpKwY506e6A1Gnl", null, null, null, 1, 0], [1, 0, 120.05000000000001, 0]], [16, "Layout", [-56, -57], [[2, -54, [0, "e3DMPcJZ1EzYaKxH5lb0ru"], [5, 228.0239715576172, 40]], [30, 1, 1, -55, [0, "4eg997Z05FhKtbqM82E3tD"]]], [1, "6bhN0f/KdHIb3KfbgI0Ddj", null, null, null, 1, 0]], [4, "btnAdd", 2, [[[2, -58, [0, "3eI7BO8uFGbJcT4/9JL8kX"], [5, 42, 40]], [5, 2, -59, [0, "1agXMIsvVBSJOr4oZwtlY2"], 7], -60], 4, 4, 1], [1, "8fl+fnc4hBva1f8wn7nmkB", null, null, null, 1, 0], [1, 96.933, 0, 0]], [4, "btnMax", 2, [[[2, -61, [0, "39XGU5F+VLlYW6mVeFiOvd"], [5, 42, 40]], [5, 2, -62, [0, "ab4thtitpEIKYya4rqwk3u"], 8], -63], 4, 4, 1], [1, "5fbb4OH0RAlaJ9uoua//yQ", null, null, null, 1, 0], [1, 146.365, 0, 0]], [4, "btnReduce", 2, [[[2, -64, [0, "42ZAn9LixG6ZNfM9Z5PXq2"], [5, 42, 40]], [5, 2, -65, [0, "a0nIfgqN1F7ZXjaYLhw6GZ"], 9], -66], 4, 4, 1], [1, "20ElVFSsRESZjqYQdHi4Zq", null, null, null, 1, 0], [1, -131.196, 0, 0]], [4, "btnMin", 2, [[[2, -67, [0, "76qyC50jZGgow4tM2ZaM2H"], [5, 42, 40]], [5, 2, -68, [0, "fbFiBfoyNJyZVzRJmms3U+"], 10], -69], 4, 4, 1], [1, "3dEAl8ZcxLIZmX4KrjJKIG", null, null, null, 1, 0], [1, -182.325, 0, 0]], [6, "Layout", 4, [-72, -73], [[2, -70, [0, "2b0IGJ4mdNZqMIGlz3kXS5"], [5, 55.65599060058594, 30]], [31, 1, 1, 5, -71, [0, "afPP++PDhH9IBch2mA7hKm"]]], [1, "301pV05P5JQaJsaIcNJ4dZ", null, null, null, 1, 0], [1, -0.6480000000000246, -2.5919999999999845, 0]], [7, "btnClose", 1, [[2, -74, [0, "fbEk0ow6JCgatnRarwdAjD"], [5, 58, 57]], [5, 2, -75, [0, "3awlueNK9FTJnrqSIwNhh8"], 16], [35, 3, -77, [0, "bdVCo682NBpK3i1ucGKuDu"], [[36, "86190xCGYZO6brCIAumb5pY", "onClickCloseBtn", 1]], [4, 4292269782], -76]], [1, "7398Smz99JLaztDcGSGPlZ", null, null, null, 1, 0], [1, 233.288, 193.83299999999997, 0]], [3, ["49+PNfEAdKea7Nn8OqLKwZ"]], [6, "itemNotEnough_topbg", 7, [8], [[2, -78, [0, "1cCMYfgNJJDoewgM/OevEJ"], [5, 458, 46]], [13, -79, [0, "b4QW6g5fpEP7Q+ykg692W4"], 3]], [1, "0bIHKyCglPJ5YX3T2WFdVT", null, null, null, 1, 0], [1, -3, 45.19799999999999, 0]], [7, "itemName", 8, [[2, -80, [0, "f4eDzEOeNAv4ErNfXnNcbC"], [5, 173.99998474121094, 37.5]], [37, "竞技场挑战次数", 24, 24, 25, false, false, 1, true, 3, -81, [0, "8d4re+t8RBY7HoBkAW/qEj"], [4, 4279627566], 1], [9, -82, [0, "f0QCx6NDdAIoRItcIUEjDE"]]], [1, "d9CrJhuvhI0IOHA4H1cLJJ", null, null, null, 1, 0], [1, -27.011993408203125, 0, 0]], [7, "txt", 8, [[2, -83, [0, "2bNUR7kI1F/psgDQ8qge3o"], [5, 54.02398681640625, 37.5]], [14, "不足", 24, 24, 25, false, false, 1, true, 3, -84, [0, "66qnsbTPxO5LLgWaiZlrrC"], 2], [9, -85, [0, "cfxfYkdW9OmKfzDHCDEPwK"]]], [1, "524jNcw+5OVrdcYKGbSGjg", null, null, null, 1, 0], [1, 86.99999237060547, 0, 0]], [7, "txt", 4, [[2, -86, [0, "66iXjL2EZI9LeG0mFqWo/7"], [5, 54, 37.5]], [14, "数量", 24, 24, 25, false, false, 1, true, 3, -87, [0, "55iUky7N9KB5MFvG76H6th"], 11], [9, -88, [0, "15gNwhAsNCZ7sZXDD9YRid"]]], [1, "88hvyhD/JPdLCIveQv/KEM", null, null, null, 1, 0], [1, -191.311, 45, 0]], [4, "img_num", 13, [[[11, -89, [0, "39/jB+oMFHOb4vFIJ3CB5d"], [5, 20.655990600585938, 35.5], [0, 0, 0.5]], -90, [9, -91, [0, "7dH+x33t9Bp6W5AAG7Rc53"]]], 4, 1, 4], [1, "6eOf1Sg3lBG4S2C3MPSEn2", null, null, null, 1, 0], [1, 7.172004699707031, -10, 0]], [19, "txt", 6, [[2, -92, [0, "5c9SqeHFpCdYlmnZ1kEajD"], [5, 53.97599792480469, 37.5]], [14, "购买", 24, 24, 25, false, false, 1, true, 3, -93, [0, "1c741GfApJJqR2UM5m17x9"], 12], [9, -94, [0, "60SX090U5GTqu5PivcsYU5"]]], [1, "7f/cC5c+1M+beQuDZB5Tlz", null, null, null, 1, 0]], [20, 0, {}, 1, [28, "49+PNfEAdKea7Nn8OqLKwZ", null, null, -95, [41, "58N7zzA0RJbY9nLQuH41r5", 1, [[42, "bgClose", ["_name"], 15], [15, ["_lpos"], 15, [1, 0, 0, 0]], [15, ["_lrot"], 15, [3, 0, 0, 0, 1]], [15, ["_euler"], 15, [1, 0, 0, 0]]]], 0]], [7, "itemNotEnough_tuoyuan", 7, [[2, -96, [0, "d2KzX974ZJKajHbXSR4i1I"], [5, 145, 32]], [13, -97, [0, "ecvXeElSVDm7SqXrEzR9pG"], 4]], [1, "d4P8q7N55D4L1hY5+dJjJS", null, null, null, 1, 0], [1, 0.21100000000001273, -37.923, 0]], [21, "icon", 7, [[2, -98, [0, "9eAuWW6GNK2JwKMeg1Gjp3"], [5, 45, 45]], [5, 2, -99, [0, "11m5NbjUdFnpc7l0qrXzo7"], 5]], [1, "25g84jF0lGfokzoaUV8O8l", null, null, null, 1, 0], [1, -5, -18.928999999999974, 0], [1, 1.3, 1.3, 1]], [4, "TEXT_LABEL", 5, [[[11, -100, [0, "a1nhlWA0FBh7/iB8JdHHJI"], [5, 156, 38], [0, 0, 1]], -101], 4, 1], [1, "c5KHl10XhKDoMc0IU/ieT9", null, null, null, 1, 0], [1, -77, 19, 0]], [24, "PLACEHOLDER_LABEL", false, 5, [[[11, -102, [0, "26Ns4HgAlNyq2hk4TAS5xe"], [5, 156, 38], [0, 0, 1]], -103], 4, 1], [1, "ceLrITr65LCK4+AAxMEHkX", null, null, null, 1, 0], [1, -77, 19, 0]], [4, "img_icon", 13, [[[2, -104, [0, "dd+xpWknVB0Lrd0Hz7zl5c"], [5, 30, 30]], -105], 4, 1], [1, "34dX+SycpAW7hceHjxHun8", null, null, null, 1, 0], [1, -12.827995300292969, -10, 0]], [38, "1", 24, 24, 28, 1, false, false, true, 25, [0, "b4jUT95jNLYqFUn0mYL8RK"]], [39, "", 0, 20, 20, 29, 1, false, 26, [0, "cdN/cMHRVFyKyHMrj4KGDR"], [4, 4290493371]], [43, "1", 2, 8, 5, [0, "e7QrfXWw5JBpCHyYAHkqqf"], 28, 29], [10, 3, 9, [0, "3es2kiYGFNbqLMBGJyIVrW"], [4, 4292269782], 9], [10, 3, 10, [0, "bau8bvhCZGvYL9Ya1gKPDR"], [4, 4292269782], 10], [10, 3, 11, [0, "74JMcucJZHmKv39sOqdlkz"], [4, 4292269782], 11], [10, 3, 12, [0, "92SjnWFC9Jib4LskOisB67"], [4, 4292269782], 12], [44, 1, 2, [0, "69OznIY49EYo57Y7EsOl1p"], 31, 33, 32, 34, 30], [32, 1, 0, 27, [0, "f9m+e2oORJgYf3VDYBXpB5"]], [40, "0", 24, 24, 25, false, false, 1, true, 20, [0, "d0TxF1i9VFiZC0Q06z8nhB"]]], 0, [0, -1, 22, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 3, 2, 0, 4, 2, 0, 6, 1, 0, 0, 1, 0, 0, 1, 0, 8, 6, 0, 9, 37, 0, 10, 36, 0, 11, 35, 0, 0, 1, 0, -1, 22, 0, -2, 3, 0, -3, 14, 0, 0, 2, 0, -2, 35, 0, -1, 5, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 7, 0, -2, 4, 0, 0, 4, 0, 0, 4, 0, -2, 19, 0, -3, 13, 0, -4, 6, 0, 0, 5, 0, 0, 5, 0, -3, 30, 0, -1, 25, 0, -2, 26, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 21, 0, 0, 7, 0, -1, 16, 0, -2, 23, 0, -3, 24, 0, 0, 8, 0, 0, 8, 0, -1, 17, 0, -2, 18, 0, 0, 9, 0, 0, 9, 0, -3, 31, 0, 0, 10, 0, 0, 10, 0, -3, 32, 0, 0, 11, 0, 0, 11, 0, -3, 33, 0, 0, 12, 0, 0, 12, 0, -3, 34, 0, 0, 13, 0, 0, 13, 0, -1, 27, 0, -2, 20, 0, 0, 14, 0, 0, 14, 0, 12, 14, 0, 0, 14, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -2, 37, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 6, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -2, 28, 0, 0, 26, 0, -2, 29, 0, 0, 27, 0, -2, 36, 0, 13, 1, 2, 7, 4, 8, 7, 16, 105], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 30, 36, 36, 37], [14, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 2, 15, 1, 16, 2], [3, 0, 0, 4, 5, 6, 2, 7, 8, 9, 10, 0, 0, 11, 12, 13, 14, 0, 2, 15, 16, 0]], [[{"name": "itemNotEnough_topbg", "rect": {"x": 2, "y": 159, "width": 458, "height": 46}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 458, "height": 46}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [5], [1]], [[{"name": "itemNotEnough_tuoyuan", "rect": {"x": 201, "y": 208, "width": 145, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 145, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [5], [1]], [[{"name": "itemNotEnough_frame", "rect": {"x": 2, "y": 208, "width": 196, "height": 60}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 196, "height": 60}, "rotated": false, "capInsets": [20, 25, 20, 15], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [4], 0, [0], [5], [1]]]]