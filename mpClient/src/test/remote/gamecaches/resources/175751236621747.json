[1, ["61NXJOyyZNU6ZGh0FJkMv2", "41Cf1vi2BOm4J8zvJo4tGH@ef69b", "2enw6dg1JAa5xhWIDgaiWv@46320", "94FGSZedBCE46Kdw4aaHkw@f9941", "2enw6dg1JAa5xhWIDgaiWv@cf4c0", "2enw6dg1JAa5xhWIDgaiWv@694c2", "2enw6dg1JAa5xhWIDgaiWv@29896", "65nnJ3SlJOoYRwNWpKye8V@f9941", "b5fXDcze5FXrMfVmVDTG7q@6c48a"], ["node", "_spriteFrame", "_font", "_target", "root", "lbTxt4", "lbTxt3", "lbTxt2", "lbTxt1", "passiveDesc", "skillIcon", "skillName", "data", "_parent", "_normalSprite", "_textureSource"], [["cc.Node", ["_name", "_components", "_prefab", "_children", "_parent", "_lpos"], 2, 9, 4, 2, 1, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_horizontalAlign", "_overflow", "node", "__prefab", "_font", "_color"], -6, 1, 4, 6, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_lpos"], 2, 1, 12, 4, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "nestedPrefabInstanceRoots", "targetOverrides", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "clickEvents", "_normalSprite"], 2, 1, 4, 5, 1, 9, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["87462CGWg9Pj56jmqxfWDrz", ["node", "__prefab", "skillName", "skillIcon", "passiveDesc", "lbTxt1", "lbTxt2", "lbTxt3", "lbTxt4"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingBottom", "_affectedByScale", "node", "__prefab"], -1, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["79625nJPs5FErtP6kcjZWHi", ["node", "__prefab"], 3, 1, 4]], [[10, 0, 2], [6, 0, 1, 3, 2, 4, 5, 5], [3, 0, 1, 2, 1], [3, 0, 1, 2, 3, 1], [15, 0, 1, 1], [4, 0, 1, 2, 3, 4, 2], [0, 0, 4, 3, 1, 2, 5, 2], [2, 0, 2, 3, 4, 2], [0, 0, 4, 1, 2, 2], [0, 0, 4, 1, 2, 5, 2], [5, 0, 1, 2, 3, 4, 4], [2, 1, 0, 2, 3, 4, 3], [1, 0, 1, 2, 3, 4, 5, 6, 9, 10, 11, 8], [1, 0, 1, 2, 3, 4, 5, 9, 10, 7], [1, 0, 1, 2, 3, 4, 5, 9, 10, 12, 7], [9, 0, 2], [0, 0, 3, 1, 2, 2], [0, 0, 3, 1, 2, 5, 2], [0, 0, 4, 3, 1, 2, 2], [4, 0, 1, 2, 3, 2], [3, 0, 1, 1], [5, 3, 4, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [6, 0, 1, 2, 3, 4, 5, 5], [2, 0, 2, 3, 5, 4, 2], [2, 0, 2, 3, 2], [7, 1, 2, 3, 4, 1], [7, 0, 1, 2, 5, 3, 4, 6, 2], [12, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 5], [14, 0, 1, 2, 3], [1, 0, 1, 2, 3, 4, 5, 6, 9, 10, 8], [1, 0, 7, 1, 2, 3, 8, 4, 5, 6, 9, 10, 10], [16, 0, 1, 1]], [[[[15, "RaceSkillDlg"], [16, "RaceSkillDlg", [-12, -13, -14, -15], [[2, -2, [0, "66pvF2C91DnKjoS/XOK17L"], [5, 750, 1334]], [10, 45, 750, 1334, -3, [0, "21kyS9MopAUIC3KyS/uBhk"]], [22, -11, [0, "69lwSBPb1Ab7DEp6z2zh9D"], -10, -9, -8, -7, -6, -5, -4]], [23, "c46/YsCPVOJYA4mWEpNYRx", null, null, [], -1, 0]], [17, "topPanel", [-17, -18, -19, -20, -21, -22, -23, -24, -25], [[3, -16, [0, "f0gSjnRoVAiZtBTbliFKJI"], [5, 447, 368.043], [0, 0.5, 1]]], [1, "eaaCMHcTFHd67OXfQLZXtC", null, null, null, 1, 0], [1, 0, 199.0215, 0]], [8, "bgClose", 1, [[2, -26, [0, "65L1csl9JGl6e5jEzEcAsb"], [5, 750, 1334]], [24, 0, -27, [0, "b2FR+bzmRIOajx6k6zG6rZ"], [4, 3019898880], 0], [26, -29, [0, "35JD05DvpCbLqGK/Nven1K"], [4, 4292269782], -28], [10, 45, 39, 39, -30, [0, "d6I9fl+ilKlpVKsrCqvm9/"]]], [1, "02iWejczJN0Y4W2/4TwoSS", null, null, null, 1, 0]], [18, "bg", 1, [2], [[2, -31, [0, "65qUXK1AVA/b0KSQF9G6NV"], [5, 470, 398.043]], [11, 1, 0, -32, [0, "47CO2lWeJAkoDPkv7C0M2q"], 8], [28, -33, [0, "19GzcNR5BIpJxnUdaSO2jk"]], [29, 1, 2, 30, true, -34, [0, "4br+QkdKpPxoaaYcP2HP9T"]]], [1, "971eTbCPdAcIAkJvLmf+mE", null, null, null, 1, 0]], [9, "btnClose", 1, [[2, -35, [0, "4dAlV7D7ZEaKIiCsVgBZ0g"], [5, 58, 57]], [11, 1, 2, -36, [0, "32CNd8HO9E34d2j4U1d9C7"], 9], [27, 3, -38, [0, "70N0iugn5Nq4g7xa+xEh86"], [[30, "ce6f4fRQZZL97fTZqM2aYl8", "onClickCloseBtn", 1]], [4, 4292269782], -37, 10], [21, -39, [0, "f2BfgMVF1NnLJd5+E4RyFc"]]], [1, "63bhn2NKJGq5l+PN4GxySH", null, null, null, 1, 0], [1, 206.98199999999997, 187.96950000000004, 0]], [6, "cellBg", 2, [-42], [[2, -40, [0, "c30yq76GJOrKvtOtLd2Q8b"], [5, 85, 85]], [7, 0, -41, [0, "4dlmIpq8ZJcZ7cIwA6Swkj"], 1]], [1, "f9zllk1tpFQbch2P692Teb", null, null, null, 1, 0], [1, -142.83, -116.751, 0]], [5, "skillName", 2, [[[2, -43, [0, "4bwGEE8zdBE6Zp6ySO0BJm"], [5, 99.97599792480469, 29.2]], -44, [4, -45, [0, "2f+BKit+1F1JKq9lN1lLem"]]], 4, 1, 4], [1, "87VaG3nJtCz78mDXge+s9c", null, null, null, 1, 0], [1, 0, -42.859, 0]], [6, "flag", 2, [-48], [[2, -46, [0, "1eGywx5xdAmaz9Qdy1gJNN"], [5, 28, 29]], [7, 0, -47, [0, "8aPSX53aVEqI8+fVuH6ABH"], 3]], [1, "d65GW6J7hDAKS7N2E0IBqJ", null, null, null, 1, 0], [1, -110.74, -149.064, 0]], [8, "skillLevel", 8, [[2, -49, [0, "4e/6OQMTJMBrVuOukHZpGD"], [5, 21.1519775390625, 24.16]], [12, "10", 16, 16, 16, false, false, true, -50, [0, "7bFyz7BcdMkoKtYnLF81ZV"], 2], [4, -51, [0, "f2KjL+5GtKSaHcGyjxHlLw"]]], [1, "64SuWQslZDgo0cKlFGYVwO", null, null, null, 1, 0]], [6, "bgTrim2", 2, [-54], [[2, -52, [0, "57FagUUK9IiairSP4qrTpB"], [5, 177, 20]], [7, 2, -53, [0, "f2JYV37RxN9p3YuSe+ueG0"], 4]], [1, "2eOUFXEANKQI3oTjtZF4zw", null, null, null, 1, 0], [1, -3.974, -81.758, 0]], [5, "lbTxt1", 10, [[[3, -55, [0, "13liICJR9Jja3STrZVz4EK"], [5, 36, 25.2], [0, 0, 0.5]], -56, [4, -57, [0, "36VGafX41Ls5VlmOlpQxNX"]]], 4, 1, 4], [1, "a9eEXaD29Jm4323lurbVam", null, null, null, 1, 0], [1, -71.45100000000002, -0.7849999999999682, 0]], [5, "lbTxt2", 2, [[[3, -58, [0, "b8kDK7Q29Jb6qWpKPeLXKm"], [5, 36, 25.2], [0, 0, 0.5]], -59, [4, -60, [0, "9byo/XDVlP5rNa1xBQmy13"]]], 4, 1, 4], [1, "252PASidNCV7BMu0AEtKv4", null, null, null, 1, 0], [1, -78.921, -104.358, 0]], [6, "bgTrim", 2, [-63], [[2, -61, [0, "021FX9oqBGVqHqwbXId/NJ"], [5, 177, 20]], [7, 2, -62, [0, "75lSEW1tZF6olpZ9bBdfSc"], 5]], [1, "9fK/srqtJO9ZFCDD0b9iVC", null, null, null, 1, 0], [1, -3.974, -126.958, 0]], [5, "lbTxt3", 13, [[[3, -64, [0, "15dzPMr61HnpAQ9PYrnibe"], [5, 71.98199462890625, 25.2], [0, 0, 0.5]], -65, [4, -66, [0, "d701LCMw9BVp9wgYESdhE/"]]], 4, 1, 4], [1, "69HM0W51hOwoGdNsWJv5kx", null, null, null, 1, 0], [1, -73.81099999999998, -0.44500000000005, 0]], [5, "lbTxt4", 2, [[[3, -67, [0, "8eenFrSfFIRqNeBO0/vd4x"], [5, 54, 25.2], [0, 0, 0.5]], -68, [4, -69, [0, "b6/DK68NFHY5kLqplY/m96"]]], 4, 1, 4], [1, "73bjnepopM1b4FfyD3Kxx3", null, null, null, 1, 0], [1, -78.889, -148.895, 0]], [6, "bgTrim4", 2, [-72], [[3, -70, [0, "cedbr7YKJLgYLtH5kcpaVI"], [5, 395, 30], [0, 0, 0.5]], [7, 2, -71, [0, "29ArIENAJH17VkBwhExMS+"], 7]], [1, "3b+N4t2/FNk6grSv1O8NdB", null, null, null, 1, 0], [1, -198.022, -213.736, 0]], [9, "passiveTxt", 16, [[3, -73, [0, "6f9hEIl+NBD41ZH5uHHpHL"], [5, 91.93399047851562, 29.2], [0, 0, 0.5]], [12, "被动效果", 22, 22, 20, false, false, true, -74, [0, "20i6lD06BOzLHX0ebdSxsU"], 6], [4, -75, [0, "2ehYK6Xc5KrK3LXiKkOb+f"]]], [1, "98Pn7O1s9LeKTR+0BaFQRj", null, null, null, 1, 0], [1, 9.575999999999993, 0.37399999999990996, 0]], [5, "des", 2, [[[3, -76, [0, "47ifDHdm5J8KB1ijBwxf8c"], [5, 363, 29.2], [0, 0.5, 1]], -77, [4, -78, [0, "73sG2lWK1DVbx603DGt2Tq"]]], 4, 1, 4], [1, "b8r2qteCVMnbwR5j8D7bsk", null, null, null, 1, 0], [1, -8.455, -233.613, 0]], [19, "skillIcon", 6, [[[2, -79, [0, "88920gYEVMB4AoNLLiFs5d"], [5, 85, 85]], -80], 4, 1], [1, "3fP0tgrvdJNb2egylTHHh/", null, null, null, 1, 0]], [8, "upSp", 1, [[20, -81, [0, "6clxZMWsBOEpjEVZhJ/Gzf"]], [33, -82, [0, "73wWV6fitHIrCQ2jEwBUAg"]]], [1, "02sOE1wDtJMqY6c0Irp+mA", null, null, null, 1, 0]], [25, 0, 19, [0, "6azWcj0H9HWp4p1ln95nXp"]], [31, "英勇打击", 24, 24, 20, false, false, true, 7, [0, "abk+hpS+RPtrQrxmkOXjdb"]], [13, "无消", 18, 18, 20, false, false, 11, [0, "90QY6CSAxBf4zujumjDGWD"]], [14, "增益", 18, 18, 20, false, false, 12, [0, "1fCHQUutpM+pO8EVx6nsru"], [4, 4280495954]], [13, "被动技能", 18, 18, 20, false, false, 14, [0, "61F4oT8n5Dp6eipr8HqEF0"]], [14, "无冷却", 18, 18, 20, false, false, 15, [0, "9fsXkNpkRKdI0joDrUelno"], [4, 4280495954]], [32, "攻击强度 +100", 0, 18, 18, 20, 3, false, false, true, 18, [0, "d5Vl4ja1lB0oDYvSdKdjZZ"]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 5, 26, 0, 6, 25, 0, 7, 24, 0, 8, 23, 0, 9, 27, 0, 10, 21, 0, 11, 22, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 20, 0, -4, 5, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 10, 0, -5, 12, 0, -6, 13, 0, -7, 15, 0, -8, 16, 0, -9, 18, 0, 0, 3, 0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 3, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, -1, 19, 0, 0, 7, 0, -2, 22, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, -2, 23, 0, 0, 11, 0, 0, 12, 0, -2, 24, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, 0, 14, 0, -2, 25, 0, 0, 14, 0, 0, 15, 0, -2, 26, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -2, 27, 0, 0, 18, 0, 0, 19, 0, -2, 21, 0, 0, 20, 0, 0, 20, 0, 12, 1, 2, 13, 4, 82], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 23, 24, 25, 26, 27], [1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 14, 2, 2, 2, 2, 2, 2], [3, 4, 0, 5, 1, 1, 0, 6, 7, 2, 2, 0, 0, 0, 0, 0, 0]], [[{"name": "fuwen_role_25", "rect": {"x": 69, "y": 1160, "width": 177, "height": 20}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 177, "height": 20}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [8], 0, [0], [15], [8]]]]