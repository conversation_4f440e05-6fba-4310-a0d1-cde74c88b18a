{"skeleton": {"hash": "2hfzYLpwuVs9W6a5PwFuYotpbZ8", "spine": "3.8.99", "x": -68.88, "y": -60.33, "width": 140.08, "height": 138.8, "images": "./image/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 30.46, "x": -8.37, "y": -45.18}, {"name": "xinbing_icon_07", "parent": "bone", "x": 9.78, "y": 42.48}, {"name": "xxx1", "parent": "bone", "x": -14.31, "y": 70.48, "scaleX": 0.3411, "scaleY": 0.3411}, {"name": "xxx2", "parent": "bone", "rotation": 148, "x": 2.88, "y": 42.87, "scaleX": 0.6141, "scaleY": 0.6141}, {"name": "ttt", "parent": "bone", "length": 31.51, "rotation": 56.65, "x": -30.99, "y": 74.5}, {"name": "guang3", "parent": "bone", "x": 8.25, "y": 52.6, "scaleX": 0.8, "scaleY": 0.8}, {"name": "guang4", "parent": "bone", "rotation": 20.68, "x": 10.76, "y": 57.69, "scaleX": 0.8, "scaleY": 0.8}], "slots": [{"name": "guang3", "bone": "guang3", "color": "e4dbffcd", "attachment": "guang3"}, {"name": "guang4", "bone": "guang3", "color": "3f00ffff", "attachment": "guang3", "blend": "additive"}, {"name": "guang6", "bone": "guang4", "color": "3f00ffff", "attachment": "guang3", "blend": "additive"}, {"name": "fuli", "bone": "xinbing_icon_07", "attachment": "fuli"}, {"name": "mask_fu", "bone": "xinbing_icon_07", "attachment": "mask_fu"}, {"name": "ttt", "bone": "ttt", "attachment": "ttt", "blend": "additive"}, {"name": "xxx1", "bone": "xxx1", "attachment": "xxx1", "blend": "additive"}, {"name": "xxx2", "bone": "xxx2", "attachment": "xxx1", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"fuli": {"fuli": {"x": -0.2, "y": -0.41, "width": 100, "height": 77}}, "guang3": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "guang4": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "guang6": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "mask_fu": {"mask_fu": {"type": "clipping", "end": "ttt", "vertexCount": 48, "vertices": [-20.26, 18.92, -15.88, 17.72, -19.77, 15.17, -21.63, 10.39, -23.15, 6.59, -27.21, 5.08, -30.83, 1.4, -32.57, -2.34, -33.92, -6.23, -32.57, -10.48, -30.52, -10.23, -25.4, -17.21, -24.47, -14.51, -23.08, -13.49, -23.73, -12.41, -20.87, -9.95, -17.27, -9.81, -16.55, -11.07, -13.93, -9.61, 1.34, -9.56, 2.38, -11.08, 5.38, -11.24, 11.82, -9.76, 13.48, -10.78, 13.84, -12.98, 16.76, -13.22, 17.92, -10.9, 23.72, -10.78, 24.61, -11.07, 26.62, -10.32, 24.58, -3.31, 23.6, -1.12, 22.9, 0.52, 19.74, 1.57, 19.08, 11.46, 7.78, 22.35, 11.27, 25.95, 12.65, 26.72, 14.33, 35.3, 11.66, 37.41, 4.89, 37, 4.06, 37.94, -4.08, 38.06, -9.36, 36.03, -15.14, 33.73, -19.34, 30.02, -18.28, 26.41, -20.77, 21.58], "color": "ce3a3aff"}}, "ttt": {"ttt": {"x": 0.36, "y": 0.15, "rotation": -86.99, "width": 52, "height": 104}}, "xxx1": {"xxx1": {"x": 2.56, "y": -2.3, "width": 57, "height": 57}}, "xxx2": {"xxx1": {"x": 2.56, "y": -2.3, "width": 57, "height": 57}}}}], "animations": {"animation": {"slots": {"ttt": {"color": [{"color": "ffffffff"}, {"time": 0.3333, "color": "ffffffa5", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffa5"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3, "color": "ffffffa5", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffa5"}, {"time": 3.6667, "color": "ffffffff"}]}, "xxx1": {"color": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "xxx2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}, {"time": 3.3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 4.0667, "color": "ffffff00"}]}}, "bones": {"xinbing_icon_07": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "xxx1": {"rotate": [{"angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -119.7, "curve": "stepped"}, {"time": 2.6667, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -119.7, "curve": "stepped"}, {"time": 5.3333, "angle": 37.69}], "scale": [{"x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 1, "curve": "stepped"}, {"time": 2.6667, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 3.6667, "curve": "stepped"}, {"time": 5.3333, "x": 0.433, "y": 0.433}]}, "xxx2": {"rotate": [{"angle": 37.69, "curve": "stepped"}, {"time": 0.4, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -119.7, "curve": "stepped"}, {"time": 2.6667, "angle": 37.69, "curve": "stepped"}, {"time": 3.0667, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 4.0667, "angle": -119.7, "curve": "stepped"}, {"time": 5.3333, "angle": 37.69}], "scale": [{"x": 0.433, "y": 0.433, "curve": "stepped"}, {"time": 0.4, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 1.4, "curve": "stepped"}, {"time": 2.6667, "x": 0.433, "y": 0.433, "curve": "stepped"}, {"time": 3.0667, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 4.0667, "curve": "stepped"}, {"time": 5.3333, "x": 0.433, "y": 0.433}]}, "ttt": {"translate": [{"x": 4.1, "y": -2.7, "curve": 0, "c2": 0.27, "c4": 0.77}, {"time": 1, "x": 73.85, "y": -48.6, "curve": "stepped"}, {"time": 2.6667, "x": 4.1, "y": -2.7, "curve": 0, "c2": 0.27, "c4": 0.77}, {"time": 3.6667, "x": 73.85, "y": -48.6}], "scale": [{"y": 0.673, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 1.721, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 0.673, "curve": "stepped"}, {"time": 2.6667, "y": 0.673, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 1.721, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 0.673}]}, "guang3": {"rotate": [{}, {"time": 1.3333, "angle": -90}, {"time": 2.6667, "angle": 180}, {"time": 4, "angle": 90}, {"time": 5.3333}]}, "guang4": {"rotate": [{}, {"time": 1.3333, "angle": 90}, {"time": 2.6667, "angle": 180}, {"time": 4, "angle": -90}, {"time": 5.3333}]}}}}}