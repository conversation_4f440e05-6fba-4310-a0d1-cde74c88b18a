{"skeleton": {"hash": "Vw9J/qYjFvqin3A/uAinV6PugiM", "spine": "3.8.99", "x": -68.88, "y": -60.33, "width": 140.08, "height": 138.8, "images": "./image/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 30.46, "x": -8.37, "y": -45.18}, {"name": "xinbing_icon_07", "parent": "bone", "x": 9.78, "y": 42.48}, {"name": "xxx1", "parent": "bone", "x": -4.47, "y": 65.07, "scaleX": 0.3411, "scaleY": 0.3411}, {"name": "xxx2", "parent": "bone", "rotation": 148, "x": 25.35, "y": 33.2, "scaleX": 0.6141, "scaleY": 0.6141}, {"name": "ttt", "parent": "bone", "length": 31.51, "rotation": 56.65, "x": -30.99, "y": 74.5}, {"name": "guang3", "parent": "bone", "x": 8.25, "y": 52.6, "scaleX": 0.8, "scaleY": 0.8}, {"name": "guang4", "parent": "bone", "rotation": 20.68, "x": 10.76, "y": 57.69, "scaleX": 0.8, "scaleY": 0.8}], "slots": [{"name": "guang3", "bone": "guang3", "color": "e4dbffcd", "attachment": "guang3"}, {"name": "guang4", "bone": "guang3", "color": "3f00ffff", "attachment": "guang3", "blend": "additive"}, {"name": "guang6", "bone": "guang4", "color": "3f00ffff", "attachment": "guang3", "blend": "additive"}, {"name": "pifu", "bone": "xinbing_icon_07", "attachment": "pifu"}, {"name": "mask_fu", "bone": "xinbing_icon_07", "attachment": "mask_fu"}, {"name": "ttt", "bone": "ttt", "attachment": "ttt", "blend": "additive"}, {"name": "xxx1", "bone": "xxx1", "attachment": "xxx1", "blend": "additive"}, {"name": "xxx2", "bone": "xxx2", "attachment": "xxx1", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"guang3": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "guang4": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "guang6": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "mask_fu": {"mask_fu": {"type": "clipping", "end": "ttt", "vertexCount": 45, "vertices": [-26.74, 22.91, -27.67, 17.05, -27.91, 11.04, -31.51, 11.19, -31.5, 5.17, -29, 1.21, -30.81, -0.94, -26.69, -6.04, -24.32, -5.65, -24.13, -10.16, -17.97, -12.59, -14.78, -10.9, -11.63, -11, -9.97, -13.06, -8.35, -11.13, -4.64, -11.11, -1.19, -13.71, -0.5, -10.4, 5.01, -11.69, 11.45, -11.48, 13.41, -10.74, 14.73, -13.01, 17.93, -10.67, 20.41, -4.94, 21.58, -0.46, 21.8, 2.1, 24.33, 4.28, 26.61, 10.11, 26.23, 15.72, 24.42, 16.53, 21.76, 14.69, 19.8, 19, 17.41, 22.64, 14.01, 25.19, 10.96, 26.87, 7.63, 27.4, 2.2, 28.18, -3.77, 28.19, -8.12, 27.25, -12.43, 25.32, -15.49, 22.99, -16.48, 19.64, -18.37, 20.85, -20.02, 21.54, -25.13, 26.83], "color": "ce3a3aff"}}, "pifu": {"pifu": {"x": -0.2, "y": -0.41, "width": 100, "height": 77}}, "ttt": {"ttt": {"x": 0.36, "y": 0.15, "rotation": -86.99, "width": 52, "height": 104}}, "xxx1": {"xxx1": {"x": 2.56, "y": -2.3, "width": 57, "height": 57}}, "xxx2": {"xxx1": {"x": 2.56, "y": -2.3, "width": 57, "height": 57}}}}], "animations": {"animation": {"slots": {"ttt": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffce", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffce"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3, "color": "ffffffa5", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffa5"}, {"time": 3.6667, "color": "ffffffff"}]}, "xxx1": {"color": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "xxx2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 3.4333, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.0333, "color": "ffffffff"}, {"time": 4.4333, "color": "ffffff00"}]}}, "bones": {"xinbing_icon_07": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "xxx1": {"rotate": [{"angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -119.7, "curve": "stepped"}, {"time": 2.6667, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -119.7, "curve": "stepped"}, {"time": 5.3333, "angle": 37.69}], "scale": [{"x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 1, "curve": "stepped"}, {"time": 2.6667, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 3.6667, "curve": "stepped"}, {"time": 5.3333, "x": 0.433, "y": 0.433}]}, "xxx2": {"rotate": [{"angle": 37.69, "curve": "stepped"}, {"time": 0.4, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -119.7, "curve": "stepped"}, {"time": 2.6667, "angle": 37.69, "curve": "stepped"}, {"time": 3.4333, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -119.7, "curve": "stepped"}, {"time": 5.3333, "angle": 37.69}], "scale": [{"x": 0.433, "y": 0.433, "curve": "stepped"}, {"time": 0.4, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 1.4, "curve": "stepped"}, {"time": 2.6667, "x": 0.433, "y": 0.433, "curve": "stepped"}, {"time": 3.4333, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 4.4333, "curve": "stepped"}, {"time": 5.3333, "x": 0.433, "y": 0.433}]}, "ttt": {"translate": [{"x": 4.1, "y": -2.7, "curve": 0, "c2": 0.27, "c4": 0.77}, {"time": 1, "x": 73.85, "y": -48.6, "curve": "stepped"}, {"time": 2.6667, "x": 4.1, "y": -2.7, "curve": 0, "c2": 0.27, "c4": 0.77}, {"time": 3.6667, "x": 73.85, "y": -48.6}], "scale": [{"y": 0.673, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 1.721, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 0.673, "curve": "stepped"}, {"time": 2.6667, "y": 0.673, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 1.721, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 0.673}]}, "guang3": {"rotate": [{}, {"time": 1.3333, "angle": -90}, {"time": 2.6667, "angle": 180}, {"time": 4, "angle": 90}, {"time": 5.3333}]}, "guang4": {"rotate": [{}, {"time": 1.3333, "angle": 90}, {"time": 2.6667, "angle": 180}, {"time": 4, "angle": -90}, {"time": 5.3333}]}}}}}