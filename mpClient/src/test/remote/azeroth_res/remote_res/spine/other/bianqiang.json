{"skeleton": {"hash": "sxubfsOa/nZDhBfvR8ui0ch39SY", "spine": "3.8.99", "x": -68.88, "y": -60.33, "width": 140.08, "height": 138.8, "images": "./image/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 30.46, "x": -8.37, "y": -45.18}, {"name": "xinbing_icon_07", "parent": "bone", "x": 9.78, "y": 42.48}, {"name": "xxx1", "parent": "bone", "x": -4.47, "y": 65.07, "scaleX": 0.3411, "scaleY": 0.3411}, {"name": "xxx2", "parent": "bone", "rotation": 148, "x": 25.35, "y": 33.2, "scaleX": 0.6141, "scaleY": 0.6141}, {"name": "ttt", "parent": "bone", "length": 31.51, "rotation": 56.65, "x": -30.99, "y": 74.5}, {"name": "guang3", "parent": "bone", "x": 8.25, "y": 52.6, "scaleX": 0.8, "scaleY": 0.8}, {"name": "guang4", "parent": "bone", "rotation": 20.68, "x": 10.76, "y": 57.69, "scaleX": 0.8, "scaleY": 0.8}], "slots": [{"name": "guang3", "bone": "guang3", "color": "e4dbffcd", "attachment": "guang3"}, {"name": "guang4", "bone": "guang3", "color": "3f00ffff", "attachment": "guang3", "blend": "additive"}, {"name": "guang6", "bone": "guang4", "color": "3f00ffff", "attachment": "guang3", "blend": "additive"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "bone": "xinbing_icon_07", "attachment": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "mask_fu", "bone": "xinbing_icon_07", "attachment": "mask_fu"}, {"name": "ttt", "bone": "ttt", "attachment": "ttt", "blend": "additive"}, {"name": "xxx1", "bone": "xxx1", "attachment": "xxx1", "blend": "additive"}, {"name": "xxx2", "bone": "xxx2", "attachment": "xxx1", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"bianqiang": {"bianqiang": {"x": -0.2, "y": -0.41, "width": 68, "height": 76}}, "guang3": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "guang4": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "guang6": {"guang3": {"x": -2.95, "y": -3.47, "scaleX": 0.3915, "scaleY": 0.3915, "width": 349, "height": 342}}, "mask_fu": {"mask_fu": {"type": "clipping", "end": "ttt", "vertexCount": 26, "vertices": [-19.11, 22.05, -20.22, 19.95, -19.94, 14.44, -30.87, 4.89, -20.63, 2.24, -26.23, -12.06, -24.51, -11.95, -17.72, -11.78, -16.38, -10.76, -9.8, -10.72, -9.13, -11.56, 9.63, -11.34, 12.82, -11.32, 19.93, -11.32, 21.84, -12.79, 23.04, -11.47, 18.14, 1.81, 29, 4.96, 15.74, 15.88, 16.12, 21.75, 10.55, 21.47, 7.27, 25.14, -2.65, 36.33, -6.41, 30.99, -9.66, 26.78, -13.96, 21.46], "color": "ce3a3aff"}}, "ttt": {"ttt": {"x": 0.36, "y": 0.15, "rotation": -86.99, "width": 52, "height": 104}}, "xxx1": {"xxx1": {"x": 2.56, "y": -2.3, "width": 57, "height": 57}}, "xxx2": {"xxx1": {"x": 2.56, "y": -2.3, "width": 57, "height": 57}}}}], "animations": {"animation": {"slots": {"ttt": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}]}, "xxx1": {"color": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "xxx2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 3.4333, "color": "ffffff00"}, {"time": 3.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.0333, "color": "ffffffff"}, {"time": 4.4333, "color": "ffffff00"}]}}, "bones": {"xinbing_icon_07": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "x": 1.068, "y": 1.068, "curve": 0.25, "c3": 0.75}, {"time": 5.3333}]}, "xxx1": {"rotate": [{"angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -119.7, "curve": "stepped"}, {"time": 2.6667, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -119.7, "curve": "stepped"}, {"time": 5.3333, "angle": 37.69}], "scale": [{"x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 1, "curve": "stepped"}, {"time": 2.6667, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 3.6667, "curve": "stepped"}, {"time": 5.3333, "x": 0.433, "y": 0.433}]}, "xxx2": {"rotate": [{"angle": 37.69, "curve": "stepped"}, {"time": 0.4, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -119.7, "curve": "stepped"}, {"time": 2.6667, "angle": 37.69, "curve": "stepped"}, {"time": 3.4333, "angle": 37.69, "curve": 0.25, "c3": 0.75}, {"time": 4.4333, "angle": -119.7, "curve": "stepped"}, {"time": 5.3333, "angle": 37.69}], "scale": [{"x": 0.433, "y": 0.433, "curve": "stepped"}, {"time": 0.4, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 1.4, "curve": "stepped"}, {"time": 2.6667, "x": 0.433, "y": 0.433, "curve": "stepped"}, {"time": 3.4333, "x": 0.433, "y": 0.433, "curve": 0.25, "c3": 0}, {"time": 4.4333, "curve": "stepped"}, {"time": 5.3333, "x": 0.433, "y": 0.433}]}, "ttt": {"rotate": [{"angle": -2.65}], "translate": [{"x": 4.1, "y": -2.7, "curve": 0, "c2": 0.27, "c4": 0.77}, {"time": 1, "x": 73.85, "y": -48.6, "curve": "stepped"}, {"time": 2.6667, "x": 4.1, "y": -2.7, "curve": 0, "c2": 0.27, "c4": 0.77}, {"time": 3.6667, "x": 73.85, "y": -48.6}], "scale": [{"y": 0.673, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 1.721, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 0.673, "curve": "stepped"}, {"time": 2.6667, "y": 0.673, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "y": 1.721, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "y": 0.673}]}, "guang3": {"rotate": [{}, {"time": 1.3333, "angle": -90}, {"time": 2.6667, "angle": 180}, {"time": 4, "angle": 90}, {"time": 5.3333}]}, "guang4": {"rotate": [{}, {"time": 1.3333, "angle": 90}, {"time": 2.6667, "angle": 180}, {"time": 4, "angle": -90}, {"time": 5.3333}]}}}}}