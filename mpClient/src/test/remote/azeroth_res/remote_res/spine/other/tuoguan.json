{"skeleton": {"hash": "u6rQBVYcpGgjHwAELkybjB/sowY", "spine": "3.8.99", "x": -50.96, "y": -46.95, "width": 101.91, "height": 91.77, "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "shalou02", "parent": "root", "x": 2.17, "y": -1.64}, {"name": "sa<PERSON><PERSON>", "parent": "shalou02", "length": 13.87, "rotation": 131.66, "x": -0.81, "y": -0.16, "scaleX": 0.8676, "scaleY": 0.8676}, {"name": "di_huang02", "parent": "shalou02", "rotation": -179.66, "x": 10.25, "y": -15.78}, {"name": "sahlou01", "parent": "shalou02", "x": -14.56, "y": 18.04}, {"name": "di_huang2", "parent": "shalou02", "x": 14.76, "y": -15.79}, {"name": "all2", "parent": "root", "scaleX": 0.7506, "scaleY": 0.7506}, {"name": "bone2", "parent": "all2", "length": 49.84, "x": 0.55, "y": -53.03}, {"name": "tuo1", "parent": "bone2", "x": -49.26, "y": 18.76, "scaleX": 1.3, "scaleY": 1.3}, {"name": "guan1", "parent": "bone2", "x": -15.6, "y": 19.35, "scaleX": 1.3, "scaleY": 1.3}, {"name": "gua1", "parent": "bone2", "x": 16.59, "y": 18.76, "scaleX": 1.3, "scaleY": 1.3}, {"name": "ji1", "parent": "bone2", "x": 46.68, "y": 18.15, "scaleX": 1.3, "scaleY": 1.3}, {"name": "t5", "parent": "all2", "length": 12.51, "rotation": 109.79, "x": -7.15, "y": -26.41, "scaleX": 0.4953, "scaleY": 0.4953}, {"name": "t6", "parent": "all2", "length": 12.51, "rotation": 109.79, "x": 0.88, "y": -23.94, "scaleX": 0.4953, "scaleY": 0.4953}, {"name": "t7", "parent": "all2", "length": 12.51, "rotation": 109.79, "x": 11.08, "y": -21.77, "scaleX": 0.3674, "scaleY": 0.3674}, {"name": "t8", "parent": "all2", "length": 12.51, "rotation": 109.79, "x": 21.9, "y": -17.76, "scaleX": 0.4953, "scaleY": 0.4953}, {"name": "xx3", "parent": "all2", "rotation": 49.18, "x": 7.43, "y": 21.78}, {"name": "xx4", "parent": "all2", "rotation": -159.09, "x": -10.01, "y": -13.68, "scaleX": 0.798, "scaleY": 0.798}, {"name": "guang5", "parent": "all2", "x": 0.62, "y": -3.22, "scaleX": 0.6833, "scaleY": 0.6833}, {"name": "lightBG_h2", "parent": "all2", "x": -5.07, "y": 2.77, "scaleX": 1.5239, "scaleY": 1.5239}, {"name": "guang6", "parent": "all2", "x": 0.62, "y": -3.22, "scaleX": 0.6833, "scaleY": 0.6833}, {"name": "st13", "parent": "all2", "x": -33.73, "y": -1.89, "scaleX": 0.3541, "scaleY": 0.3541}, {"name": "st14", "parent": "all2", "x": -25.13, "y": 30.02, "scaleX": 0.4726, "scaleY": 0.4726}, {"name": "st15", "parent": "all2", "x": 0.85, "y": 35.8, "scaleX": 0.4242, "scaleY": 0.4242}, {"name": "st16", "parent": "all2", "x": 32.47, "y": 21.6, "scaleX": 0.5243, "scaleY": 0.5243}, {"name": "st17", "parent": "all2", "x": 34.29, "y": -1.51, "scaleX": 0.6334, "scaleY": 0.6334}, {"name": "st18", "parent": "all2", "x": 17.12, "y": -25.36, "scaleX": 0.4726, "scaleY": 0.4726}, {"name": "st19", "parent": "all2", "x": 2.41, "y": -30.83, "scaleX": 0.284, "scaleY": 0.284}, {"name": "st20", "parent": "all2", "x": -32.2, "y": 15.46, "scaleX": 0.4295, "scaleY": 0.4295}, {"name": "st21", "parent": "all2", "x": 9.54, "y": 35.63, "scaleX": 0.4303, "scaleY": 0.4303}, {"name": "st22", "parent": "all2", "x": 35.48, "y": 10.3, "scaleX": 0.3339, "scaleY": 0.3339}, {"name": "st23", "parent": "all2", "x": 26.24, "y": -17.81, "scaleX": 0.3954, "scaleY": 0.3954}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "guang5", "bone": "guang5", "attachment": "guang3", "blend": "additive"}, {"name": "guang6", "bone": "guang6", "attachment": "guang3", "blend": "additive"}, {"name": "lightBG_h2", "bone": "lightBG_h2", "attachment": "lightBG_h", "blend": "additive"}, {"name": "t5", "bone": "t5", "attachment": "t1", "blend": "additive"}, {"name": "t6", "bone": "t6", "attachment": "t1", "blend": "additive"}, {"name": "t7", "bone": "t7", "attachment": "t1", "blend": "additive"}, {"name": "t8", "bone": "t8", "attachment": "t1", "blend": "additive"}, {"name": "xx3", "bone": "xx3", "attachment": "xx"}, {"name": "xx4", "bone": "xx4", "attachment": "xx"}, {"name": "st13", "bone": "st13", "attachment": "st2", "blend": "additive"}, {"name": "st20", "bone": "st20", "attachment": "st2", "blend": "additive"}, {"name": "st21", "bone": "st21", "attachment": "st2", "blend": "additive"}, {"name": "st22", "bone": "st22", "attachment": "st2", "blend": "additive"}, {"name": "st23", "bone": "st23", "attachment": "st2", "blend": "additive"}, {"name": "st14", "bone": "st14", "attachment": "st2", "blend": "additive"}, {"name": "st15", "bone": "st15", "attachment": "st2", "blend": "additive"}, {"name": "st16", "bone": "st16", "attachment": "st2", "blend": "additive"}, {"name": "st17", "bone": "st17", "attachment": "st2", "blend": "additive"}, {"name": "st18", "bone": "st18", "attachment": "st2", "blend": "additive"}, {"name": "st19", "bone": "st19", "attachment": "st2", "blend": "additive"}, {"name": "shalou02", "bone": "shalou02", "attachment": "shalou02"}, {"name": "di_huang02", "bone": "di_huang2"}, {"name": "sahlou01", "bone": "sahlou01"}, {"name": "tiao", "bone": "sa<PERSON><PERSON>", "attachment": "tiao"}, {"name": "sha", "bone": "sa<PERSON><PERSON>", "attachment": "sha"}, {"name": "sha2", "bone": "sa<PERSON><PERSON>", "attachment": "sha"}, {"name": "tuo1", "bone": "tuo1", "attachment": "tuo01"}, {"name": "guan1", "bone": "guan1", "attachment": "guan01"}, {"name": "gua1", "bone": "gua1", "attachment": "gua01"}, {"name": "ji1", "bone": "ji1", "attachment": "ji01"}], "skins": [{"name": "default", "attachments": {"bg": {"bg": {"x": 0.32, "y": -0.78, "width": 68, "height": 69}}, "di_huang02": {"di_huang02": {"type": "mesh", "uvs": [0.08864, 0.434, 0, 0.62343, 0, 0.79343, 0.11749, 1, 0.31119, 1, 0.5461, 0.83229, 0.781, 0.64772, 0.96234, 0.42429, 1, 0.23, 0.84282, 0, 0.54198, 0, 0.31531, 0.128, 0.16695, 0.298, 0.71046, 0.29059, 0.48247, 0.55616, 0.20565, 0.69511], "triangles": [13, 10, 9, 13, 9, 8, 7, 13, 8, 13, 14, 11, 13, 11, 10, 12, 11, 14, 15, 0, 12, 6, 13, 7, 14, 13, 6, 14, 15, 12, 1, 0, 15, 2, 1, 15, 5, 14, 6, 15, 14, 5, 3, 2, 15, 4, 3, 15, 5, 4, 15], "vertices": [9.6, -0.2, 12.5, 5.12, 12.47, 9.88, 8.56, 15.64, 2.17, 15.61, -5.56, 10.87, -13.28, 5.65, -19.23, -0.64, -20.44, -6.09, -15.21, -12.5, -5.28, -12.44, 2.17, -8.81, 7.04, -4.02, -10.89, -4.33, -3.41, 3.15, 5.7, 7.09], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24, 18, 26, 26, 28, 28, 30, 30, 4], "width": 33, "height": 28}}, "gua1": {"gua01": {"x": -0.34, "y": 0.13, "width": 31, "height": 30}}, "guan1": {"guan01": {"x": -0.29, "y": 0.04, "width": 31, "height": 31}}, "guang5": {"guang3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [91.69, -86.84, -90.9, -86.84, -90.9, 92.09, 91.69, 92.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 105, "height": 103}}, "guang6": {"guang3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [91.69, -86.84, -90.9, -86.84, -90.9, 92.09, 91.69, 92.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 105, "height": 103}}, "ji1": {"ji01": {"x": 0.88, "y": 0.13, "width": 30, "height": 30}}, "lightBG_h2": {"lightBG_h": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [37.69, -42.39, -35.77, -42.39, -35.77, 36.92, 37.69, 36.92], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 104, "height": 112}}, "sahlou01": {"sahlou01": {"type": "mesh", "uvs": [0, 0.64616, 0, 0.88116, 0.09741, 1, 0.31951, 1, 0.65483, 0.86616, 0.88128, 0.63116, 1, 0.31616, 1, 0.16116, 0.86822, 0, 0.65919, 0, 0.58951, 0.25616, 0.39354, 0.46616, 0.16709, 0.56116], "triangles": [10, 8, 7, 8, 10, 9, 10, 5, 11, 4, 11, 5, 10, 7, 6, 6, 5, 10, 1, 0, 12, 2, 1, 12, 3, 12, 11, 3, 11, 4, 2, 12, 3], "vertices": [17.65, 0.08, 18.26, 6.4, 15.56, 9.88, 8.71, 10.54, -1.99, 7.93, -9.58, 2.29, -14.06, -5.83, -14.46, -9.99, -10.81, -14.71, -4.36, -15.33, -1.54, -8.66, 5.04, -3.59, 12.28, -1.71], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 31, "height": 27}}, "sha": {"sha": {"type": "mesh", "uvs": [0, 0, 0, 0.40366, 0.20324, 0.90532, 0.52036, 1, 0.77298, 1, 0.93423, 0.61865, 1, 0.18866, 1, 0, 0.74074, 0, 0.43436, 0, 0.18174, 0], "triangles": [8, 7, 6, 1, 0, 10, 5, 8, 6, 2, 10, 9, 1, 10, 2, 3, 9, 8, 4, 3, 8, 2, 9, 3, 5, 4, 8], "vertices": [8.92, 11.68, 5.29, 11.86, 0.54, 7.21, -0.68, -0.35, -0.98, -6.41, 2.26, -10.44, 6.05, -12.2, 7.75, -12.29, 8.05, -6.07, 8.41, 1.27, 8.71, 7.33], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 24, "height": 9}}, "sha2": {"sha": {"type": "mesh", "uvs": [0, 0, 0, 0.40366, 0.20324, 0.90532, 0.52036, 1, 0.77298, 1, 0.93423, 0.61865, 1, 0.18866, 1, 0, 0.74074, 0, 0.43436, 0, 0.18174, 0], "triangles": [8, 7, 6, 1, 0, 10, 5, 8, 6, 2, 10, 9, 1, 10, 2, 3, 9, 8, 4, 3, 8, 2, 9, 3, 5, 4, 8], "vertices": [-11.51, -12.45, -9.61, -10.85, -6.34, -4.44, -4.47, -0.65, -6.44, 4.36, -7.84, 8.54, -8.94, 12.27, -9.54, 13.39, -13.15, 5.46, -13.96, -0.83, -13.52, -7.47], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 24, "height": 9}}, "shalou02": {"shalou02": {"type": "mesh", "uvs": [0, 0.40227, 0, 0.31024, 0.10154, 0.14194, 0.26766, 0.02887, 0.37483, 0, 0.5463, 0, 0.63739, 0.09987, 0.61328, 0.18138, 0.69366, 0.31812, 0.65347, 0.40753, 0.80886, 0.45486, 0.88918, 0.49156, 0.96426, 0.52586, 1, 0.59423, 0.96962, 0.72046, 0.86245, 0.85457, 0.70705, 0.96764, 0.54898, 1, 0.4552, 0.9992, 0.36679, 0.93083, 0.37751, 0.76779, 0.35339, 0.6179, 0.2623, 0.62842, 0.16585, 0.52586, 0.04796, 0.53112, 0.22309, 0.46334, 0.38186, 0.36067, 0.51075, 0.21951, 0.17079, 0.35517, 0.3389, 0.24884, 0.46218, 0.11868, 0.48406, 0.83759, 0.6391, 0.78076, 0.77732, 0.66709], "triangles": [30, 4, 5, 30, 5, 6, 6, 27, 30, 7, 27, 6, 30, 29, 3, 30, 3, 4, 29, 30, 27, 2, 3, 29, 28, 2, 29, 1, 2, 28, 26, 29, 27, 28, 29, 26, 0, 1, 28, 8, 27, 7, 9, 27, 8, 26, 27, 9, 25, 28, 26, 28, 24, 0, 25, 24, 28, 25, 23, 24, 21, 25, 26, 21, 26, 9, 22, 23, 25, 21, 22, 25, 33, 9, 10, 33, 10, 11, 32, 21, 9, 13, 33, 12, 32, 20, 21, 9, 33, 32, 33, 11, 12, 14, 33, 13, 31, 20, 32, 15, 33, 14, 32, 33, 15, 19, 20, 31, 16, 32, 15, 17, 31, 32, 18, 19, 31, 17, 18, 31, 32, 16, 17], "vertices": [-27.78, 6.34, -27.78, 11.31, -22.4, 20.4, -13.59, 26.5, -7.91, 28.06, 1.18, 28.06, 6, 22.67, 4.73, 18.27, 8.99, 10.88, 6.86, 6.05, 15.09, 3.5, 19.35, 1.52, 23.33, -0.34, 25.22, -4.03, 23.61, -10.84, 17.93, -18.09, 9.7, -24.19, 1.32, -25.94, -3.65, -25.9, -8.34, -22.2, -7.77, -13.4, -9.05, -5.31, -13.88, -5.87, -18.99, -0.34, -25.24, -0.62, -15.95, 3.04, -7.54, 8.58, -0.71, 16.21, -18.73, 8.88, -9.82, 14.62, -3.28, 21.65, -2.12, -17.17, 6.09, -14.1, 13.42, -7.96], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 48, 50, 50, 52, 52, 54, 54, 12, 0, 56, 56, 58, 58, 60, 60, 10, 38, 62, 62, 64, 64, 66, 20, 22, 22, 24, 66, 22], "width": 53, "height": 54}}, "st13": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st14": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st15": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st16": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st17": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st18": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st19": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st20": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st21": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st22": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "st23": {"st2": {"x": -0.49, "y": -0.46, "width": 13, "height": 13}}, "t5": {"t1": {"x": 2.05, "y": 0.67, "scaleX": 0.3897, "scaleY": 0.3897, "rotation": -94.89, "width": 43, "height": 129}}, "t6": {"t1": {"x": 2.05, "y": 0.67, "scaleX": 0.3897, "scaleY": 0.3897, "rotation": -94.89, "width": 43, "height": 129}}, "t7": {"t1": {"x": 2.05, "y": 0.67, "scaleX": 0.3897, "scaleY": 0.3897, "rotation": -94.89, "width": 43, "height": 129}}, "t8": {"t1": {"x": 2.05, "y": 0.67, "scaleX": 0.3897, "scaleY": 0.3897, "rotation": -94.89, "width": 43, "height": 129}}, "tiao": {"tiao": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0.8, 0, 0.6, 0, 0.4, 0, 0.2, 0, 0, 1, 0, 1, 0.2, 1, 0.4, 1, 0.6, 1, 0.8], "triangles": [0, 1, 11, 1, 2, 11, 11, 2, 10, 2, 3, 10, 10, 3, 9, 3, 4, 9, 9, 4, 8, 4, 5, 8, 8, 5, 7, 5, 6, 7], "vertices": [-9.42, -2.63, -9.41, -0.01, -5.69, 2.18, -4.53, 0.1, -2.11, 0.03, -0.74, 2.01, 0.7, 4.58, 0.57, -5.22, -1, -3.02, -2.19, -1.83, -5.27, -2.48, -7.12, -4.44], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0], "width": 1, "height": 9}}, "tuo1": {"tuo01": {"x": 0.74, "y": -0.26, "width": 31, "height": 30}}, "xx3": {"xx": {"x": 0.29, "y": 0.18, "scaleX": 0.359, "scaleY": 0.359, "width": 76, "height": 81}}, "xx4": {"xx": {"x": 0.29, "y": 0.18, "scaleX": 0.359, "scaleY": 0.359, "width": 76, "height": 81}}}}], "animations": {"animation": {"slots": {"di_huang02": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00"}], "attachment": [{"name": "di_huang02"}]}, "guang5": {"color": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "guang6": {"color": [{"color": "ffffffda"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.9333, "color": "ffffffff"}, {"time": 4, "color": "ffffffda"}]}, "lightBG_h2": {"color": [{"time": 0.4, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9667, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff"}]}, "sahlou01": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00"}], "attachment": [{"name": "sahlou01"}]}, "sha": {"attachment": [{"name": null}, {"time": 0.5, "name": "sha"}, {"time": 3.9667, "name": null}]}, "sha2": {"attachment": [{"time": 2, "name": null}, {"time": 2.5, "name": "sha"}]}, "st13": {"color": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "st14": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00"}]}, "st15": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.9333, "color": "ffffff00"}, {"time": 3.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.4333, "color": "ffffffff"}, {"time": 3.9333, "color": "ffffff00"}]}, "st16": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 3.2333, "color": "ffffff00"}]}, "st17": {"color": [{"color": "ffffffcc"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 3.4, "color": "ffffff00"}, {"time": 3.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.9, "color": "ffffffff"}, {"time": 4, "color": "ffffffcc"}]}, "st18": {"color": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "st19": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "st20": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "st21": {"color": [{"color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "st22": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00"}]}, "st23": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.9333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00"}]}, "t5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff"}, {"time": 3.3667, "color": "ffffff00"}]}, "t6": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.9, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5667, "color": "ffffffff"}, {"time": 3.9, "color": "ffffff00"}]}, "t7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}]}, "t8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00"}, {"time": 3.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6667, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}]}, "tiao": {"attachment": [{"name": null}, {"time": 0.5, "name": "tiao"}, {"time": 2.1, "name": null}, {"time": 2.5, "name": "tiao"}]}, "xx3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1667, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff00"}]}, "xx4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 3.1333, "color": "ffffff00"}]}}, "bones": {"sahlou": {"rotate": [{"angle": 0.38}, {"time": 0.5, "angle": -2.01, "curve": "stepped"}, {"time": 2, "angle": -2.01}, {"time": 2.5, "angle": 1.81}], "translate": [{"x": -1.25, "y": 0.58}, {"time": 0.5, "x": 3.45, "y": -1.35, "curve": "stepped"}, {"time": 2, "x": 3.45, "y": -1.35}, {"time": 2.1667, "x": 0.08, "y": -2.14}, {"time": 2.5, "x": -1, "y": 1.09}], "scale": [{"x": 1.106, "y": 1.106}, {"time": 0.5, "x": 1.067, "y": 1.067}]}, "shalou02": {"rotate": [{}, {"time": 0.5, "angle": 176.17, "curve": "stepped"}, {"time": 2, "angle": 176.17}, {"time": 2.2333, "angle": -116.31}, {"time": 2.5}]}, "tuo1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -11, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -11, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -11, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "guan1": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 3.4333}], "scale": [{"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "curve": "stepped"}, {"time": 2.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 3.4333}]}, "gua1": {"translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.78, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.78, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": "stepped"}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 1.78, "curve": 0.25, "c3": 0.75}, {"time": 3.5333}], "scale": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": "stepped"}, {"time": 2.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 3.5333}]}, "ji1": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": "stepped"}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 8.25, "curve": 0.25, "c3": 0.75}, {"time": 3.6333}], "scale": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.6333, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": "stepped"}, {"time": 2.9667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": 1.229, "y": 1.229, "curve": 0.25, "c3": 0.75}, {"time": 3.6333}]}, "t5": {"rotate": [{"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -4.63, "curve": "stepped"}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "angle": -4.63}], "translate": [{"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -13.6, "y": 52.35, "curve": "stepped"}, {"time": 2.3667, "curve": 0.25, "c3": 0.75}, {"time": 3.3667, "x": -13.6, "y": 52.35}], "scale": [{"time": 0.3667}, {"time": 0.7667, "x": 1.444, "y": 1.444}, {"time": 1.3667, "x": 1.444, "y": 0.315, "curve": "stepped"}, {"time": 2.3667}, {"time": 2.7667, "x": 1.444, "y": 1.444}, {"time": 3.3667, "x": 1.444, "y": 0.315}]}, "t6": {"rotate": [{"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "angle": -4.63, "curve": "stepped"}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -4.63}], "translate": [{"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.9, "x": -13.6, "y": 52.35, "curve": "stepped"}, {"time": 2.9, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -13.6, "y": 52.35}], "scale": [{"time": 0.9}, {"time": 1.3, "x": 1.444, "y": 1.444}, {"time": 1.9, "x": 1.444, "y": 0.315, "curve": "stepped"}, {"time": 2.9}, {"time": 3.3, "x": 1.444, "y": 1.444}, {"time": 3.9, "x": 1.444, "y": 0.315}]}, "t7": {"rotate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.63, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -4.63}], "translate": [{"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -13.6, "y": 52.35, "curve": "stepped"}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "x": -13.6, "y": 52.35}], "scale": [{"time": 0.6667}, {"time": 1.0667, "x": 1.444, "y": 1.444}, {"time": 1.6667, "x": 1.444, "y": 0.315, "curve": "stepped"}, {"time": 2.6667}, {"time": 3.0667, "x": 1.444, "y": 1.444}, {"time": 3.6667, "x": 1.444, "y": 0.315}]}, "t8": {"rotate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": -4.63, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "angle": -4.63}], "translate": [{"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": -13.6, "y": 52.35, "curve": "stepped"}, {"time": 3, "curve": 0.25, "c3": 0.75}, {"time": 4, "x": -13.6, "y": 52.35}], "scale": [{"time": 1}, {"time": 1.4, "x": 1.444, "y": 1.444}, {"time": 2, "x": 1.444, "y": 0.315, "curve": "stepped"}, {"time": 3}, {"time": 3.4, "x": 1.444, "y": 1.444}, {"time": 4, "x": 1.444, "y": 0.315}]}, "xx3": {"rotate": [{"angle": 139.24, "curve": "stepped"}, {"time": 0.6667, "angle": 139.24, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "curve": "stepped"}, {"time": 2.6667, "angle": 139.24, "curve": 0.25, "c3": 0.75}, {"time": 3.5}], "scale": [{"x": 0.916, "y": 0.916, "curve": "stepped"}, {"time": 0.6667, "x": 0.916, "y": 0.916, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.5, "curve": "stepped"}, {"time": 2.6667, "x": 0.916, "y": 0.916, "curve": 0.25, "c3": 0.75}, {"time": 3.0667}]}, "xx4": {"rotate": [{"angle": 139.24, "curve": "stepped"}, {"time": 0.3, "angle": 139.24, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": "stepped"}, {"time": 2.3, "angle": 139.24, "curve": 0.25, "c3": 0.75}, {"time": 3.1333}], "scale": [{"x": 0.916, "y": 0.916, "curve": "stepped"}, {"time": 0.3, "x": 0.916, "y": 0.916, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 2.3, "x": 0.916, "y": 0.916, "curve": 0.25, "c3": 0.75}, {"time": 2.7}]}, "guang5": {"rotate": [{}, {"time": 3, "angle": -172.3, "curve": "stepped"}, {"time": 4}], "scale": [{"x": 0.633, "y": 0.633}, {"time": 0.9, "x": 1.068, "y": 1.068, "curve": "stepped"}, {"time": 2.1333, "x": 1.068, "y": 1.068}, {"time": 3, "x": 0.633, "y": 0.633}]}, "lightBG_h2": {"rotate": [{"angle": 106.78}, {"time": 1.3, "angle": 175.05, "curve": "stepped"}, {"time": 1.9667}, {"time": 4, "angle": 106.78}]}, "guang6": {"rotate": [{"angle": -149.33}, {"time": 0.4, "angle": -172.3, "curve": "stepped"}, {"time": 1.4}, {"time": 4, "angle": -149.33}], "scale": [{"x": 0.834, "y": 0.834}, {"time": 0.4, "x": 0.633, "y": 0.633, "curve": "stepped"}, {"time": 1.4, "x": 0.633, "y": 0.633}, {"time": 2.3, "x": 1.068, "y": 1.068, "curve": "stepped"}, {"time": 3.5333, "x": 1.068, "y": 1.068}, {"time": 4, "x": 0.834, "y": 0.834}]}, "st13": {"translate": [{"curve": 0.108, "c2": 0.7, "c3": 0.75}, {"time": 1, "x": -30.6, "y": -3.74, "curve": "stepped"}, {"time": 4}]}, "st14": {"translate": [{"x": -30.6, "y": -3.74, "curve": "stepped"}, {"time": 1.8667, "curve": 0.108, "c2": 0.7, "c3": 0.75}, {"time": 2.8667, "x": -30.6, "y": -3.74}]}, "st15": {"translate": [{"x": -2.6, "y": 27.56, "curve": "stepped"}, {"time": 2.9333, "curve": 0.108, "c2": 0.7, "c3": 0.75}, {"time": 3.9333, "x": -2.6, "y": 27.56}]}, "st16": {"translate": [{"x": 19.24, "y": 23.4, "curve": "stepped"}, {"time": 2.2333, "curve": 0.108, "c2": 0.7, "c3": 0.75}, {"time": 3.2333, "x": 19.24, "y": 23.4}]}, "st17": {"translate": [{"x": 20.88, "y": 1.14, "curve": 0.29, "c2": 0.36, "c3": 0.627, "c4": 0.69}, {"time": 0.4, "x": 28.6, "y": 1.56, "curve": "stepped"}, {"time": 3.4, "curve": 0.198, "c2": 0.36, "c3": 0.544, "c4": 0.7}, {"time": 4, "x": 20.88, "y": 1.14}]}, "st18": {"translate": [{"curve": 0.162, "c2": 0.39, "c3": 0.524, "c4": 0.72}, {"time": 1, "x": 24.18, "y": -18.2, "curve": "stepped"}, {"time": 4}]}, "st19": {"translate": [{"y": -38.02, "curve": "stepped"}, {"time": 0.8333, "curve": 0.162, "c2": 0.39, "c3": 0.524, "c4": 0.72}, {"time": 1.8333, "y": -38.02}]}, "st20": {"translate": [{"x": -11, "y": 24.63, "curve": "stepped"}, {"time": 0.3333, "curve": 0.108, "c2": 0.7, "c3": 0.75}, {"time": 1.3333, "x": -35.69, "y": 0.97, "curve": "stepped"}, {"time": 2.1667, "x": -11, "y": 24.63}]}, "st21": {"translate": [{"curve": 0.162, "c2": 0.39, "c3": 0.524, "c4": 0.72}, {"time": 1, "x": 8.12, "y": 29.08, "curve": "stepped"}, {"time": 4}]}, "st22": {"translate": [{"x": 22.79, "y": 13.36, "curve": "stepped"}, {"time": 1.9333, "curve": 0.108, "c2": 0.7, "c3": 0.75}, {"time": 2.9333, "x": 22.79, "y": 13.36}]}, "st23": {"translate": [{"x": 17.29, "y": -17.03, "curve": "stepped"}, {"time": 1.9333, "curve": 0.108, "c2": 0.7, "c3": 0.75}, {"time": 2.9333, "x": 17.29, "y": -17.03}]}}, "deform": {"default": {"di_huang02": {"di_huang02": [{"vertices": [-2.57999, 3.35399, -3.483, 1.935, -1.79398, 2.06998, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.28999, 1.935, -1.935, 3.87, -2.451, 4.38599, -2.967, 2.83799, -0.85599, 0.535, 0, 0, -0.82799, 1.24199]}, {"time": 0.2667, "vertices": [-2.78387, 2.86739, -3.1605, 1.12848, -2.32301, 1.90384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.05988, 2.09251, -1.87123, 3.58477, -2.78013, 3.9259, -3.51128, 2.93804, -0.39946, 0.24966, 0, 0, -0.38639, 0.57959]}, {"time": 0.5, "offset": 20, "vertices": [-0.42058, 0.48902, -0.12871, -0.00861], "curve": "stepped"}, {"time": 2, "offset": 20, "vertices": [-0.42058, 0.48902, -0.12871, -0.00861]}, {"time": 2.1333, "vertices": [-4.08413, 2.28332, -2.69036, 2.43006, -1.95713, 1.31775, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.93634, 1.22482, -3.89651, 2.68412, -4.44757, 2.79972, -4.48872, 1.99021, 0.45438, -0.31525, 0.55907, -0.58015]}, {"time": 2.2667, "vertices": [-2.98399, 2.89268, -2.39538, 2.78864, -2.18701, 1.24478, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.87268, 2.44963, -2.48753, 3.60527, -2.76341, 3.17387, -2.46334, 3.11464]}, {"time": 2.5}]}, "sha": {"sha": [{"vertices": [-7.52337, -5.64929, -5.09097, -8.14514, -0.93279, -5.6683, -0.01186, -0.34134, 0.2184, 3.75266, -2.42653, 4.44147, -5.23506, 4.89597, -6.52403, 5.27264, -7.91484, 2.14108, -8.02628, -0.48593, -7.68815, -3.35471]}, {"time": 0.5, "vertices": [11.00317, 3.513, 15.61093, 1.27548, 21.92888, 2.59369, 23.40883, 4.55362, 22.29733, 5.03301, 17.84485, 4.82528, 13.52296, 3.24299, 12.56614, 3.16953, 14.47722, 4.03085, 14.78661, 2.82495, 14.31042, 3.85266]}, {"time": 0.7, "vertices": [13.15376, 2.55535, 15.47106, 1.58574, 20.70226, 2.9783, 22.2945, 4.54991, 22.19479, 4.85614, 17.78612, 4.54551, 13.3492, 3.41764, 12.52997, 3.04207, 14.77234, 4.06998, 14.89256, 2.8494, 14.41637, 3.8771]}, {"time": 0.8, "vertices": [11.32458, 3.82719, 15.4587, 1.90231, 20.73394, 2.96283, 21.7949, 4.70949, 21.77518, 5.07384, 17.62476, 4.73533, 13.24435, 3.72884, 11.84623, 2.50429, 14.8756, 4.16731, 15.0031, 3.02306, 14.52692, 4.05077]}, {"time": 2, "vertices": [12.19134, 2.62361, 9.79471, 0.94827, 9.68322, 1.65141, 9.34071, 3.55758, 10.37031, 4.47205, 10.1283, 3.92769, 9.08862, 2.81502, 11.97466, 3.95071, 13.57, 2.26631, 14.24189, 1.75077, 13.18761, 3.59717]}, {"time": 2.1, "vertices": [13.77772, -3.34949, 12.71626, -3.32544, 10.75275, -2.83291, 9.70009, 0.01097, 9.23479, -1.46546, 9.35726, -1.11252, 10.09657, 0.15359, 11.63917, 0.1231, 13.59595, -0.77367, 14.58141, -2.74834, 14.19696, -3.25824]}, {"time": 2.1667, "vertices": [11.09039, -10.15452, 7.57299, -10.43667, 7.18369, -7.5834, 4.94426, -3.31639, 4.23251, -1.58728, 4.75803, -0.64714, 3.90981, -1.73348, 4.89913, -2.09483, 10.10443, -8.51902, 13.82943, -7.83657, 13.79418, -9.2773]}, {"time": 2.3333, "vertices": [1.6122, -9.1506, -0.22806, -7.14636, 1.81137, -4.29756, 1.35775, -1.77054, 2.06604, -1.17669, 2.00019, -1.01484, 0.40902, -2.8143, 5.57004, -4.36454, 7.62786, -4.48359, 6.72313, -6.67521, 5.03413, -9.6843]}, {"time": 2.5, "vertices": [0.57651, -1.79998, -1.49384, -2.23496, -0.70752, -2.24143, 0, 0, 0.19058, 0.32556, 0.47949, 0.76157, -0.44257, -0.49716, 0.2702, -0.14137, 0.6879, -0.11699, 1.30225, 0.12843, 0.62047, 0.14123]}, {"time": 4, "vertices": [-7.52337, -5.64929, -5.09097, -8.14514, -0.93279, -5.6683, -0.01186, -0.34134, 0.2184, 3.75266, -2.42653, 4.44147, -5.23506, 4.89597, -6.52403, 5.27264, -7.91484, 2.14108, -8.02628, -0.48593, -7.68815, -3.35471]}]}, "sha2": {"sha": [{"vertices": [-4.18684, -0.65995, -2.80397, -1.53872, -0.91636, -3.97501, -0.24024, -0.54682, 1.1862, 0.63041, -0.25574, 0.34792, -2.41813, -1.02567, -4.46674, -0.82105, -3.22049, -1.1099, -3.02536, -0.73695, -3.54243, -0.55007]}, {"time": 0.2333, "vertices": [1.07463, 6.38068, 4.66631, 7.24174, 5.59801, 9.27386, -0.22958, 9.58148, 0.14385, 5.89518, -0.52888, 3.00569, -2.20762, 0.52023, -4.69258, 0.56498, -2.21638, 4.34934, -2.68749, 3.24217, -3.19577, 4.11362]}, {"time": 0.3667, "vertices": [6.08798, 9.85861, 8.91878, 7.89884, 8.2839, 2.11499, 9.11684, 2.68758, 10.04108, 1.27528, 8.50492, -0.14476, 6.19306, 0.55007, 2.13949, 2.35272, 4.58645, 3.36255, 5.52792, 5.67401, 5.88113, 7.59231]}, {"time": 0.5, "vertices": [6.24966, 2.44334, 7.81472, 3.84589, 9.84849, 0.38433, 9.35679, 1.23842, 10.93933, 1.5657, 8.58844, -0.69265, 6.36229, -2.01138, 4.9226, -0.90711, 7.58015, 1.61256, 7.47477, -0.05466, 7.49078, 0.90444]}, {"time": 1.2667, "vertices": [10.17346, 7.04347, 10.97174, 6.90151, 10.2596, 1.22964, 9.72913, 1.42128, 11.41057, 1.62597, 10.36201, -1.61178, 9.56421, -4.93532, 8.59649, -5.53676, 11.97297, 0.19078, 13.0909, 0.81616, 12.31962, 3.61172]}, {"time": 2, "vertices": [15.22373, 7.71796, 14.36121, 7.42498, 11.55122, 2.74582, 10.08528, 1.59619, 11.86133, 1.68363, 12.66714, -1.14002, 13.0822, -4.26969, 13.9685, -6.4301, 18.07204, 0.0441, 19.09109, 2.26554, 18.14656, 5.44203]}, {"time": 2.5, "vertices": [-4.0144, 0.00261, -5.4314, -1.15181, -10.94493, -2.6501, -13.10798, 0.34914, -10.23603, 1.0248, -7.27621, 0.76006, -4.30225, 0.39134, -4.0144, 0.00261, -4.0144, 0.00261, -4.0144, 0.00261, -4.0144]}, {"time": 4, "vertices": [-4.18684, -0.65995, -2.80397, -1.53872, -0.91636, -3.97501, -0.24024, -0.54682, 1.1862, 0.63041, -0.25574, 0.34792, -2.41813, -1.02567, -4.46674, -0.82105, -3.22049, -1.1099, -3.02536, -0.73695, -3.54243, -0.55007]}]}, "shalou02": {"shalou02": [{}, {"time": 0.5, "offset": 14, "vertices": [-0.74229, -0.51571, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30943, 1.5989, -3.94359, 4.47272, -4.59309, 6.09088, -2.5766, 4.30957]}, {"time": 2, "offset": 14, "vertices": [-0.74229, -0.51571, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.30943, 1.5989, -3.94359, 4.47272, -4.59309, 6.09088, -2.5766, 4.30957, -3.61833, 3.16438, -3.67337, 3.98657, -0.87755, 2.31554]}, {"time": 2.2667, "offset": 14, "vertices": [-0.3464, -0.24066, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.1444, 0.74615, -3.74765, 5.12197, -4.46984, 6.47628, -3.32894, 4.2156, -1.68856, 1.47671, -1.71424, 1.8604, -0.40952, 1.08058]}, {"time": 2.5}]}, "tiao": {"tiao": [{"vertices": [13.71868, -0.69983, 13.84406, 0.07175, 10.49342, 1.11545, 10.54332, 0.23331, 8.72934, 0.09531, 7.07391, -1.85681, 4.61743, -4.53531, 5.92307, 4.35553, 7.1857, 1.84967, 8.70009, 0.85385, 11.6097, 0.58151, 12.21205, -0.99936]}, {"time": 0.5, "vertices": [11.48956, 1.91204, 11.84718, 3.53101, 7.30583, 4.69896, 10.13063, 3.30578, 8.31665, 3.16778, 6.66122, 1.21566, 4.20474, -1.46284, 5.51038, 7.428, 6.77301, 4.92214, 8.2874, 3.92632, 11.19701, 3.65398, 9.52571, 1.57375]}, {"time": 0.6333, "vertices": [11.69159, 2.27501, 11.86929, 3.36635, 7.19901, 4.7996, 9.83388, 4.87327, 9.41999, 3.06903, 21.23046, 1.03, 21.57263, 0.08608, 21.4666, 4.86005, 21.55342, 4.02899, 9.31796, 2.43314, 10.78644, 1.14355, 8.67378, 0.54762], "curve": "stepped"}, {"time": 1.5, "vertices": [11.69159, 2.27501, 11.86929, 3.36635, 7.19901, 4.7996, 9.83388, 4.87327, 9.41999, 3.06903, 21.23046, 1.03, 21.57263, 0.08608, 21.4666, 4.86005, 21.55342, 4.02899, 9.31796, 2.43314, 10.78644, 1.14355, 8.67378, 0.54762]}, {"time": 1.7, "vertices": [12.42276, 2.16969, 12.79911, 3.22475, 8.70125, 4.65242, 9.83388, 4.87327, 9.41999, 3.06903, 21.23046, 1.03, 21.57263, 0.08608, 21.4666, 4.86005, 21.55342, 4.02899, 9.31796, 2.43314, 10.78644, 1.14355, 10.02736, 1.29141]}, {"time": 1.8667, "vertices": [14.14662, 1.95214, 14.28742, 3.81476, 10.08382, 4.31464, 10.43084, 5.1181, 9.41999, 3.06903, 21.23046, 1.03, 21.57263, 0.08608, 21.4666, 4.86005, 21.55342, 4.02899, 9.31796, 2.43314, 11.25548, 1.11448, 11.49981, 1.73884]}, {"time": 2, "vertices": [14.60594, 3.88079, 14.67796, 2.4394, 11.31382, 0.94702, 11.10054, 2.85591, 9.46703, 2.41184, 20.98017, 0.09952, 21.57263, 0.08608, 21.4666, 4.86005, 21.5243, 4.38195, 10.03877, 3.77162, 12.1408, 3.65395, 12.85716, 5.36205]}, {"time": 2.5, "vertices": [13.81631, 4.88287, 13.07843, -3.40656, 8.20322, -8.95163, 4.51647, -5.32516, 0.43589, -3.32272, -2.51276, -4.44714, -5.66182, -6.9144, -5.32198, 4.56232, -2.5413, 3.09717, 0.07971, 2.55844, 5.5856, 5.49805, 10.9008, 9.5611]}, {"time": 2.7, "vertices": [12.58368, 5.51101, 12.48452, -3.62027, 7.9005, -9.73645, 2.41126, -5.5099, -2.5287, -1.97096, -13.46662, -3.53691, -17.05743, -7.11374, -17.12436, 8.04346, -13.34736, 3.58832, -2.28485, 1.8049, 2.72868, 3.96649, 8.77467, 9.86954], "curve": "stepped"}, {"time": 2.8, "vertices": [12.58368, 5.51101, 12.48452, -3.62027, 7.9005, -9.73645, 2.41126, -5.5099, -2.5287, -1.97096, -13.46662, -3.53691, -17.05743, -7.11374, -17.12436, 8.04346, -13.34736, 3.58832, -2.28485, 1.8049, 2.72868, 3.96649, 8.77467, 9.86954]}, {"time": 3.7, "vertices": [9.98885, 3.84571, 9.3942, -3.51978, 7.35758, -10.52482, 2.48722, -4.88847, -2.5287, -1.97096, -13.46662, -3.53691, -17.05743, -7.11374, -17.12436, 8.04346, -13.34736, 3.58832, -2.28485, 1.8049, 3.28824, 5.4297, 8.77467, 9.86954]}, {"time": 3.9, "vertices": [9.50671, 3.23606, 9.48385, -1.2164, 5.63443, -6.24509, 2.48307, -1.49074, -3.08958, -1.39505, -6.82556, -4.89948, -10.38566, -7.00414, -11.94001, 5.97183, -6.72777, 5.28663, -2.74766, 2.43336, 3.29783, 2.81591, 7.47762, 7.45469]}, {"time": 3.9667, "vertices": [9.59438, 2.23806, 9.08741, -1.25381, 3.75983, -2.91373, -0.7003, -0.44084, -4.80986, -1.52734, -6.62525, -4.18878, -9.04078, -6.50525, -8.91883, 5.61512, -4.8338, 3.72721, -2.87761, 2.0089, 3.22136, 2.44523, 6.53821, 4.82305]}, {"time": 4, "vertices": [3.05769, 2.91364, 3.09468, -0.55023, 0.47759, -2.89371, -1.76352, -1.12007, -3.06331, -2.03854, -8.74897, -5.71165, -9.04078, -6.50525, -8.91883, 5.61512, -5.5158, 7.18363, -2.53714, 4.02464, 0.3396, 4.20372, 2.78913, 5.27079]}]}}}}}}