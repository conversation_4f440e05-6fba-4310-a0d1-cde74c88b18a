{"skeleton": {"hash": "8Mwa/hxeLHeYfrhj86Xt5HU5xbw", "spine": "3.8.99", "x": -663.51, "y": -976.94, "width": 760.75, "height": 1334, "images": "./image/", "audio": ""}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "length": 163.75, "x": -14.23, "y": 0.43}, {"name": "bone", "parent": "all", "length": 22.71, "x": 10.25, "y": 1.14}, {"name": "ttt", "parent": "all", "x": -29.74, "y": 23.55, "scaleY": 0.5257}, {"name": "ttt3", "parent": "ttt", "length": 2.51, "rotation": 178.58, "x": 83.33, "y": -0.06, "scaleY": 0.7032}, {"name": "ttt3b", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3c", "parent": "ttt3b", "length": 2.51, "x": 2.51}, {"name": "ttt3d", "parent": "ttt3c", "length": 2.51, "x": 2.51}, {"name": "ttt3e", "parent": "ttt3d", "length": 2.51, "x": 2.51}, {"name": "ttt3f", "parent": "ttt3e", "length": 2.51, "x": 2.51}, {"name": "ttt3g", "parent": "ttt3f", "length": 2.51, "x": 2.51}, {"name": "ttt3h", "parent": "ttt3g", "length": 2.51, "x": 2.51}, {"name": "ttt3i", "parent": "ttt3h", "length": 2.51, "x": 2.51}, {"name": "ttt3j", "parent": "ttt3i", "length": 2.51, "x": 2.51}, {"name": "ttt3k", "parent": "ttt3j", "length": 2.51, "x": 2.51}, {"name": "ttt3l", "parent": "ttt3k", "length": 2.51, "x": 2.51}, {"name": "ttt3m", "parent": "ttt3l", "length": 2.51, "x": 2.51}, {"name": "ttt3n", "parent": "ttt3m", "length": 2.51, "x": 2.51}, {"name": "ttt3o", "parent": "ttt3n", "length": 2.51, "x": 2.51}, {"name": "ttt3p", "parent": "ttt3o", "length": 2.51, "x": 2.51}, {"name": "ttt3q", "parent": "ttt3p", "length": 2.51, "x": 2.51}, {"name": "ttt3r", "parent": "ttt3q", "length": 2.51, "x": 2.51}, {"name": "ttt3s", "parent": "ttt3r", "length": 2.51, "x": 2.51}, {"name": "ttt3t", "parent": "ttt3s", "length": 2.51, "x": 2.51}, {"name": "ttt3u", "parent": "ttt3t", "length": 2.51, "x": 2.51}, {"name": "ttt3v", "parent": "ttt3u", "length": 2.51, "x": 2.51}, {"name": "ttt3w", "parent": "ttt3v", "length": 2.51, "x": 2.51}, {"name": "ttt3x", "parent": "ttt3w", "length": 2.51, "x": 2.51}, {"name": "ttt3y", "parent": "ttt3x", "length": 2.51, "x": 2.51}, {"name": "ttt3z", "parent": "ttt3y", "length": 2.51, "x": 2.51}, {"name": "ttt3{", "parent": "ttt3z", "length": 2.51, "x": 2.51}, {"name": "ttt3|", "parent": "ttt3{", "length": 2.51, "x": 2.51}, {"name": "ttt3}", "parent": "ttt3|", "length": 2.51, "x": 2.51}, {"name": "ttt3~", "parent": "ttt3}", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3~", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt3", "parent": "ttt3", "length": 2.51, "x": 2.51}, {"name": "ttt4", "parent": "ttt", "length": 2.51, "rotation": 178.58, "x": 83.33, "y": -0.06, "scaleY": 0.7032}, {"name": "ttt3b2", "parent": "ttt4", "length": 2.51, "x": 2.51}, {"name": "ttt3c2", "parent": "ttt3b2", "length": 2.51, "x": 2.51}, {"name": "ttt3d2", "parent": "ttt3c2", "length": 2.51, "x": 2.51}, {"name": "ttt3e2", "parent": "ttt3d2", "length": 2.51, "x": 2.51}, {"name": "ttt3f2", "parent": "ttt3e2", "length": 2.51, "x": 2.51}, {"name": "ttt3g2", "parent": "ttt3f2", "length": 2.51, "x": 2.51}, {"name": "ttt3h2", "parent": "ttt3g2", "length": 2.51, "x": 2.51}, {"name": "ttt3i2", "parent": "ttt3h2", "length": 2.51, "x": 2.51}, {"name": "ttt3j2", "parent": "ttt3i2", "length": 2.51, "x": 2.51}, {"name": "ttt3k2", "parent": "ttt3j2", "length": 2.51, "x": 2.51}, {"name": "ttt3l2", "parent": "ttt3k2", "length": 2.51, "x": 2.51}, {"name": "ttt3m2", "parent": "ttt3l2", "length": 2.51, "x": 2.51}, {"name": "ttt3n2", "parent": "ttt3m2", "length": 2.51, "x": 2.51}, {"name": "ttt3o2", "parent": "ttt3n2", "length": 2.51, "x": 2.51}, {"name": "ttt3p2", "parent": "ttt3o2", "length": 2.51, "x": 2.51}, {"name": "ttt3q2", "parent": "ttt3p2", "length": 2.51, "x": 2.51}, {"name": "ttt3r2", "parent": "ttt3q2", "length": 2.51, "x": 2.51}, {"name": "ttt3s2", "parent": "ttt3r2", "length": 2.51, "x": 2.51}, {"name": "ttt3t2", "parent": "ttt3s2", "length": 2.51, "x": 2.51}, {"name": "ttt3u2", "parent": "ttt3t2", "length": 2.51, "x": 2.51}, {"name": "ttt3v2", "parent": "ttt3u2", "length": 2.51, "x": 2.51}, {"name": "ttt3w2", "parent": "ttt3v2", "length": 2.51, "x": 2.51}, {"name": "ttt3x2", "parent": "ttt3w2", "length": 2.51, "x": 2.51}, {"name": "ttt3y2", "parent": "ttt3x2", "length": 2.51, "x": 2.51}, {"name": "ttt3z2", "parent": "ttt3y2", "length": 2.51, "x": 2.51}, {"name": "ttt3{2", "parent": "ttt3z2", "length": 2.51, "x": 2.51}, {"name": "ttt3|2", "parent": "ttt3{2", "length": 2.51, "x": 2.51}, {"name": "ttt3}2", "parent": "ttt3|2", "length": 2.51, "x": 2.51}, {"name": "ttt3~2", "parent": "ttt3}2", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt3~2", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "ttt32", "parent": "ttt32", "length": 2.51, "x": 2.51}, {"name": "sg", "parent": "all", "length": 47.53, "rotation": 58.79, "x": -31.81, "y": -0.22, "scaleX": 1.8376, "scaleY": 0.8678}, {"name": "xxx1", "parent": "all", "x": -71.2, "y": 22.24, "scaleX": 0.3675, "scaleY": 0.3675}, {"name": "xxx2", "parent": "all", "rotation": -32.52, "x": -31.77, "y": -5.36, "scaleX": 0.3675, "scaleY": 0.3675}, {"name": "xxx3", "parent": "all", "rotation": 80.83, "x": 21.45, "y": 11.46, "scaleX": 0.4489, "scaleY": 0.4489}, {"name": "xxx4", "parent": "all", "rotation": 16.27, "x": 82.75, "y": -13.04, "scaleX": 0.6208, "scaleY": 0.6208}, {"name": "maoxian_btn_03", "parent": "all", "length": 24.88, "rotation": 78.89, "x": 60.86, "y": -17.18}], "slots": [{"name": "path01", "bone": "bone", "attachment": "path01"}, {"name": "ttt", "bone": "ttt3", "attachment": "ttt", "blend": "additive"}, {"name": "ttt3", "bone": "ttt4", "attachment": "ttt", "blend": "additive"}, {"name": "ttt2", "bone": "ttt3", "attachment": "ttt", "blend": "additive"}, {"name": "ttt4", "bone": "ttt4", "attachment": "ttt", "blend": "additive"}, {"name": "mask", "bone": "all", "attachment": "mask"}, {"name": "sg", "bone": "sg", "color": "ffffff8f", "attachment": "sg", "blend": "additive"}, {"name": "maoxian_btn_03", "bone": "maoxian_btn_03", "attachment": "maoxian_btn_03"}, {"name": "maoxian_btn_3", "bone": "maoxian_btn_03", "color": "ffffff00", "attachment": "biyan"}, {"name": "xxx1", "bone": "xxx1", "attachment": "xxx1"}, {"name": "xxx2", "bone": "xxx2", "attachment": "xxx1"}, {"name": "xxx3", "bone": "xxx3", "attachment": "xxx1"}, {"name": "xxx4", "bone": "xxx4", "attachment": "xxx1"}], "path": [{"name": "path01", "bones": ["ttt3", "ttt3b", "ttt3c", "ttt3d", "ttt3e", "ttt3f", "ttt3g", "ttt3h", "ttt3i", "ttt3j", "ttt3k", "ttt3l", "ttt3m", "ttt3n", "ttt3o", "ttt3p", "ttt3q", "ttt3r", "ttt3s", "ttt3t", "ttt3u", "ttt3v", "ttt3w", "ttt3x", "ttt3y", "ttt3z", "ttt3{", "ttt3|", "ttt3}", "ttt3~", "ttt3", "ttt3", "ttt3", "ttt3", "ttt3", "ttt3", "ttt3", "ttt3", "ttt3", "ttt3"], "target": "path01"}, {"name": "path02", "order": 1, "bones": ["ttt4", "ttt3b2", "ttt3c2", "ttt3d2", "ttt3e2", "ttt3f2", "ttt3g2", "ttt3h2", "ttt3i2", "ttt3j2", "ttt3k2", "ttt3l2", "ttt3m2", "ttt3n2", "ttt3o2", "ttt3p2", "ttt3q2", "ttt3r2", "ttt3s2", "ttt3t2", "ttt3u2", "ttt3v2", "ttt3w2", "ttt3x2", "ttt3y2", "ttt3z2", "ttt3{2", "ttt3|2", "ttt3}2", "ttt3~2", "ttt32", "ttt32", "ttt32", "ttt32", "ttt32", "ttt32", "ttt32", "ttt32", "ttt32"], "target": "path01"}], "skins": [{"name": "default", "attachments": {"maoxian_btn_03": {"maoxian_btn_03": {"x": 23.74, "y": -1.97, "rotation": -78.89, "width": 58, "height": 52}}, "maoxian_btn_3": {"biyan": {"x": 23.74, "y": -1.97, "rotation": -78.89, "width": 58, "height": 52}}, "mask": {"mask": {"type": "clipping", "end": "sg", "vertexCount": 13, "vertices": [-74.11, 22.28, -77.63, 19.09, -73.34, 1.49, -77.5, -13.43, -74.31, -17.61, 26.62, -17.74, 30.47, -12.49, 34.5, -17.21, 105.2, -17.56, 105.55, 22.69, -34.24, 22.42, -37.56, 15.94, -41.76, 22.07], "color": "ce3a3aff"}}, "path01": {"path01": {"type": "path", "closed": true, "lengths": [7.15, 13.19, 18.14, 25.18, 32.97, 46.64, 89.28, 270.33, 277.27, 311.24, 317.42, 456.56], "vertexCount": 36, "vertices": [38.4, 22.81, 54.1, 23.01, 56.07, 25.21, 57.33, 27.18, 59.04, 28.14, 60.75, 29.1, 63.34, 29.27, 64.93, 29.09, 66.52, 28.91, 68.11, 28.04, 69.46, 27.21, 71.24, 27.59, 74.31, 28, 76.44, 27.26, 78.56, 26.51, 81.74, 24.05, 82.57, 22.6, 86.48, 22.65, 91.2, 22.79, 96.23, 22.67, 96.18, 10.27, 96.1, -10.6, 96.15, -19.97, 77.15, -20.19, -66.65, -20.31, -84.9, -20.25, -86.64, -20.09, -89.2, -17.44, -89.27, -15.34, -89.37, -10.38, -89.1, 12.57, -89.28, 18.63, -89.32, 20.28, -86.35, 22.58, -85.04, 22.73, -76.08, 22.89]}}, "sg": {"sg": {"x": 8.03, "y": -0.29, "rotation": -90, "width": 43, "height": 78}}, "ttt": {"ttt": {"type": "mesh", "uvs": [0.49931, 0, 0.44481, 0.06168, 0.40289, 0.13504, 0.33581, 0.21679, 0.29389, 0.29854, 0.26873, 0.38029, 0.26454, 0.45785, 0.26454, 0.5375, 0.27712, 0.62345, 0.29808, 0.701, 0.31904, 0.79114, 0.33581, 0.86031, 0.37774, 0.94416, 0.44481, 1, 0.53704, 0.95045, 0.6125, 0.87079, 0.65862, 0.78485, 0.69216, 0.69826, 0.72989, 0.61232, 0.74247, 0.53578, 0.74666, 0.44984, 0.74666, 0.37225, 0.73408, 0.27792, 0.70893, 0.20037, 0.66281, 0.127, 0.60412, 0.04316, 0.56639, 0, 0.46049, 0.89188, 0.49375, 0.81412, 0.48997, 0.73446, 0.49237, 0.65484, 0.50709, 0.57694, 0.50939, 0.50058, 0.51778, 0.42751, 0.54476, 0.35295, 0.55349, 0.26851, 0.5316, 0.17408, 0.53999, 0.10101], "triangles": [13, 12, 14, 12, 27, 14, 14, 27, 15, 12, 11, 27, 27, 28, 15, 27, 11, 28, 15, 28, 16, 11, 10, 28, 10, 29, 28, 28, 29, 16, 10, 9, 29, 16, 29, 17, 29, 30, 17, 29, 9, 30, 9, 8, 30, 17, 30, 18, 30, 31, 18, 30, 8, 31, 8, 7, 31, 18, 31, 19, 31, 32, 19, 31, 7, 32, 7, 6, 32, 19, 32, 20, 32, 33, 20, 32, 6, 33, 6, 5, 33, 33, 21, 20, 33, 34, 21, 33, 5, 34, 5, 4, 34, 34, 22, 21, 34, 4, 35, 34, 35, 22, 4, 3, 35, 35, 23, 22, 3, 36, 35, 35, 36, 23, 3, 2, 36, 36, 24, 23, 36, 2, 37, 36, 37, 24, 2, 1, 37, 37, 25, 24, 37, 1, 25, 25, 0, 26, 0, 25, 1], "vertices": [1, 4, -2.27, -1.49, 1, 3, 4, 4.26, -4.07, 0.0335, 5, 1.74, -4.07, 0.69671, 6, -0.77, -4.07, 0.26979, 5, 7, 4.43, -5.94, 0.11483, 8, 1.92, -5.94, 0.48101, 9, -0.59, -5.94, 0.34831, 10, -3.1, -5.94, 0.05538, 11, -5.61, -5.94, 0.00047, 7, 9, 8.04, -9.09, 0.00115, 10, 5.53, -9.09, 0.08862, 11, 3.02, -9.09, 0.34696, 12, 0.51, -9.09, 0.35037, 13, -2, -9.09, 0.15024, 14, -4.51, -9.09, 0.05973, 15, -7.02, -9.09, 0.00292, 8, 12, 9.09, -10.93, 0.00494, 13, 6.58, -10.93, 0.05168, 14, 4.07, -10.93, 0.20663, 15, 1.56, -10.93, 0.40117, 16, -0.95, -10.93, 0.2172, 17, -3.46, -10.93, 0.1021, 18, -5.97, -10.93, 0.01348, 19, -8.49, -10.93, 0.00282, 9, 15, 10.11, -11.9, 0.00831, 16, 7.6, -11.9, 0.02573, 17, 5.08, -11.9, 0.20501, 18, 2.57, -11.9, 0.26579, 19, 0.06, -11.9, 0.31562, 20, -2.45, -11.9, 0.13698, 21, -4.96, -11.9, 0.03977, 22, -7.47, -11.9, 0.0025, 23, -9.98, -11.9, 0.00028, 10, 17, 13.15, -11.79, 0.00065, 18, 10.64, -11.79, 0.00333, 19, 8.13, -11.79, 0.03989, 20, 5.62, -11.79, 0.12781, 21, 3.11, -11.79, 0.31367, 22, 0.6, -11.79, 0.26503, 23, -1.92, -11.79, 0.1758, 24, -4.43, -11.79, 0.06623, 25, -6.94, -11.79, 0.00753, 26, -9.45, -11.79, 5e-05, 9, 21, 11.38, -11.46, 0.00446, 22, 8.87, -11.46, 0.01958, 23, 6.36, -11.46, 0.05992, 24, 3.85, -11.46, 0.28594, 25, 1.34, -11.46, 0.30347, 26, -1.17, -11.46, 0.23539, 27, -3.68, -11.46, 0.0815, 28, -6.19, -11.46, 0.00859, 29, -8.71, -11.46, 0.00115, 9, 24, 12.76, -10.45, 1e-05, 25, 10.24, -10.45, 0.00143, 26, 7.73, -10.45, 0.03237, 27, 5.22, -10.45, 0.14464, 28, 2.71, -10.46, 0.29982, 29, 0.2, -10.46, 0.38361, 30, -2.31, -10.46, 0.11123, 31, -4.82, -10.46, 0.0265, 32, -7.33, -10.46, 0.00039, 7, 29, 8.22, -9.04, 0.01652, 30, 5.7, -9.04, 0.09497, 31, 3.19, -9.04, 0.3377, 32, 0.68, -9.04, 0.39696, 33, -1.83, -9.04, 0.12755, 34, -4.34, -9.04, 0.02534, 35, -6.85, -9.04, 0.00096, 6, 33, 7.49, -7.58, 0.02115, 34, 4.98, -7.58, 0.10756, 35, 2.47, -7.58, 0.39757, 36, -0.04, -7.58, 0.37895, 37, -2.55, -7.58, 0.09155, 38, -5.06, -7.58, 0.00323, 5, 36, 7.11, -6.42, 0.01076, 37, 4.6, -6.42, 0.1056, 38, 2.09, -6.42, 0.5601, 39, -0.42, -6.42, 0.30032, 40, -2.93, -6.42, 0.02322, 4, 40, 5.69, -3.9, 0.00353, 41, 3.18, -3.9, 0.33728, 42, 0.67, -3.9, 0.65563, 43, -1.84, -3.9, 0.00356, 1, 43, 3.82, -0.18, 1, 4, 40, 6.02, 4.41, 0.00216, 41, 3.51, 4.41, 0.23111, 42, 1, 4.41, 0.73992, 43, -1.52, 4.41, 0.02681, 6, 36, 7.63, 8, 0.00882, 37, 5.12, 8, 0.10025, 38, 2.61, 8, 0.39939, 39, 0.1, 8, 0.37692, 40, -2.42, 8, 0.11308, 41, -4.93, 8, 0.00154, 7, 32, 8.65, 10.04, 0.01141, 33, 6.14, 10.04, 0.07865, 34, 3.63, 10.04, 0.24906, 35, 1.11, 10.04, 0.33976, 36, -1.4, 10.04, 0.28288, 37, -3.91, 10.04, 0.03627, 38, -6.42, 10.04, 0.00197, 8, 27, 12.14, 11.42, 0.00038, 28, 9.62, 11.42, 0.00418, 29, 7.11, 11.42, 0.08035, 30, 4.6, 11.42, 0.12856, 31, 2.09, 11.42, 0.30163, 32, -0.42, 11.42, 0.31298, 33, -2.93, 11.42, 0.14531, 34, -5.44, 11.42, 0.0266, 10, 23, 13.17, 13.02, 0.0002, 24, 10.66, 13.02, 0.00726, 25, 8.15, 13.02, 0.02303, 26, 5.64, 13.02, 0.10519, 27, 3.13, 13.02, 0.26371, 28, 0.62, 13.02, 0.29859, 29, -1.9, 13.02, 0.22035, 30, -4.41, 13.02, 0.05899, 31, -6.92, 13.02, 0.02192, 32, -9.43, 13.02, 0.00077, 10, 20, 12.73, 13.36, 0.00019, 21, 10.22, 13.36, 0.01254, 22, 7.7, 13.36, 0.03697, 23, 5.19, 13.36, 0.13554, 24, 2.68, 13.36, 0.28342, 25, 0.17, 13.36, 0.2419, 26, -2.34, 13.36, 0.19546, 27, -4.85, 13.36, 0.0786, 28, -7.36, 13.36, 0.01431, 29, -9.87, 13.36, 0.00107, 10, 17, 11.32, 13.22, 0.00338, 18, 8.81, 13.22, 0.02671, 19, 6.3, 13.22, 0.12365, 20, 3.79, 13.22, 0.18783, 21, 1.28, 13.22, 0.30607, 22, -1.24, 13.22, 0.21285, 23, -3.75, 13.22, 0.08952, 24, -6.26, 13.22, 0.04671, 25, -8.77, 13.22, 0.00266, 26, -11.28, 13.22, 0.00062, 9, 14, 10.79, 12.9, 0.00401, 15, 8.28, 12.9, 0.0457, 16, 5.77, 12.9, 0.05744, 17, 3.26, 12.9, 0.26657, 18, 0.75, 12.9, 0.30673, 19, -1.76, 12.9, 0.21835, 20, -4.28, 12.9, 0.07972, 21, -6.79, 12.9, 0.01908, 22, -9.3, 12.9, 0.0024, 8, 11, 8.55, 11.86, 0.01218, 12, 6.04, 11.86, 0.07562, 13, 3.53, 11.86, 0.21366, 14, 1.02, 11.86, 0.35463, 15, -1.5, 11.86, 0.24075, 16, -4.01, 11.86, 0.06327, 17, -6.52, 11.86, 0.03744, 18, -9.03, 11.86, 0.00245, 8, 8, 8.08, 10.23, 0.00146, 9, 5.56, 10.23, 0.06573, 10, 3.05, 10.23, 0.2803, 11, 0.54, 10.23, 0.38374, 12, -1.97, 10.23, 0.21156, 13, -4.48, 10.23, 0.04752, 14, -6.99, 10.23, 0.00966, 15, -9.5, 10.23, 3e-05, 6, 6, 5.57, 7.53, 0.03765, 7, 3.06, 7.53, 0.28807, 8, 0.55, 7.53, 0.44447, 9, -1.96, 7.53, 0.18036, 10, -4.48, 7.53, 0.0461, 11, -6.99, 7.53, 0.00336, 3, 4, 2, 4.13, 0.58875, 5, -0.51, 4.13, 0.40764, 6, -3.02, 4.13, 0.00361, 1, 4, -2.41, 1.99, 1, 4, 38, 5.11, 0.19, 0.00118, 39, 2.6, 0.19, 0.43838, 40, 0.09, 0.19, 0.55957, 41, -2.42, 0.19, 0.00087, 4, 35, 4.5, 1.59, 0.02581, 36, 1.99, 1.59, 0.7818, 37, -0.53, 1.59, 0.18292, 38, -3.04, 1.59, 0.00948, 5, 29, 11.29, 1.06, 1e-05, 32, 3.76, 1.06, 0.01002, 33, 1.25, 1.06, 0.98063, 34, -1.26, 1.06, 0.00919, 36, -6.28, 1.06, 0.00014, 6, 27, 8.04, 0.86, 0, 29, 3.02, 0.86, 0.14789, 30, 0.5, 0.86, 0.84641, 31, -2.01, 0.86, 0.00521, 32, -4.52, 0.86, 0.00045, 33, -7.03, 0.86, 3e-05, 5, 24, 7.45, 1.3, 0.0002, 25, 4.94, 1.3, 0.00868, 26, 2.42, 1.3, 0.54398, 27, -0.09, 1.3, 0.44473, 28, -2.6, 1.3, 0.00241, 6, 21, 7.04, 1.1, 0.00024, 22, 4.53, 1.1, 0.00793, 23, 2.02, 1.1, 0.76642, 24, -0.49, 1.1, 0.22182, 25, -3.01, 1.1, 0.00255, 26, -5.52, 1.1, 0.00103, 6, 18, 6.96, 1.24, 0.00269, 19, 4.45, 1.24, 0.0102, 20, 1.94, 1.24, 0.84129, 21, -0.57, 1.24, 0.14173, 22, -3.08, 1.24, 0.004, 24, -8.1, 1.24, 8e-05, 7, 14, 9.2, 2.33, 0.00051, 15, 6.69, 2.33, 0.00684, 16, 4.18, 2.33, 0.02213, 17, 1.67, 2.33, 0.77374, 18, -0.84, 2.33, 0.18603, 19, -3.35, 2.33, 0.01037, 20, -5.86, 2.33, 0.00037, 6, 12, 5.43, 2.43, 0.00213, 13, 2.92, 2.43, 0.29991, 14, 0.41, 2.43, 0.65474, 15, -2.1, 2.43, 0.03933, 16, -4.61, 2.43, 0.00189, 17, -7.12, 2.43, 0.002, 3, 9, 3.2, 0.91, 0.08143, 10, 0.69, 0.91, 0.91838, 12, -4.33, 0.91, 0.00019, 3, 6, 3.12, 1.04, 0.15467, 7, 0.61, 1.04, 0.83082, 8, -1.9, 1.04, 0.01451], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 52, "height": 104}}, "ttt2": {"ttt": {"type": "mesh", "uvs": [0.49931, 0, 0.44481, 0.06168, 0.40289, 0.13504, 0.33581, 0.21679, 0.29389, 0.29854, 0.26873, 0.38029, 0.26454, 0.45785, 0.26454, 0.5375, 0.27712, 0.62345, 0.29808, 0.701, 0.31904, 0.79114, 0.33581, 0.86031, 0.37774, 0.94416, 0.44481, 1, 0.53704, 0.95045, 0.6125, 0.87079, 0.65862, 0.78485, 0.69216, 0.69826, 0.72989, 0.61232, 0.74247, 0.53578, 0.74666, 0.44984, 0.74666, 0.37225, 0.73408, 0.27792, 0.70893, 0.20037, 0.66281, 0.127, 0.60412, 0.04316, 0.56639, 0, 0.46049, 0.89188, 0.49375, 0.81412, 0.48997, 0.73446, 0.49237, 0.65484, 0.50709, 0.57694, 0.50939, 0.50058, 0.51778, 0.42751, 0.54476, 0.35295, 0.55349, 0.26851, 0.5316, 0.17408, 0.53999, 0.10101], "triangles": [13, 12, 14, 12, 27, 14, 14, 27, 15, 12, 11, 27, 27, 28, 15, 27, 11, 28, 15, 28, 16, 11, 10, 28, 10, 29, 28, 28, 29, 16, 10, 9, 29, 16, 29, 17, 29, 30, 17, 29, 9, 30, 9, 8, 30, 17, 30, 18, 30, 31, 18, 30, 8, 31, 8, 7, 31, 18, 31, 19, 31, 32, 19, 31, 7, 32, 7, 6, 32, 19, 32, 20, 32, 33, 20, 32, 6, 33, 6, 5, 33, 33, 21, 20, 33, 34, 21, 33, 5, 34, 5, 4, 34, 34, 22, 21, 34, 4, 35, 34, 35, 22, 4, 3, 35, 35, 23, 22, 3, 36, 35, 35, 36, 23, 3, 2, 36, 36, 24, 23, 36, 2, 37, 36, 37, 24, 2, 1, 37, 37, 25, 24, 37, 1, 25, 25, 0, 26, 0, 25, 1], "vertices": [1, 4, -2.27, -1.49, 1, 3, 4, 4.26, -4.07, 0.0335, 5, 1.74, -4.07, 0.69671, 6, -0.77, -4.07, 0.26979, 5, 7, 4.43, -5.94, 0.11483, 8, 1.92, -5.94, 0.48101, 9, -0.59, -5.94, 0.34831, 10, -3.1, -5.94, 0.05538, 11, -5.61, -5.94, 0.00047, 7, 9, 8.04, -9.09, 0.00115, 10, 5.53, -9.09, 0.08862, 11, 3.02, -9.09, 0.34696, 12, 0.51, -9.09, 0.35037, 13, -2, -9.09, 0.15024, 14, -4.51, -9.09, 0.05973, 15, -7.02, -9.09, 0.00292, 8, 12, 9.09, -10.93, 0.00494, 13, 6.58, -10.93, 0.05168, 14, 4.07, -10.93, 0.20663, 15, 1.56, -10.93, 0.40117, 16, -0.95, -10.93, 0.2172, 17, -3.46, -10.93, 0.1021, 18, -5.97, -10.93, 0.01348, 19, -8.49, -10.93, 0.00282, 9, 15, 10.11, -11.9, 0.00831, 16, 7.6, -11.9, 0.02573, 17, 5.08, -11.9, 0.20501, 18, 2.57, -11.9, 0.26579, 19, 0.06, -11.9, 0.31562, 20, -2.45, -11.9, 0.13698, 21, -4.96, -11.9, 0.03977, 22, -7.47, -11.9, 0.0025, 23, -9.98, -11.9, 0.00028, 10, 17, 13.15, -11.79, 0.00065, 18, 10.64, -11.79, 0.00333, 19, 8.13, -11.79, 0.03989, 20, 5.62, -11.79, 0.12781, 21, 3.11, -11.79, 0.31367, 22, 0.6, -11.79, 0.26503, 23, -1.92, -11.79, 0.1758, 24, -4.43, -11.79, 0.06623, 25, -6.94, -11.79, 0.00753, 26, -9.45, -11.79, 5e-05, 9, 21, 11.38, -11.46, 0.00446, 22, 8.87, -11.46, 0.01958, 23, 6.36, -11.46, 0.05992, 24, 3.85, -11.46, 0.28594, 25, 1.34, -11.46, 0.30347, 26, -1.17, -11.46, 0.23539, 27, -3.68, -11.46, 0.0815, 28, -6.19, -11.46, 0.00859, 29, -8.71, -11.46, 0.00115, 9, 24, 12.76, -10.45, 1e-05, 25, 10.24, -10.45, 0.00143, 26, 7.73, -10.45, 0.03237, 27, 5.22, -10.45, 0.14464, 28, 2.71, -10.46, 0.29982, 29, 0.2, -10.46, 0.38361, 30, -2.31, -10.46, 0.11123, 31, -4.82, -10.46, 0.0265, 32, -7.33, -10.46, 0.00039, 7, 29, 8.22, -9.04, 0.01652, 30, 5.7, -9.04, 0.09497, 31, 3.19, -9.04, 0.3377, 32, 0.68, -9.04, 0.39696, 33, -1.83, -9.04, 0.12755, 34, -4.34, -9.04, 0.02534, 35, -6.85, -9.04, 0.00096, 6, 33, 7.49, -7.58, 0.02115, 34, 4.98, -7.58, 0.10756, 35, 2.47, -7.58, 0.39757, 36, -0.04, -7.58, 0.37895, 37, -2.55, -7.58, 0.09155, 38, -5.06, -7.58, 0.00323, 5, 36, 7.11, -6.42, 0.01076, 37, 4.6, -6.42, 0.1056, 38, 2.09, -6.42, 0.5601, 39, -0.42, -6.42, 0.30032, 40, -2.93, -6.42, 0.02322, 4, 40, 5.69, -3.9, 0.00353, 41, 3.18, -3.9, 0.33728, 42, 0.67, -3.9, 0.65563, 43, -1.84, -3.9, 0.00356, 1, 43, 3.82, -0.18, 1, 4, 40, 6.02, 4.41, 0.00216, 41, 3.51, 4.41, 0.23111, 42, 1, 4.41, 0.73992, 43, -1.52, 4.41, 0.02681, 6, 36, 7.63, 8, 0.00882, 37, 5.12, 8, 0.10025, 38, 2.61, 8, 0.39939, 39, 0.1, 8, 0.37692, 40, -2.42, 8, 0.11308, 41, -4.93, 8, 0.00154, 7, 32, 8.65, 10.04, 0.01141, 33, 6.14, 10.04, 0.07865, 34, 3.63, 10.04, 0.24906, 35, 1.11, 10.04, 0.33976, 36, -1.4, 10.04, 0.28288, 37, -3.91, 10.04, 0.03627, 38, -6.42, 10.04, 0.00197, 8, 27, 12.14, 11.42, 0.00038, 28, 9.62, 11.42, 0.00418, 29, 7.11, 11.42, 0.08035, 30, 4.6, 11.42, 0.12856, 31, 2.09, 11.42, 0.30163, 32, -0.42, 11.42, 0.31298, 33, -2.93, 11.42, 0.14531, 34, -5.44, 11.42, 0.0266, 10, 23, 13.17, 13.02, 0.0002, 24, 10.66, 13.02, 0.00726, 25, 8.15, 13.02, 0.02303, 26, 5.64, 13.02, 0.10519, 27, 3.13, 13.02, 0.26371, 28, 0.62, 13.02, 0.29859, 29, -1.9, 13.02, 0.22035, 30, -4.41, 13.02, 0.05899, 31, -6.92, 13.02, 0.02192, 32, -9.43, 13.02, 0.00077, 10, 20, 12.73, 13.36, 0.00019, 21, 10.22, 13.36, 0.01254, 22, 7.7, 13.36, 0.03697, 23, 5.19, 13.36, 0.13554, 24, 2.68, 13.36, 0.28342, 25, 0.17, 13.36, 0.2419, 26, -2.34, 13.36, 0.19546, 27, -4.85, 13.36, 0.0786, 28, -7.36, 13.36, 0.01431, 29, -9.87, 13.36, 0.00107, 10, 17, 11.32, 13.22, 0.00338, 18, 8.81, 13.22, 0.02671, 19, 6.3, 13.22, 0.12365, 20, 3.79, 13.22, 0.18783, 21, 1.28, 13.22, 0.30607, 22, -1.24, 13.22, 0.21285, 23, -3.75, 13.22, 0.08952, 24, -6.26, 13.22, 0.04671, 25, -8.77, 13.22, 0.00266, 26, -11.28, 13.22, 0.00062, 9, 14, 10.79, 12.9, 0.00401, 15, 8.28, 12.9, 0.0457, 16, 5.77, 12.9, 0.05744, 17, 3.26, 12.9, 0.26657, 18, 0.75, 12.9, 0.30673, 19, -1.76, 12.9, 0.21835, 20, -4.28, 12.9, 0.07972, 21, -6.79, 12.9, 0.01908, 22, -9.3, 12.9, 0.0024, 8, 11, 8.55, 11.86, 0.01218, 12, 6.04, 11.86, 0.07562, 13, 3.53, 11.86, 0.21366, 14, 1.02, 11.86, 0.35463, 15, -1.5, 11.86, 0.24075, 16, -4.01, 11.86, 0.06327, 17, -6.52, 11.86, 0.03744, 18, -9.03, 11.86, 0.00245, 8, 8, 8.08, 10.23, 0.00146, 9, 5.56, 10.23, 0.06573, 10, 3.05, 10.23, 0.2803, 11, 0.54, 10.23, 0.38374, 12, -1.97, 10.23, 0.21156, 13, -4.48, 10.23, 0.04752, 14, -6.99, 10.23, 0.00966, 15, -9.5, 10.23, 3e-05, 6, 6, 5.57, 7.53, 0.03765, 7, 3.06, 7.53, 0.28807, 8, 0.55, 7.53, 0.44447, 9, -1.96, 7.53, 0.18036, 10, -4.48, 7.53, 0.0461, 11, -6.99, 7.53, 0.00336, 3, 4, 2, 4.13, 0.58875, 5, -0.51, 4.13, 0.40764, 6, -3.02, 4.13, 0.00361, 1, 4, -2.41, 1.99, 1, 4, 38, 5.11, 0.19, 0.00118, 39, 2.6, 0.19, 0.43838, 40, 0.09, 0.19, 0.55957, 41, -2.42, 0.19, 0.00087, 4, 35, 4.5, 1.59, 0.02581, 36, 1.99, 1.59, 0.7818, 37, -0.53, 1.59, 0.18292, 38, -3.04, 1.59, 0.00948, 5, 29, 11.29, 1.06, 1e-05, 32, 3.76, 1.06, 0.01002, 33, 1.25, 1.06, 0.98063, 34, -1.26, 1.06, 0.00919, 36, -6.28, 1.06, 0.00014, 6, 27, 8.04, 0.86, 0, 29, 3.02, 0.86, 0.14789, 30, 0.5, 0.86, 0.84641, 31, -2.01, 0.86, 0.00521, 32, -4.52, 0.86, 0.00045, 33, -7.03, 0.86, 3e-05, 5, 24, 7.45, 1.3, 0.0002, 25, 4.94, 1.3, 0.00868, 26, 2.42, 1.3, 0.54398, 27, -0.09, 1.3, 0.44473, 28, -2.6, 1.3, 0.00241, 6, 21, 7.04, 1.1, 0.00024, 22, 4.53, 1.1, 0.00793, 23, 2.02, 1.1, 0.76642, 24, -0.49, 1.1, 0.22182, 25, -3.01, 1.1, 0.00255, 26, -5.52, 1.1, 0.00103, 6, 18, 6.96, 1.24, 0.00269, 19, 4.45, 1.24, 0.0102, 20, 1.94, 1.24, 0.84129, 21, -0.57, 1.24, 0.14173, 22, -3.08, 1.24, 0.004, 24, -8.1, 1.24, 8e-05, 7, 14, 9.2, 2.33, 0.00051, 15, 6.69, 2.33, 0.00684, 16, 4.18, 2.33, 0.02213, 17, 1.67, 2.33, 0.77374, 18, -0.84, 2.33, 0.18603, 19, -3.35, 2.33, 0.01037, 20, -5.86, 2.33, 0.00037, 6, 12, 5.43, 2.43, 0.00213, 13, 2.92, 2.43, 0.29991, 14, 0.41, 2.43, 0.65474, 15, -2.1, 2.43, 0.03933, 16, -4.61, 2.43, 0.00189, 17, -7.12, 2.43, 0.002, 3, 9, 3.2, 0.91, 0.08143, 10, 0.69, 0.91, 0.91838, 12, -4.33, 0.91, 0.00019, 3, 6, 3.12, 1.04, 0.15467, 7, 0.61, 1.04, 0.83082, 8, -1.9, 1.04, 0.01451], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 52, "height": 104}}, "ttt3": {"ttt": {"type": "mesh", "uvs": [0.49931, 0, 0.44481, 0.06168, 0.40289, 0.13504, 0.33581, 0.21679, 0.29389, 0.29854, 0.26873, 0.38029, 0.26454, 0.45785, 0.26454, 0.5375, 0.27712, 0.62345, 0.29808, 0.701, 0.31904, 0.79114, 0.33581, 0.86031, 0.37774, 0.94416, 0.44481, 1, 0.53704, 0.95045, 0.6125, 0.87079, 0.65862, 0.78485, 0.69216, 0.69826, 0.72989, 0.61232, 0.74247, 0.53578, 0.74666, 0.44984, 0.74666, 0.37225, 0.73408, 0.27792, 0.70893, 0.20037, 0.66281, 0.127, 0.60412, 0.04316, 0.56639, 0, 0.46049, 0.89188, 0.49375, 0.81412, 0.48997, 0.73446, 0.49237, 0.65484, 0.50709, 0.57694, 0.50939, 0.50058, 0.51778, 0.42751, 0.54476, 0.35295, 0.55349, 0.26851, 0.5316, 0.17408, 0.53999, 0.10101], "triangles": [13, 12, 14, 12, 27, 14, 14, 27, 15, 12, 11, 27, 27, 28, 15, 27, 11, 28, 15, 28, 16, 11, 10, 28, 10, 29, 28, 28, 29, 16, 10, 9, 29, 16, 29, 17, 29, 30, 17, 29, 9, 30, 9, 8, 30, 17, 30, 18, 30, 31, 18, 30, 8, 31, 8, 7, 31, 18, 31, 19, 31, 32, 19, 31, 7, 32, 7, 6, 32, 19, 32, 20, 32, 33, 20, 32, 6, 33, 6, 5, 33, 33, 21, 20, 33, 34, 21, 33, 5, 34, 5, 4, 34, 34, 22, 21, 34, 4, 35, 34, 35, 22, 4, 3, 35, 35, 23, 22, 3, 36, 35, 35, 36, 23, 3, 2, 36, 36, 24, 23, 36, 2, 37, 36, 37, 24, 2, 1, 37, 37, 25, 24, 37, 1, 25, 25, 0, 26, 0, 25, 1], "vertices": [1, 44, -2.27, -1.49, 1, 3, 44, 4.26, -4.07, 0.0335, 45, 1.74, -4.07, 0.69671, 46, -0.77, -4.07, 0.26979, 5, 47, 4.43, -5.94, 0.11483, 48, 1.92, -5.94, 0.48101, 49, -0.59, -5.94, 0.34831, 50, -3.1, -5.94, 0.05538, 51, -5.61, -5.94, 0.00047, 7, 49, 8.04, -9.09, 0.00115, 50, 5.53, -9.09, 0.08862, 51, 3.02, -9.09, 0.34696, 52, 0.51, -9.09, 0.35037, 53, -2, -9.09, 0.15024, 54, -4.51, -9.09, 0.05973, 55, -7.02, -9.09, 0.00292, 8, 52, 9.09, -10.93, 0.00494, 53, 6.58, -10.93, 0.05168, 54, 4.07, -10.93, 0.20663, 55, 1.56, -10.93, 0.40117, 56, -0.95, -10.93, 0.2172, 57, -3.46, -10.93, 0.1021, 58, -5.97, -10.93, 0.01348, 59, -8.49, -10.93, 0.00282, 9, 55, 10.11, -11.9, 0.00831, 56, 7.6, -11.9, 0.02573, 57, 5.08, -11.9, 0.20501, 58, 2.57, -11.9, 0.26579, 59, 0.06, -11.9, 0.31562, 60, -2.45, -11.9, 0.13698, 61, -4.96, -11.9, 0.03977, 62, -7.47, -11.9, 0.0025, 63, -9.98, -11.9, 0.00028, 10, 57, 13.15, -11.79, 0.00065, 58, 10.64, -11.79, 0.00333, 59, 8.13, -11.79, 0.03989, 60, 5.62, -11.79, 0.12781, 61, 3.11, -11.79, 0.31367, 62, 0.6, -11.79, 0.26503, 63, -1.92, -11.79, 0.1758, 64, -4.43, -11.79, 0.06623, 65, -6.94, -11.79, 0.00753, 66, -9.45, -11.79, 5e-05, 9, 61, 11.38, -11.46, 0.00446, 62, 8.87, -11.46, 0.01958, 63, 6.36, -11.46, 0.05992, 64, 3.85, -11.46, 0.28594, 65, 1.34, -11.46, 0.30347, 66, -1.17, -11.46, 0.23539, 67, -3.68, -11.46, 0.0815, 68, -6.19, -11.46, 0.00859, 69, -8.71, -11.46, 0.00115, 9, 64, 12.76, -10.45, 1e-05, 65, 10.24, -10.45, 0.00143, 66, 7.73, -10.45, 0.03237, 67, 5.22, -10.45, 0.14464, 68, 2.71, -10.46, 0.29982, 69, 0.2, -10.46, 0.38361, 70, -2.31, -10.46, 0.11123, 71, -4.82, -10.46, 0.0265, 72, -7.33, -10.46, 0.00039, 7, 69, 8.22, -9.04, 0.01652, 70, 5.7, -9.04, 0.09497, 71, 3.19, -9.04, 0.3377, 72, 0.68, -9.04, 0.39696, 73, -1.83, -9.04, 0.12755, 74, -4.34, -9.04, 0.02534, 75, -6.85, -9.04, 0.00096, 6, 73, 7.49, -7.58, 0.02115, 74, 4.98, -7.58, 0.10756, 75, 2.47, -7.58, 0.39757, 76, -0.04, -7.58, 0.37895, 77, -2.55, -7.58, 0.09155, 78, -5.06, -7.58, 0.00323, 5, 76, 7.11, -6.42, 0.01076, 77, 4.6, -6.42, 0.1056, 78, 2.09, -6.42, 0.5601, 79, -0.42, -6.42, 0.30032, 80, -2.93, -6.42, 0.02322, 4, 80, 5.69, -3.9, 0.00353, 81, 3.18, -3.9, 0.33728, 82, 0.67, -3.9, 0.65563, 83, -1.84, -3.9, 0.00356, 1, 83, 3.82, -0.18, 1, 4, 80, 6.02, 4.41, 0.00216, 81, 3.51, 4.41, 0.23111, 82, 1, 4.41, 0.73992, 83, -1.52, 4.41, 0.02681, 6, 76, 7.63, 8, 0.00882, 77, 5.12, 8, 0.10025, 78, 2.61, 8, 0.39939, 79, 0.1, 8, 0.37692, 80, -2.42, 8, 0.11308, 81, -4.93, 8, 0.00154, 7, 72, 8.65, 10.04, 0.01141, 73, 6.14, 10.04, 0.07865, 74, 3.63, 10.04, 0.24906, 75, 1.11, 10.04, 0.33976, 76, -1.4, 10.04, 0.28288, 77, -3.91, 10.04, 0.03627, 78, -6.42, 10.04, 0.00197, 8, 67, 12.14, 11.42, 0.00038, 68, 9.62, 11.42, 0.00418, 69, 7.11, 11.42, 0.08035, 70, 4.6, 11.42, 0.12856, 71, 2.09, 11.42, 0.30163, 72, -0.42, 11.42, 0.31298, 73, -2.93, 11.42, 0.14531, 74, -5.44, 11.42, 0.0266, 10, 63, 13.17, 13.02, 0.0002, 64, 10.66, 13.02, 0.00726, 65, 8.15, 13.02, 0.02303, 66, 5.64, 13.02, 0.10519, 67, 3.13, 13.02, 0.26371, 68, 0.62, 13.02, 0.29859, 69, -1.9, 13.02, 0.22035, 70, -4.41, 13.02, 0.05899, 71, -6.92, 13.02, 0.02192, 72, -9.43, 13.02, 0.00077, 10, 60, 12.73, 13.36, 0.00019, 61, 10.22, 13.36, 0.01254, 62, 7.7, 13.36, 0.03697, 63, 5.19, 13.36, 0.13554, 64, 2.68, 13.36, 0.28342, 65, 0.17, 13.36, 0.2419, 66, -2.34, 13.36, 0.19546, 67, -4.85, 13.36, 0.0786, 68, -7.36, 13.36, 0.01431, 69, -9.87, 13.36, 0.00107, 10, 57, 11.32, 13.22, 0.00338, 58, 8.81, 13.22, 0.02671, 59, 6.3, 13.22, 0.12365, 60, 3.79, 13.22, 0.18783, 61, 1.28, 13.22, 0.30607, 62, -1.24, 13.22, 0.21285, 63, -3.75, 13.22, 0.08952, 64, -6.26, 13.22, 0.04671, 65, -8.77, 13.22, 0.00266, 66, -11.28, 13.22, 0.00062, 9, 54, 10.79, 12.9, 0.00401, 55, 8.28, 12.9, 0.0457, 56, 5.77, 12.9, 0.05744, 57, 3.26, 12.9, 0.26657, 58, 0.75, 12.9, 0.30673, 59, -1.76, 12.9, 0.21835, 60, -4.28, 12.9, 0.07972, 61, -6.79, 12.9, 0.01908, 62, -9.3, 12.9, 0.0024, 8, 51, 8.55, 11.86, 0.01218, 52, 6.04, 11.86, 0.07562, 53, 3.53, 11.86, 0.21366, 54, 1.02, 11.86, 0.35463, 55, -1.5, 11.86, 0.24075, 56, -4.01, 11.86, 0.06327, 57, -6.52, 11.86, 0.03744, 58, -9.03, 11.86, 0.00245, 8, 48, 8.08, 10.23, 0.00146, 49, 5.56, 10.23, 0.06573, 50, 3.05, 10.23, 0.2803, 51, 0.54, 10.23, 0.38374, 52, -1.97, 10.23, 0.21156, 53, -4.48, 10.23, 0.04752, 54, -6.99, 10.23, 0.00966, 55, -9.5, 10.23, 3e-05, 6, 46, 5.57, 7.53, 0.03765, 47, 3.06, 7.53, 0.28807, 48, 0.55, 7.53, 0.44447, 49, -1.96, 7.53, 0.18036, 50, -4.48, 7.53, 0.0461, 51, -6.99, 7.53, 0.00336, 3, 44, 2, 4.13, 0.58875, 45, -0.51, 4.13, 0.40764, 46, -3.02, 4.13, 0.00361, 1, 44, -2.41, 1.99, 1, 4, 78, 5.11, 0.19, 0.00118, 79, 2.6, 0.19, 0.43838, 80, 0.09, 0.19, 0.55957, 81, -2.42, 0.19, 0.00087, 4, 75, 4.5, 1.59, 0.02581, 76, 1.99, 1.59, 0.7818, 77, -0.53, 1.59, 0.18292, 78, -3.04, 1.59, 0.00948, 5, 69, 11.29, 1.06, 1e-05, 72, 3.76, 1.06, 0.01002, 73, 1.25, 1.06, 0.98063, 74, -1.26, 1.06, 0.00919, 76, -6.28, 1.06, 0.00014, 6, 67, 8.04, 0.86, 0, 69, 3.02, 0.86, 0.14789, 70, 0.5, 0.86, 0.84641, 71, -2.01, 0.86, 0.00521, 72, -4.52, 0.86, 0.00045, 73, -7.03, 0.86, 3e-05, 5, 64, 7.45, 1.3, 0.0002, 65, 4.94, 1.3, 0.00868, 66, 2.42, 1.3, 0.54398, 67, -0.09, 1.3, 0.44473, 68, -2.6, 1.3, 0.00241, 6, 61, 7.04, 1.1, 0.00024, 62, 4.53, 1.1, 0.00793, 63, 2.02, 1.1, 0.76642, 64, -0.49, 1.1, 0.22182, 65, -3.01, 1.1, 0.00255, 66, -5.52, 1.1, 0.00103, 6, 58, 6.96, 1.24, 0.00269, 59, 4.45, 1.24, 0.0102, 60, 1.94, 1.24, 0.84129, 61, -0.57, 1.24, 0.14173, 62, -3.08, 1.24, 0.004, 64, -8.1, 1.24, 8e-05, 7, 54, 9.2, 2.33, 0.00051, 55, 6.69, 2.33, 0.00684, 56, 4.18, 2.33, 0.02213, 57, 1.67, 2.33, 0.77374, 58, -0.84, 2.33, 0.18603, 59, -3.35, 2.33, 0.01037, 60, -5.86, 2.33, 0.00037, 6, 52, 5.43, 2.43, 0.00213, 53, 2.92, 2.43, 0.29991, 54, 0.41, 2.43, 0.65474, 55, -2.1, 2.43, 0.03933, 56, -4.61, 2.43, 0.00189, 57, -7.12, 2.43, 0.002, 3, 49, 3.2, 0.91, 0.08143, 50, 0.69, 0.91, 0.91838, 52, -4.33, 0.91, 0.00019, 3, 46, 3.12, 1.04, 0.15467, 47, 0.61, 1.04, 0.83082, 48, -1.9, 1.04, 0.01451], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 52, "height": 104}}, "ttt4": {"ttt": {"type": "mesh", "uvs": [0.49931, 0, 0.44481, 0.06168, 0.40289, 0.13504, 0.33581, 0.21679, 0.29389, 0.29854, 0.26873, 0.38029, 0.26454, 0.45785, 0.26454, 0.5375, 0.27712, 0.62345, 0.29808, 0.701, 0.31904, 0.79114, 0.33581, 0.86031, 0.37774, 0.94416, 0.44481, 1, 0.53704, 0.95045, 0.6125, 0.87079, 0.65862, 0.78485, 0.69216, 0.69826, 0.72989, 0.61232, 0.74247, 0.53578, 0.74666, 0.44984, 0.74666, 0.37225, 0.73408, 0.27792, 0.70893, 0.20037, 0.66281, 0.127, 0.60412, 0.04316, 0.56639, 0, 0.46049, 0.89188, 0.49375, 0.81412, 0.48997, 0.73446, 0.49237, 0.65484, 0.50709, 0.57694, 0.50939, 0.50058, 0.51778, 0.42751, 0.54476, 0.35295, 0.55349, 0.26851, 0.5316, 0.17408, 0.53999, 0.10101], "triangles": [13, 12, 14, 12, 27, 14, 14, 27, 15, 12, 11, 27, 27, 28, 15, 27, 11, 28, 15, 28, 16, 11, 10, 28, 10, 29, 28, 28, 29, 16, 10, 9, 29, 16, 29, 17, 29, 30, 17, 29, 9, 30, 9, 8, 30, 17, 30, 18, 30, 31, 18, 30, 8, 31, 8, 7, 31, 18, 31, 19, 31, 32, 19, 31, 7, 32, 7, 6, 32, 19, 32, 20, 32, 33, 20, 32, 6, 33, 6, 5, 33, 33, 21, 20, 33, 34, 21, 33, 5, 34, 5, 4, 34, 34, 22, 21, 34, 4, 35, 34, 35, 22, 4, 3, 35, 35, 23, 22, 3, 36, 35, 35, 36, 23, 3, 2, 36, 36, 24, 23, 36, 2, 37, 36, 37, 24, 2, 1, 37, 37, 25, 24, 37, 1, 25, 25, 0, 26, 0, 25, 1], "vertices": [1, 44, -2.27, -1.49, 1, 3, 44, 4.26, -4.07, 0.0335, 45, 1.74, -4.07, 0.69671, 46, -0.77, -4.07, 0.26979, 5, 47, 4.43, -5.94, 0.11483, 48, 1.92, -5.94, 0.48101, 49, -0.59, -5.94, 0.34831, 50, -3.1, -5.94, 0.05538, 51, -5.61, -5.94, 0.00047, 7, 49, 8.04, -9.09, 0.00115, 50, 5.53, -9.09, 0.08862, 51, 3.02, -9.09, 0.34696, 52, 0.51, -9.09, 0.35037, 53, -2, -9.09, 0.15024, 54, -4.51, -9.09, 0.05973, 55, -7.02, -9.09, 0.00292, 8, 52, 9.09, -10.93, 0.00494, 53, 6.58, -10.93, 0.05168, 54, 4.07, -10.93, 0.20663, 55, 1.56, -10.93, 0.40117, 56, -0.95, -10.93, 0.2172, 57, -3.46, -10.93, 0.1021, 58, -5.97, -10.93, 0.01348, 59, -8.49, -10.93, 0.00282, 9, 55, 10.11, -11.9, 0.00831, 56, 7.6, -11.9, 0.02573, 57, 5.08, -11.9, 0.20501, 58, 2.57, -11.9, 0.26579, 59, 0.06, -11.9, 0.31562, 60, -2.45, -11.9, 0.13698, 61, -4.96, -11.9, 0.03977, 62, -7.47, -11.9, 0.0025, 63, -9.98, -11.9, 0.00028, 10, 57, 13.15, -11.79, 0.00065, 58, 10.64, -11.79, 0.00333, 59, 8.13, -11.79, 0.03989, 60, 5.62, -11.79, 0.12781, 61, 3.11, -11.79, 0.31367, 62, 0.6, -11.79, 0.26503, 63, -1.92, -11.79, 0.1758, 64, -4.43, -11.79, 0.06623, 65, -6.94, -11.79, 0.00753, 66, -9.45, -11.79, 5e-05, 9, 61, 11.38, -11.46, 0.00446, 62, 8.87, -11.46, 0.01958, 63, 6.36, -11.46, 0.05992, 64, 3.85, -11.46, 0.28594, 65, 1.34, -11.46, 0.30347, 66, -1.17, -11.46, 0.23539, 67, -3.68, -11.46, 0.0815, 68, -6.19, -11.46, 0.00859, 69, -8.71, -11.46, 0.00115, 9, 64, 12.76, -10.45, 1e-05, 65, 10.24, -10.45, 0.00143, 66, 7.73, -10.45, 0.03237, 67, 5.22, -10.45, 0.14464, 68, 2.71, -10.46, 0.29982, 69, 0.2, -10.46, 0.38361, 70, -2.31, -10.46, 0.11123, 71, -4.82, -10.46, 0.0265, 72, -7.33, -10.46, 0.00039, 7, 69, 8.22, -9.04, 0.01652, 70, 5.7, -9.04, 0.09497, 71, 3.19, -9.04, 0.3377, 72, 0.68, -9.04, 0.39696, 73, -1.83, -9.04, 0.12755, 74, -4.34, -9.04, 0.02534, 75, -6.85, -9.04, 0.00096, 6, 73, 7.49, -7.58, 0.02115, 74, 4.98, -7.58, 0.10756, 75, 2.47, -7.58, 0.39757, 76, -0.04, -7.58, 0.37895, 77, -2.55, -7.58, 0.09155, 78, -5.06, -7.58, 0.00323, 5, 76, 7.11, -6.42, 0.01076, 77, 4.6, -6.42, 0.1056, 78, 2.09, -6.42, 0.5601, 79, -0.42, -6.42, 0.30032, 80, -2.93, -6.42, 0.02322, 4, 80, 5.69, -3.9, 0.00353, 81, 3.18, -3.9, 0.33728, 82, 0.67, -3.9, 0.65563, 83, -1.84, -3.9, 0.00356, 1, 83, 3.82, -0.18, 1, 4, 80, 6.02, 4.41, 0.00216, 81, 3.51, 4.41, 0.23111, 82, 1, 4.41, 0.73992, 83, -1.52, 4.41, 0.02681, 6, 76, 7.63, 8, 0.00882, 77, 5.12, 8, 0.10025, 78, 2.61, 8, 0.39939, 79, 0.1, 8, 0.37692, 80, -2.42, 8, 0.11308, 81, -4.93, 8, 0.00154, 7, 72, 8.65, 10.04, 0.01141, 73, 6.14, 10.04, 0.07865, 74, 3.63, 10.04, 0.24906, 75, 1.11, 10.04, 0.33976, 76, -1.4, 10.04, 0.28288, 77, -3.91, 10.04, 0.03627, 78, -6.42, 10.04, 0.00197, 8, 67, 12.14, 11.42, 0.00038, 68, 9.62, 11.42, 0.00418, 69, 7.11, 11.42, 0.08035, 70, 4.6, 11.42, 0.12856, 71, 2.09, 11.42, 0.30163, 72, -0.42, 11.42, 0.31298, 73, -2.93, 11.42, 0.14531, 74, -5.44, 11.42, 0.0266, 10, 63, 13.17, 13.02, 0.0002, 64, 10.66, 13.02, 0.00726, 65, 8.15, 13.02, 0.02303, 66, 5.64, 13.02, 0.10519, 67, 3.13, 13.02, 0.26371, 68, 0.62, 13.02, 0.29859, 69, -1.9, 13.02, 0.22035, 70, -4.41, 13.02, 0.05899, 71, -6.92, 13.02, 0.02192, 72, -9.43, 13.02, 0.00077, 10, 60, 12.73, 13.36, 0.00019, 61, 10.22, 13.36, 0.01254, 62, 7.7, 13.36, 0.03697, 63, 5.19, 13.36, 0.13554, 64, 2.68, 13.36, 0.28342, 65, 0.17, 13.36, 0.2419, 66, -2.34, 13.36, 0.19546, 67, -4.85, 13.36, 0.0786, 68, -7.36, 13.36, 0.01431, 69, -9.87, 13.36, 0.00107, 10, 57, 11.32, 13.22, 0.00338, 58, 8.81, 13.22, 0.02671, 59, 6.3, 13.22, 0.12365, 60, 3.79, 13.22, 0.18783, 61, 1.28, 13.22, 0.30607, 62, -1.24, 13.22, 0.21285, 63, -3.75, 13.22, 0.08952, 64, -6.26, 13.22, 0.04671, 65, -8.77, 13.22, 0.00266, 66, -11.28, 13.22, 0.00062, 9, 54, 10.79, 12.9, 0.00401, 55, 8.28, 12.9, 0.0457, 56, 5.77, 12.9, 0.05744, 57, 3.26, 12.9, 0.26657, 58, 0.75, 12.9, 0.30673, 59, -1.76, 12.9, 0.21835, 60, -4.28, 12.9, 0.07972, 61, -6.79, 12.9, 0.01908, 62, -9.3, 12.9, 0.0024, 8, 51, 8.55, 11.86, 0.01218, 52, 6.04, 11.86, 0.07562, 53, 3.53, 11.86, 0.21366, 54, 1.02, 11.86, 0.35463, 55, -1.5, 11.86, 0.24075, 56, -4.01, 11.86, 0.06327, 57, -6.52, 11.86, 0.03744, 58, -9.03, 11.86, 0.00245, 8, 48, 8.08, 10.23, 0.00146, 49, 5.56, 10.23, 0.06573, 50, 3.05, 10.23, 0.2803, 51, 0.54, 10.23, 0.38374, 52, -1.97, 10.23, 0.21156, 53, -4.48, 10.23, 0.04752, 54, -6.99, 10.23, 0.00966, 55, -9.5, 10.23, 3e-05, 6, 46, 5.57, 7.53, 0.03765, 47, 3.06, 7.53, 0.28807, 48, 0.55, 7.53, 0.44447, 49, -1.96, 7.53, 0.18036, 50, -4.48, 7.53, 0.0461, 51, -6.99, 7.53, 0.00336, 3, 44, 2, 4.13, 0.58875, 45, -0.51, 4.13, 0.40764, 46, -3.02, 4.13, 0.00361, 1, 44, -2.41, 1.99, 1, 4, 78, 5.11, 0.19, 0.00118, 79, 2.6, 0.19, 0.43838, 80, 0.09, 0.19, 0.55957, 81, -2.42, 0.19, 0.00087, 4, 75, 4.5, 1.59, 0.02581, 76, 1.99, 1.59, 0.7818, 77, -0.53, 1.59, 0.18292, 78, -3.04, 1.59, 0.00948, 5, 69, 11.29, 1.06, 1e-05, 72, 3.76, 1.06, 0.01002, 73, 1.25, 1.06, 0.98063, 74, -1.26, 1.06, 0.00919, 76, -6.28, 1.06, 0.00014, 6, 67, 8.04, 0.86, 0, 69, 3.02, 0.86, 0.14789, 70, 0.5, 0.86, 0.84641, 71, -2.01, 0.86, 0.00521, 72, -4.52, 0.86, 0.00045, 73, -7.03, 0.86, 3e-05, 5, 64, 7.45, 1.3, 0.0002, 65, 4.94, 1.3, 0.00868, 66, 2.42, 1.3, 0.54398, 67, -0.09, 1.3, 0.44473, 68, -2.6, 1.3, 0.00241, 6, 61, 7.04, 1.1, 0.00024, 62, 4.53, 1.1, 0.00793, 63, 2.02, 1.1, 0.76642, 64, -0.49, 1.1, 0.22182, 65, -3.01, 1.1, 0.00255, 66, -5.52, 1.1, 0.00103, 6, 58, 6.96, 1.24, 0.00269, 59, 4.45, 1.24, 0.0102, 60, 1.94, 1.24, 0.84129, 61, -0.57, 1.24, 0.14173, 62, -3.08, 1.24, 0.004, 64, -8.1, 1.24, 8e-05, 7, 54, 9.2, 2.33, 0.00051, 55, 6.69, 2.33, 0.00684, 56, 4.18, 2.33, 0.02213, 57, 1.67, 2.33, 0.77374, 58, -0.84, 2.33, 0.18603, 59, -3.35, 2.33, 0.01037, 60, -5.86, 2.33, 0.00037, 6, 52, 5.43, 2.43, 0.00213, 53, 2.92, 2.43, 0.29991, 54, 0.41, 2.43, 0.65474, 55, -2.1, 2.43, 0.03933, 56, -4.61, 2.43, 0.00189, 57, -7.12, 2.43, 0.002, 3, 49, 3.2, 0.91, 0.08143, 50, 0.69, 0.91, 0.91838, 52, -4.33, 0.91, 0.00019, 3, 46, 3.12, 1.04, 0.15467, 47, 0.61, 1.04, 0.83082, 48, -1.9, 1.04, 0.01451], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 52, "height": 104}}, "xxx1": {"xxx1": {"x": 2.1, "y": -2.14, "width": 57, "height": 57}}, "xxx2": {"xxx1": {"x": 2.1, "y": -2.14, "width": 57, "height": 57}}, "xxx3": {"xxx1": {"x": 2.1, "y": -2.14, "width": 57, "height": 57}}, "xxx4": {"xxx1": {"x": 2.1, "y": -2.14, "width": 57, "height": 57}}}}], "animations": {"animation": {"slots": {"maoxian_btn_3": {"color": [{"time": 0.5, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00"}]}, "xxx1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}]}, "xxx2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}]}, "xxx3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.0667, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8667, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00"}]}, "xxx4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffff00"}]}}, "bones": {"sg": {"translate": [{"x": -68.1, "y": 41.26, "curve": 0, "c2": 0.28, "c3": 0.981, "c4": 0.75}, {"time": 1.6667, "x": 151.45, "y": -62.98}], "scale": [{"y": 0.717, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "y": 2.199, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 0.717}]}, "xxx1": {"rotate": [{"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -174.62}], "scale": [{"x": 0.51, "y": 0.51, "curve": "stepped"}, {"time": 0.1333, "x": 0.51, "y": 0.51, "curve": 0.25, "c3": 0.118}, {"time": 0.5333, "x": 1.462, "y": 1.462, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.51, "y": 0.51}]}, "xxx2": {"rotate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": -174.62}], "scale": [{"x": 0.51, "y": 0.51, "curve": "stepped"}, {"time": 0.5333, "x": 0.51, "y": 0.51, "curve": 0.25, "c3": 0.118}, {"time": 0.9333, "x": 1.462, "y": 1.462, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.51, "y": 0.51}]}, "xxx3": {"rotate": [{"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "angle": -174.62}], "scale": [{"x": 0.51, "y": 0.51, "curve": "stepped"}, {"time": 1.0667, "x": 0.51, "y": 0.51, "curve": 0.25, "c3": 0.118}, {"time": 1.4667, "x": 1.462, "y": 1.462, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 0.51, "y": 0.51}]}, "xxx4": {"rotate": [{"time": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "angle": -174.62}], "scale": [{"x": 0.51, "y": 0.51, "curve": "stepped"}, {"time": 1.4, "x": 0.51, "y": 0.51, "curve": 0.25, "c3": 0.118}, {"time": 1.8, "x": 1.462, "y": 1.462, "curve": 0.25, "c3": 0.75}, {"time": 2.4333, "x": 0.51, "y": 0.51}]}, "maoxian_btn_03": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.65, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -9.65, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}}, "path": {"path01": {"position": [{}, {"time": 3.3333, "position": 1}]}, "path02": {"position": [{"position": 0.4444}, {"time": 1.8333, "position": 1, "curve": "stepped"}, {"time": 1.8667}, {"time": 3.3333, "position": 0.4444}]}}}}}