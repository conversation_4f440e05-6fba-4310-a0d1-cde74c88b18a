{"skeleton": {"hash": "/M6Bn+JYNxJdAtBvdCrDKZOkQnI", "spine": "3.8.99", "images": "./images/", "audio": ""}, "bones": [{"name": "root"}, {"name": "x", "parent": "root", "scaleX": 0.22, "scaleY": 0.22}, {"name": "ft", "parent": "x"}, {"name": "ft2a", "parent": "ft", "length": 62.15, "x": -384.9, "y": 70.65}, {"name": "ft2b", "parent": "ft", "length": 62.15, "x": -322.76, "y": 70.65}, {"name": "ft2c", "parent": "ft", "length": 62.15, "x": -260.61, "y": 70.65}, {"name": "ft2d", "parent": "ft", "length": 62.15, "x": -198.46, "y": 70.65}, {"name": "ft2e", "parent": "ft", "length": 62.15, "x": -136.32, "y": 70.65}, {"name": "ft2f", "parent": "ft", "length": 62.15, "x": -74.17, "y": 70.65}, {"name": "ft2", "parent": "x"}, {"name": "ft2a2", "parent": "ft2", "length": 62.15, "x": -384.9, "y": 70.65}, {"name": "ft2b2", "parent": "ft2", "length": 62.15, "x": -322.76, "y": 70.65}, {"name": "ft2c2", "parent": "ft2", "length": 62.15, "x": -260.61, "y": 70.65}, {"name": "ft2d2", "parent": "ft2", "length": 62.15, "x": -198.46, "y": 70.65}, {"name": "ft2e2", "parent": "ft2", "length": 62.15, "x": -136.32, "y": 70.65}, {"name": "ft2f2", "parent": "ft2", "length": 62.15, "x": -74.17, "y": 70.65}, {"name": "bone", "parent": "root", "x": 389.66, "y": 40.23}, {"name": "bd5", "parent": "bone", "x": 80.29, "y": -42.37}, {"name": "baoci_2_52", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_53", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_54", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_55", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_56", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_57", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_58", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_59", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_60", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_61", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_62", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_63", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "baoci_2_64", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "ci_10", "parent": "bd5", "x": -559.1, "y": -11.97}, {"name": "ci_11", "parent": "bd5", "x": -559.1, "y": -11.97}, {"name": "glow13", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "glow14", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "glow15", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "glow16", "parent": "bd5", "x": -469.95, "y": 2.14}, {"name": "yw2", "parent": "bone", "x": -76.45, "y": -33.3}, {"name": "yw_10", "parent": "yw2", "x": -98.83, "y": 164.77, "scaleX": 2.4997, "scaleY": 2.4997}, {"name": "yw_13", "parent": "yw2", "x": -98.83, "y": 164.77, "scaleX": 2.4997, "scaleY": 2.4997}, {"name": "yw_20", "parent": "yw2", "x": -98.83, "y": 164.77, "scaleX": 2.4997, "scaleY": 2.4997}, {"name": "yw_21", "parent": "yw2", "x": -98.83, "y": 164.77, "scaleX": 2.4997, "scaleY": 2.4997}, {"name": "yw_23", "parent": "yw2", "x": -98.83, "y": 164.77, "scaleX": 2.4997, "scaleY": 2.4997}, {"name": "yw_25", "parent": "yw2", "x": -98.83, "y": 164.77, "scaleX": 2.4997, "scaleY": 2.4997}, {"name": "yw_26", "parent": "yw2", "x": -98.83, "y": 164.77, "scaleX": 2.4997, "scaleY": 2.4997}, {"name": "lizi9", "parent": "bone", "x": -389.66, "y": -40.23}, {"name": "lizi10", "parent": "bone", "x": -389.66, "y": -40.23}, {"name": "lizi11", "parent": "bone", "x": -389.66, "y": -40.23}, {"name": "lizi12", "parent": "bone", "x": -389.66, "y": -40.23}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "length": 18.35, "rotation": 90, "x": -1.05, "y": 35.04}], "slots": [{"name": "ft1", "bone": "ft"}, {"name": "ft21", "bone": "ft2"}, {"name": "ft", "bone": "ft", "attachment": "ft"}, {"name": "ft2", "bone": "ft2", "attachment": "ft"}, {"name": "glow34", "bone": "glow16"}, {"name": "glow31", "bone": "glow13", "blend": "additive"}, {"name": "baoci_2_104", "bone": "baoci_2_52"}, {"name": "baoci_2_105", "bone": "baoci_2_52", "blend": "additive"}, {"name": "baoci_2_120", "bone": "baoci_2_60"}, {"name": "baoci_2_121", "bone": "baoci_2_60", "blend": "additive"}, {"name": "baoci_2_122", "bone": "baoci_2_61"}, {"name": "baoci_2_123", "bone": "baoci_2_61", "blend": "additive"}, {"name": "baoci_2_118", "bone": "baoci_2_59"}, {"name": "baoci_2_119", "bone": "baoci_2_59", "blend": "additive"}, {"name": "baoci_2_124", "bone": "baoci_2_62"}, {"name": "baoci_2_125", "bone": "baoci_2_62", "blend": "additive"}, {"name": "glow33", "bone": "glow15", "blend": "additive"}, {"name": "baoci_2_106", "bone": "baoci_2_53"}, {"name": "baoci_2_107", "bone": "baoci_2_53", "blend": "additive"}, {"name": "baoci_2_116", "bone": "baoci_2_58"}, {"name": "baoci_2_117", "bone": "baoci_2_58", "blend": "additive"}, {"name": "baoci_2_108", "bone": "baoci_2_54"}, {"name": "baoci_2_109", "bone": "baoci_2_54", "blend": "additive"}, {"name": "baoci_2_110", "bone": "baoci_2_55"}, {"name": "baoci_2_111", "bone": "baoci_2_55", "blend": "additive"}, {"name": "baoci_2_112", "bone": "baoci_2_56"}, {"name": "baoci_2_113", "bone": "baoci_2_56", "blend": "additive"}, {"name": "baoci_2_114", "bone": "baoci_2_57"}, {"name": "baoci_2_115", "bone": "baoci_2_57", "blend": "additive"}, {"name": "ci_18", "bone": "ci_10"}, {"name": "ci_19", "bone": "ci_10", "blend": "additive"}, {"name": "ci_20", "bone": "ci_11"}, {"name": "ci_21", "bone": "ci_11", "blend": "additive"}, {"name": "baoci_2_128", "bone": "baoci_2_64"}, {"name": "baoci_2_129", "bone": "baoci_2_64", "blend": "additive"}, {"name": "baoci_2_126", "bone": "baoci_2_63"}, {"name": "baoci_2_127", "bone": "baoci_2_63", "blend": "additive"}, {"name": "glow32", "bone": "glow14", "blend": "additive"}, {"name": "yw_25", "bone": "yw_25"}, {"name": "yw_20", "bone": "yw_20"}, {"name": "lizi10", "bone": "lizi9", "blend": "additive"}, {"name": "lizi11", "bone": "lizi10", "blend": "additive"}, {"name": "lizi12", "bone": "lizi11", "blend": "additive"}, {"name": "lizi13", "bone": "lizi12", "blend": "additive"}, {"name": "yw_10", "bone": "yw_10"}, {"name": "yw_13", "bone": "yw_13", "blend": "additive"}, {"name": "yw_26", "bone": "yw_26", "blend": "additive"}, {"name": "yw_21", "bone": "yw_21", "blend": "additive"}, {"name": "yw_23", "bone": "yw_23", "blend": "additive"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>"}], "path": [{"name": "ft", "bones": ["ft2a", "ft2b", "ft2c", "ft2d", "ft2e", "ft2f"], "target": "ft", "rotateMode": "chainScale"}, {"name": "ft2", "order": 1, "bones": ["ft2a2", "ft2b2", "ft2c2", "ft2d2", "ft2e2", "ft2f2"], "target": "ft2", "rotateMode": "chainScale"}], "skins": [{"name": "default", "attachments": {"baoci_2_104": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_105": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_106": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_107": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_108": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_109": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_110": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_111": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_112": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_113": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_114": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_115": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_116": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_117": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_118": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_119": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_120": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_121": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_122": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_123": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_124": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_125": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_126": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_127": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_128": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "baoci_2_129": {"baoci_2_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}, "baoci_2_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -128, -64, -128, -64, 128, 64, 128], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 42, "height": 84}}, "ci_18": {"ci_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}, "ci_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}}, "ci_19": {"ci_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}, "ci_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}}, "ci_20": {"ci_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}, "ci_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}}, "ci_21": {"ci_00003": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}, "ci_00004": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [289.14, -135.89, -110.86, -135.89, -110.86, 164.11, 289.14, 164.11], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 60}}, "ft": {"ft": {"type": "path", "lengths": [262.32, 316.36, 459.06], "vertexCount": 9, "vertices": [-321.12, 597.25, -433.07, 514.87, -1023.88, 80.08, -80.39, -117.36, 136.7, -13.1, 220.49, 27.15, 101.48, 48.82, -38.04, 30.51, -220.35, 6.59]}}, "ft1": {"ft": {"type": "mesh", "uvs": [1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.5, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 1, 0.5, 0.85714, 0.5, 0.71429, 0.5, 0.57143, 0.5, 0.42857, 0.5, 0.28571, 0.5, 0.14286, 0.5], "triangles": [18, 19, 15, 1, 2, 18, 18, 15, 16, 17, 18, 16, 1, 18, 17, 0, 1, 17, 19, 20, 14, 2, 3, 19, 19, 14, 15, 2, 19, 18, 20, 21, 13, 3, 4, 20, 20, 13, 14, 3, 20, 19, 21, 22, 12, 4, 5, 21, 21, 12, 13, 4, 21, 20, 22, 23, 11, 5, 6, 22, 22, 11, 12, 5, 22, 21, 8, 9, 10, 23, 8, 10, 7, 8, 23, 6, 7, 23, 23, 10, 11, 6, 23, 22], "vertices": [2, 7, 151.14, -73.44, 0.01234, 8, 88.99, -73.44, 0.98766, 3, 6, 147.43, -73.44, 0.00331, 7, 85.28, -73.44, 0.29713, 8, 23.14, -73.44, 0.69956, 4, 5, 143.72, -73.44, 0.00962, 6, 81.57, -73.44, 0.26151, 7, 19.43, -73.44, 0.64376, 8, -42.72, -73.44, 0.08511, 4, 4, 140.01, -73.44, 0.00445, 5, 77.86, -73.44, 0.33812, 6, 15.72, -73.44, 0.55444, 7, -46.43, -73.44, 0.10299, 4, 3, 136.3, -73.44, 0.00826, 4, 74.15, -73.44, 0.27932, 5, 12.01, -73.44, 0.64177, 6, -50.14, -73.44, 0.07065, 3, 3, 70.44, -73.44, 0.35307, 4, 8.29, -73.44, 0.53587, 5, -53.85, -73.44, 0.11106, 3, 3, 4.58, -73.44, 0.92453, 4, -57.56, -73.44, 0.07518, 5, -119.71, -73.44, 0.00029, 1, 3, -61.27, -73.44, 1, 1, 3, -61.27, 3.06, 1, 1, 3, -61.27, 79.56, 1, 2, 3, 4.58, 79.56, 0.92737, 4, -57.56, 79.56, 0.07263, 4, 3, 70.44, 79.56, 0.38764, 4, 8.29, 79.56, 0.53851, 5, -53.85, 79.56, 0.07377, 6, -116, 79.56, 8e-05, 5, 3, 136.3, 79.56, 0.01599, 4, 74.15, 79.56, 0.38629, 5, 12.01, 79.56, 0.50138, 6, -50.14, 79.56, 0.09525, 7, -112.29, 79.56, 0.00108, 4, 4, 140.01, 79.56, 0.03175, 5, 77.86, 79.56, 0.27724, 6, 15.72, 79.56, 0.55781, 7, -46.43, 79.56, 0.1332, 4, 5, 143.72, 79.56, 0.00851, 6, 81.57, 79.56, 0.27022, 7, 19.43, 79.56, 0.63615, 8, -42.72, 79.56, 0.08513, 3, 6, 147.43, 79.56, 0.00788, 7, 85.28, 79.56, 0.31379, 8, 23.14, 79.56, 0.67833, 2, 7, 151.14, 79.56, 0.01935, 8, 88.99, 79.56, 0.98065, 1, 8, 88.99, 3.06, 1, 2, 7, 85.28, 3.06, 0.00117, 8, 23.14, 3.06, 0.99883, 1, 7, 19.43, 3.06, 1, 4, 4, 140.01, 3.06, 6e-05, 5, 77.86, 3.06, 0.00584, 6, 15.72, 3.06, 0.99351, 7, -46.43, 3.06, 0.00059, 1, 5, 12.01, 3.06, 1, 2, 3, 70.44, 3.06, 0.00386, 4, 8.29, 3.06, 0.99614, 1, 3, 4.58, 3.06, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0], "width": 161, "height": 54}}, "ft2": {"ft": {"type": "path", "lengths": [262.32, 316.36, 459.06], "vertexCount": 9, "vertices": [-321.12, 597.25, -433.07, 514.87, -1023.88, 80.08, -80.39, -117.36, 136.7, -13.1, 220.49, 27.15, 101.48, 48.82, -38.04, 30.51, -220.35, 6.59]}}, "ft21": {"ft": {"type": "mesh", "uvs": [1, 1, 0.85714, 1, 0.71429, 1, 0.57143, 1, 0.42857, 1, 0.28571, 1, 0.14286, 1, 0, 1, 0, 0.5, 0, 0, 0.14286, 0, 0.28571, 0, 0.42857, 0, 0.57143, 0, 0.71429, 0, 0.85714, 0, 1, 0, 1, 0.5, 0.85714, 0.5, 0.71429, 0.5, 0.57143, 0.5, 0.42857, 0.5, 0.28571, 0.5, 0.14286, 0.5], "triangles": [18, 19, 15, 1, 2, 18, 18, 15, 16, 17, 18, 16, 1, 18, 17, 0, 1, 17, 19, 20, 14, 2, 3, 19, 19, 14, 15, 2, 19, 18, 20, 21, 13, 3, 4, 20, 20, 13, 14, 3, 20, 19, 21, 22, 12, 4, 5, 21, 21, 12, 13, 4, 21, 20, 22, 23, 11, 5, 6, 22, 22, 11, 12, 5, 22, 21, 8, 9, 10, 23, 8, 10, 7, 8, 23, 6, 7, 23, 23, 10, 11, 6, 23, 22], "vertices": [2, 14, 151.14, -73.44, 0.01234, 15, 88.99, -73.44, 0.98766, 3, 13, 147.43, -73.44, 0.00331, 14, 85.28, -73.44, 0.29713, 15, 23.14, -73.44, 0.69956, 4, 12, 143.72, -73.44, 0.00962, 13, 81.57, -73.44, 0.26151, 14, 19.43, -73.44, 0.64376, 15, -42.72, -73.44, 0.08511, 4, 11, 140.01, -73.44, 0.00445, 12, 77.86, -73.44, 0.33812, 13, 15.72, -73.44, 0.55444, 14, -46.43, -73.44, 0.10299, 4, 10, 136.3, -73.44, 0.00826, 11, 74.15, -73.44, 0.27932, 12, 12.01, -73.44, 0.64177, 13, -50.14, -73.44, 0.07065, 3, 10, 70.44, -73.44, 0.35307, 11, 8.29, -73.44, 0.53587, 12, -53.85, -73.44, 0.11106, 3, 10, 4.58, -73.44, 0.92453, 11, -57.56, -73.44, 0.07518, 12, -119.71, -73.44, 0.00029, 1, 10, -61.27, -73.44, 1, 1, 10, -61.27, 3.06, 1, 1, 10, -61.27, 79.56, 1, 2, 10, 4.58, 79.56, 0.92737, 11, -57.56, 79.56, 0.07263, 4, 10, 70.44, 79.56, 0.38764, 11, 8.29, 79.56, 0.53851, 12, -53.85, 79.56, 0.07377, 13, -116, 79.56, 8e-05, 5, 10, 136.3, 79.56, 0.01599, 11, 74.15, 79.56, 0.38629, 12, 12.01, 79.56, 0.50138, 13, -50.14, 79.56, 0.09525, 14, -112.29, 79.56, 0.00108, 4, 11, 140.01, 79.56, 0.03175, 12, 77.86, 79.56, 0.27724, 13, 15.72, 79.56, 0.55781, 14, -46.43, 79.56, 0.1332, 4, 12, 143.72, 79.56, 0.00851, 13, 81.57, 79.56, 0.27022, 14, 19.43, 79.56, 0.63615, 15, -42.72, 79.56, 0.08513, 3, 13, 147.43, 79.56, 0.00788, 14, 85.28, 79.56, 0.31379, 15, 23.14, 79.56, 0.67833, 2, 14, 151.14, 79.56, 0.01935, 15, 88.99, 79.56, 0.98065, 1, 15, 88.99, 3.06, 1, 2, 14, 85.28, 3.06, 0.00117, 15, 23.14, 3.06, 0.99883, 1, 14, 19.43, 3.06, 1, 4, 11, 140.01, 3.06, 6e-05, 12, 77.86, 3.06, 0.00584, 13, 15.72, 3.06, 0.99351, 14, -46.43, 3.06, 0.00059, 1, 12, 12.01, 3.06, 1, 2, 10, 70.44, 3.06, 0.00386, 11, 8.29, 3.06, 0.99614, 1, 10, 4.58, 3.06, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 0], "width": 161, "height": 54}}, "glow31": {"glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 102}}, "glow32": {"glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 102}}, "glow33": {"glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 102}}, "glow34": {"glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64, -64, -64, -64, -64, 64, 64, 64], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 102, "height": 102}}, "lizi10": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60.5, -65.5, -60.5, -65.5, -60.5, 65.5, 60.5, 65.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 20}}, "lizi11": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60.5, -65.5, -60.5, -65.5, -60.5, 65.5, 60.5, 65.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 20}}, "lizi12": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60.5, -65.5, -60.5, -65.5, -60.5, 65.5, 60.5, 65.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 20}}, "lizi13": {"lizi": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [60.5, -65.5, -60.5, -65.5, -60.5, 65.5, 60.5, 65.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 20}}, "yw_10": {"yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "yw_13": {"yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "yw_20": {"yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "yw_21": {"yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "yw_23": {"yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "yw_25": {"yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}, "yw_26": {"yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_11": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_13": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_15": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}, "yw_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [32, -32, -32, -32, -32, 32, 32, 32], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 32}}}}], "animations": {"animation": {"slots": {"anyanshu": {"color": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "baoci_2_104": {"color": [{"time": 0.3333, "color": "170027ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5333, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_105": {"color": [{"time": 0.3333, "color": "170027ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_06"}, {"time": 0.5, "name": "baoci_2_07"}, {"time": 0.5667, "name": null}]}, "baoci_2_106": {"color": [{"time": 0.3333, "color": "7600ffff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4667, "name": "baoci_2_02"}, {"time": 0.5, "name": "baoci_2_03"}, {"time": 0.5333, "name": "baoci_2_04"}, {"time": 0.5667, "name": "baoci_2_05"}, {"time": 0.6, "name": "baoci_2_06"}, {"time": 0.6333, "name": "baoci_2_07"}, {"time": 0.6667, "name": null}]}, "baoci_2_107": {"color": [{"time": 0.3333, "color": "3a519bff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4667, "name": "baoci_2_02"}, {"time": 0.5, "name": "baoci_2_03"}, {"time": 0.5333, "name": "baoci_2_04"}, {"time": 0.5667, "name": "baoci_2_05"}, {"time": 0.6, "name": "baoci_2_06"}, {"time": 0.6333, "name": "baoci_2_07"}, {"time": 0.6667, "name": null}]}, "baoci_2_108": {"color": [{"time": 0.3333, "color": "200167ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4333, "name": "baoci_2_02"}, {"time": 0.4667, "name": "baoci_2_03"}, {"time": 0.5, "name": "baoci_2_04"}, {"time": 0.5333, "name": "baoci_2_05"}, {"time": 0.5667, "name": "baoci_2_06"}, {"time": 0.6, "name": "baoci_2_07"}, {"time": 0.6333, "name": null}]}, "baoci_2_109": {"color": [{"time": 0.3333, "color": "200167ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4333, "name": "baoci_2_02"}, {"time": 0.4667, "name": "baoci_2_03"}, {"time": 0.5, "name": "baoci_2_04"}, {"time": 0.5333, "name": "baoci_2_05"}, {"time": 0.5667, "name": "baoci_2_06"}, {"time": 0.6, "name": "baoci_2_07"}, {"time": 0.6333, "name": null}]}, "baoci_2_110": {"color": [{"time": 0.3333, "color": "6630f3ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4, "name": "baoci_2_02"}, {"time": 0.4333, "name": "baoci_2_03"}, {"time": 0.4667, "name": "baoci_2_04"}, {"time": 0.5, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_111": {"color": [{"time": 0.3333, "color": "9362a4ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4, "name": "baoci_2_02"}, {"time": 0.4333, "name": "baoci_2_03"}, {"time": 0.4667, "name": "baoci_2_04"}, {"time": 0.5, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_112": {"color": [{"time": 0.3333, "color": "ca25ffff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_03"}, {"time": 0.4, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5333, "name": "baoci_2_07"}, {"time": 0.5667, "name": null}]}, "baoci_2_113": {"attachment": [{"time": 0.3333, "name": "baoci_2_02"}, {"time": 0.4, "name": "baoci_2_03"}, {"time": 0.4333, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5333, "name": "baoci_2_07"}, {"time": 0.5667, "name": null}]}, "baoci_2_114": {"color": [{"time": 0.3333, "color": "6732d5ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_03"}, {"time": 0.4333, "name": "baoci_2_04"}, {"time": 0.4667, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_115": {"color": [{"time": 0.3333, "color": "ffffffa0"}], "attachment": [{"time": 0.3333, "name": "baoci_2_03"}, {"time": 0.4333, "name": "baoci_2_04"}, {"time": 0.4667, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_116": {"color": [{"time": 0.3333, "color": "8300ffff"}, {"time": 0.3667, "color": "00173eff"}, {"time": 0.4, "color": "8300ffff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_05"}, {"time": 0.4333, "name": "baoci_2_06"}, {"time": 0.5, "name": "baoci_2_07"}, {"time": 0.5333, "name": null}]}, "baoci_2_117": {"color": [{"time": 0.3333, "color": "f5f170ff"}, {"time": 0.3667, "color": "00173eff"}, {"time": 0.4, "color": "f5f170ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_05"}, {"time": 0.4667, "name": "baoci_2_06"}, {"time": 0.5333, "name": "baoci_2_07"}, {"time": 0.5667, "name": null}]}, "baoci_2_118": {"color": [{"time": 0.3333, "color": "130127ac"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4333, "name": "baoci_2_03"}, {"time": 0.4667, "name": "baoci_2_04"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5333, "name": "baoci_2_07"}, {"time": 0.5667, "name": null}]}, "baoci_2_119": {"color": [{"time": 0.3333, "color": "130127ac"}], "attachment": [{"time": 0.3333, "name": "baoci_2_01"}, {"time": 0.4333, "name": "baoci_2_03"}, {"time": 0.4667, "name": "baoci_2_04"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5333, "name": "baoci_2_07"}, {"time": 0.5667, "name": null}]}, "baoci_2_120": {"color": [{"time": 0.3333, "color": "11001eff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.6, "name": "baoci_2_07"}, {"time": 0.6333, "name": null}]}, "baoci_2_121": {"color": [{"time": 0.3333, "color": "11001eff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.6, "name": "baoci_2_07"}, {"time": 0.6667, "name": null}]}, "baoci_2_122": {"color": [{"time": 0.3333, "color": "11001eff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6333, "name": null}]}, "baoci_2_123": {"color": [{"time": 0.3333, "color": "11001eff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_06"}, {"time": 0.5333, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_124": {"color": [{"time": 0.3333, "color": "270c79ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_04"}, {"time": 0.4667, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_07"}, {"time": 0.5333, "name": null}]}, "baoci_2_125": {"color": [{"time": 0.3333, "color": "270c79ff"}], "attachment": [{"time": 0.3333, "name": "baoci_2_04"}, {"time": 0.4667, "name": "baoci_2_05"}, {"time": 0.5, "name": "baoci_2_07"}, {"time": 0.5333, "name": null}]}, "baoci_2_126": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.4, "color": "ca25ffff"}, {"time": 0.4333, "color": "c324ffff"}], "attachment": [{"time": 0.4, "name": "baoci_2_01"}, {"time": 0.4333, "name": "baoci_2_03"}, {"time": 0.4667, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_127": {"attachment": [{"time": 0.4, "name": "baoci_2_01"}, {"time": 0.4333, "name": "baoci_2_02"}, {"time": 0.4667, "name": "baoci_2_03"}, {"time": 0.5, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_128": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.3667, "color": "ca25ffff"}, {"time": 0.4, "color": "8700ffff"}], "attachment": [{"time": 0.3667, "name": "baoci_2_01"}, {"time": 0.4, "name": "baoci_2_03"}, {"time": 0.4333, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "baoci_2_129": {"attachment": [{"time": 0.3667, "name": "baoci_2_01"}, {"time": 0.4, "name": "baoci_2_02"}, {"time": 0.4333, "name": "baoci_2_03"}, {"time": 0.4667, "name": "baoci_2_05"}, {"time": 0.5333, "name": "baoci_2_06"}, {"time": 0.5667, "name": "baoci_2_07"}, {"time": 0.6, "name": null}]}, "ci_18": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.3667, "color": "000000ff"}, {"time": 0.4, "color": "6f00ffff"}], "attachment": [{"time": 0.3667, "name": "ci_00003"}, {"time": 0.4667, "name": "ci_00004"}, {"time": 0.5333, "name": null}]}, "ci_19": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.4, "color": "000000ff"}, {"time": 0.4333, "color": "6e27ffff"}, {"time": 0.4667, "color": "8e46a2e5"}], "attachment": [{"time": 0.3667, "name": "ci_00003"}, {"time": 0.4667, "name": "ci_00004"}, {"time": 0.5333, "name": null}]}, "ci_20": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.3667, "color": "000000ff"}, {"time": 0.4, "color": "6f00ffff"}, {"time": 0.4333, "color": "7e56038b"}], "attachment": [{"time": 0.3667, "name": "ci_00003"}, {"time": 0.4667, "name": "ci_00004"}, {"time": 0.5333, "name": null}]}, "ci_21": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.4, "color": "000000ff"}, {"time": 0.4333, "color": "6e27ffff"}, {"time": 0.4667, "color": "8e46a2e5"}], "attachment": [{"time": 0.3667, "name": "ci_00003"}, {"time": 0.4667, "name": "ci_00004"}, {"time": 0.5333, "name": null}]}, "ft1": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "ft"}]}, "ft21": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"name": "ft"}]}, "glow31": {"attachment": [{"time": 0.3667, "name": "glow"}, {"time": 0.4333, "name": null}]}, "glow32": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.4, "color": "b343ffff", "curve": "stepped"}, {"time": 0.4667, "color": "00adffff"}, {"time": 0.5333, "color": "00ffee00"}], "attachment": [{"time": 0.4, "name": "glow"}, {"time": 0.4333, "name": null}, {"time": 0.4667, "name": "glow"}, {"time": 0.5333, "name": null}]}, "glow33": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.3667, "color": "5400e4ff"}], "attachment": [{"time": 0.3667, "name": "glow"}, {"time": 0.4, "name": null}]}, "glow34": {"color": [{"time": 0.3333, "color": "000000ff", "curve": "stepped"}, {"time": 0.3667, "color": "000000ff", "curve": "stepped"}, {"time": 0.4, "color": "00adffff"}, {"time": 0.4333, "color": "9100ff80", "curve": "stepped"}, {"time": 0.6333, "color": "9100ff80"}, {"time": 0.8667, "color": "9000ff00"}], "attachment": [{"time": 0.3333, "name": "glow"}, {"time": 0.3667, "name": null}, {"time": 0.4, "name": "glow"}]}, "lizi10": {"color": [{"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "lizi"}]}, "lizi11": {"color": [{"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "lizi"}]}, "lizi12": {"color": [{"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "lizi"}]}, "lizi13": {"color": [{"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "lizi"}]}, "yw_10": {"color": [{"time": 0.3667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "yw_01"}, {"time": 0.4333, "name": "yw_02"}, {"time": 0.4667, "name": "yw_03"}, {"time": 0.5, "name": "yw_04"}, {"time": 0.5333, "name": "yw_05"}, {"time": 0.5667, "name": "yw_06"}, {"time": 0.6, "name": "yw_07"}, {"time": 0.6333, "name": "yw_08"}, {"time": 0.6667, "name": "yw_09"}, {"time": 0.7, "name": "yw_10"}, {"time": 0.7333, "name": "yw_11"}, {"time": 0.7667, "name": "yw_12"}, {"time": 0.8, "name": "yw_13"}, {"time": 0.8333, "name": "yw_14"}, {"time": 0.8667, "name": "yw_15"}, {"time": 0.9, "name": "yw_16"}, {"time": 0.9333, "name": null}]}, "yw_13": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "yw_01"}, {"time": 0.4, "name": "yw_02"}, {"time": 0.4333, "name": "yw_03"}, {"time": 0.4667, "name": "yw_04"}, {"time": 0.5, "name": "yw_05"}, {"time": 0.5333, "name": "yw_06"}, {"time": 0.5667, "name": "yw_07"}, {"time": 0.6, "name": "yw_08"}, {"time": 0.6333, "name": "yw_09"}, {"time": 0.6667, "name": "yw_10"}, {"time": 0.7, "name": "yw_11"}, {"time": 0.7333, "name": "yw_12"}, {"time": 0.7667, "name": "yw_13"}, {"time": 0.8, "name": "yw_14"}, {"time": 0.8333, "name": "yw_15"}, {"time": 0.8667, "name": "yw_16"}, {"time": 0.9, "name": null}]}, "yw_20": {"color": [{"time": 0.4, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffff68", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff68"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0.4, "name": "yw_01"}, {"time": 0.4667, "name": "yw_02"}, {"time": 0.5, "name": "yw_03"}, {"time": 0.5333, "name": "yw_04"}, {"time": 0.5667, "name": "yw_05"}, {"time": 0.6333, "name": "yw_06"}, {"time": 0.6667, "name": "yw_07"}, {"time": 0.7333, "name": "yw_08"}, {"time": 0.7667, "name": "yw_09"}, {"time": 0.8333, "name": "yw_10"}, {"time": 0.8667, "name": "yw_11"}, {"time": 0.9333, "name": "yw_12"}, {"time": 0.9667, "name": "yw_13"}, {"time": 1.0333, "name": "yw_14"}, {"time": 1.0667, "name": "yw_15"}, {"time": 1.1, "name": "yw_16"}, {"time": 1.1667, "name": null}]}, "yw_21": {"color": [{"time": 0.4333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.4333, "name": "yw_01"}, {"time": 0.5, "name": "yw_02"}, {"time": 0.5333, "name": "yw_03"}, {"time": 0.5667, "name": "yw_04"}, {"time": 0.6, "name": "yw_05"}, {"time": 0.6333, "name": "yw_06"}, {"time": 0.6667, "name": "yw_07"}, {"time": 0.7, "name": "yw_08"}, {"time": 0.7333, "name": "yw_09"}, {"time": 0.7667, "name": "yw_10"}, {"time": 0.8, "name": "yw_11"}, {"time": 0.8333, "name": "yw_12"}, {"time": 0.8667, "name": "yw_13"}, {"time": 0.9, "name": "yw_14"}, {"time": 0.9333, "name": "yw_15"}, {"time": 0.9667, "name": "yw_16"}, {"time": 1, "name": null}]}, "yw_23": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "yw_01"}, {"time": 0.4, "name": "yw_02"}, {"time": 0.4333, "name": "yw_03"}, {"time": 0.4667, "name": "yw_04"}, {"time": 0.5, "name": "yw_05"}, {"time": 0.5333, "name": "yw_06"}, {"time": 0.5667, "name": "yw_07"}, {"time": 0.6, "name": "yw_08"}, {"time": 0.6333, "name": "yw_09"}, {"time": 0.6667, "name": "yw_10"}, {"time": 0.7, "name": "yw_11"}, {"time": 0.7333, "name": "yw_12"}, {"time": 0.7667, "name": "yw_13"}, {"time": 0.8, "name": "yw_14"}, {"time": 0.8333, "name": "yw_15"}, {"time": 0.8667, "name": "yw_16"}, {"time": 0.9, "name": null}]}, "yw_25": {"color": [{"time": 0.4, "color": "ffffff00"}, {"time": 0.4667, "color": "00000091", "curve": "stepped"}, {"time": 0.7, "color": "00000091"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"time": 0.4, "name": "yw_01"}, {"time": 0.4667, "name": "yw_02"}, {"time": 0.5, "name": "yw_03"}, {"time": 0.5333, "name": "yw_04"}, {"time": 0.5667, "name": "yw_05"}, {"time": 0.6, "name": "yw_06"}, {"time": 0.6333, "name": "yw_07"}, {"time": 0.6667, "name": "yw_08"}, {"time": 0.7, "name": "yw_09"}, {"time": 0.7333, "name": "yw_10"}, {"time": 0.7667, "name": "yw_11"}, {"time": 0.8, "name": "yw_12"}, {"time": 0.8333, "name": "yw_13"}, {"time": 0.8667, "name": "yw_14"}, {"time": 0.9, "name": "yw_15"}, {"time": 0.9333, "name": "yw_16"}, {"time": 0.9667, "name": null}]}, "yw_26": {"color": [{"time": 0.4333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.4333, "name": "yw_01"}, {"time": 0.5, "name": "yw_02"}, {"time": 0.5333, "name": "yw_03"}, {"time": 0.5667, "name": "yw_04"}, {"time": 0.6, "name": "yw_05"}, {"time": 0.6333, "name": "yw_06"}, {"time": 0.6667, "name": "yw_07"}, {"time": 0.7, "name": "yw_08"}, {"time": 0.7333, "name": "yw_09"}, {"time": 0.7667, "name": "yw_10"}, {"time": 0.8, "name": "yw_11"}, {"time": 0.8333, "name": "yw_12"}, {"time": 0.8667, "name": "yw_13"}, {"time": 0.9, "name": "yw_14"}, {"time": 0.9333, "name": "yw_15"}, {"time": 0.9667, "name": "yw_16"}, {"time": 1, "name": null}]}}, "bones": {"ft": {"scale": [{"x": 1.127, "y": 1.127}]}, "ft2a": {"scale": [{"y": 1.301}]}, "ft2b": {"scale": [{"y": 1.301}]}, "ft2c": {"scale": [{"y": 1.301}]}, "ft2d": {"scale": [{"y": 1.301}]}, "ft2e": {"scale": [{"y": 1.301}]}, "ft2f": {"scale": [{"y": 1.301}]}, "ft2": {"scale": [{"x": -1.382, "y": 1.382}]}, "ft2a2": {"scale": [{"y": 1.777}]}, "ft2b2": {"scale": [{"y": 1.777}]}, "ft2c2": {"scale": [{"y": 1.777}]}, "ft2d2": {"scale": [{"y": 1.777}]}, "ft2e2": {"scale": [{"y": 1.777}]}, "ft2f2": {"scale": [{"y": 1.777}]}, "lizi9": {"rotate": [{"time": 0.3333, "angle": 40.36}], "translate": [{"time": 0.3333, "x": 392.09, "y": 33.54, "curve": 0, "c2": 0.23, "c3": 0.652}, {"time": 0.6667, "x": 345.79, "y": 5.29}], "scale": [{"time": 0.3333, "x": 0.047, "y": 0.047, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.23, "y": 0.09, "curve": "stepped"}, {"time": 0.5333, "x": 0.23, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.047, "y": 0.047}]}, "lizi10": {"rotate": [{"time": 0.3333, "angle": 154.23}], "translate": [{"time": 0.3333, "x": 399.4, "y": 38.66, "curve": 0, "c2": 0.23, "c3": 0.652}, {"time": 0.6667, "x": 432.78, "y": 25.76}], "scale": [{"time": 0.3333, "x": 0.038, "y": 0.038, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.23, "y": 0.09, "curve": "stepped"}, {"time": 0.5333, "x": 0.23, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.038, "y": 0.038}]}, "lizi11": {"rotate": [{"time": 0.3333, "angle": -134.91}], "translate": [{"time": 0.3333, "x": 397.94, "y": 43.77, "curve": 0, "c2": 0.23, "c3": 0.652}, {"time": 0.6667, "x": 416.7, "y": 67.42}], "scale": [{"time": 0.3333, "x": 0.027, "y": 0.042, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.169, "y": 0.066, "curve": "stepped"}, {"time": 0.5333, "x": 0.169, "y": 0.066, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.027, "y": 0.042}]}, "lizi12": {"rotate": [{"time": 0.3333, "angle": -67.36}], "translate": [{"time": 0.3333, "x": 381.12, "y": 48.16, "curve": 0, "c2": 0.23, "c3": 0.652}, {"time": 0.6667, "x": 359.68, "y": 120.05}], "scale": [{"time": 0.3333, "x": 0.072, "y": 0.109, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.169, "y": 0.066, "curve": "stepped"}, {"time": 0.5333, "x": 0.169, "y": 0.066, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.072, "y": 0.109}]}, "baoci_2_60": {"rotate": [{"time": 0.3333, "angle": 122.12}], "translate": [{"time": 0.3333, "x": 422.79, "y": 30.38}], "scale": [{"time": 0.3333, "x": 0.74, "y": 0.74, "curve": "stepped"}, {"time": 0.3667, "x": 0.74, "y": 0.74, "curve": 0, "c2": 0.51, "c3": 0.75}, {"time": 0.6667, "x": 1.091, "y": 1.091}]}, "baoci_2_64": {"rotate": [{"time": 0.3333}, {"time": 0.3667, "angle": 86.04}, {"time": 0.4, "angle": 9.91}], "translate": [{"time": 0.3333}, {"time": 0.3667, "x": 428.08, "y": 23.16}, {"time": 0.4, "x": 464.97, "y": 37.09}], "scale": [{"time": 0.3333}, {"time": 0.3667, "x": -0.782, "y": 0.473}, {"time": 0.4, "x": -0.633, "y": 0.505}, {"time": 0.6, "x": -0.576, "y": 0.739}], "shear": [{"time": 0.3333}, {"time": 0.3667, "y": -10}]}, "yw_23": {"rotate": [{"time": 0.3333, "angle": 94.09}], "translate": [{"time": 0.3333, "x": 158.79, "y": -74.1}, {"time": 0.4667, "x": 220.29, "y": -45.26}, {"time": 0.9, "x": 220.29, "y": -81.99}], "scale": [{"time": 0.3333, "x": 1.178, "y": 1.178}, {"time": 0.3667, "x": 0.61, "y": 0.61}, {"time": 0.4667, "x": 0.802, "y": 0.397}]}, "baoci_2_62": {"rotate": [{"time": 0.3333, "angle": 168.53}], "translate": [{"time": 0.3333, "x": 454.53, "y": 43.93, "curve": "stepped"}, {"time": 0.3667, "x": 454.53, "y": 43.93, "curve": 0, "c2": 0.33, "c3": 0.75}, {"time": 0.5333, "x": 445.34, "y": 78.7}], "scale": [{"time": 0.3333, "x": 0.866, "y": 0.598, "curve": "stepped"}, {"time": 0.3667, "x": 0.866, "y": 0.598, "curve": 0, "c2": 0.33, "c3": 0.75}, {"time": 0.5333, "x": 1.592, "y": 1.099}]}, "ci_11": {"rotate": [{"time": 0.3333}, {"time": 0.3667, "angle": 162.59}], "translate": [{"time": 0.3333}, {"time": 0.3667, "x": 560.17, "y": 38.95}], "scale": [{"time": 0.3333}, {"time": 0.3667, "x": 0.224, "y": 0.224, "curve": 0, "c2": 0.3, "c3": 0.485, "c4": 0.9}, {"time": 0.5333, "x": 0.577, "y": 0.577}]}, "glow16": {"translate": [{"time": 0.3333, "x": 468.32, "y": -2.63, "curve": "stepped"}, {"time": 0.4, "x": 464.43, "y": -11.07}, {"time": 0.4333, "x": 471.29, "y": 30.82}], "scale": [{"time": 0.3333, "x": 2.12, "y": 1.331, "curve": "stepped"}, {"time": 0.4, "x": 0.205, "y": 1.33}, {"time": 0.4333, "x": 2.692, "y": 2.541}]}, "baoci_2_52": {"rotate": [{"time": 0.3333, "angle": -20.3}], "translate": [{"time": 0.3333, "x": 439.12, "y": 58.72, "curve": "stepped"}, {"time": 0.3667, "x": 439.12, "y": 58.72, "curve": 0, "c2": 0.6, "c3": 0.75}, {"time": 0.6, "x": 439.12, "y": 72.98}], "scale": [{"time": 0.3333, "x": 1.179, "y": 0.424, "curve": "stepped"}, {"time": 0.3667, "x": 1.179, "y": 0.424, "curve": 0, "c2": 0.6, "c3": 0.75}, {"time": 0.6, "x": 0.897, "y": 1.034}], "shear": [{"time": 0.3333, "x": -1.34, "y": 39.28}]}, "baoci_2_53": {"rotate": [{"time": 0.3333, "angle": 78.22}], "translate": [{"time": 0.3333, "x": 375.54, "y": 28.61, "curve": 0, "c2": 0.62, "c3": 0.75}, {"time": 0.3667, "x": 410.85, "y": 15.66, "curve": 0, "c2": 0.62, "c3": 0.75}, {"time": 0.6667, "x": 389.2, "y": 27.47}], "scale": [{"time": 0.3333, "x": 0.816, "y": 0.697, "curve": "stepped"}, {"time": 0.3667, "x": 0.816, "y": 0.697, "curve": 0, "c2": 0.62, "c3": 0.75}, {"time": 0.6667, "x": 0.936, "y": 0.817}]}, "baoci_2_54": {"rotate": [{"time": 0.3333, "angle": -125.94}], "translate": [{"time": 0.3333, "x": 552.06, "y": -31.88}, {"time": 0.3667, "x": 496.73, "y": -1.28}]}, "baoci_2_55": {"translate": [{"time": 0.3333, "x": 465.91, "y": 89.84}]}, "baoci_2_56": {"rotate": [{"time": 0.3333, "angle": -23.1}], "translate": [{"time": 0.3333, "x": 509.65, "y": 69.84}], "scale": [{"time": 0.3333, "x": 0.632, "y": 0.653, "curve": "stepped"}, {"time": 0.3667, "x": 0.632, "y": 0.653}, {"time": 0.5667, "x": 0.632, "y": 1.353}], "shear": [{"time": 0.3333, "y": -10}]}, "baoci_2_57": {"rotate": [{"time": 0.3333, "angle": -174.27}], "translate": [{"time": 0.3333, "x": 468.76, "y": -20.33}, {"time": 0.3667, "x": 460.52, "y": -2.67}, {"time": 0.5333, "x": 460.52, "y": -28.71}, {"time": 0.6667, "x": 460.52, "y": -2.67}], "scale": [{"time": 0.3333, "x": 0.735, "y": 0.965}, {"time": 0.3667, "x": 0.969, "y": 1.271}]}, "baoci_2_58": {"rotate": [{"time": 0.3333, "angle": 122.43}], "translate": [{"time": 0.3333, "x": 468.54, "y": 23.09}], "scale": [{"time": 0.3333, "x": 0.794, "y": 1.253, "curve": "stepped"}, {"time": 0.3667, "x": 0.794, "y": 1.253, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.879, "y": 1.938}]}, "baoci_2_59": {"rotate": [{"time": 0.3333, "angle": -26.09}], "translate": [{"time": 0.3333, "x": 495.07, "y": 113.1, "curve": "stepped"}, {"time": 0.3667, "x": 495.07, "y": 113.1, "curve": 0, "c2": 0.44, "c3": 0.75}, {"time": 0.5667, "x": 518.55, "y": 138.6}], "scale": [{"time": 0.3333, "x": 0.979, "y": 0.929, "curve": "stepped"}, {"time": 0.3667, "x": 0.979, "y": 0.929, "curve": 0, "c2": 0.44, "c3": 0.75}, {"time": 0.5667, "x": 1.47, "y": 1.024}]}, "baoci_2_61": {"rotate": [{"time": 0.3333, "angle": -77.63}], "translate": [{"time": 0.3333, "x": 495.06, "y": 36.93}], "scale": [{"time": 0.3333, "x": -0.928, "y": 1.049, "curve": "stepped"}, {"time": 0.3667, "x": -0.928, "y": 1.049, "curve": 0, "c2": 0.35, "c3": 0.75}, {"time": 0.6, "x": -1.056, "y": 1.51}]}, "baoci_2_63": {"rotate": [{"time": 0.3333}, {"time": 0.4, "angle": 32.09}], "translate": [{"time": 0.3333}, {"time": 0.4, "x": 432.06, "y": 54.58}], "scale": [{"time": 0.3333}, {"time": 0.4, "x": 0.664, "y": 0.621}, {"time": 0.4333, "x": 0.632, "y": 0.653}, {"time": 0.6, "x": 0.632, "y": 1.353}], "shear": [{"time": 0.3333}, {"time": 0.4, "y": -10}]}, "ci_10": {"rotate": [{"time": 0.3333, "angle": -153.93}, {"time": 0.3667, "angle": -106.57}], "translate": [{"time": 0.3333}, {"time": 0.3667, "x": 560.17, "y": 38.95}], "scale": [{"time": 0.3333}, {"time": 0.3667, "x": 0.224, "y": 0.224, "curve": 0, "c2": 0.3, "c3": 0.485, "c4": 0.9}, {"time": 0.5333, "x": 0.798, "y": 0.798}]}, "glow13": {"translate": [{"time": 0.3333}, {"time": 0.3667, "x": 442.15, "y": 69.52}], "scale": [{"time": 0.3333}, {"time": 0.3667, "x": 2.397, "y": 2.127}]}, "glow15": {"translate": [{"time": 0.3333}, {"time": 0.3667, "x": 468.28, "y": 85.39}], "scale": [{"time": 0.3333}, {"time": 0.3667, "x": 2.815, "y": 2.586}]}, "glow14": {"translate": [{"time": 0.3333}, {"time": 0.4, "x": 464.97, "y": 75.45, "curve": "stepped"}, {"time": 0.4667, "x": 461.07, "y": 67.01}], "scale": [{"time": 0.3333}, {"time": 0.4, "x": 3.5, "y": 3.215, "curve": "stepped"}, {"time": 0.4667, "x": 1.584, "y": 1.33}]}, "bd5": {"translate": [{"time": 0.3333, "x": -76.38, "y": 31.25}], "scale": [{"time": 0.3333, "x": 0.384, "y": 0.384}]}, "yw_13": {"rotate": [{"time": 0.3333, "angle": 65.67}, {"time": 0.4667, "angle": 98.08}], "translate": [{"time": 0.3333, "x": 158.66, "y": -121.56}, {"time": 0.4667, "x": 2.4, "y": -139.25}], "scale": [{"time": 0.3333, "x": 1.178, "y": 1.178}, {"time": 0.3667, "x": 0.61, "y": 0.61}, {"time": 0.4667, "x": 0.65, "y": 1.452}]}, "yw2": {"translate": [{"time": 0.3333, "x": 77.03, "y": 28.82}], "scale": [{"time": 0.3333, "x": 0.2, "y": 0.2}]}, "bone": {"translate": [{"x": -390.45, "y": 3.28}], "scale": [{"x": 0.765, "y": 0.765}]}, "yw_10": {"rotate": [{"time": 0.3667, "angle": -80.52}, {"time": 0.4, "angle": -109.93}, {"time": 0.5, "angle": -103.19}], "translate": [{"time": 0.3667, "x": 183.3, "y": -156.32}, {"time": 0.5, "x": 244.73, "y": -148.39}], "scale": [{"time": 0.3667, "x": 0.459, "y": 0.459}, {"time": 0.5}]}, "yw_25": {"rotate": [{"time": 0.4, "angle": 129.27}], "translate": [{"time": 0.4, "x": 96.45, "y": -226.68}, {"time": 0.4333, "x": 89.61, "y": -224.44}], "scale": [{"time": 0.4, "x": 1.178, "y": 1.178}, {"time": 0.4333, "x": 0.893, "y": 1.331}]}, "yw_20": {"rotate": [{"time": 0.4, "angle": -95.48}, {"time": 0.5333, "angle": 136.69}], "translate": [{"time": 0.4, "x": 158.79, "y": -74.1}, {"time": 0.5333, "x": -13.26, "y": -33.9}], "scale": [{"time": 0.4, "x": 1.178, "y": 1.178}, {"time": 0.4333, "x": 0.61, "y": 0.61}, {"time": 0.5333, "x": 0.893, "y": 1.331}]}, "yw_26": {"rotate": [{"time": 0.4333, "angle": 65.67}, {"time": 0.5667, "angle": 81.14}], "translate": [{"time": 0.4333, "x": 158.79, "y": -74.1}, {"time": 0.5667, "x": 287.92, "y": -88.94}], "scale": [{"time": 0.4333, "x": 1.178, "y": 1.178}, {"time": 0.4667, "x": 0.61, "y": 0.61}, {"time": 0.5667, "x": 0.65, "y": -1.452}]}, "yw_21": {"rotate": [{"time": 0.4333, "angle": -150.43}], "translate": [{"time": 0.4333, "x": 158.79, "y": -74.1}, {"time": 1.0667, "x": 132.68, "y": -76.54}], "scale": [{"time": 0.4333, "x": 1.178, "y": 1.178}, {"time": 0.4667, "x": 0.61, "y": 0.61}, {"time": 0.5667, "x": 0.557, "y": 0.614}]}, "x": {"translate": [{"x": 0.79, "y": 41.13}], "scale": [{"x": 0.403, "y": 0.403}]}, "anyanshu": {"translate": [{"curve": 0, "c2": 0.99, "c3": 0.506}, {"time": 0.6667, "y": 73.47}]}}, "path": {"ft": {"position": [{"time": 0.3333, "curve": 0.319, "c2": 0.29, "c3": 0.658, "c4": 0.64}, {"time": 0.6667, "position": 0.9293}], "spacing": [{"spacing": -43.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "spacing": 154.7, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6667, "spacing": -43.4}]}, "ft2": {"position": [{"time": 0.3333, "curve": 0.381, "c2": 0.54, "c3": 0.744}, {"time": 0.6667, "position": 0.9293}], "spacing": [{"spacing": -43.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "spacing": 125.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "spacing": -43.4}]}}, "deform": {"default": {"ft": {"ft": [{"vertices": [276.6373, 315.14783, 260.17032, 344.34308, 22.95627, 436.1679]}]}, "ft2": {"ft": [{"vertices": [402.55615, 5.88824, 397.27972, 162.95654, 705.92535, 777.51105, -602.0227, 335.64627, -295.61816, 28.60039, -268.904, -54.45211, 200.60889, -83.16357, 190.5545, -9.63637, 200.37004, 77.96352]}]}}}}}}