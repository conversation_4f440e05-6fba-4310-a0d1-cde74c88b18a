{"skeleton": {"hash": "Mq9ZMtxq3QO1PC5f6iO+1tfIat4", "spine": "3.8.99", "images": "D:/工程/技能特效/导出spine文件法师/暴风雪/images/", "audio": "D:/工程/技能特效/导出spine文件法师/寒冰箭/../魔法箭/../暴风雪/"}, "bones": [{"name": "root"}, {"name": "bao6", "parent": "root", "rotation": 90}, {"name": "bingdi_15", "parent": "bao6"}, {"name": "bingdi_16", "parent": "bao6", "y": 7.32, "scaleY": -1}, {"name": "bingdi_17", "parent": "bao6"}, {"name": "FK_Smoke_11", "parent": "bao6", "x": -3.13}, {"name": "yan_23", "parent": "bao6"}, {"name": "yan_24", "parent": "bao6"}, {"name": "glow6", "parent": "bao6"}, {"name": "yw_4", "parent": "bao6"}], "slots": [{"name": "FK_Smoke_18", "bone": "FK_Smoke_11"}, {"name": "FK_Smoke_19", "bone": "FK_Smoke_11", "blend": "additive"}, {"name": "yan_23", "bone": "yan_23"}, {"name": "yan_24", "bone": "yan_24"}, {"name": "bingdi_16", "bone": "bingdi_16"}, {"name": "bingdi_17", "bone": "bingdi_17"}, {"name": "bingdi_15", "bone": "bingdi_15"}, {"name": "glow6", "bone": "glow6", "blend": "additive"}, {"name": "yw_4", "bone": "yw_4"}, {"name": "bingkuai11", "bone": "bao6"}, {"name": "bingkuai12", "bone": "bao6"}], "skins": [{"name": "default", "attachments": {"FK_Smoke_18": {"FK_Smoke_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33.5, -33.5, -33.5, -33.5, -33.5, 33.5, 33.5, 33.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 67}}, "FK_Smoke_19": {"FK_Smoke_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33.5, -33.5, -33.5, -33.5, -33.5, 33.5, 33.5, 33.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 67}}, "bingdi_15": {"bingdi_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}}, "bingdi_16": {"bingdi_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}}, "bingdi_17": {"bingdi_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}, "bingdi_09": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [83, -83, -83, -83, -83, 83, 83, 83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 166, "height": 166}}, "bingkuai11": {"bingkuai": {"type": "mesh", "uvs": [1, 0.2706, 1, 0.55249, 1, 0.73187, 1, 1, 0.84193, 1, 0.70804, 1, 0.47014, 1, 0.2193, 1, 0, 1, 0, 0.65227, 0, 0.40566, 0, 0, 0.25249, 0, 1, 0, 0.13214, 0.58238, 0.22718, 0.651, 0.32841, 0.6164, 0.33347, 0.1823, 0.58656, 0.49687, 0.76372, 0.44968, 0.83964, 0.6573, 0.87192, 0.74585, 0.83068, 0.31253, 0.82319, 0.51055], "triangles": [17, 12, 13, 22, 17, 13, 0, 22, 13, 10, 11, 12, 10, 12, 17, 22, 18, 17, 19, 18, 22, 23, 19, 22, 22, 0, 1, 23, 22, 1, 15, 14, 10, 10, 17, 15, 18, 16, 17, 16, 15, 17, 9, 10, 14, 20, 23, 1, 19, 23, 20, 20, 1, 2, 21, 20, 2, 9, 7, 8, 14, 7, 9, 14, 15, 7, 6, 16, 18, 18, 19, 20, 5, 6, 18, 7, 15, 16, 6, 7, 16, 20, 5, 18, 5, 20, 21, 4, 5, 21, 21, 2, 3, 4, 21, 3], "vertices": [87.19, -27.83, 87.19, -50.46, 87.19, -64.86, 87.19, -86.38, 71.42, -86.38, 58.06, -86.38, 34.33, -86.38, 9.3, -86.38, -12.58, -86.38, -12.58, -58.47, -12.58, -38.68, -12.58, -6.11, 12.61, -6.11, 87.19, -6.11, 0.6, -52.86, 10.09, -58.37, 20.19, -55.59, 20.69, -20.75, 45.94, -46, 63.62, -42.21, 71.19, -58.87, 74.41, -65.98, 70.3, -31.2, 69.55, -47.1], "hull": 14, "edges": [16, 18, 18, 28, 14, 16, 28, 14, 18, 20, 20, 22, 20, 30, 30, 32, 32, 34, 22, 24, 24, 26, 34, 24, 36, 38, 38, 40, 40, 10, 10, 12, 12, 14, 36, 12, 6, 4, 4, 42, 6, 8, 8, 10, 42, 8, 0, 26, 44, 0, 44, 46, 0, 2, 2, 4, 46, 2], "width": 59, "height": 48}}, "bingkuai12": {"bingkuai": {"type": "mesh", "uvs": [1, 0.2706, 1, 0.55249, 1, 0.73187, 1, 1, 0.84193, 1, 0.70804, 1, 0.47014, 1, 0.2193, 1, 0, 1, 0, 0.65227, 0, 0.40566, 0, 0, 0.25249, 0, 1, 0, 0.13214, 0.58238, 0.22718, 0.651, 0.32841, 0.6164, 0.33347, 0.1823, 0.58656, 0.49687, 0.76372, 0.44968, 0.83964, 0.6573, 0.87192, 0.74585, 0.83068, 0.31253, 0.82319, 0.51055], "triangles": [17, 12, 13, 22, 17, 13, 0, 22, 13, 10, 11, 12, 10, 12, 17, 22, 18, 17, 19, 18, 22, 23, 19, 22, 22, 0, 1, 23, 22, 1, 15, 14, 10, 10, 17, 15, 18, 16, 17, 16, 15, 17, 9, 10, 14, 20, 23, 1, 19, 23, 20, 20, 1, 2, 21, 20, 2, 9, 7, 8, 14, 7, 9, 14, 15, 7, 6, 16, 18, 18, 19, 20, 5, 6, 18, 7, 15, 16, 6, 7, 16, 20, 5, 18, 5, 20, 21, 4, 5, 21, 21, 2, 3, 4, 21, 3], "vertices": [87.19, -27.83, 87.19, -50.46, 87.19, -64.86, 87.19, -86.38, 71.42, -86.38, 58.06, -86.38, 34.33, -86.38, 9.3, -86.38, -12.58, -86.38, -12.58, -58.47, -12.58, -38.68, -12.58, -6.11, 12.61, -6.11, 87.19, -6.11, 0.6, -52.86, 10.09, -58.37, 20.19, -55.59, 20.69, -20.75, 45.94, -46, 63.62, -42.21, 71.19, -58.87, 74.41, -65.98, 70.3, -31.2, 69.55, -47.1], "hull": 14, "edges": [16, 18, 18, 28, 14, 16, 28, 14, 18, 20, 20, 22, 20, 30, 30, 32, 32, 34, 22, 24, 24, 26, 34, 24, 36, 38, 38, 40, 40, 10, 10, 12, 12, 14, 36, 12, 6, 4, 4, 42, 6, 8, 8, 10, 42, 8, 0, 26, 44, 0, 44, 46, 0, 2, 2, 4, 46, 2], "width": 59, "height": 48}}, "glow6": {"glow": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50, -50, -50, -50, -50, 50, 50, 50], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 100, "height": 100}}, "yan_23": {"yan_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}}, "yan_24": {"yan_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_08": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_10": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_12": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_14": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yan_16": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}}, "yw_4": {"yw_00": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yw_01": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yw_02": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yw_03": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yw_04": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yw_05": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yw_06": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}, "yw_07": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [62.5, -62.5, -62.5, -62.5, -62.5, 62.5, 62.5, 62.5], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 125, "height": 125}}}}], "animations": {"animation": {"slots": {"FK_Smoke_18": {"color": [{"color": "ffffffff"}, {"time": 0.0667, "color": "b4caffff", "curve": "stepped"}, {"time": 0.3, "color": "b4caffff", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "dffbff00", "curve": "stepped"}, {"time": 0.6, "color": "dffbff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "FK_Smoke_05"}, {"time": 0.7, "name": null}]}, "FK_Smoke_19": {"color": [{"color": "ffffffff"}, {"time": 0.0667, "color": "003dd0ff", "curve": "stepped"}, {"time": 0.3, "color": "003dd0ff", "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "color": "26a8f500", "curve": "stepped"}, {"time": 0.6, "color": "26a8f500", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "FK_Smoke_05"}, {"time": 0.7, "name": null}]}, "bingdi_15": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "545dffff"}, {"time": 0.1333, "color": "3e86f8ff"}, {"time": 0.1667, "color": "ffffffff"}], "attachment": [{"time": 0.1, "name": "bingdi_03"}, {"time": 0.1667, "name": "bingdi_04"}, {"time": 0.2, "name": "bingdi_05"}, {"time": 0.2333, "name": "bingdi_06"}, {"time": 0.2667, "name": "bingdi_07"}, {"time": 0.3, "name": null}]}, "bingdi_16": {"color": [{"time": 0.0333, "color": "ffffffff"}, {"time": 0.0667, "color": "0b8be5ff"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"name": "bingdi_03"}, {"time": 0.0667, "name": "bingdi_04"}, {"time": 0.1, "name": "bingdi_05"}, {"time": 0.1333, "name": "bingdi_06"}, {"time": 0.1667, "name": "bingdi_07"}, {"time": 0.2, "name": "bingdi_08"}, {"time": 0.2333, "name": "bingdi_09"}, {"time": 0.2667, "name": null}]}, "bingdi_17": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "545dffff"}, {"time": 0.1333, "color": "3e86f8ff"}, {"time": 0.1667, "color": "023e9eff", "curve": "stepped"}, {"time": 0.2333, "color": "023e9eff"}, {"time": 0.3333, "color": "023e9e00", "curve": "stepped"}, {"time": 0.6, "color": "023e9e00", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0.1, "name": "bingdi_03"}, {"time": 0.1667, "name": "bingdi_04"}, {"time": 0.2, "name": "bingdi_05"}, {"time": 0.2333, "name": "bingdi_06"}, {"time": 0.3333, "name": null}]}, "bingkuai11": {"color": [{"time": 0.2, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0.0333, "name": "bin<PERSON><PERSON><PERSON>"}, {"time": 0.7, "name": null}]}, "bingkuai12": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0.1333, "name": "bin<PERSON><PERSON><PERSON>"}, {"time": 0.7, "name": null}]}, "glow6": {"color": [{"color": "ffffffff"}, {"time": 0.0667, "color": "60fff8ff"}, {"time": 0.1, "color": "29b5ffff"}, {"time": 0.1333, "color": "ffffffda"}, {"time": 0.1667, "color": "0091ff76"}, {"time": 0.2, "color": "0eb5ffff"}, {"time": 0.5333, "color": "0090ff00", "curve": "stepped"}, {"time": 0.6, "color": "0090ff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "glow"}, {"time": 0.7, "name": null}]}, "yan_23": {"color": [{"color": "93dbffff"}], "attachment": [{"name": "yan_04"}, {"time": 0.0667, "name": "yan_06"}, {"time": 0.1333, "name": "yan_08"}, {"time": 0.2, "name": "yan_10"}, {"time": 0.2667, "name": "yan_12"}, {"time": 0.3333, "name": "yan_14"}, {"time": 0.4, "name": "yan_16"}, {"time": 0.4333, "name": null}]}, "yan_24": {"color": [{"color": "ffffffff"}, {"time": 0.0667, "color": "93dbffff", "curve": "stepped"}, {"time": 0.6, "color": "93dbffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "yan_02"}, {"time": 0.1, "name": "yan_04"}, {"time": 0.1667, "name": "yan_06"}, {"time": 0.2333, "name": "yan_08"}, {"time": 0.2667, "name": "yan_10"}, {"time": 0.3333, "name": "yan_12"}, {"time": 0.4, "name": "yan_14"}, {"time": 0.4667, "name": "yan_16"}, {"time": 0.5, "name": null}]}, "yw_4": {"color": [{"color": "ffffffc2"}], "attachment": [{"time": 0.0333, "name": "yw_00"}, {"time": 0.1, "name": "yw_01"}, {"time": 0.1333, "name": "yw_02"}, {"time": 0.2, "name": "yw_03"}, {"time": 0.2333, "name": "yw_04"}, {"time": 0.3, "name": "yw_05"}, {"time": 0.3333, "name": "yw_06"}, {"time": 0.4, "name": "yw_07"}, {"time": 0.4667, "name": null}]}}, "bones": {"bao6": {"scale": [{"x": 0.134, "y": 0.134}, {"time": 0.5, "x": 0.135, "y": 0.135}]}, "bingdi_15": {"translate": [{}, {"time": 0.1, "x": 2.1, "y": 21.04, "curve": "stepped"}, {"time": 0.6, "x": 2.1, "y": 21.04, "curve": "stepped"}, {"time": 0.7}], "scale": [{}, {"time": 0.1, "x": 2.796, "y": 2.796, "curve": 0, "c2": 0.32, "c3": 0.358, "c4": 0.65}, {"time": 0.1333, "x": 0.966, "y": 1.083, "curve": 0.238, "c2": 0.33, "c3": 0.58, "c4": 0.66}, {"time": 0.1667, "x": 2.001, "y": 2.401, "curve": 0.253, "c2": 0.5, "c3": 0.787}, {"time": 0.3, "x": 3.725, "y": 5.054, "curve": "stepped"}, {"time": 0.6, "x": 3.725, "y": 5.054, "curve": "stepped"}, {"time": 0.7}]}, "bingdi_16": {"translate": [{"y": 21.85, "curve": "stepped"}, {"time": 0.6, "y": 21.85, "curve": "stepped"}, {"time": 0.7}, {"time": 0.8667, "y": 13.65}], "scale": [{"x": 0.233, "y": -1.382, "curve": 0, "c2": 0.55, "c3": 0.649, "c4": 0.92}, {"time": 0.2, "x": 4.922, "y": 3.531, "curve": "stepped"}, {"time": 0.6, "x": 4.922, "y": 3.531, "curve": "stepped"}, {"time": 0.7}, {"time": 0.8667, "x": 0.52, "y": -0.489}]}, "bingdi_17": {"rotate": [{}, {"time": 0.1, "angle": 0.88, "curve": "stepped"}, {"time": 0.6, "angle": 0.88, "curve": "stepped"}, {"time": 0.7}], "translate": [{}, {"time": 0.1, "x": -0.24, "y": 59.99, "curve": "stepped"}, {"time": 0.6, "x": -0.24, "y": 59.99, "curve": "stepped"}, {"time": 0.7}], "scale": [{}, {"time": 0.1, "x": 1.863, "y": 2.796, "curve": 0, "c2": 0.39, "c3": 0.75}, {"time": 0.3333, "x": 3.253, "y": 4.248, "curve": "stepped"}, {"time": 0.6, "x": 3.253, "y": 4.248, "curve": "stepped"}, {"time": 0.7}]}, "FK_Smoke_11": {"rotate": [{}, {"time": 0.0667, "angle": -7.73, "curve": "stepped"}, {"time": 0.6, "angle": -7.73, "curve": "stepped"}, {"time": 0.7}], "translate": [{}, {"time": 0.0667, "x": -14.49, "y": 24.36, "curve": "stepped"}, {"time": 0.6, "x": -14.49, "y": 24.36}, {"time": 0.7}], "scale": [{}, {"time": 0.0667, "x": 1.527, "y": 0.976, "curve": "stepped"}, {"time": 0.6, "x": 1.527, "y": 0.976, "curve": "stepped"}, {"time": 0.7}]}, "yan_23": {"rotate": [{"angle": -15.55, "curve": "stepped"}, {"time": 0.6, "angle": -15.55, "curve": "stepped"}, {"time": 0.7}, {"time": 0.8667, "angle": -9.72}], "translate": [{"x": -7.31, "y": 34.94, "curve": "stepped"}, {"time": 0.6, "x": -7.31, "y": 34.94, "curve": "stepped"}, {"time": 0.7}, {"time": 0.8667, "x": -4.57, "y": 21.84}], "scale": [{"x": 0.233, "y": -0.289}, {"time": 0.4333, "x": 1.963, "y": -2.437, "curve": "stepped"}, {"time": 0.6, "x": 1.963, "y": -2.437, "curve": "stepped"}, {"time": 0.7}, {"time": 0.8667, "x": 0.521, "y": 0.194}], "shear": [{"x": 38.62, "y": 56.3}]}, "yan_24": {"rotate": [{}, {"time": 0.0667, "angle": -98.94, "curve": "stepped"}, {"time": 0.6, "angle": -98.94, "curve": "stepped"}, {"time": 0.7}], "translate": [{}, {"time": 0.0667, "x": 36.76, "y": 54.91, "curve": "stepped"}, {"time": 0.6, "x": 36.76, "y": 54.91, "curve": "stepped"}, {"time": 0.7}], "scale": [{}, {"time": 0.0667, "x": -0.078, "y": -0.221, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -1.276, "y": -2.319, "curve": "stepped"}, {"time": 0.6, "x": -1.276, "y": -2.319, "curve": "stepped"}, {"time": 0.7}], "shear": [{"time": 0.0667, "x": 69.95, "y": 68.26}]}, "glow6": {"translate": [{}, {"time": 0.0667, "x": -26.73, "y": 67.69}, {"time": 0.1667, "x": -26.73, "y": 64.4, "curve": "stepped"}, {"time": 0.6, "x": -26.73, "y": 64.4, "curve": "stepped"}, {"time": 0.7}], "scale": [{}, {"time": 0.0667, "x": 4.43, "y": 1.556}, {"time": 0.1667, "x": 8.523, "y": 2.994}, {"time": 0.2, "x": 5.017, "y": 2.882, "curve": "stepped"}, {"time": 0.6, "x": 5.017, "y": 2.882, "curve": "stepped"}, {"time": 0.7}]}, "yw_4": {"translate": [{"x": -1.53, "y": 47.51}, {"time": 0.0333, "x": -3.06, "y": 95.02, "curve": 0, "c2": 0.95, "c3": 0.63}, {"time": 0.3, "x": 3.09, "y": 217.53, "curve": "stepped"}, {"time": 0.6, "x": 3.09, "y": 217.53, "curve": "stepped"}, {"time": 0.7}, {"time": 0.8667, "x": -1.53, "y": 47.51}], "scale": [{"x": 1.457, "y": 1.457}, {"time": 0.0333, "x": 1.914, "y": 1.914, "curve": 0, "c2": 0.95, "c3": 0.63}, {"time": 0.3, "x": 3.943, "y": 3.943, "curve": "stepped"}, {"time": 0.6, "x": 3.943, "y": 3.943, "curve": "stepped"}, {"time": 0.7}, {"time": 0.8667, "x": 1.457, "y": 1.457}]}}, "deform": {"default": {"bingkuai11": {"bingkuai": [{"curve": "stepped"}, {"time": 0.0333, "vertices": [-40.22406, 79.40463, -40.22389, 78.65024, -40.22386, 78.17059, -40.22386, 77.45279, -40.74966, 77.45279, -41.19501, 77.45279, -41.98636, 77.45279, -42.82065, 77.45279, -43.54994, 77.45279, -43.54991, 78.38339, -43.54991, 79.04291, -43.54994, 80.12878, -42.71024, 80.12878, -44.76967, 90.28976, -43.11026, 78.57004, -42.7942, 78.38647, -42.45761, 78.47919, -42.44089, 79.64081, -41.59897, 78.7991, -41.00972, 78.92532, -40.75716, 78.36963, -40.64987, 78.13272, -40.78703, 79.2923, -40.81192, 78.76263], "curve": 0, "c2": 0.57, "c3": 0.75}, {"time": 0.3667, "vertices": [214.2331, 291.16638, 214.23308, 219.94525, 226.8256, 99.06653, 226.82556, 31.3215, 85.4388, 45.7131, 73.97415, 286.77283, -0.73473, 286.77283, -231.01494, 121.67609, -245.86371, 38.69406, -351.72766, 57.63754, -128.5044, 204.22293, -128.50441, 264.2244, -82.08586, 264.2244, 124.2854, 296.57108, -364.05725, 111.4469, -86.73862, 167.93338, -68.12743, 173.05142, -67.19682, 237.26013, 35.82507, 413.893, 91.45943, 425.81458, 115.30247, 373.35953, 186.60326, 95.53473, 161.06142, 280.57138, 158.70694, 230.53998], "curve": "stepped"}, {"time": 0.6, "vertices": [214.2331, 291.16638, 214.23308, 219.94525, 226.8256, 99.06653, 226.82556, 31.3215, 85.4388, 45.7131, 73.97415, 286.77283, -0.73473, 286.77283, -231.01494, 121.67609, -245.86371, 38.69406, -351.72766, 57.63754, -128.5044, 204.22293, -128.50441, 264.2244, -82.08586, 264.2244, 124.2854, 296.57108, -364.05725, 111.4469, -86.73862, 167.93338, -68.12743, 173.05142, -67.19682, 237.26013, 35.82507, 413.893, 91.45943, 425.81458, 115.30247, 373.35953, 186.60326, 95.53473, 161.06142, 280.57138, 158.70694, 230.53998], "curve": "stepped"}, {"time": 0.7}]}, "bingkuai12": {"bingkuai": [{"curve": "stepped"}, {"time": 0.1333, "vertices": [-47.85158, 94.31766, -40.63042, 94.70651, -36.03519, 94.95407, -29.1667, 95.32346, -28.89583, 90.29037, -28.66644, 86.02759, -28.25876, 78.45282, -27.82892, 70.46619, -27.453, 63.48389, -36.36063, 63.00473, -42.6777, 62.66425, -53.06934, 62.10526, -53.50206, 70.14426, -62.24467, 102.20508, -38.37727, 67.11499, -36.7821, 70.23578, -37.84212, 73.41156, -48.97105, 72.974, -41.34654, 81.46606, -42.8588, 87.04147, -37.67059, 89.74472, -35.45763, 90.89484, -46.48698, 88.98456, -41.40155, 89.01895], "curve": 0, "c2": 0.57, "c3": 0.75}, {"time": 0.4667, "vertices": [148.7626, 443.16226, 164.95352, 373.3484, 271.81552, 187.31662, 271.81552, 119.57159, 130.42874, 133.9632, -140.152, 82.06079, -139.37122, 140.34769, -178.06961, 262.09827, -226.03818, 204.16519, -299.9461, 265.3609, -56.15791, 362.65692, -56.15791, 386.80844, -37.47371, 386.80844, 55.48386, 432.64215, -285.89606, 312.56873, -39.34653, 348.0498, -31.85521, 350.10986, -31.48062, 375.9549, -40.57559, 110.4957, -31.85589, 66.966, -73.02982, 48.91171, 231.5932, 183.78482, 99.05013, 420.68896, 108.11601, 371.11087], "curve": "stepped"}, {"time": 0.6, "vertices": [148.7626, 443.16226, 164.95352, 373.3484, 271.81552, 187.31662, 271.81552, 119.57159, 130.42874, 133.9632, -140.152, 82.06079, -139.37122, 140.34769, -178.06961, 262.09827, -226.03818, 204.16519, -299.9461, 265.3609, -56.15791, 362.65692, -56.15791, 386.80844, -37.47371, 386.80844, 55.48386, 432.64215, -285.89606, 312.56873, -39.34653, 348.0498, -31.85521, 350.10986, -31.48062, 375.9549, -40.57559, 110.4957, -31.85589, 66.966, -73.02982, 48.91171, 231.5932, 183.78482, 99.05013, 420.68896, 108.11601, 371.11087], "curve": "stepped"}, {"time": 0.7}]}}}}}}