{"skeleton": {"hash": "zYB8aSsn8Q8l0kirR2PKLjifT80", "spine": "3.8.99", "x": -33.33, "y": -18.98, "width": 73.16, "height": 102.92, "images": "./images/", "audio": "D:/spine动效/怪物/寒脊山谷/霜鬃巨魔"}, "bones": [{"name": "root", "scaleX": 0.4, "scaleY": 0.4}, {"name": "zong", "parent": "root"}, {"name": "body2", "parent": "zong", "x": -1.85, "y": 46.56}, {"name": "body3", "parent": "body2", "length": 18.23, "rotation": 73.3, "x": -0.87, "y": 5.53}, {"name": "body4", "parent": "body3", "length": 27.26, "rotation": 0.59, "x": 18.23}, {"name": "head", "parent": "body4", "length": 37.79, "rotation": -7.49, "x": 26.98, "y": -0.08}, {"name": "hand_R2", "parent": "body4", "length": 52.81, "rotation": 166.02, "x": 32.52, "y": 43.62}, {"name": "hand_R", "parent": "hand_R2", "length": 28.52, "rotation": 38.31, "x": 54.65, "y": -0.28}, {"name": "hand_R3", "parent": "hand_R", "length": 23.75, "rotation": 8.89, "x": 28.52}, {"name": "hand_L3", "parent": "body4", "length": 45.88, "rotation": -134.23, "x": 38.33, "y": -3.77}, {"name": "hand_L2", "parent": "hand_L3", "length": 32.6, "rotation": -30.68, "x": 47.64, "y": 1.34}, {"name": "hand_L", "parent": "hand_L2", "length": 17, "rotation": -5.86, "x": 32.58, "y": 1.16}, {"name": "weapons", "parent": "hand_L", "length": 44.54, "rotation": 78.21, "x": 0.7, "y": 2.31}, {"name": "leg_L3", "parent": "body2", "length": 27.94, "rotation": -63.11, "x": 11.75, "y": 6.45}, {"name": "leg_L4", "parent": "leg_L3", "length": 26.41, "rotation": -50.39, "x": 27.78, "y": -0.31}, {"name": "leg_L2", "parent": "zong", "length": 31.87, "rotation": -11.43, "x": 11.65, "y": 4.57, "color": "ff3f00ff"}, {"name": "leg_R3", "parent": "body2", "length": 29.55, "rotation": -74.15, "x": -21.95, "y": 4.34}, {"name": "leg_R2", "parent": "leg_R3", "length": 29.07, "rotation": -58.4, "x": 28.83, "y": -1.25}, {"name": "leg_R", "parent": "zong", "length": 22.53, "rotation": -18.15, "x": -35.38, "y": 1.06, "color": "ff3f00ff"}, {"name": "ear", "parent": "head", "length": 23.65, "rotation": 80.33, "x": 6.2, "y": 25.25}, {"name": "ear2", "parent": "ear", "length": 21.2, "rotation": -12.32, "x": 23.65}, {"name": "ear3", "parent": "ear2", "length": 18.87, "rotation": -4.72, "x": 21.2}, {"name": "ear4", "parent": "ear3", "length": 11.57, "rotation": -5.57, "x": 18.87}, {"name": "ear5", "parent": "ear4", "length": 12.62, "rotation": -12.57, "x": 11.57}, {"name": "ear6", "parent": "head", "length": 25.42, "rotation": 7.37, "x": 26.65, "y": -26.18}, {"name": "ear7", "parent": "ear6", "length": 18.74, "rotation": 2.88, "x": 25.42}, {"name": "ear8", "parent": "ear7", "length": 15.24, "rotation": -4.35, "x": 18.74}, {"name": "ear9", "parent": "ear8", "length": 10.23, "rotation": -7.32, "x": 15.24}, {"name": "hair", "parent": "head", "length": 21.76, "rotation": 35.07, "x": 26.69, "y": -14.7}, {"name": "hair2", "parent": "hair", "length": 26.14, "rotation": -4.68, "x": 22.97, "y": -0.25}, {"name": "hair3", "parent": "hair2", "length": 22.5, "rotation": 9.16, "x": 26.14}, {"name": "hair4", "parent": "hair3", "length": 17.86, "rotation": 14.17, "x": 22.5}, {"name": "hair5", "parent": "hair4", "length": 13.77, "rotation": 13.98, "x": 17.86}, {"name": "hair6", "parent": "head", "length": 24.39, "rotation": 45.94, "x": 24.65, "y": 4.64}, {"name": "hair7", "parent": "hair6", "length": 25.43, "rotation": 3.6, "x": 24.39}, {"name": "hair8", "parent": "hair7", "length": 16.63, "rotation": 16.04, "x": 25.43}, {"name": "hair9", "parent": "hair8", "length": 13.85, "rotation": 9.35, "x": 16.63}, {"name": "hair10", "parent": "head", "length": 32.26, "rotation": 18.66, "x": 20.02, "y": -23.35}, {"name": "hair11", "parent": "hair10", "length": 23.49, "rotation": 5.7, "x": 32.85, "y": 0.36}, {"name": "hair12", "parent": "hair11", "length": 17.01, "rotation": -2.84, "x": 23.49}, {"name": "hair13", "parent": "hair12", "length": 14.12, "rotation": 12.16, "x": 17.01}, {"name": "hair14", "parent": "head", "length": 23.55, "rotation": 61.68, "x": 14.6, "y": 13.74}, {"name": "hair15", "parent": "hair14", "length": 20.77, "rotation": -1.54, "x": 23.55}, {"name": "hair16", "parent": "hair15", "length": 20.08, "rotation": 0.34, "x": 20.77}, {"name": "hair17", "parent": "hair16", "length": 15.76, "rotation": -8.8, "x": 20.08}, {"name": "yinying", "parent": "root", "length": 205.58, "rotation": 0.53, "x": -85.3, "y": -0.18, "scaleX": 0.7614}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "hand_L3", "bone": "hand_L3", "attachment": "hand_L3"}, {"name": "hand_L2", "bone": "hand_L2", "attachment": "hand_L2"}, {"name": "weapons", "bone": "weapons", "attachment": "weapons"}, {"name": "hand_L", "bone": "hand_L", "attachment": "hand_L"}, {"name": "leg_L3", "bone": "leg_L3", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L4", "attachment": "leg_L"}, {"name": "body2", "bone": "body2", "attachment": "body2"}, {"name": "body", "bone": "body2", "attachment": "body"}, {"name": "leg_R3", "bone": "leg_R3", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R2", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "hand_R2", "bone": "hand_R2", "attachment": "hand_R2"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}, {"name": "ear2", "bone": "ear6", "attachment": "ear2"}, {"name": "hair4", "bone": "hair14", "attachment": "hair4"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye_close2", "bone": "head"}, {"name": "eye_close", "bone": "head"}, {"name": "hair3", "bone": "hair6", "attachment": "hair3"}, {"name": "ear", "bone": "ear", "attachment": "ear"}, {"name": "hair2", "bone": "hair10", "attachment": "hair2"}, {"name": "hair", "bone": "hair", "attachment": "hair"}], "ik": [{"name": "leg_L2", "bones": ["leg_L3", "leg_L4"], "target": "leg_L2", "bendPositive": false}, {"name": "leg_R", "order": 1, "bones": ["leg_R3", "leg_R2"], "target": "leg_R", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.0915, 0.09217, 0.11632, 0.00063, 0.23386, 0.05833, 0.33327, 0.10714, 0.41726, 0.14837, 0.52007, 0.19884, 0.61501, 0.19722, 0.65779, 0.18826, 0.73707, 0.17166, 0.86719, 0.14441, 0.91869, 0.33504, 0.95745, 0.47849, 1, 0.636, 1, 0.68932, 0.90461, 0.74803, 0.81744, 0.80167, 0.74227, 0.84793, 0.63751, 0.91241, 0.52069, 0.9843, 0.43623, 0.96357, 0.33055, 0.93763, 0.24985, 0.91782, 0.16124, 0.89607, 0.06173, 0.7871, 0.00326, 0.61342, 0.02902, 0.42065, 0.04091, 0.33171, 0.05486, 0.22732, 0.51035, 0.27888, 0.53168, 0.43107, 0.55568, 0.58325, 0.55568, 0.72561, 0.55568, 0.84834], "triangles": [28, 4, 5, 27, 2, 26, 26, 2, 25, 23, 25, 2, 24, 25, 23, 30, 21, 29, 30, 31, 21, 2, 0, 1, 3, 23, 2, 27, 0, 2, 3, 22, 23, 21, 22, 3, 4, 21, 3, 4, 29, 21, 29, 4, 28, 31, 20, 21, 31, 19, 20, 32, 19, 31, 18, 19, 32, 6, 28, 5, 29, 28, 6, 7, 30, 29, 14, 11, 12, 7, 31, 30, 9, 10, 8, 11, 15, 10, 13, 14, 12, 10, 15, 8, 11, 14, 15, 16, 8, 15, 7, 8, 16, 6, 7, 29, 16, 31, 7, 17, 31, 16, 32, 31, 17, 18, 32, 17], "vertices": [1, 16, -16.26, -10.16, 1, 1, 16, -19.58, -7.12, 1, 2, 13, -27.37, -26.91, 0.00601, 16, -14.54, 1.34, 0.99399, 2, 13, -21.82, -20.7, 0.07124, 16, -10.27, 8.5, 0.92876, 2, 13, -17.12, -15.45, 0.22843, 16, -6.67, 14.55, 0.77157, 2, 13, -11.38, -9.03, 0.56852, 16, -2.26, 21.95, 0.43148, 2, 13, -7.96, -2.14, 0.88558, 16, -0.23, 29.37, 0.11442, 2, 13, -6.75, 1.13, 0.9665, 16, 0.34, 32.81, 0.0335, 1, 13, -4.49, 7.19, 1, 1, 13, -0.8, 17.13, 1, 1, 13, 8.57, 17.06, 1, 1, 13, 15.62, 17.01, 1, 1, 13, 23.36, 16.95, 1, 1, 13, 25.45, 15.89, 1, 1, 13, 24.26, 7.83, 1, 1, 13, 23.17, 0.46, 1, 2, 13, 22.24, -5.89, 0.93951, 16, 30.13, 31.46, 0.06049, 2, 13, 20.93, -14.74, 0.68453, 16, 30.54, 22.53, 0.31547, 2, 13, 19.47, -24.61, 0.34701, 16, 31, 12.56, 0.65299, 2, 13, 15.56, -30.3, 0.14647, 16, 28.25, 6.23, 0.85353, 1, 16, 24.82, -1.69, 1, 1, 16, 22.19, -7.74, 1, 1, 16, 19.31, -14.39, 1, 1, 16, 12.5, -20.83, 1, 1, 16, 3.85, -23.3, 1, 1, 16, -3.74, -18.98, 1, 1, 16, -7.24, -16.98, 1, 1, 16, -11.35, -14.64, 1, 2, 13, -8.59, -11.32, 0.54415, 16, 0.91, 20.23, 0.45585, 2, 13, -1.84, -12.81, 0.5942, 16, 7.83, 20.07, 0.4058, 2, 13, 5.01, -14.1, 0.60998, 16, 14.8, 20.11, 0.39002, 2, 13, 10.6, -16.94, 0.53882, 16, 20.82, 18.4, 0.46118, 2, 13, 15.42, -19.38, 0.48148, 16, 26.02, 16.92, 0.51852], "hull": 28, "edges": [10, 12, 24, 26, 44, 46, 46, 48, 64, 36, 34, 36, 32, 34, 30, 32, 26, 28, 28, 30, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 8, 10, 6, 8, 2, 4, 4, 6, 2, 0, 0, 54, 52, 54, 48, 50, 50, 52, 36, 38, 38, 40, 40, 42, 42, 44], "width": 81, "height": 44}}, "body2": {"body2": {"type": "mesh", "uvs": [0.38768, 0.0116, 0.65779, 0.01135, 0.75376, 0.02843, 0.85646, 0.04671, 0.9858, 0.10018, 0.98591, 0.29412, 0.92793, 0.46782, 0.92792, 0.54919, 0.92791, 0.65295, 0.92789, 0.76831, 0.87017, 0.88265, 0.77655, 0.95817, 0.60793, 0.98861, 0.34702, 0.98836, 0.2354, 0.93343, 0.05603, 0.78684, 0.0571, 0.66002, 0.0581, 0.54151, 0.07204, 0.50707, 0.04155, 0.41352, 0.02287, 0.35618, 0, 0.28601, 0, 0.25288, 0.03701, 0.17679, 0.08204, 0.08419, 0.1934, 0.03886, 0.51515, 0.92274, 0.5535, 0.82589, 0.60889, 0.71867, 0.63872, 0.62528, 0.68559, 0.50422, 0.73672, 0.37625, 0.76228, 0.28286, 0.79211, 0.19293], "triangles": [33, 2, 3, 33, 3, 4, 33, 1, 2, 21, 22, 23, 33, 4, 5, 32, 33, 5, 20, 21, 23, 1, 32, 0, 33, 32, 1, 28, 19, 23, 20, 23, 19, 5, 31, 32, 6, 31, 5, 32, 31, 0, 30, 31, 6, 28, 18, 19, 7, 30, 6, 0, 30, 25, 31, 30, 0, 7, 29, 30, 8, 29, 7, 30, 29, 25, 8, 28, 29, 9, 28, 8, 25, 23, 24, 25, 29, 23, 28, 23, 29, 28, 17, 18, 28, 16, 17, 27, 16, 28, 14, 15, 16, 10, 28, 9, 27, 28, 10, 16, 27, 14, 26, 14, 27, 11, 27, 10, 12, 26, 27, 13, 14, 26, 11, 12, 27, 13, 26, 12], "vertices": [1, 4, 50.82, 30.49, 1, 1, 4, 56.01, 12.59, 1, 1, 4, 56.46, 5.83, 1, 1, 4, 56.93, -1.41, 1, 1, 4, 55.04, -11.25, 1, 1, 4, 39.21, -15.83, 1, 2, 2, 26.45, 41.5, 0.00098, 4, 23.91, -16.08, 0.99902, 3, 2, 26.45, 34.58, 0.00904, 3, 35.68, -17.82, 0.00548, 4, 17.27, -18, 0.98547, 3, 2, 26.45, 25.76, 0.04567, 3, 27.23, -20.36, 0.08385, 4, 8.79, -20.45, 0.87047, 3, 2, 26.45, 15.95, 0.13896, 3, 17.84, -23.17, 0.27178, 4, -0.63, -23.17, 0.58926, 3, 2, 22.47, 6.24, 0.30929, 3, 7.38, -22.15, 0.41584, 4, -11.07, -22.04, 0.27486, 3, 2, 16.01, -0.18, 0.51813, 3, -0.62, -17.81, 0.38304, 4, -19.03, -17.62, 0.09884, 3, 2, 4.37, -2.77, 0.95137, 3, -6.44, -7.41, 0.04527, 4, -24.75, -7.16, 0.00335, 3, 2, -13.63, -2.75, 0.2767, 3, -11.6, 9.84, 0.69053, 4, -29.72, 10.14, 0.03278, 3, 2, -21.33, 1.92, 0.09631, 3, -9.34, 18.56, 0.79869, 4, -27.37, 18.84, 0.105, 3, 2, -33.71, 14.38, 0.00252, 3, -0.96, 33.99, 0.68201, 4, -18.84, 34.19, 0.31548, 2, 3, 9.39, 37.02, 0.53155, 4, -8.46, 37.11, 0.46845, 2, 3, 19.06, 39.85, 0.34427, 4, 1.23, 39.84, 0.65573, 2, 3, 22.14, 39.77, 0.28231, 4, 4.31, 39.73, 0.71769, 2, 3, 29.15, 44.07, 0.14872, 4, 11.37, 43.96, 0.85128, 2, 3, 33.45, 46.7, 0.09983, 4, 15.69, 46.55, 0.90017, 2, 3, 38.71, 49.93, 0.06305, 4, 20.99, 49.72, 0.93695, 2, 3, 41.4, 50.74, 0.05322, 4, 23.69, 50.5, 0.94678, 2, 3, 48.33, 50.15, 0.03021, 4, 30.61, 49.84, 0.96979, 2, 3, 56.76, 49.44, 0.01272, 4, 39.04, 49.04, 0.98728, 2, 3, 62.66, 43.18, 0.00474, 4, 44.87, 42.73, 0.99526, 3, 2, -2.03, 2.83, 0.42869, 3, -2.92, 0.33, 0.57075, 4, -21.14, 0.55, 0.00056, 2, 3, 5.73, 0.16, 0.99968, 4, -12.5, 0.29, 0.00032, 3, 2, 4.44, 20.17, 0.00196, 3, 15.55, -0.88, 0.99347, 4, -2.68, -0.85, 0.00457, 2, 2, 6.5, 28.11, 0.00037, 4, 5.51, -0.63, 0.99963, 2, 2, 9.73, 38.4, 5e-05, 4, 16.3, -0.88, 0.99995, 1, 4, 27.73, -1.25, 1, 1, 4, 35.84, -0.74, 1, 1, 4, 43.76, -0.6, 1], "hull": 26, "edges": [0, 50, 0, 2, 6, 8, 8, 10, 10, 12, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 36, 42, 44, 48, 50, 2, 4, 4, 6, 36, 38, 12, 14, 14, 16, 16, 18, 30, 32, 32, 34, 38, 40, 40, 42, 44, 46, 46, 48], "width": 69, "height": 85}}, "ear": {"ear": {"type": "mesh", "uvs": [0.0655, 0, 0.09745, 0.01962, 0.22571, 0.10862, 0.39838, 0.22843, 0.56214, 0.31482, 0.72745, 0.40202, 0.87281, 0.4787, 0.93194, 0.53506, 0.98662, 0.62977, 0.98586, 0.75872, 0.81733, 0.83456, 0.81703, 0.98853, 0.74721, 0.98818, 0.61175, 0.93017, 0.49713, 0.85541, 0.40369, 0.78002, 0.30717, 0.70215, 0.20684, 0.62121, 0.10028, 0.43796, 0.01369, 0.20951, 0.0139, 0.01707, 0.06067, 0, 0.70423, 0.73206, 0.5435, 0.63813, 0.44527, 0.54812, 0.35598, 0.44637, 0.25329, 0.33679, 0.17739, 0.24677, 0.11042, 0.16459], "triangles": [28, 20, 1, 28, 19, 20, 20, 21, 1, 21, 0, 1, 28, 1, 2, 27, 19, 28, 27, 18, 19, 27, 2, 3, 27, 28, 2, 25, 18, 26, 25, 17, 18, 25, 3, 4, 25, 26, 3, 18, 27, 26, 26, 27, 3, 14, 15, 23, 15, 16, 23, 6, 23, 5, 16, 24, 23, 16, 17, 24, 23, 24, 5, 17, 25, 24, 24, 4, 5, 24, 25, 4, 11, 12, 10, 12, 13, 10, 13, 22, 10, 13, 14, 22, 14, 23, 22, 10, 22, 9, 9, 22, 8, 22, 7, 8, 22, 6, 7, 6, 22, 23], "vertices": [1, 23, 13.55, -2.06, 1, 2, 22, 21.76, -5.95, 0.0023, 23, 11.24, -3.59, 0.9977, 3, 21, 28.59, -10.44, 0.03252, 22, 10.68, -9.44, 0.61006, 23, 1.19, -9.41, 0.35742, 4, 19, 53.06, -21.49, 0, 20, 33.31, -14.72, 0.08206, 21, 13.29, -13.67, 0.75656, 22, -4.23, -14.15, 0.16137, 4, 19, 39.5, -22.02, 0.04861, 20, 20.18, -18.13, 0.55223, 21, 0.48, -18.15, 0.39874, 22, -16.54, -19.85, 0.00042, 3, 19, 25.81, -22.55, 0.39007, 20, 6.92, -21.57, 0.56244, 21, -12.45, -22.67, 0.04749, 3, 19, 13.78, -23.02, 0.78562, 20, -4.74, -24.6, 0.21373, 21, -23.82, -26.65, 0.00065, 2, 19, 7.76, -21.51, 0.89399, 20, -10.94, -24.4, 0.10601, 2, 19, 0.31, -17.22, 0.97557, 20, -19.14, -21.81, 0.02443, 2, 19, -5.38, -8.46, 0.99996, 20, -26.56, -14.46, 4e-05, 1, 19, 1.25, 3.24, 1, 1, 19, -5.57, 13.68, 1, 1, 19, -1.41, 16.37, 1, 2, 19, 9.21, 17.72, 0.9905, 20, -17.89, 14.23, 0.0095, 2, 19, 19.33, 17.12, 0.8173, 20, -7.87, 15.81, 0.1827, 3, 19, 28.23, 15.66, 0.38384, 20, 1.13, 16.27, 0.61144, 21, -21.33, 14.57, 0.00472, 3, 19, 37.42, 14.15, 0.05745, 20, 10.43, 16.76, 0.84251, 21, -12.11, 15.81, 0.10004, 3, 19, 46.97, 12.57, 0.00017, 20, 20.1, 17.26, 0.58059, 21, -2.51, 17.11, 0.41924, 3, 20, 36, 12.27, 0.02137, 21, 13.74, 13.45, 0.84816, 22, -6.41, 12.89, 0.13046, 3, 21, 31.91, 6.37, 0.00069, 22, 12.36, 7.6, 0.64325, 23, -0.89, 7.59, 0.35606, 2, 22, 25.26, -1.15, 0, 23, 13.61, 1.85, 1, 2, 22, 24.54, -4.68, 0, 23, 13.67, -1.74, 1, 1, 19, 12.52, 0.7, 1, 2, 19, 26.24, 0.6, 0.0177, 20, 2.4, 1.14, 0.9823, 2, 20, 12.48, 1.02, 0.99984, 21, -8.77, 0.3, 0.00016, 1, 21, 1.63, -0.08, 1, 3, 20, 34.25, -1.22, 0.00018, 21, 13.11, -0.14, 0.99976, 22, -5.72, -0.7, 6e-05, 2, 22, 3.34, -0.33, 0.9998, 23, -7.96, -2.11, 0.0002, 2, 22, 11.52, -0.12, 0.52334, 23, -0.02, -0.13, 0.47666], "hull": 22, "edges": [0, 42, 0, 2, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 34, 36, 36, 38, 38, 40, 40, 42, 6, 8, 8, 10, 10, 12, 2, 4, 4, 6, 32, 34, 28, 30, 30, 32], "width": 71, "height": 81}}, "ear2": {"ear2": {"type": "mesh", "uvs": [0.83347, 0, 0.95956, 0.07944, 0.97736, 0.17043, 0.9705, 0.2721, 0.96283, 0.38587, 0.92087, 0.4701, 0.83812, 0.63621, 0.75058, 0.7286, 0.66201, 0.82208, 0.567, 0.92236, 0.41276, 0.98778, 0.32401, 0.98801, 0.10742, 0.89598, 0.02656, 0.81069, 0.02961, 0.66117, 0.09247, 0.58116, 0.2098, 0.43179, 0.30636, 0.30887, 0.43202, 0.20611, 0.56714, 0.09562, 0.79418, 0, 0.29682, 0.87245, 0.37996, 0.74074, 0.45555, 0.62509, 0.50846, 0.51586, 0.57649, 0.41306, 0.64452, 0.30063, 0.69743, 0.20104, 0.76546, 0.14643], "triangles": [28, 19, 20, 20, 0, 1, 28, 20, 1, 28, 1, 2, 27, 19, 28, 2, 27, 28, 18, 19, 27, 3, 27, 2, 26, 18, 27, 26, 27, 3, 17, 18, 26, 4, 26, 3, 25, 17, 26, 25, 26, 4, 16, 17, 25, 5, 25, 4, 24, 16, 25, 24, 25, 5, 15, 16, 24, 6, 24, 5, 23, 24, 6, 23, 15, 24, 7, 23, 6, 23, 14, 15, 22, 23, 7, 22, 14, 23, 13, 14, 22, 8, 22, 7, 21, 13, 22, 21, 22, 8, 12, 13, 21, 9, 21, 8, 10, 21, 9, 11, 21, 10, 12, 21, 11], "vertices": [1, 27, 15.09, 1.41, 1, 2, 26, 25.64, -6.54, 0.00981, 27, 11.14, -5.16, 0.99019, 2, 26, 18.89, -9.33, 0.30059, 27, 4.8, -8.79, 0.69941, 3, 25, 28.9, -12.39, 0.0424, 26, 11.07, -11.58, 0.82703, 27, -2.67, -12.02, 0.13057, 3, 25, 19.98, -14.23, 0.38322, 26, 2.32, -14.1, 0.61603, 27, -11.03, -15.63, 0.00074, 3, 24, 39.23, -13.72, 0.016, 25, 13.09, -14.4, 0.72528, 26, -4.53, -14.79, 0.25872, 3, 24, 25.68, -14.74, 0.48997, 25, -0.49, -14.73, 0.50608, 26, -18.05, -16.15, 0.00395, 2, 24, 17.75, -13.95, 0.87719, 25, -8.36, -13.54, 0.12281, 2, 24, 9.73, -13.15, 0.99549, 25, -16.34, -12.34, 0.00451, 1, 24, 1.12, -12.29, 1, 1, 24, -5.37, -8.71, 1, 1, 24, -6.23, -5.82, 1, 1, 24, -1.22, 3.31, 1, 1, 24, 4.57, 7.85, 1, 2, 24, 16.08, 11.1, 0.97398, 25, -8.78, 11.55, 0.02602, 2, 24, 22.82, 10.83, 0.72819, 25, -2.05, 10.95, 0.27181, 3, 24, 35.41, 10.34, 0.02725, 25, 10.49, 9.83, 0.95351, 26, -8.97, 9.18, 0.01924, 2, 25, 20.82, 8.91, 0.3584, 26, 1.4, 9.04, 0.6416, 3, 25, 29.8, 6.65, 0.00029, 26, 10.53, 7.47, 0.94454, 27, -5.63, 6.81, 0.05516, 2, 26, 20.35, 5.78, 0.04871, 27, 4.33, 6.38, 0.95129, 1, 27, 14.52, 2.62, 1, 1, 24, 2.39, -2.35, 1, 1, 24, 13.3, -2.12, 1, 2, 24, 22.9, -2, 0.99972, 25, -2.62, -1.87, 0.00028, 2, 25, 6.29, -1.6, 0.99925, 26, -12.29, -2.54, 0.00075, 2, 25, 14.83, -1.96, 0.95925, 26, -3.75, -2.25, 0.04075, 2, 25, 24.12, -2.13, 0.00563, 26, 5.52, -1.72, 0.99437, 2, 26, 13.66, -1.01, 0.94204, 27, -1.44, -1.2, 0.05796, 2, 26, 18.53, -1.88, 0.04932, 27, 3.49, -1.45, 0.95068], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 38, 40, 32, 34, 28, 30, 30, 32, 8, 10, 10, 12, 12, 14, 4, 6, 6, 8, 34, 36, 36, 38, 14, 16, 16, 18], "width": 34, "height": 80}}, "eye_close": {"eye_close": {"x": 17.5, "y": -31.63, "rotation": -66.4, "width": 11, "height": 22}}, "eye_close2": {"eye_close2": {"x": 6.35, "y": -4.86, "rotation": -66.4, "width": 29, "height": 27}}, "hair": {"hair": {"type": "mesh", "uvs": [0.00654, 0.00874, 0.176, 0.0085, 0.39291, 0.04813, 0.56599, 0.12014, 0.70636, 0.17853, 0.76275, 0.22641, 0.86245, 0.31106, 0.89793, 0.38676, 0.96091, 0.5211, 1, 0.6045, 1, 0.66732, 0.96642, 0.76092, 0.92721, 0.8702, 0.85881, 0.99124, 0.80161, 0.99141, 0.68148, 0.92342, 0.59368, 0.88444, 0.36353, 0.81267, 0.26366, 0.69477, 0.18647, 0.60363, 0.11173, 0.51538, 0.07125, 0.38052, 0.03417, 0.25697, 0.08276, 0.18288, 0.08273, 0.15076, 0.76017, 0.84449, 0.71642, 0.77853, 0.71095, 0.67619, 0.64534, 0.60341, 0.6344, 0.51471, 0.58519, 0.42601, 0.54144, 0.33959, 0.44302, 0.23497, 0.36646, 0.17129, 0.28444, 0.10306], "triangles": [24, 1, 34, 24, 0, 1, 34, 1, 2, 32, 22, 23, 23, 33, 32, 32, 21, 22, 32, 3, 4, 32, 33, 3, 23, 24, 33, 24, 34, 33, 33, 34, 3, 34, 2, 3, 30, 21, 31, 31, 21, 32, 21, 30, 20, 30, 31, 7, 31, 6, 7, 31, 5, 6, 31, 32, 5, 32, 4, 5, 18, 28, 27, 18, 19, 28, 10, 27, 9, 27, 28, 9, 28, 8, 9, 19, 29, 28, 19, 20, 29, 28, 29, 8, 29, 7, 8, 20, 30, 29, 29, 30, 7, 14, 15, 13, 13, 15, 12, 12, 15, 25, 15, 16, 25, 16, 26, 25, 16, 17, 26, 12, 25, 11, 25, 26, 11, 26, 17, 27, 17, 18, 27, 26, 27, 11, 11, 27, 10], "vertices": [1, 32, 16.24, 2.08, 1, 1, 32, 10.72, -3.66, 1, 2, 31, 20.16, -7.53, 0.47182, 32, 0.41, -7.86, 0.52818, 2, 30, 33.83, -7.96, 0.05206, 31, 9.04, -10.49, 0.94794, 2, 30, 25.67, -12.49, 0.50501, 31, 0.02, -12.88, 0.49499, 2, 30, 19.74, -13.55, 0.83227, 31, -5.99, -12.46, 0.16773, 3, 29, 37.73, -13.76, 0.04292, 30, 9.25, -15.43, 0.95624, 31, -16.61, -11.72, 0.00085, 2, 29, 29.04, -14.4, 0.35124, 30, 0.57, -14.68, 0.64876, 3, 28, 35.27, -16.85, 0.0003, 29, 13.61, -15.55, 0.97782, 30, -14.84, -13.35, 0.02188, 2, 28, 25.67, -16.78, 0.0933, 29, 4.04, -16.26, 0.9067, 2, 28, 18.71, -15.37, 0.36891, 29, -3.01, -15.42, 0.63109, 2, 28, 8.66, -11.72, 0.87829, 29, -13.33, -12.6, 0.12171, 1, 28, -3.08, -7.45, 1, 1, 28, -15.84, -1.58, 1, 1, 28, -15.33, 1.05, 1, 1, 28, -6.68, 5.06, 1, 1, 28, -1.54, 8.23, 1, 2, 28, 8.56, 17.22, 0.97044, 29, -15.78, 16.23, 0.02956, 3, 28, 22.55, 19.17, 0.57266, 29, -2, 19.31, 0.42429, 30, -24.7, 23.55, 0.00304, 4, 28, 33.37, 20.68, 0.17391, 29, 8.66, 21.7, 0.75929, 30, -13.81, 24.21, 0.06355, 31, -29.27, 32.35, 0.00326, 4, 28, 43.84, 22.14, 0.02878, 29, 18.97, 24.01, 0.68758, 30, -3.25, 24.84, 0.24944, 31, -18.88, 30.39, 0.0342, 3, 29, 34.33, 24.1, 0.22651, 30, 11.92, 22.49, 0.51972, 31, -4.75, 24.39, 0.25377, 4, 29, 48.4, 24.18, 0.0261, 30, 25.83, 20.33, 0.26261, 31, 8.2, 18.89, 0.7094, 32, -4.81, 20.67, 0.0019, 4, 29, 56.45, 20.92, 0.00215, 30, 33.25, 15.83, 0.06315, 31, 14.3, 12.72, 0.82355, 32, -0.38, 13.2, 0.11115, 4, 29, 60.05, 20.49, 0.00011, 30, 36.74, 14.83, 0.01562, 31, 17.44, 10.9, 0.66419, 32, 2.23, 10.68, 0.32007, 2, 28, 1.33, -0.34, 0.99999, 29, -21.56, -1.86, 1e-05, 1, 28, 9.04, 0.19, 1, 2, 28, 20.43, -1.85, 0.759, 29, -2.4, -1.81, 0.241, 2, 28, 29.1, -0.47, 0.00112, 29, 6.13, 0.28, 0.99888, 1, 29, 16.15, -0.39, 1, 3, 29, 26.37, 0.72, 0.35477, 30, 0.34, 0.67, 0.64498, 31, -21.31, 6.07, 0.00025, 3, 29, 36.31, 1.61, 4e-05, 30, 10.3, -0.03, 0.99995, 31, -11.83, 2.95, 1e-05, 3, 29, 48.6, 4.8, 0.00018, 30, 22.94, 1.17, 0.18256, 31, 0.71, 1.02, 0.81726, 2, 30, 30.85, 2.65, 0.00068, 31, 8.74, 0.53, 0.99932, 2, 31, 17.35, -0.01, 0.72708, 32, -0.5, 0.12, 0.27292], "hull": 25, "edges": [0, 48, 0, 2, 2, 4, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 44, 46, 46, 48, 4, 6, 6, 8, 8, 10, 10, 12, 40, 42, 42, 44, 16, 18, 12, 14, 14, 16, 38, 40, 34, 36, 36, 38, 20, 22, 22, 24], "width": 47, "height": 113}}, "hair2": {"hair2": {"type": "mesh", "uvs": [0.70271, 0.09009, 0.91952, 0.22508, 0.97365, 0.36132, 0.97311, 0.43495, 0.97205, 0.57799, 0.8653, 0.69687, 0.76279, 0.81102, 0.54137, 0.94651, 0.4304, 1, 0.41125, 1, 0.31324, 0.95795, 0.15699, 0.87161, 0.10642, 0.80432, 0.02709, 0.69875, 0.02732, 0.61776, 0.0276, 0.51748, 0.0777, 0.41673, 0.12816, 0.31525, 0.21484, 0.22342, 0.28668, 0.1473, 0.41722, 0.00901, 0.52795, 0.01019, 0.43999, 0.87476, 0.45427, 0.75819, 0.44713, 0.64426, 0.48997, 0.54888, 0.52566, 0.45615, 0.5328, 0.30513, 0.5328, 0.22034, 0.5328, 0.11701], "triangles": [29, 21, 0, 29, 19, 20, 29, 20, 21, 28, 29, 0, 19, 29, 28, 18, 19, 28, 28, 0, 1, 27, 28, 1, 18, 28, 27, 17, 18, 27, 27, 1, 2, 26, 17, 27, 2, 26, 27, 3, 26, 2, 16, 17, 26, 15, 16, 26, 25, 15, 26, 4, 26, 3, 25, 26, 4, 14, 15, 25, 24, 14, 25, 4, 24, 25, 5, 24, 4, 13, 14, 24, 23, 24, 5, 13, 24, 23, 12, 13, 23, 6, 23, 5, 22, 11, 12, 23, 22, 12, 22, 23, 6, 7, 22, 6, 10, 11, 22, 10, 22, 7, 8, 9, 10, 7, 8, 10], "vertices": [2, 39, 24.61, -5.33, 0.04862, 40, 6.31, -6.81, 0.95138, 3, 38, 34.61, -14.17, 0.04035, 39, 11.81, -13.61, 0.9258, 40, -7.95, -12.2, 0.03385, 3, 37, 55.69, -13.39, 0.03421, 38, 21.37, -15.95, 0.51361, 39, -1.33, -16.03, 0.45218, 3, 37, 48.57, -13.98, 0.14629, 38, 14.23, -15.83, 0.70231, 39, -8.47, -16.27, 0.1514, 3, 37, 34.75, -15.14, 0.75251, 38, 0.35, -15.61, 0.2469, 39, -22.33, -16.74, 0.0006, 1, 37, 22.93, -12.31, 1, 1, 37, 11.58, -9.58, 1, 1, 37, -2.2, -2.78, 1, 1, 37, -7.72, 0.76, 1, 1, 37, -7.78, 1.44, 1, 1, 37, -4.02, 5.31, 1, 2, 37, 3.84, 11.64, 0.99485, 38, -27.74, 14.1, 0.00515, 2, 37, 10.19, 14.01, 0.95593, 38, -21.19, 15.84, 0.04407, 2, 37, 20.14, 17.74, 0.72741, 38, -10.91, 18.56, 0.27259, 2, 37, 27.97, 18.41, 0.41254, 38, -3.06, 18.44, 0.58746, 3, 37, 37.66, 19.24, 0.07418, 38, 6.67, 18.31, 0.9154, 39, -17.7, 17.45, 0.01042, 3, 37, 47.56, 18.29, 0.00025, 38, 16.42, 16.37, 0.84689, 39, -7.87, 16, 0.15285, 3, 38, 26.24, 14.43, 0.38474, 39, 2.03, 14.55, 0.6152, 40, -11.57, 17.38, 6e-05, 3, 38, 35.1, 11.19, 0.03527, 39, 11.05, 11.75, 0.87039, 40, -3.35, 12.74, 0.09434, 2, 39, 18.52, 9.44, 0.38499, 40, 3.47, 8.9, 0.61501, 1, 40, 15.85, 1.93, 1, 1, 40, 15.04, -1.97, 1, 1, 37, 4.42, 1.46, 1, 2, 37, 15.73, 1.92, 0.99433, 38, -16.88, 3.25, 0.00567, 2, 37, 26.71, 3.13, 0.87282, 38, -5.83, 3.37, 0.12718, 1, 38, 3.41, 1.7, 1, 1, 38, 12.38, 0.3, 1, 1, 39, 3.54, 0.03, 1, 1, 39, 11.76, 0.32, 1, 1, 40, 4.81, -0.33, 1], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 40, 42, 38, 40, 34, 36, 36, 38, 30, 32, 32, 34, 4, 6, 6, 8, 8, 10, 10, 12, 26, 28, 28, 30, 22, 24, 24, 26], "width": 36, "height": 97}}, "hair3": {"hair3": {"type": "mesh", "uvs": [0.21734, 0.01165, 0.39934, 0.05725, 0.61891, 0.11226, 0.79744, 0.20852, 0.89045, 0.27151, 0.93844, 0.35683, 0.96089, 0.45197, 0.98617, 0.5591, 0.98693, 0.69005, 0.98781, 0.84361, 0.98485, 0.92633, 0.87924, 0.92627, 0.69949, 0.92616, 0.69873, 1, 0.68531, 1, 0.60544, 0.96511, 0.47776, 0.88784, 0.3768, 0.79703, 0.30722, 0.73444, 0.2198, 0.59161, 0.15255, 0.48172, 0.11346, 0.41785, 0.04549, 0.23614, 0.00833, 0.05359, 0.05384, 0.01214, 0.749, 0.82015, 0.70323, 0.66785, 0.68563, 0.56315, 0.63282, 0.42672, 0.58353, 0.30298, 0.52368, 0.1951, 0.18571, 0.13164, 0.27724, 0.22366, 0.38286, 0.34105, 0.45679, 0.43941, 0.50256, 0.54094, 0.54481, 0.62343, 0.59057, 0.73131, 0.62578, 0.85505, 0.79124, 0.3125, 0.81237, 0.47114, 0.82645, 0.59805, 0.84757, 0.70593, 0.87574, 0.79477], "triangles": [5, 39, 4, 28, 29, 39, 39, 3, 4, 39, 29, 3, 29, 2, 3, 29, 30, 2, 26, 41, 42, 41, 7, 8, 26, 27, 41, 27, 40, 41, 7, 40, 6, 7, 41, 40, 27, 28, 40, 28, 39, 40, 40, 5, 6, 5, 40, 39, 10, 11, 9, 12, 25, 11, 25, 43, 11, 11, 43, 9, 43, 8, 9, 25, 42, 43, 25, 26, 42, 43, 42, 8, 42, 41, 8, 32, 22, 31, 22, 23, 31, 23, 24, 31, 31, 0, 1, 31, 24, 0, 21, 32, 33, 32, 21, 22, 33, 32, 30, 32, 1, 30, 32, 31, 1, 30, 1, 2, 18, 19, 35, 19, 34, 35, 19, 20, 34, 35, 28, 27, 35, 34, 28, 20, 33, 34, 20, 21, 33, 34, 29, 28, 34, 33, 29, 33, 30, 29, 13, 14, 12, 14, 15, 12, 15, 38, 12, 15, 16, 38, 12, 38, 25, 38, 16, 37, 16, 17, 37, 38, 37, 25, 37, 26, 25, 37, 17, 36, 17, 18, 36, 18, 35, 36, 37, 36, 26, 36, 27, 26, 36, 35, 27], "vertices": [3, 35, 25.52, -8.36, 0.1047, 36, 7.42, -9.69, 0.8894, 30, 37.93, 33.24, 0.00591, 4, 34, 43.13, -11.31, 0.01465, 35, 13.89, -15.76, 0.69693, 36, -5.26, -15.11, 0.15252, 30, 30.73, 21.48, 0.1359, 4, 34, 32.11, -23.77, 0.09609, 35, -0.15, -24.7, 0.27018, 36, -20.56, -21.64, 5e-05, 30, 22.04, 7.29, 0.63368, 3, 34, 19.4, -32.08, 1e-05, 35, -14.66, -29.17, 3e-05, 30, 10.97, -3.1, 0.99995, 4, 34, 11.84, -35.96, 0, 35, -22.99, -30.8, 0, 29, 31.59, -7.45, 0.13493, 30, 4.2, -8.23, 0.86506, 2, 29, 24.31, -10.12, 0.75782, 30, -3.41, -9.7, 0.24218, 2, 29, 16.47, -10.83, 0.99578, 30, -11.27, -9.16, 0.00422, 2, 28, 29.62, -12.47, 0.01096, 29, 7.63, -11.64, 0.98904, 2, 28, 19.22, -10.41, 0.51664, 29, -2.91, -10.44, 0.48336, 3, 33, -7.1, -24.1, 8e-05, 28, 7.01, -8, 0.99471, 29, -15.27, -9.03, 0.00521, 2, 33, -13.21, -21.35, 0.00045, 28, 0.49, -6.46, 0.99955, 3, 33, -10.28, -14.22, 0.01358, 35, -61.87, 5.29, 0, 28, 2.03, 1.1, 0.98642, 3, 33, -5.28, -2.09, 0.86855, 35, -53.08, 15.03, 0, 28, 4.65, 13.95, 0.13145, 1, 33, -10.79, 0.23, 1, 2, 33, -10.42, 1.14, 0.99991, 28, -1.01, 16.16, 9e-05, 1, 33, -5.59, 5.46, 1, 2, 33, 3.74, 11.7, 0.99023, 34, -19.87, 12.98, 0.00977, 2, 33, 13.35, 15.72, 0.82676, 34, -10.03, 16.38, 0.17324, 3, 33, 19.97, 18.49, 0.57468, 34, -3.25, 18.73, 0.42427, 35, -22.38, 25.93, 0.00105, 3, 33, 33.09, 20, 0.11523, 34, 9.94, 19.41, 0.81614, 35, -9.52, 22.93, 0.06863, 4, 33, 43.19, 21.16, 0.00763, 34, 20.1, 19.93, 0.6821, 35, 0.38, 20.63, 0.29991, 36, -12.68, 23, 0.01035, 4, 33, 49.06, 21.83, 0.0001, 34, 26, 20.23, 0.47152, 35, 6.14, 19.29, 0.47436, 36, -7.21, 20.74, 0.05403, 3, 34, 41.4, 18.26, 0.03896, 35, 20.4, 13.13, 0.3147, 36, 5.85, 12.34, 0.64634, 1, 36, 17.21, 2.49, 1, 1, 36, 16.71, -2.2, 1, 3, 33, 1.29, -8.7, 0.46328, 35, -49.12, 6.6, 0, 28, 12.34, 8.7, 0.53672, 5, 33, 13.97, -10.3, 0.47076, 34, -11.05, -9.62, 0.01063, 35, -37.71, 0.84, 0, 28, 25.1, 9.53, 0.20428, 29, 1.33, 9.91, 0.31434, 6, 33, 22.3, -12.33, 0.25319, 34, -2.86, -12.18, 0.17234, 35, -30.55, -3.88, 0, 28, 33.66, 9.1, 0.01198, 29, 9.9, 10.19, 0.55807, 30, -14.41, 12.64, 0.00442, 5, 33, 33.99, -12.97, 0.01322, 34, 8.76, -13.54, 0.46867, 35, -19.76, -8.41, 9e-05, 29, 21.33, 12.71, 0.33958, 30, -2.73, 13.31, 0.17844, 4, 34, 19.35, -14.69, 0.42975, 35, -9.9, -12.44, 0.07355, 29, 31.71, 15.1, 0.0498, 30, 7.9, 14.02, 0.44691, 5, 34, 29.12, -14.59, 0.20866, 35, -0.48, -15.04, 0.38704, 36, -19.33, -12.06, 0.0007, 29, 40.9, 18.4, 0.00033, 30, 17.5, 15.82, 0.40328, 3, 35, 19.84, -0.14, 0.00324, 36, 3.15, -0.66, 0.99638, 30, 29.22, 38.13, 0.00037, 2, 35, 9.83, -0.12, 0.99974, 30, 20.22, 33.75, 0.00026, 3, 34, 22.99, -0.17, 0.99888, 29, 30.38, 30.01, 3e-05, 30, 8.96, 28.95, 0.00109, 3, 34, 13.46, -1.54, 0.98069, 29, 21.83, 25.59, 0.00798, 30, -0.18, 25.95, 0.01132, 3, 34, 4.61, -0.95, 0.98804, 29, 13.27, 23.24, 0.01036, 30, -9.01, 25, 0.0016, 6, 33, 21.69, -0.97, 0.97851, 34, -2.75, -0.8, 0.00744, 35, -27.3, 7.02, 0, 28, 30.92, 20.14, 0.00084, 29, 6.27, 20.97, 0.0132, 30, -16.28, 23.87, 1e-05, 4, 33, 12.34, -0.74, 0.9882, 35, -36.03, 10.39, 0, 28, 21.7, 18.61, 0.00738, 29, -2.81, 18.69, 0.00442, 1, 33, 2.09, 0.7, 1, 1, 30, 2.99, -0.35, 1, 5, 33, 25.68, -23.72, 0.00024, 34, -0.2, -23.76, 0.0007, 35, -31.2, -15.75, 0, 29, 16.21, 0.12, 0.999, 30, -9.79, 1.7, 5e-05, 5, 33, 15.78, -20.77, 0.00266, 34, -9.9, -20.18, 0.00036, 35, -39.53, -9.63, 0, 28, 28.85, -0.41, 0.00042, 29, 5.88, 0.31, 0.99656, 2, 28, 19.98, -0.19, 0.99595, 29, -2.98, -0.19, 0.00405, 2, 28, 12.52, -0.77, 0.99792, 29, -10.37, -1.38, 0.00208], "hull": 25, "edges": [0, 48, 4, 6, 6, 8, 8, 10, 18, 20, 24, 26, 26, 28, 28, 30, 30, 32, 42, 44, 44, 46, 46, 48, 0, 2, 2, 4, 40, 42, 36, 38, 38, 40, 20, 22, 22, 24, 14, 16, 16, 18, 10, 12, 12, 14, 32, 34, 34, 36], "width": 73, "height": 81}}, "hair4": {"hair4": {"type": "mesh", "uvs": [0.05982, 0, 0.25841, 0.08055, 0.36939, 0.12556, 0.57635, 0.2095, 0.73744, 0.29255, 0.8775, 0.43212, 0.94493, 0.60316, 0.98249, 0.69844, 0.98216, 0.84338, 0.90686, 0.98682, 0.80101, 0.98749, 0.64978, 0.93277, 0.50245, 0.85087, 0.33296, 0.72821, 0.20046, 0.63231, 0.09201, 0.51536, 0.01804, 0.35837, 0.01788, 0.21089, 0.0177, 0.04681, 0.85155, 0.87367, 0.75343, 0.76208, 0.70203, 0.68769, 0.56185, 0.54904, 0.45905, 0.4645, 0.38895, 0.38334, 0.22074, 0.23794, 0.13663, 0.12973], "triangles": [26, 0, 1, 18, 0, 26, 17, 18, 26, 25, 26, 1, 17, 26, 25, 25, 1, 2, 16, 17, 25, 24, 2, 3, 25, 2, 24, 4, 24, 3, 24, 15, 16, 24, 16, 25, 23, 15, 24, 4, 23, 24, 22, 23, 4, 5, 22, 4, 22, 5, 6, 23, 14, 15, 14, 23, 22, 13, 14, 22, 13, 22, 21, 21, 22, 6, 21, 6, 7, 20, 21, 7, 8, 20, 7, 12, 13, 21, 12, 21, 20, 19, 20, 8, 11, 12, 20, 11, 20, 19, 9, 19, 8, 10, 11, 19, 9, 10, 19], "vertices": [1, 44, 19.19, -0.56, 1, 2, 43, 27.51, -8.55, 0.1174, 44, 8.65, -7.32, 0.8826, 3, 42, 41.95, -11.26, 0.00042, 43, 21.11, -11.38, 0.58073, 44, 2.76, -11.09, 0.41884, 3, 42, 30.04, -16.61, 0.15613, 43, 9.18, -16.66, 0.83469, 44, -8.23, -18.13, 0.00919, 3, 41, 42.71, -20.49, 0.00296, 42, 19.7, -19.97, 0.56022, 43, -1.18, -19.96, 0.43682, 3, 41, 29.6, -20.02, 0.13172, 42, 6.59, -19.85, 0.79226, 43, -14.29, -19.76, 0.07602, 3, 41, 17.08, -14.92, 0.70359, 42, -6.06, -15.09, 0.29611, 43, -26.92, -14.93, 0.0003, 2, 41, 10.11, -12.08, 0.9482, 42, -13.11, -12.44, 0.0518, 1, 41, 1.45, -5.27, 1, 1, 41, -4.58, 4.71, 1, 1, 41, -1.03, 9.32, 1, 2, 41, 7.37, 13.31, 0.98625, 42, -16.53, 12.87, 0.01375, 2, 41, 17.27, 15.85, 0.77162, 42, -6.7, 15.67, 0.22838, 3, 41, 30.36, 17.44, 0.17923, 42, 6.34, 17.61, 0.78537, 43, -14.32, 17.7, 0.0354, 3, 41, 40.59, 18.68, 0.01402, 42, 16.53, 19.13, 0.70586, 43, -4.12, 19.16, 0.28012, 3, 42, 27.22, 18.64, 0.29475, 43, 6.57, 18.6, 0.70246, 44, -16.2, 16.31, 0.00279, 3, 42, 39.23, 14.8, 0.02356, 43, 18.55, 14.69, 0.78082, 44, -3.76, 14.29, 0.19562, 2, 43, 27.53, 7.97, 0.13564, 44, 6.13, 9.02, 0.86436, 1, 44, 17.14, 3.16, 1, 1, 41, 4.07, 1.8, 1, 1, 41, 14.07, 0.82, 1, 1, 41, 20.27, -0.44, 1, 2, 42, 9.78, -0.61, 0.99996, 43, -10.98, -0.54, 4e-05, 1, 42, 18.31, 0.11, 1, 1, 43, 4.8, -0.49, 1, 1, 43, 19.19, 0.28, 1, 1, 44, 8.5, 0.35, 1], "hull": 19, "edges": [0, 36, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 0, 2, 2, 4, 4, 6, 24, 26, 26, 28, 10, 12, 12, 14], "width": 55, "height": 76}}, "hand_L": {"hand_L": {"x": 4.84, "y": 3.09, "rotation": 96.88, "width": 44, "height": 30}}, "hand_L2": {"hand_L2": {"type": "mesh", "uvs": [0.745, 0.01745, 0.85118, 0.22729, 0.92673, 0.37657, 0.96225, 0.57134, 1, 0.77827, 1, 0.81236, 0.84506, 0.92095, 0.63194, 0.96494, 0.46206, 1, 0.39692, 1, 0.27716, 0.88562, 0.22508, 0.76184, 0.14947, 0.58215, 0.07865, 0.41384, 0, 0.22692, 0, 0.18362, 0.37087, 0.18443, 0.37713, 0.17128, 0.56663, 0.09975, 0.53467, 0.2195, 0.55427, 0.37781, 0.58857, 0.51802, 0.61307, 0.62205, 0.60817, 0.77584, 0.63267, 0.87534], "triangles": [18, 0, 1, 20, 19, 1, 20, 1, 2, 21, 20, 2, 21, 2, 3, 20, 12, 13, 22, 21, 3, 11, 12, 21, 21, 22, 11, 22, 3, 4, 4, 23, 22, 23, 6, 24, 23, 4, 6, 11, 22, 10, 20, 21, 12, 23, 10, 22, 9, 10, 23, 5, 6, 4, 23, 24, 9, 7, 24, 6, 24, 8, 9, 7, 8, 24, 19, 17, 18, 14, 15, 16, 19, 18, 1, 13, 14, 16, 16, 17, 19, 13, 16, 20, 19, 20, 16], "vertices": [2, 9, 38.48, 17.79, 0.29032, 10, -16.27, 9.48, 0.70969, 2, 9, 53.49, 16.58, 0.02204, 10, -2.75, 16.09, 0.97796, 1, 10, 6.87, 20.8, 1, 1, 10, 19.49, 23.15, 1, 1, 10, 32.9, 25.66, 1, 1, 10, 35.12, 25.7, 1, 1, 10, 42.34, 16.53, 1, 1, 10, 45.43, 3.79, 1, 2, 9, 85.59, -28.56, 0.00029, 10, 47.89, -6.36, 0.99971, 2, 9, 83.65, -31.96, 0.00169, 10, 47.96, -10.26, 0.99831, 2, 9, 73.64, -34.52, 0.02671, 10, 40.65, -17.58, 0.97329, 2, 9, 65.1, -33.26, 0.10241, 10, 32.66, -20.85, 0.89759, 2, 9, 52.7, -31.42, 0.38638, 10, 21.07, -25.59, 0.61362, 2, 9, 41.09, -29.7, 0.76478, 10, 10.2, -30.04, 0.23522, 2, 9, 28.2, -27.79, 0.98539, 10, -1.86, -34.97, 0.01461, 2, 9, 25.75, -26.4, 0.99056, 10, -4.67, -35.02, 0.00944, 2, 9, 36.81, -7.09, 0.98275, 10, -5.02, -12.77, 0.01725, 2, 9, 36.25, -6.34, 0.98984, 10, -5.88, -12.41, 0.01016, 2, 9, 37.84, 5.84, 0.72426, 10, -10.73, -1.13, 0.27574, 2, 9, 43.65, 0.33, 0.98308, 10, -2.91, -2.91, 0.01692, 2, 9, 53.18, -3.74, 0.05355, 10, 7.35, -1.55, 0.94645, 1, 10, 16.43, 0.67, 1, 1, 10, 23.16, 2.26, 1, 1, 10, 33.16, 2.15, 1, 1, 10, 39.6, 3.73, 1], "hull": 19, "edges": [0, 36, 8, 10, 10, 12, 16, 18, 18, 20, 28, 30, 30, 32, 32, 34, 34, 36, 26, 28, 24, 26, 20, 22, 22, 24, 4, 6, 6, 8, 0, 2, 2, 4, 12, 14, 14, 16], "width": 60, "height": 65}}, "hand_L3": {"hand_L3": {"type": "mesh", "uvs": [0.4023, 0.01224, 0.55821, 0.06575, 0.67365, 0.14485, 0.77219, 0.27947, 0.86325, 0.4039, 0.98166, 0.64426, 0.98088, 0.80959, 0.86547, 0.92819, 0.68882, 0.98818, 0.38446, 0.98702, 0.2071, 0.86662, 0.11829, 0.77012, 0.01905, 0.56567, 0.01898, 0.4386, 0.01888, 0.26309, 0.05681, 0.10819, 0.24209, 0.01311, 0.25623, 0.15233, 0.31842, 0.26839, 0.4315, 0.39218, 0.52762, 0.51983, 0.62938, 0.66683, 0.69158, 0.77128, 0.73115, 0.87573], "triangles": [22, 21, 5, 6, 22, 5, 23, 22, 6, 9, 10, 22, 7, 23, 6, 23, 9, 22, 8, 9, 23, 8, 23, 7, 17, 16, 0, 15, 16, 17, 14, 15, 17, 1, 18, 17, 1, 17, 0, 18, 1, 2, 14, 17, 18, 19, 18, 2, 3, 19, 2, 13, 14, 18, 13, 18, 19, 20, 19, 3, 20, 3, 4, 12, 13, 19, 12, 19, 20, 21, 20, 4, 5, 21, 4, 11, 12, 20, 11, 20, 21, 10, 11, 21, 10, 21, 22], "vertices": [1, 9, -7.1, 12.93, 1, 1, 9, 0.45, 17.96, 1, 2, 9, 8.64, 20.2, 0.99982, 10, -43.17, -3.67, 0.00018, 2, 9, 20.07, 19.59, 0.97997, 10, -33.03, 1.63, 0.02003, 2, 9, 30.63, 19.03, 0.86832, 10, -23.66, 6.53, 0.13168, 2, 9, 49.55, 15.34, 0.14956, 10, -5.5, 13.02, 0.85044, 1, 10, 7.06, 13.2, 1, 1, 10, 16.18, 7.36, 1, 2, 9, 64.73, -10.82, 0.0133, 10, 20.9, -1.74, 0.9867, 2, 9, 56.82, -24.54, 0.40224, 10, 21.1, -17.57, 0.59776, 2, 9, 44.31, -28.02, 0.73818, 10, 12.11, -26.95, 0.26182, 2, 9, 35.65, -28.41, 0.89617, 10, 4.86, -31.7, 0.10383, 2, 9, 19.59, -25.2, 0.99815, 10, -10.58, -37.14, 0.00185, 1, 9, 11.2, -20.43, 1, 1, 9, -0.39, -13.83, 1, 1, 9, -9.65, -6.3, 1, 1, 9, -11.16, 5.65, 1, 1, 9, -1.6, 1.06, 1, 1, 9, 7.66, -0.5, 1, 1, 9, 18.75, -0.04, 1, 1, 9, 29.65, -0.5, 1, 1, 9, 41.98, -1.43, 1, 2, 9, 50.48, -2.54, 0.15133, 10, 4.42, -1.89, 0.84867, 1, 10, 12.32, 0.31, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 24, 26, 26, 28, 4, 6, 6, 8], "width": 52, "height": 76}}, "hand_R": {"hand_R": {"type": "mesh", "uvs": [0.09621, 0, 0.20865, 0.08683, 0.44711, 0.14549, 0.5889, 0.14618, 0.87971, 0.07201, 0.93925, 0.13497, 0.96207, 0.24608, 0.97498, 0.39199, 0.98687, 0.52637, 0.98342, 0.68973, 0.98135, 0.78751, 0.83166, 0.893, 0.61932, 0.96829, 0.52719, 0.98005, 0.37099, 1, 0.3584, 1, 0.17783, 0.8729, 0.05179, 0.78418, 0.04125, 0.69728, 0.01793, 0.505, 0.0183, 0.34853, 0.01878, 0.14051, 0.05226, 0.05595, 0.0821, 0, 0.36517, 0.231, 0.34853, 0.37212, 0.37626, 0.51716, 0.43728, 0.6426, 0.46502, 0.77196, 0.48166, 0.88956], "triangles": [13, 14, 29, 14, 15, 29, 15, 16, 29, 13, 29, 12, 12, 29, 11, 9, 10, 11, 16, 28, 29, 11, 29, 28, 16, 17, 28, 9, 11, 28, 28, 17, 27, 17, 18, 27, 28, 27, 9, 18, 26, 27, 9, 27, 8, 27, 26, 8, 18, 19, 26, 7, 8, 26, 7, 26, 6, 19, 25, 26, 6, 26, 25, 3, 25, 24, 19, 20, 25, 25, 20, 24, 24, 20, 21, 21, 1, 24, 21, 22, 1, 24, 1, 2, 1, 22, 0, 22, 23, 0, 3, 6, 25, 3, 5, 6, 3, 4, 5, 24, 2, 3], "vertices": [2, 6, 47.06, -21.22, 0.30126, 7, -18.94, -11.73, 0.69874, 2, 6, 49.7, -12.8, 0.37595, 7, -11.64, -6.76, 0.62405, 1, 6, 47.17, 0.34, 1, 2, 6, 43.45, 6.87, 0.93179, 7, -4.36, 12.55, 0.06821, 3, 6, 30.91, 17.42, 0.93099, 7, -7.66, 28.6, 0.06898, 8, -31.33, 33.85, 3e-05, 3, 6, 33.41, 22.51, 0.90162, 7, -2.54, 31.05, 0.09421, 8, -25.89, 35.48, 0.00417, 3, 6, 40.01, 27.74, 0.75778, 7, 5.88, 31.06, 0.2083, 8, -17.56, 34.18, 0.03392, 3, 6, 49.14, 33.82, 0.45677, 7, 16.81, 30.17, 0.37984, 8, -6.9, 31.62, 0.16339, 3, 6, 57.54, 39.42, 0.22418, 7, 26.88, 29.35, 0.36979, 8, 2.91, 29.26, 0.40603, 3, 6, 68.23, 45.4, 0.06819, 7, 38.98, 27.42, 0.17898, 8, 14.57, 25.48, 0.75283, 3, 6, 74.63, 48.99, 0.03238, 7, 46.22, 26.27, 0.09556, 8, 21.55, 23.22, 0.87207, 3, 6, 85.46, 46.09, 0.00727, 7, 52.92, 17.28, 0.02253, 8, 26.78, 13.31, 0.9702, 1, 8, 28.86, 0.89, 1, 1, 8, 28.27, -4.04, 1, 1, 8, 27.27, -12.39, 1, 2, 7, 57.28, -8.69, 2e-05, 8, 27.07, -13.03, 0.99998, 2, 7, 46.48, -16.8, 0.07132, 8, 15.14, -19.37, 0.92868, 2, 7, 38.94, -22.46, 0.23772, 8, 6.82, -23.8, 0.76228, 2, 7, 32.41, -22.08, 0.41122, 8, 0.43, -22.42, 0.58878, 2, 7, 17.96, -21.24, 0.89226, 8, -13.72, -19.36, 0.10774, 3, 6, 71.74, -11.68, 0.00221, 7, 6.34, -19.55, 0.99493, 8, -24.93, -15.89, 0.00286, 2, 6, 58.23, -19.49, 0.198, 7, -9.09, -17.29, 0.802, 2, 6, 51.86, -21.13, 0.28588, 7, -15.12, -14.63, 0.71412, 2, 6, 47.43, -21.87, 0.30121, 7, -19.05, -12.47, 0.69879, 1, 7, 0.25, -0.09, 1, 1, 7, 10.59, -2.48, 1, 1, 7, 21.57, -2.58, 1, 2, 7, 31.34, -0.72, 0.00441, 8, 2.68, -1.15, 0.99559, 1, 8, 12.39, -2.6, 1, 1, 8, 21.07, -4.35, 1], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 28, 30, 42, 44, 44, 46, 38, 40, 40, 42, 12, 14, 14, 16, 34, 36, 36, 38, 16, 18, 18, 20, 30, 32, 32, 34, 24, 26, 26, 28], "width": 53, "height": 75}}, "hand_R2": {"hand_R2": {"type": "mesh", "uvs": [0.72783, 0.01432, 0.80245, 0.04209, 0.98312, 0.23193, 0.9847, 0.40616, 0.91819, 0.51877, 0.78632, 0.65037, 0.74064, 0.89298, 0.70722, 0.94451, 0.53996, 0.99485, 0.3882, 0.97022, 0.26338, 0.94996, 0.17445, 0.93553, 0.08395, 0.8556, 0.03023, 0.76497, 0.00129, 0.65015, 0.05282, 0.55781, 0.11103, 0.46441, 0.16261, 0.38165, 0.23044, 0.27281, 0.30569, 0.15206, 0.45957, 0.04498, 0.58328, 0.01433, 0.76613, 0.15071, 0.68773, 0.27854, 0.57503, 0.39358, 0.45743, 0.55975, 0.34963, 0.68758, 0.30063, 0.80262], "triangles": [12, 13, 27, 11, 12, 27, 27, 8, 9, 10, 11, 27, 9, 10, 27, 22, 0, 1, 22, 1, 2, 22, 23, 21, 22, 21, 0, 20, 23, 19, 23, 20, 21, 24, 19, 23, 18, 19, 24, 23, 2, 3, 2, 23, 22, 4, 23, 3, 24, 23, 4, 25, 18, 24, 17, 18, 25, 16, 17, 25, 5, 24, 4, 25, 24, 5, 26, 16, 25, 15, 16, 26, 14, 15, 26, 13, 14, 26, 27, 13, 26, 5, 26, 25, 6, 26, 5, 8, 27, 26, 6, 8, 26, 7, 8, 6], "vertices": [1, 6, -6.17, -5.18, 1, 1, 6, -6.76, -0.35, 1, 1, 6, -0.86, 15.6, 1, 1, 6, 9.49, 21.71, 1, 1, 6, 18.21, 22.16, 1, 2, 6, 30.04, 19.86, 0.98557, 7, -6.83, 31.06, 0.01443, 2, 6, 45.89, 25.89, 0.79124, 7, 9.35, 25.96, 0.20876, 2, 6, 49.98, 25.94, 0.74389, 7, 12.58, 23.46, 0.25611, 2, 6, 58.01, 18.99, 0.52559, 7, 14.59, 13.04, 0.47441, 2, 6, 61.11, 10.26, 0.16094, 7, 11.6, 4.27, 0.83906, 2, 6, 63.66, 3.08, 0.00023, 7, 9.15, -2.95, 0.99977, 1, 7, 7.4, -8.09, 1, 2, 6, 63.42, -9.5, 0.07733, 7, 1.17, -12.67, 0.92267, 2, 6, 59.63, -15.42, 0.30756, 7, -5.48, -14.97, 0.69244, 2, 6, 53.64, -20.9, 0.55533, 7, -13.57, -15.56, 0.44467, 2, 6, 46.58, -21.42, 0.74286, 7, -19.43, -11.59, 0.25714, 2, 6, 39.25, -21.63, 0.90003, 7, -25.31, -7.21, 0.09997, 2, 6, 32.76, -21.81, 0.96985, 7, -30.52, -3.33, 0.03015, 2, 6, 24.22, -22.06, 0.9977, 7, -37.38, 1.77, 0.0023, 1, 6, 14.75, -22.33, 1, 1, 6, 3.73, -18.05, 1, 1, 6, -1.82, -12.68, 1, 1, 6, 0.82, 1.53, 1, 1, 6, 10.81, 1.88, 1, 1, 6, 21.07, 0.01, 1, 2, 6, 34.52, -0.34, 0.99997, 7, -15.83, 12.42, 3e-05, 2, 6, 45.4, -1.52, 0.99163, 7, -8.03, 4.76, 0.00837, 2, 6, 53.74, -0.08, 0.51358, 7, -0.59, 0.72, 0.48642], "hull": 22, "edges": [0, 42, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 38, 40, 40, 42, 30, 32, 32, 34, 34, 36, 36, 38, 20, 22, 16, 18, 18, 20], "width": 60, "height": 69}}, "head": {"head": {"x": 9.16, "y": -2.54, "rotation": -66.4, "width": 75, "height": 80}}, "leg_L": {"leg_L": {"x": 14.14, "y": -0.51, "rotation": 113.5, "width": 41, "height": 33}}, "leg_L2": {"leg_L2": {"x": 12.72, "y": 2.32, "rotation": 11.67, "width": 56, "height": 29}}, "leg_L3": {"leg_L3": {"type": "mesh", "uvs": [0.90144, 0.00182, 0.93856, 0.168, 0.97532, 0.33255, 0.9755, 0.4589, 0.97576, 0.64158, 0.93604, 0.77046, 0.66063, 0.97385, 0.56071, 0.9736, 0.40147, 0.97321, 0.19375, 0.89716, 0.08043, 0.6868, 0, 0.53749, 0, 0.50277, 0.33028, 0.27309, 0.48919, 0.18564, 0.70589, 0.0664, 0.5878, 0.40491, 0.6094, 0.67491, 0.5878, 0.81891], "triangles": [10, 11, 12, 13, 10, 12, 18, 17, 5, 10, 17, 9, 13, 17, 10, 18, 9, 17, 8, 9, 18, 7, 8, 18, 6, 18, 5, 7, 18, 6, 15, 0, 1, 16, 14, 15, 16, 15, 1, 16, 1, 2, 16, 2, 3, 17, 16, 3, 17, 3, 4, 5, 17, 4, 13, 16, 17, 13, 14, 16], "vertices": [1, 13, 14.97, 17.9, 1, 1, 13, 20.97, 16.52, 1, 1, 13, 26.92, 15.15, 1, 1, 13, 30.98, 13.1, 1, 2, 13, 36.85, 10.14, 0.98444, 14, -2.27, 13.65, 0.01556, 2, 13, 40.27, 6.62, 0.89067, 14, 2.62, 14.04, 0.10933, 2, 13, 41.82, -6.51, 0.11649, 14, 13.73, 6.86, 0.88351, 2, 13, 40.01, -10.07, 0.02224, 14, 15.31, 3.19, 0.97776, 2, 13, 37.11, -15.75, 0.00012, 14, 17.84, -2.65, 0.99988, 2, 13, 30.91, -21.92, 0.03235, 14, 18.64, -11.37, 0.96765, 2, 13, 22.11, -22.54, 0.15318, 14, 13.5, -18.54, 0.84682, 2, 13, 15.86, -22.98, 0.22413, 14, 9.86, -23.64, 0.77587, 2, 13, 14.74, -22.41, 0.22811, 14, 8.71, -24.13, 0.77189, 2, 13, 13.34, -6.89, 0.74995, 14, -4.14, -15.32, 0.25005, 1, 13, 13.41, 0.2, 1, 1, 13, 13.5, 9.88, 1, 1, 13, 22.24, 0.15, 1, 2, 13, 31.3, -3.47, 0.04542, 14, 4.67, 0.69, 0.95458, 2, 13, 35.53, -6.59, 0.03934, 14, 9.77, 1.97, 0.96066], "hull": 16, "edges": [0, 30, 8, 10, 10, 12, 16, 18, 22, 24, 24, 26, 26, 28, 28, 30, 12, 14, 14, 16, 36, 14, 18, 20, 20, 22, 4, 6, 6, 8, 0, 2, 2, 4], "width": 40, "height": 36}}, "leg_R": {"leg_R": {"x": 8.56, "y": -3.76, "rotation": 18.15, "width": 56, "height": 28}}, "leg_R2": {"leg_R2": {"type": "mesh", "uvs": [0.67077, 0.20429, 0.72872, 0.20261, 1, 0.1761, 1, 0.19396, 0.94747, 0.35535, 0.9078, 0.47719, 0.90774, 0.63221, 0.90767, 0.78863, 0.82897, 0.90174, 0.51911, 0.9777, 0.25958, 0.97801, 0.018, 0.88868, 0.01812, 0.72059, 0.01821, 0.5913, 0.08872, 0.33165, 0.14606, 0.12047, 0.20784, 0, 0.22937, 0, 0.32735, 0.07249, 0.56741, 0.26893, 0.47941, 0.49475, 0.45141, 0.60275, 0.43141, 0.78929, 0.45541, 0.88747], "triangles": [21, 8, 22, 23, 22, 8, 7, 8, 6, 6, 8, 21, 9, 23, 8, 10, 22, 23, 10, 23, 9, 3, 1, 2, 19, 18, 0, 4, 1, 3, 5, 1, 4, 20, 18, 19, 15, 16, 17, 18, 15, 17, 18, 14, 15, 5, 0, 1, 18, 20, 14, 21, 14, 20, 5, 6, 0, 21, 13, 14, 21, 12, 13, 22, 12, 21, 6, 19, 0, 6, 20, 19, 20, 6, 21, 10, 11, 12, 22, 10, 12], "vertices": [1, 17, 3.48, -2.33, 1, 2, 17, 1.31, -0.08, 0.9999, 18, 9.95, 25.2, 0.0001, 2, 17, -9.45, 9.93, 0.98876, 18, 23.51, 30.87, 0.01124, 2, 17, -8.87, 10.46, 0.98844, 18, 23.75, 30.13, 0.01156, 2, 17, -1.73, 13.17, 0.90301, 18, 23.27, 22.5, 0.09699, 2, 17, 3.67, 15.22, 0.67716, 18, 22.91, 16.73, 0.32284, 2, 17, 8.7, 19.83, 0.29044, 18, 25.03, 10.25, 0.70956, 2, 17, 13.77, 24.48, 0.05708, 18, 27.17, 3.71, 0.94292, 1, 18, 24.68, -2.34, 1, 2, 17, 34.09, 14.65, 0.0109, 18, 9.82, -10.73, 0.9891, 2, 17, 43.58, 4.33, 0.58407, 18, -3.49, -15.11, 0.41593, 2, 17, 49.5, -7.93, 0.95513, 18, -17.11, -15.44, 0.04487, 2, 17, 44.05, -12.93, 0.98982, 18, -19.41, -8.41, 0.01018, 1, 17, 39.86, -16.77, 1, 1, 17, 28.87, -21.69, 1, 1, 17, 19.93, -25.7, 1, 1, 17, 13.77, -26.82, 1, 1, 17, 12.98, -25.97, 1, 1, 17, 11.75, -19.91, 1, 1, 17, 9.35, -4.52, 1, 1, 17, 19.89, -1.3, 1, 2, 17, 24.41, 0.8, 0.92129, 18, 1.21, 3.81, 0.07871, 2, 17, 31.19, 5.55, 0.05845, 18, 2.74, -4.33, 0.94155, 2, 17, 33.49, 9.43, 0.053, 18, 5.32, -8.03, 0.947], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 14, 16, 16, 18, 18, 20, 20, 22, 30, 32, 32, 34, 34, 36, 26, 28, 28, 30, 22, 24, 24, 26, 10, 12, 12, 14, 6, 8, 8, 10], "width": 54, "height": 44}}, "leg_R3": {"leg_R3": {"type": "mesh", "uvs": [0.40712, 0.02176, 0.55566, 0.08192, 0.6638, 0.12572, 0.89311, 0.28659, 0.98024, 0.38923, 0.98003, 0.49959, 0.9093, 0.67323, 0.84794, 0.82387, 0.67215, 0.97783, 0.35897, 0.97738, 0.16468, 0.8616, 0.06282, 0.70412, 0.01959, 0.63729, 0.01718, 0.511, 0.05309, 0.35895, 0.09242, 0.19247, 0.13266, 0.02209, 0.56997, 0.30569, 0.61895, 0.46933, 0.61283, 0.6466, 0.50262, 0.78296, 0.40915, 0.90202], "triangles": [11, 12, 13, 19, 20, 18, 20, 10, 17, 10, 11, 17, 21, 10, 20, 9, 10, 21, 8, 19, 7, 20, 19, 8, 21, 20, 8, 9, 21, 8, 17, 1, 2, 17, 2, 3, 18, 17, 3, 5, 3, 4, 18, 3, 5, 18, 20, 17, 6, 19, 18, 5, 6, 18, 14, 17, 13, 17, 11, 13, 15, 16, 0, 17, 0, 1, 7, 19, 6, 15, 0, 17, 15, 17, 14], "vertices": [2, 16, 0.41, -6.02, 0.95174, 17, -10.84, -26.71, 0.04826, 1, 16, 4.94, 0.26, 1, 1, 16, 8.25, 4.83, 1, 1, 16, 18.12, 13.71, 1, 1, 16, 23.64, 16.58, 1, 1, 16, 28.3, 15.24, 1, 2, 16, 34.71, 9.82, 0.99409, 17, -6.36, 10.81, 0.00592, 2, 16, 40.26, 5.12, 0.79841, 17, 0.56, 13.07, 0.20159, 2, 16, 44.42, -5.02, 0.2181, 17, 11.37, 11.31, 0.7819, 2, 16, 40.21, -19.77, 9e-05, 17, 21.73, -0.01, 0.99991, 2, 16, 32.71, -27.54, 0.07256, 17, 24.42, -10.47, 0.92744, 2, 16, 24.68, -30.45, 0.20765, 17, 22.69, -18.83, 0.79235, 2, 16, 21.28, -31.68, 0.26357, 17, 21.96, -22.38, 0.73643, 2, 16, 15.9, -30.28, 0.36129, 17, 17.94, -26.22, 0.63871, 2, 16, 9.94, -26.76, 0.5388, 17, 11.83, -29.45, 0.4612, 2, 16, 3.42, -22.9, 0.73822, 17, 5.13, -32.99, 0.26178, 2, 16, -3.25, -18.96, 0.83727, 17, -1.73, -36.6, 0.16273, 2, 16, 14.61, -1.75, 0.98299, 17, -7.03, -12.38, 0.01701, 2, 16, 22.19, -1.41, 0.93042, 17, -3.35, -5.74, 0.06958, 1, 17, 2.6, -0.69, 1, 1, 17, 10.67, -0.61, 1, 2, 16, 37.7, -16.5, 0.00023, 17, 17.63, -0.44, 0.99977], "hull": 17, "edges": [0, 32, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 24, 26, 0, 2, 2, 4, 26, 28, 28, 30, 30, 32, 10, 12, 12, 14, 20, 22, 22, 24], "width": 49, "height": 44}}, "weapons": {"weapons": {"x": 15.19, "y": -1.94, "rotation": 18.67, "width": 83, "height": 59}}, "yinying": {"yinying": {"x": 103.21, "y": -0.76, "rotation": -0.53, "width": 201, "height": 94}}}}], "animations": {"attack": {"bones": {"hand_L2": {"rotate": [{"angle": -2.9, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 79.72, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 42.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 45.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 78.76, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 38.61, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 35.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -7.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.9}]}, "hair10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "ear9": {"rotate": [{"angle": -18.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -8.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -37.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.92, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -37.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -18.54}]}, "hair8": {"rotate": [{"angle": 9.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 18.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -3.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 18.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.18, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.35}]}, "ear5": {"rotate": [{"angle": 21.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 30.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 30.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 21.37}]}, "ear4": {"rotate": [{"angle": 16.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 25.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 25.78, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 16.15}]}, "ear3": {"rotate": [{"angle": 9.89, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 19.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.89}]}, "hand_R2": {"rotate": [{"angle": 0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -14.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -28, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -33.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.81}], "translate": [{"x": 0.33, "y": 0.1, "curve": "stepped"}, {"time": 0.3333, "x": 0.33, "y": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.57, "y": -7.14, "curve": "stepped"}, {"time": 0.6667, "x": -0.57, "y": -7.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.33, "y": 0.1}]}, "hair": {"rotate": [{"angle": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 2.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.45}]}, "body4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 9.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -1.87, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -2.21, "curve": 0.25, "c3": 0.75}, {"time": 0.9}], "translate": [{"x": 0.44, "y": 0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 4.39, "y": -0.31, "curve": "stepped"}, {"time": 0.3667, "x": 4.39, "y": -0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 1.91, "y": -4.52, "curve": "stepped"}, {"time": 0.7333, "x": 1.91, "y": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.17, "y": 2.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.44, "y": 0.13}]}, "head": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": 0.6, "y": 0.17}]}, "hand_R3": {"rotate": [{"angle": 3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 20.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 10.7, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.24}]}, "hand_R": {"rotate": [{"angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 46.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 50.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.82}]}, "hand_L3": {"rotate": [{"angle": -0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 52.78, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -177.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -174.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 48.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.52, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.17, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.67}], "translate": [{"x": 0.55, "y": 0.16}]}, "hand_L": {"rotate": [{"angle": -2.95, "curve": "stepped"}, {"time": 0.1667, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -37.24, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -40.26, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -9.71, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.95}]}, "weapons": {"rotate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.34, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 34.72, "curve": "stepped"}, {"time": 0.7667, "angle": 34.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}, "leg_L3": {"rotate": [{"angle": 1.69}]}, "leg_L4": {"rotate": [{"angle": -3.42}]}, "leg_L2": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 16.27, "curve": "stepped"}, {"time": 0.6667, "x": 16.27, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7667, "x": 5.98, "y": 3.16, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8333}]}, "leg_R3": {"rotate": [{"angle": 1.61}]}, "leg_R2": {"rotate": [{"angle": 0.02}]}, "body3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 9.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667}], "translate": [{"y": 0.21, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -0.34, "y": 4.1, "curve": "stepped"}, {"time": 0.3333, "x": -0.34, "y": 4.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 3.79, "y": 0.72, "curve": "stepped"}, {"time": 0.7, "x": 3.79, "y": 0.72, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 0.51, "y": 2.07, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 0.21}]}, "ear": {"rotate": [{"angle": 0.22, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.78, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.78, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.22}]}, "ear2": {"rotate": [{"angle": 3.21, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 9.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.4, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.21}]}, "hair15": {"rotate": [{"angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.99, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.58}]}, "ear8": {"rotate": [{"angle": -11.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -23.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -23.89, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.36}]}, "leg_R": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -9.46, "y": 5.74, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -14.96, "curve": "stepped"}, {"time": 0.3333, "x": -14.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "ear7": {"rotate": [{"angle": -11.55, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -20.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -20.12, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.55}]}, "hair16": {"rotate": [{"angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 16.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 16.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -5.75, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.78}]}, "ear6": {"rotate": [{"angle": -3.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -8.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.1}]}, "hair14": {"rotate": [{"angle": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 2.74, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.45}]}, "hair11": {"rotate": [{"angle": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -7.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -7.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.92}]}, "hair12": {"rotate": [{"angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 13.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.57, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 13.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -8.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.95}]}, "hair5": {"rotate": [{"angle": 19.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 28.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 19.09}]}, "hair2": {"rotate": [{"angle": 2.58, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -5.99, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.58}]}, "hair3": {"rotate": [{"angle": 6.78, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 16.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -5.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 16.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -5.75, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.78}]}, "body2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9.44, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"y": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -14.28, "y": -1.19, "curve": "stepped"}, {"time": 0.3333, "x": -14.28, "y": -1.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 24.88, "y": -13.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 26.8, "y": -14.86, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.96, "y": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -0.51}]}, "hair9": {"rotate": [{"angle": 19.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 28.72, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 19.09}]}, "hair4": {"rotate": [{"angle": 9.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 18.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 18.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -9.39, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.36}]}, "hair6": {"rotate": [{"angle": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.74, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.74, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.26}]}, "hair7": {"rotate": [{"angle": 4.45, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 10.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -4.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.63, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -4.13, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.45}]}, "hair13": {"rotate": [{"angle": 12.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 21.69, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 21.69, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.07}]}, "hair17": {"rotate": [{"angle": 16.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 26.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -2.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 26.28, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.11, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 16.65}]}, "yinying": {"translate": [{}, {"time": 0.1667, "x": -41.4, "curve": "stepped"}, {"time": 0.3333, "x": -41.4}, {"time": 0.4, "x": 20.7, "curve": "stepped"}, {"time": 0.6667, "x": 20.7}, {"time": 0.8333}], "scale": [{}, {"time": 0.1667, "x": 1.207, "curve": "stepped"}, {"time": 0.6667, "x": 1.207}, {"time": 0.8333}]}}}, "idle": {"slots": {"eye_close": {"attachment": [{"time": 0.7, "name": "eye_close"}, {"time": 0.8333, "name": null}]}, "eye_close2": {"attachment": [{"time": 0.7, "name": "eye_close2"}, {"time": 0.8333, "name": null}]}}, "bones": {"body2": {"translate": [{"y": -0.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 2.03, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -0.51}]}, "hair": {"rotate": [{"angle": 0.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.43, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.45}]}, "body3": {"translate": [{"y": 0.21, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": 3.55, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "y": 0.21}]}, "body4": {"translate": [{"x": 0.44, "y": 0.13, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 3.4, "y": 1.02, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": 0.44, "y": 0.13}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.02, "y": 1.02, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "head": {"translate": [{"x": 0.6, "y": 0.17, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 2.75, "y": 0.79, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "x": 0.6, "y": 0.17}]}, "hand_R2": {"rotate": [{"angle": 0.81, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.19, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.81}], "translate": [{"x": 0.33, "y": 0.1, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.54, "y": 0.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": 0.33, "y": 0.1}]}, "hand_R": {"rotate": [{"angle": 1.82, "curve": 0.363, "c2": 0.47, "c3": 0.703, "c4": 0.83}, {"time": 0.1667, "angle": 0.23, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 8.58, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 1.82}]}, "hand_R3": {"rotate": [{"angle": 3.24, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": 1.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 8.91, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.24}]}, "hand_L3": {"rotate": [{"angle": -0.67, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -3.08, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -0.67}], "translate": [{"x": 0.55, "y": 0.16, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 2.54, "y": 0.73, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "x": 0.55, "y": 0.16}]}, "hand_L2": {"rotate": [{"angle": -2.9, "curve": 0.36, "c2": 0.43, "c3": 0.706, "c4": 0.81}, {"time": 0.2333, "angle": -0.47, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -7.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -2.9}]}, "hand_L": {"rotate": [{"angle": -2.95, "curve": 0.348, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.2333, "angle": -0.82, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -6.3, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": -2.95}]}, "leg_L3": {"rotate": [{"angle": 1.69}]}, "leg_L4": {"rotate": [{"angle": -3.42}]}, "leg_R3": {"rotate": [{"angle": 1.61}]}, "leg_R2": {"rotate": [{"angle": 0.02}]}, "ear": {"rotate": [{"angle": 0.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.72, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.22}]}, "ear2": {"rotate": [{"angle": 3.21, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 8.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.21}]}, "ear3": {"rotate": [{"angle": 9.89, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 5.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 15.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 9.89}]}, "ear4": {"rotate": [{"angle": 16.15, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": 11.71, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 18.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 16.15}]}, "ear5": {"rotate": [{"angle": 21.37, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": 18.59, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 21.37}]}, "ear6": {"rotate": [{"angle": -3.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -8.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -3.1}]}, "ear7": {"rotate": [{"angle": -11.55, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3333, "angle": -2.39, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -18.4, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -11.55}]}, "ear8": {"rotate": [{"angle": -11.36, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3333, "angle": -4.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -13.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": -11.36}]}, "ear9": {"rotate": [{"angle": -18.54, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -11.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -18.54}]}, "hair2": {"rotate": [{"angle": 2.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 7.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 2.58}]}, "hair3": {"rotate": [{"angle": 6.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 6.78}]}, "hair4": {"rotate": [{"angle": 9.36, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": 6.79, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 10.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 9.36}]}, "hair5": {"rotate": [{"angle": 19.09, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": 16.61, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 19.09}]}, "hair6": {"rotate": [{"angle": 1.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 1.26}]}, "hair7": {"rotate": [{"angle": 4.45, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3333, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 4.45}]}, "hair8": {"rotate": [{"angle": 9.35, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3333, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 10.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 9.35}]}, "hair9": {"rotate": [{"angle": 19.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 19.09}]}, "hair10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hair11": {"rotate": [{"angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.92}]}, "hair12": {"rotate": [{"angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 10.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.95}]}, "hair13": {"rotate": [{"angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 19.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 12.07}]}, "hair14": {"rotate": [{"angle": 0.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.43, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.45}]}, "hair15": {"rotate": [{"angle": 2.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 7.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 2.58}]}, "hair16": {"rotate": [{"angle": 6.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 10.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 6.78}]}, "hair17": {"rotate": [{"angle": 16.65, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 19.09, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 16.65}]}}}, "run": {"bones": {"leg_L2": {"rotate": [{"angle": -121.83}, {"time": 0.1667, "angle": 27.82}, {"time": 0.2333, "angle": 5.3}, {"time": 0.3333}, {"time": 0.4, "angle": -16.09}, {"time": 0.5, "angle": -128.03}, {"time": 0.6667, "angle": -121.83}], "translate": [{"x": -26.7, "y": 27.79}, {"time": 0.1667, "x": 29.49, "y": 21.14}, {"time": 0.2333, "x": 4.58, "y": -1.04}, {"time": 0.3333, "x": -17.98}, {"time": 0.4, "x": -62.79, "y": 14.32}, {"time": 0.5, "x": -75.76, "y": 47.79}, {"time": 0.6667, "x": -26.7, "y": 27.79}]}, "body2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 10.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 10.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "body3": {"rotate": [{"angle": 5.57}], "translate": [{"y": 3.07, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 3.07, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 3.07}]}, "body4": {"translate": [{"x": 4.71, "y": 1.55}]}, "hand_R2": {"rotate": [{"angle": 23.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 32.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 23.14, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": 13.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 23.14}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 2.95, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 2.95, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "hand_R": {"rotate": [{"angle": 31.64, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 42.5, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 38.99, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": 30.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": 28.17, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 31.64}]}, "hand_R3": {"rotate": [{"angle": -10.17, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "angle": 3.55, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 2.97, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.5, "angle": -5.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6333, "angle": -10.78, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -10.17}]}, "hand_L3": {"rotate": [{"angle": -50, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -56.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -50, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "angle": -43.28, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -50}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 2.95, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 2.95, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "hand_L2": {"rotate": [{"angle": 121.81, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 111.63, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": 114.92, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": 123.32, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": 125.07, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 121.81}]}, "hand_L": {"rotate": [{"angle": -10.45, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.3, "angle": -23.31, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": -22.77, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.5, "angle": -14.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6333, "angle": -9.88, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -10.45}]}, "weapons": {"rotate": [{"angle": 34.78}], "translate": [{"x": 3.16, "y": 4.58}]}, "leg_L3": {"rotate": [{"angle": 1.69}], "translate": [{"time": 0.3333}, {"time": 0.5, "x": -6.25, "y": 2.08}, {"time": 0.6667}], "scale": [{"time": 0.3333}, {"time": 0.5, "x": 1.732}, {"time": 0.6667}]}, "leg_L4": {"rotate": [{"angle": -3.42}]}, "leg_R3": {"rotate": [{"angle": 1.61}], "translate": [{"time": 0.3333}, {"time": 0.5, "x": 14.57, "y": 0.69}, {"time": 0.6667}], "scale": [{"time": 0.3333}, {"time": 0.5, "x": 1.455}, {"time": 0.6667}]}, "leg_R2": {"rotate": [{"angle": 0.02}]}, "leg_R": {"rotate": [{"angle": 0.89}, {"time": 0.1, "angle": -8.67}, {"time": 0.1667, "angle": -122.89}, {"time": 0.3333, "angle": -14.43}, {"time": 0.5, "angle": 26.62}, {"time": 0.5667, "angle": 1.44}, {"time": 0.6667, "angle": 0.89}], "translate": [{"x": 25.07}, {"time": 0.1, "x": -9.38, "y": 6.03}, {"time": 0.1667, "x": -38.85, "y": 42.76}, {"time": 0.3333, "x": 9.85, "y": 28.34}, {"time": 0.5, "x": 82.17, "y": 31.39}, {"time": 0.5667, "x": 58.53, "y": 5.34}, {"time": 0.6667, "x": 25.07}]}, "ear": {"rotate": [{"angle": 0.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 1.72, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 0.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 1.72, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 0.22}]}, "ear2": {"rotate": [{"angle": 3.21, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0333, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 8.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 3.21, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.3667, "angle": 1.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 3.21}]}, "ear3": {"rotate": [{"angle": 9.89, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": 5.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 15.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 9.89, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.3667, "angle": 5.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 15.67, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 9.89}]}, "ear4": {"rotate": [{"angle": 16.15, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0333, "angle": 11.71, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 18.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 16.15, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.3667, "angle": 11.71, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 18.52, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 16.15}]}, "ear5": {"rotate": [{"angle": 21.37, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0333, "angle": 18.59, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 21.37, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": 18.59, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 21.37}]}, "ear6": {"rotate": [{"angle": -3.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -8.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -3.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -8.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.1}]}, "ear7": {"rotate": [{"angle": -11.55, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.0667, "angle": -2.39, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -18.4, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -11.55, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.4, "angle": -2.39, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -18.4, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -11.55}]}, "ear8": {"rotate": [{"angle": -11.36, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": -4.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -11.36, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.4, "angle": -4.8, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -13.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -11.36}]}, "ear9": {"rotate": [{"angle": -18.54, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "angle": -11.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.54, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4, "angle": -11.72, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -18.54}]}, "hair": {"rotate": [{"angle": 0.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.43, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 0.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.43, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 0.45}]}, "hair2": {"rotate": [{"angle": 2.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0333, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 2.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.3667, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 2.58}]}, "hair3": {"rotate": [{"angle": 6.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 6.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.3667, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 10.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 6.78}]}, "hair4": {"rotate": [{"angle": 9.36, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0333, "angle": 6.79, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 9.36, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.3667, "angle": 6.79, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 9.36}]}, "hair5": {"rotate": [{"angle": 19.09, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.0333, "angle": 16.61, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 19.09, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": 16.61, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 19.09}]}, "hair6": {"rotate": [{"angle": 1.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 1.26, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.43, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 1.26}]}, "hair7": {"rotate": [{"angle": 4.45, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.0667, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 7.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 4.45, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.4, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 7.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 4.45}]}, "hair8": {"rotate": [{"angle": 9.35, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 10.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 9.35, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.4, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 10.74, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 9.35}]}, "hair9": {"rotate": [{"angle": 19.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 19.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4, "angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 19.09}]}, "hair10": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "hair11": {"rotate": [{"angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 7.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 0.92}]}, "hair12": {"rotate": [{"angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 10.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 3.95}]}, "hair13": {"rotate": [{"angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 19.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 19.09, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 12.07}]}, "hair14": {"rotate": [{"angle": 0.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 3.43, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 0.45, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 3.43, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 0.45}]}, "hair15": {"rotate": [{"angle": 2.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0333, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 2.58, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.3667, "angle": 0.92, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 2.58}]}, "hair16": {"rotate": [{"angle": 6.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0333, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 6.78, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.3667, "angle": 3.95, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 10.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 6.78}]}, "hair17": {"rotate": [{"angle": 16.65, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0333, "angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 19.09, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": 16.65, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.3667, "angle": 12.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 19.09, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 16.65}]}, "zong": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": -0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": -0.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "yinying": {"translate": [{}, {"time": 0.1667, "x": -12.42}, {"time": 0.3333}, {"time": 0.5, "x": -12.42}, {"time": 0.6667}], "scale": [{}, {"time": 0.1667, "x": 1.165}, {"time": 0.3333}, {"time": 0.5, "x": 1.165}, {"time": 0.6667}]}}}}}