{"skeleton": {"hash": "nAkAGbdd6I17E668EMX6Wnpw9Xg", "spine": "3.8.99", "x": -54.98, "y": -23.24, "width": 109.47, "height": 105.88, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/黑石深渊小怪/血犬"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 10.57, "rotation": 0.06}, {"name": "yinying", "parent": "bone", "length": 5.84, "rotation": 90}, {"name": "body", "parent": "bone", "x": -14.4, "y": 42.78}, {"name": "body3", "parent": "body", "length": 21.9, "rotation": 1.4, "x": -0.18, "y": 0.53}, {"name": "body4", "parent": "body3", "length": 21.17, "rotation": -4.93, "x": 21.9}, {"name": "body5", "parent": "body", "length": 14.19, "rotation": -162.47, "x": -1.36, "y": 0.36}, {"name": "tails", "parent": "body5", "length": 14.58, "rotation": 44.03, "x": 3.32, "y": -10.76}, {"name": "tails3", "parent": "tails", "length": 11.23, "rotation": -34.18, "x": 14.58}, {"name": "tails_2", "parent": "tails3", "length": 12.18, "rotation": -12.12, "x": 10.9, "y": -0.23}, {"name": "tails_3", "parent": "tails_2", "length": 10.66, "rotation": 89.25, "x": 12.18}, {"name": "head", "parent": "body4", "length": 27.79, "rotation": 2.77, "x": 9.77, "y": 0.93}, {"name": "head_2", "parent": "body4", "length": 17.87, "rotation": -22.89, "x": 11.91, "y": -2.76}, {"name": "ear_R", "parent": "head", "length": 4.31, "rotation": 123.06, "x": -5.24, "y": 6.02}, {"name": "ear_R3", "parent": "ear_R", "length": 6.83, "rotation": 6.57, "x": 5.13, "y": -0.08}, {"name": "ear_R4", "parent": "ear_R3", "length": 7.36, "rotation": 10.95, "x": 6.83}, {"name": "ear_L", "parent": "head", "length": 4.78, "rotation": 102.93, "x": 14.87, "y": 7.83}, {"name": "ear_L3", "parent": "ear_L", "length": 7.21, "rotation": 9.93, "x": 5.07, "y": -0.19}, {"name": "ear_L4", "parent": "ear_L3", "length": 9.59, "rotation": 7.24, "x": 7.21}, {"name": "hair", "parent": "body4", "length": 4.18, "rotation": 116.78, "x": 17.07, "y": 11.98}, {"name": "hair3", "parent": "hair", "length": 7.73, "rotation": 5.76, "x": 4.22, "y": 0.12}, {"name": "hair4", "parent": "hair3", "length": 7.7, "rotation": 12.32, "x": 7.84, "y": -0.05}, {"name": "hair_5", "parent": "body3", "length": 7.88, "rotation": 122.89, "x": 35.93, "y": 17.58, "color": "ff0000ff"}, {"name": "hair_7", "parent": "hair_5", "length": 10.37, "rotation": 12.45, "x": 9.03, "y": -0.42, "color": "ff0000ff"}, {"name": "hair_8", "parent": "hair_7", "length": 10.88, "rotation": 20.88, "x": 10.37, "color": "ff0000ff"}, {"name": "hair_4", "parent": "body3", "length": 7.58, "rotation": 139.94, "x": 27.97, "y": 18.81}, {"name": "hair_9", "parent": "hair_4", "length": 9.48, "rotation": 15.7, "x": 7.81, "y": 0.18}, {"name": "hair_10", "parent": "hair_9", "length": 11.05, "rotation": -12.22, "x": 9.48}, {"name": "hair_6", "parent": "body3", "length": 6.57, "rotation": 156.08, "x": 1.74, "y": 17.83}, {"name": "hair_2", "parent": "body4", "length": 7.13, "rotation": -159.91, "x": 5.25, "y": 0.5}, {"name": "hair_11", "parent": "hair_2", "length": 7.51, "rotation": -20.32, "x": 7.55, "y": -0.35}, {"name": "hair_3", "parent": "body4", "length": 4.06, "rotation": -143.15, "x": 11.27, "y": -10.3}, {"name": "hair_13", "parent": "hair_3", "length": 5.82, "rotation": -22.75, "x": 4.25, "y": 0.11}, {"name": "hair_14", "parent": "hair_13", "length": 5.52, "rotation": -29, "x": 5.79, "y": -0.19}, {"name": "leg_R4", "parent": "body", "length": 24.15, "rotation": -113.76, "x": -2.5, "y": -2.44}, {"name": "leg_R6", "parent": "leg_R4", "length": 15.45, "rotation": 20.67, "x": 24.39, "y": -0.2}, {"name": "leg_R5", "parent": "leg_R6", "length": 10.99, "rotation": -2.17, "x": 15.84, "y": 0.58, "transform": "noRotationOrReflection"}, {"name": "leg_R3", "parent": "body", "length": 35.13, "rotation": -98.43, "x": 18.23, "y": 13.36}, {"name": "leg_R2", "parent": "leg_R3", "length": 20.39, "rotation": 11.39, "x": 35.16, "y": 0.6}, {"name": "leg_R", "parent": "leg_R2", "length": 15.44, "x": 20.4, "y": 0.23, "transform": "noRotationOrReflection"}, {"name": "leg_L2", "parent": "body", "length": 23.29, "rotation": -93.21, "x": 34.13, "y": 11.6}, {"name": "leg_L4", "parent": "leg_L2", "length": 26.55, "rotation": 9.04, "x": 23.1, "y": -0.01}, {"name": "leg_L", "parent": "leg_L4", "length": 17.89, "rotation": -3.28, "x": 26.46, "y": 0.01, "transform": "noRotationOrReflection"}, {"name": "leg_L6", "parent": "body", "length": 22.26, "rotation": -104.04, "x": 11.44, "y": 9.04}, {"name": "leg_L8", "parent": "leg_L6", "length": 25.79, "rotation": 14.34, "x": 22.53, "y": 0.07}, {"name": "leg_L5", "parent": "leg_L8", "length": 12.96, "x": 25.38, "y": 0.27, "transform": "noRotationOrReflection"}, {"name": "leg_L_q", "parent": "root", "x": 20.91, "y": 5.84, "color": "ff3f00ff"}, {"name": "leg_R_q", "parent": "root", "x": 0.71, "y": 1.52, "color": "ff3f00ff"}, {"name": "leg_L_H", "parent": "root", "x": -8.07, "y": 5.23, "color": "ff3f00ff"}, {"name": "leg_R_h", "parent": "root", "x": -27.76, "y": 2.9, "color": "ff3f00ff"}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "hair_6", "bone": "hair_6", "attachment": "hair_6"}, {"name": "tails_2", "bone": "tails_2", "attachment": "tails_2"}, {"name": "tails", "bone": "tails3", "attachment": "tails"}, {"name": "leg_L6", "bone": "leg_L8", "attachment": "leg_L6"}, {"name": "leg_L5", "bone": "leg_L6", "attachment": "leg_L5"}, {"name": "leg_L4", "bone": "leg_L5", "attachment": "leg_L4"}, {"name": "leg_L3", "bone": "leg_L4", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L", "attachment": "leg_L"}, {"name": "body", "bone": "body5", "attachment": "body"}, {"name": "leg_R6", "bone": "leg_R6", "attachment": "leg_R6"}, {"name": "leg_R5", "bone": "leg_R5", "attachment": "leg_R5"}, {"name": "leg_R4", "bone": "leg_R4", "attachment": "leg_R4"}, {"name": "leg_R3", "bone": "leg_R3", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R2", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "hair_5", "bone": "hair_7", "attachment": "hair_5"}, {"name": "hair_4", "bone": "hair_9", "attachment": "hair_4"}, {"name": "ear_L", "bone": "ear_L3", "attachment": "ear_L"}, {"name": "head_3", "bone": "body4", "attachment": "head_3"}, {"name": "head_2", "bone": "head_2", "attachment": "head_2"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye_R", "bone": "body4", "color": "ffffff00", "attachment": "eye_R"}, {"name": "eye_L", "bone": "body4", "color": "ffffff00", "attachment": "eye_L"}, {"name": "hair_3", "bone": "hair_13", "attachment": "hair_3"}, {"name": "hair_2", "bone": "hair_11", "attachment": "hair_2"}, {"name": "hair", "bone": "hair3", "attachment": "hair"}, {"name": "ear_R", "bone": "ear_R3", "attachment": "ear_R"}], "ik": [{"name": "leg_L_H", "order": 2, "bones": ["leg_L6", "leg_L8"], "target": "leg_L_H"}, {"name": "leg_L_q", "bones": ["leg_L2", "leg_L4"], "target": "leg_L_q"}, {"name": "leg_R_h", "order": 3, "bones": ["leg_R4", "leg_R6"], "target": "leg_R_h"}, {"name": "leg_R_q", "order": 1, "bones": ["leg_R3", "leg_R2"], "target": "leg_R_q"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.56106, 1, 0.69663, 0.95107, 0.78391, 0.8695, 0.90463, 0.81597, 0.98077, 0.70381, 0.99748, 0.54068, 0.98263, 0.39028, 0.93805, 0.23225, 0.84334, 0.12774, 0.73748, 0.05636, 0.62234, 0.00793, 0.48491, 0, 0.3382, 0.02323, 0.24163, 0.09205, 0.1432, 0.19146, 0.07634, 0.29342, 0.02434, 0.40813, 0.0002, 0.54577, 0.00391, 0.68597, 0.0392, 0.81597, 0.12834, 0.89754, 0.24349, 0.97146, 0.38834, 1, 0.26763, 0.4897, 0.1172, 0.55852, 0.41991, 0.48205, 0.59263, 0.46421, 0.75791, 0.4846, 0.91206, 0.49989, 0.24349, 0.28578, 0.4032, 0.2348, 0.5852, 0.20931, 0.74306, 0.22715, 0.88606, 0.30617, 0.09491, 0.40558, 0.10049, 0.71911, 0.26949, 0.74715, 0.44406, 0.79048, 0.61306, 0.78283, 0.76906, 0.71656, 0.90834, 0.66813], "triangles": [31, 10, 9, 32, 9, 8, 31, 9, 32, 33, 8, 7, 32, 8, 33, 33, 7, 6, 26, 31, 32, 27, 32, 33, 26, 32, 27, 28, 33, 6, 27, 33, 28, 28, 6, 5, 40, 27, 28, 40, 28, 5, 4, 40, 5, 39, 27, 40, 39, 38, 26, 39, 26, 27, 3, 39, 40, 3, 40, 4, 2, 39, 3, 38, 39, 2, 1, 38, 2, 31, 11, 10, 30, 12, 11, 30, 11, 31, 13, 12, 30, 29, 13, 30, 14, 13, 29, 30, 31, 26, 25, 30, 26, 29, 30, 25, 23, 29, 25, 36, 23, 25, 37, 25, 26, 37, 26, 38, 36, 25, 37, 22, 21, 36, 37, 22, 36, 0, 37, 38, 0, 38, 1, 22, 37, 0, 29, 34, 15, 29, 15, 14, 16, 15, 34, 34, 29, 23, 24, 34, 23, 24, 18, 17, 17, 16, 34, 34, 24, 17, 35, 18, 24, 24, 23, 36, 35, 24, 36, 19, 18, 35, 20, 35, 36, 19, 35, 20, 21, 20, 36], "vertices": [3, 6, -12.46, 31.7, 0.11029, 4, 19.59, -27.14, 0.60424, 5, 0.04, -27.24, 0.28548, 3, 6, -22.26, 32.18, 0.0267, 4, 29.14, -24.88, 0.45108, 5, 9.35, -24.16, 0.52221, 3, 6, -29.34, 30.05, 0.00471, 4, 35.35, -20.87, 0.24798, 5, 15.2, -19.63, 0.74731, 3, 6, -38.22, 30, 0.00041, 4, 43.87, -18.35, 0.10094, 5, 23.46, -16.39, 0.89866, 2, 4, 49.33, -12.76, 0.02322, 5, 28.43, -10.35, 0.97678, 2, 4, 50.71, -4.47, 0.00365, 5, 29.08, -1.97, 0.99635, 2, 4, 49.85, 3.22, 0.00037, 5, 27.57, 5.62, 0.99963, 2, 4, 46.93, 11.36, 0.01449, 5, 23.96, 13.47, 0.98551, 2, 4, 40.43, 16.85, 0.07688, 5, 17.01, 18.38, 0.92312, 2, 4, 33.11, 20.67, 0.22169, 5, 9.39, 21.56, 0.77831, 3, 6, -31.79, -15.25, 0.00344, 4, 25.12, 23.33, 0.44909, 5, 1.2, 23.52, 0.54747, 3, 6, -22.74, -18.53, 0.03755, 4, 15.51, 23.97, 0.66286, 5, -8.43, 23.33, 0.29959, 3, 6, -12.59, -20.5, 0.13146, 4, 5.21, 23.04, 0.75229, 5, -18.61, 21.52, 0.11625, 3, 6, -5.09, -19.19, 0.3398, 4, -1.63, 19.69, 0.63232, 5, -25.14, 17.6, 0.02789, 3, 6, 3.01, -16.43, 0.60451, 4, -8.64, 14.79, 0.39067, 5, -31.7, 12.11, 0.00483, 3, 6, 9.04, -12.88, 0.84237, 4, -13.45, 9.71, 0.15761, 5, -36.05, 6.63, 2e-05, 2, 6, 14.27, -8.39, 0.96391, 4, -17.23, 3.95, 0.03609, 2, 6, 18, -2.21, 0.9927, 4, -19.09, -3.03, 0.0073, 2, 6, 19.9, 4.69, 0.96638, 4, -19, -10.18, 0.03362, 2, 6, 19.54, 11.75, 0.88948, 4, -16.7, -16.87, 0.11052, 3, 6, 14.85, 17.6, 0.7351, 4, -10.56, -21.18, 0.26368, 5, -30.52, -23.89, 0.00122, 3, 6, 8.3, 23.62, 0.51365, 4, -2.59, -25.15, 0.46667, 5, -22.24, -27.16, 0.01969, 3, 6, -0.94, 28.06, 0.27921, 4, 7.51, -26.85, 0.61244, 5, -12.03, -27.99, 0.10835, 2, 6, -0.71, 0.7, 0.29751, 4, -0.31, -0.62, 0.70249, 2, 6, 10.38, 0.88, 0.9965, 4, -10.92, -3.88, 0.0035, 2, 6, -11, 3.54, 0.00077, 4, 10.36, -0.49, 0.99923, 2, 4, 22.47, 0.12, 0.16544, 5, 0.56, 0.17, 0.83456, 2, 4, 34.01, -1.2, 0.00033, 5, 12.17, -0.15, 0.99967, 1, 5, 22.99, -0.27, 1, 3, 6, -2.23, -9.73, 0.43808, 4, -1.74, 9.81, 0.56103, 5, -24.4, 7.74, 0.00088, 3, 6, -13.68, -8.84, 0.03535, 4, 9.5, 12.14, 0.90026, 5, -13.4, 11.03, 0.06439, 2, 4, 22.27, 13.13, 0.489, 5, -0.76, 13.11, 0.511, 2, 4, 33.29, 11.95, 0.08552, 5, 10.32, 12.89, 0.91448, 2, 4, 43.2, 7.68, 0.00095, 5, 20.56, 9.48, 0.99905, 2, 6, 9.52, -7.03, 0.97977, 4, -12.29, 3.96, 0.02023, 2, 6, 13.97, 8.33, 0.92281, 4, -12.29, -12.03, 0.07719, 3, 6, 3.12, 13.26, 0.51859, 4, -0.5, -13.75, 0.4807, 5, -21.13, -15.63, 0.00072, 3, 6, -7.87, 19.05, 0.13512, 4, 11.67, -16.26, 0.78944, 5, -8.8, -17.08, 0.07544, 3, 6, -19.27, 22.24, 0.0189, 4, 23.5, -16.16, 0.55022, 5, 2.99, -15.96, 0.43088, 3, 6, -30.7, 22.3, 9e-05, 4, 34.5, -13.05, 0.14588, 5, 13.68, -11.91, 0.85404, 2, 4, 44.31, -10.81, 0.01813, 5, 23.26, -8.85, 0.98188], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 85, "height": 70}}, "ear_L": {"ear_L": {"type": "mesh", "uvs": [0.15617, 0, 0.13245, 0.16078, 0.10195, 0.32266, 0.06467, 0.46812, 0, 0.64408, 0.10873, 0.80362, 0.268, 0.93735, 0.5425, 0.99366, 0.83056, 0.92562, 0.97967, 0.70743, 0.939, 0.48689, 0.79328, 0.32501, 0.56623, 0.17485, 0.39, 0.04816, 0.29512, 0, 0.39585, 0.3089, 0.44964, 0.50981, 0.28238, 0.12335, 0.49248, 0.68889, 0.52799, 0.87225], "triangles": [17, 0, 14, 17, 14, 13, 1, 0, 17, 17, 13, 12, 15, 17, 12, 15, 12, 11, 16, 15, 11, 1, 17, 15, 2, 1, 15, 3, 15, 16, 15, 3, 2, 16, 11, 10, 18, 16, 10, 18, 10, 9, 16, 4, 3, 18, 4, 16, 18, 5, 4, 8, 19, 18, 6, 5, 18, 9, 8, 18, 19, 6, 18, 7, 19, 8, 6, 19, 7], "vertices": [1, 18, 12.3, 0.85, 1, 2, 16, 19.58, 6.45, 0.00137, 18, 8.67, 2.97, 0.99863, 3, 16, 15.47, 7.54, 0.05759, 17, 11.58, 5.82, 0.01104, 18, 5.07, 5.22, 0.93136, 3, 16, 11.81, 8.68, 0.27325, 17, 8.17, 7.58, 0.11286, 18, 1.91, 7.4, 0.61389, 3, 16, 7.42, 10.42, 0.63019, 17, 4.15, 10.05, 0.1102, 18, -1.77, 10.36, 0.25961, 3, 16, 3.06, 9.01, 0.88226, 17, -0.39, 9.41, 0.03038, 18, -6.36, 10.3, 0.08736, 2, 16, -0.76, 6.62, 0.98731, 18, -10.71, 9.13, 0.01269, 1, 16, -2.84, 1.9, 1, 2, 16, -1.75, -3.47, 0.88374, 17, -7.28, -2.05, 0.11626, 2, 16, 3.53, -6.85, 0.20453, 17, -2.66, -6.3, 0.79547, 2, 17, 3.03, -7.3, 0.9386, 18, -5.07, -6.72, 0.0614, 2, 17, 7.83, -6.05, 0.5072, 18, -0.15, -6.08, 0.4928, 2, 17, 12.77, -3.31, 0.00496, 18, 5.1, -3.98, 0.99504, 1, 18, 9.41, -2.46, 1, 1, 18, 11.26, -1.43, 1, 2, 16, 15.15, 2.24, 0.00103, 18, 3.2, 0.26, 0.99897, 3, 16, 9.85, 1.95, 0.06068, 17, 5.08, 1.29, 0.8704, 18, -1.95, 1.54, 0.06892, 2, 16, 20.2, 3.65, 0, 18, 8.44, 0.11, 1, 3, 16, 5.13, 1.78, 0.8707, 17, 0.4, 1.93, 0.12021, 18, -6.51, 2.77, 0.00909, 2, 16, 0.32, 1.76, 0.99978, 18, -11.11, 4.17, 0.00022], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 21, "height": 31}}, "ear_R": {"ear_R": {"type": "mesh", "uvs": [0.13146, 0, 0.04803, 0.18841, 0.02607, 0.40359, 0.06559, 0.63632, 0.13146, 0.86467, 0.25881, 1, 0.50911, 0.97006, 0.76381, 0.88224, 0.98776, 0.71976, 0.93946, 0.47385, 0.78576, 0.27185, 0.55303, 0.15328, 0.36859, 0.07424, 0.25078, 0.22286, 0.35839, 0.37503, 0.51664, 0.53313, 0.64648, 0.70114, 0.40136, 0.81402, 0.30036, 0.61441, 0.19006, 0.40092, 0.16854, 0.22109, 0.36487, 0.15908, 0.52227, 0.27967, 0.65671, 0.39309, 0.82789, 0.59861], "triangles": [4, 3, 18, 3, 19, 18, 3, 2, 19, 18, 19, 14, 15, 14, 22, 2, 20, 19, 2, 1, 20, 19, 13, 14, 19, 20, 13, 14, 21, 22, 14, 13, 21, 22, 11, 10, 22, 21, 11, 21, 13, 12, 13, 0, 12, 20, 1, 0, 21, 12, 11, 0, 13, 20, 17, 18, 15, 24, 16, 23, 18, 14, 15, 16, 15, 23, 24, 23, 9, 15, 22, 23, 23, 10, 9, 23, 22, 10, 5, 17, 6, 5, 4, 17, 6, 16, 7, 6, 17, 16, 7, 24, 8, 7, 16, 24, 4, 18, 17, 17, 15, 16, 24, 9, 8], "vertices": [3, 13, 22.97, 1.66, 0, 14, 17.92, -0.31, 0.00266, 15, 10.83, -2.42, 0.99734, 3, 13, 20.01, 5.36, 0.00749, 14, 15.41, 3.7, 0.0135, 15, 9.12, 2, 0.979, 3, 13, 15.86, 8.09, 0.07162, 14, 11.59, 6.89, 0.10665, 15, 5.98, 5.86, 0.82173, 3, 13, 10.69, 9.75, 0.24943, 14, 6.65, 9.13, 0.20447, 15, 1.55, 9, 0.5461, 3, 13, 5.33, 10.83, 0.48826, 14, 1.45, 10.82, 0.2549, 15, -3.23, 11.65, 0.25684, 1, 13, 1.22, 9.67, 1, 1, 13, -0.83, 4.24, 1, 1, 13, -1.74, -1.89, 1, 1, 13, -0.8, -8.18, 1, 3, 13, 4.73, -9.8, 0.31917, 14, -1.51, -9.61, 0.61781, 15, -10.02, -7.85, 0.06302, 3, 13, 10.48, -8.81, 0.111, 14, 4.32, -9.28, 0.59072, 15, -4.23, -8.64, 0.29828, 3, 13, 15.37, -5.32, 0.0127, 14, 9.57, -6.38, 0.36339, 15, 1.48, -6.78, 0.62392, 2, 14, 13.46, -3.88, 0.10072, 15, 5.77, -5.07, 0.89928, 1, 15, 5.26, -0.73, 1, 2, 13, 12.91, 1.01, 0, 15, 1.04, -0.01, 1, 1, 14, 2.8, -0.79, 1, 2, 13, 3.2, -1.41, 0.81989, 14, -2.07, -1.1, 0.18011, 3, 13, 3.5, 4.79, 0.74058, 14, -1.06, 5.02, 0.21874, 15, -6.8, 6.43, 0.04068, 3, 13, 8.64, 4.73, 0.17759, 14, 4.04, 4.38, 0.56024, 15, -1.91, 4.83, 0.26217, 3, 13, 14.17, 4.72, 0.01681, 14, 9.53, 3.73, 0.04526, 15, 3.36, 3.15, 0.93793, 2, 13, 18.07, 3.25, 4e-05, 15, 6.63, 0.58, 0.99996, 2, 14, 11.89, -2.71, 0.01411, 15, 4.45, -3.63, 0.98589, 2, 14, 7.56, -4.16, 0.40772, 15, -0.07, -4.22, 0.59228, 3, 13, 9.38, -4.89, 0.02284, 14, 3.67, -5.26, 0.83663, 15, -4.11, -4.57, 0.14053, 3, 13, 3.37, -6.2, 0.44228, 14, -2.45, -5.88, 0.555, 15, -10.23, -4.01, 0.00272], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 28, "height": 28}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [36.09, 1, 28.1, 0.51, 27.61, 8.49, 35.6, 8.99], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 8, "height": 8}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [28.29, -2.49, 11.32, -3.54, 10.46, 10.44, 27.43, 11.49], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 14}}, "hair": {"hair": {"type": "mesh", "uvs": [0.00418, 0.02176, 0.08818, 0.20843, 0.09582, 0.39976, 0.03473, 0.55843, 0, 0.70776, 0.15436, 0.79643, 0.386, 0.79643, 0.61, 0.85709, 0.77036, 0.94343, 1, 1, 1, 0.93176, 0.93836, 0.70543, 0.81872, 0.49543, 0.71181, 0.35309, 0.498, 0.18043, 0.28163, 0.0521, 0.15182, 0, 0.23612, 0.19916, 0.3254, 0.30734, 0.42261, 0.48381, 0.48182, 0.6206, 0.54864, 0.75785, 0.77593, 0.79006, 0.68848, 0.58377, 0.60077, 0.41723, 0.43432, 0.24601, 0.19527, 0.34404, 0.22317, 0.49067, 0.28924, 0.67463], "triangles": [17, 16, 15, 25, 17, 15, 1, 0, 16, 1, 16, 17, 14, 25, 15, 18, 17, 25, 26, 1, 17, 26, 17, 18, 2, 1, 26, 24, 14, 13, 25, 14, 24, 19, 18, 25, 19, 25, 24, 27, 26, 18, 27, 18, 19, 2, 26, 27, 3, 2, 27, 20, 19, 24, 28, 27, 19, 28, 19, 20, 3, 27, 28, 5, 28, 6, 6, 20, 21, 12, 23, 24, 12, 24, 13, 20, 24, 23, 23, 12, 11, 21, 20, 23, 22, 23, 11, 21, 23, 22, 7, 21, 22, 6, 21, 7, 22, 11, 10, 8, 7, 22, 8, 22, 10, 8, 10, 9, 5, 4, 28, 4, 3, 28, 6, 28, 20], "vertices": [2, 20, 17.81, 3.46, 0.02029, 21, 10.49, 1.31, 0.97971, 2, 20, 12.97, 3.71, 0.27821, 21, 5.81, 2.58, 0.72179, 3, 19, 12.37, 6.49, 0.33333, 20, 8.75, 5.52, 0.27821, 21, 2.07, 5.25, 0.38845, 3, 19, 9.23, 9.03, 0.66667, 20, 5.88, 8.36, 0.25792, 21, -0.12, 8.64, 0.07541, 1, 19, 6.11, 10.95, 1, 1, 19, 2.97, 8.46, 1, 1, 19, 1.26, 3.66, 1, 1, 19, -1.76, -0.5, 1, 1, 19, -4.89, -3.13, 1, 1, 19, -7.86, -7.44, 1, 2, 19, -6.32, -7.99, 0.99894, 21, -20.18, -2.71, 0.00106, 3, 19, -0.74, -8.53, 0.96047, 20, -5.81, -8.1, 0.01246, 21, -15.05, -4.95, 0.02707, 3, 19, 4.89, -7.73, 0.797, 20, -0.13, -7.88, 0.07752, 21, -9.45, -5.94, 0.12549, 3, 19, 8.89, -6.66, 0.47773, 20, 3.97, -7.21, 0.08283, 21, -5.31, -6.17, 0.43944, 3, 19, 14.37, -3.61, 0.18287, 20, 9.72, -4.73, 0.07037, 21, 0.84, -4.97, 0.74676, 3, 19, 18.86, -0.15, 0.01301, 20, 14.54, -1.74, 0.00531, 21, 6.19, -3.08, 0.98168, 1, 21, 8.92, -1.58, 1, 1, 21, 4, -0.13, 1, 2, 19, 12.77, 0.99, 0.00014, 21, 0.75, -0.1, 0.99986, 3, 19, 8.06, 0.39, 0.00393, 20, 3.85, -0.12, 0.9934, 21, -3.91, 0.79, 0.00267, 1, 20, 0.33, 0.11, 1, 1, 19, 0.94, -0.03, 1, 1, 19, -1.46, -4.48, 1, 3, 19, 3.85, -4.32, 0.93846, 20, -0.82, -4.38, 0.02179, 21, -9.38, -2.38, 0.03975, 3, 19, 8.26, -3.84, 0.41783, 20, 3.62, -4.35, 0.32995, 21, -5.04, -3.29, 0.25222, 3, 19, 13.35, -1.76, 0.02668, 20, 8.9, -2.79, 0.01536, 21, 0.45, -2.9, 0.95797, 2, 20, 9.02, 2.97, 0.56542, 21, 1.8, 2.7, 0.43458, 2, 20, 5.58, 3.92, 0.98618, 21, -1.36, 4.36, 0.01382, 1, 19, 4.73, 4.68, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 26, "height": 29}}, "hair_2": {"hair_2": {"type": "mesh", "uvs": [0.26189, 0.25904, 0.39619, 0.11233, 0.57394, 0.00324, 0.75959, 0.03333, 0.94524, 0.10481, 1, 0.21766, 0.96499, 0.35685, 0.91364, 0.50357, 0.96894, 0.66533, 1, 0.79699, 0.86229, 0.90233, 0.70429, 0.99261, 0.53839, 0.99261, 0.36854, 0.87975, 0.24214, 0.73304, 0.12364, 0.60514, 0.00909, 0.43209, 0.15524, 0.33428, 0.2432, 0.47641, 0.47894, 0.50227, 0.68672, 0.47362, 0.8603, 0.42402, 0.78215, 0.23448, 0.56777, 0.24387, 0.49935, 0.72595, 0.77884, 0.69042], "triangles": [23, 1, 2, 18, 17, 0, 16, 17, 18, 23, 0, 1, 19, 0, 23, 18, 0, 19, 15, 16, 18, 14, 15, 18, 14, 18, 19, 14, 19, 24, 22, 3, 4, 22, 4, 5, 23, 2, 3, 23, 3, 22, 6, 22, 5, 21, 22, 6, 20, 23, 22, 20, 22, 21, 19, 23, 20, 7, 21, 6, 7, 25, 20, 7, 20, 21, 25, 7, 8, 24, 19, 20, 24, 20, 25, 13, 14, 24, 9, 10, 25, 9, 25, 8, 11, 12, 24, 25, 11, 24, 13, 24, 12, 10, 11, 25], "vertices": [2, 29, 10.87, -6.1, 0.0291, 30, 5.11, -4.25, 0.9709, 2, 29, 7.56, -8.51, 0.26869, 30, 2.84, -7.65, 0.73131, 2, 29, 3.59, -9.95, 0.54057, 30, -0.38, -10.39, 0.45943, 2, 29, 0.11, -8.51, 0.7509, 30, -4.15, -10.24, 0.2491, 2, 29, -3.18, -6.22, 0.91548, 30, -8.02, -9.24, 0.08452, 2, 29, -3.72, -3.67, 0.95908, 30, -9.42, -7.03, 0.04092, 2, 29, -2.39, -0.98, 0.99177, 30, -9.1, -4.04, 0.00823, 1, 29, -0.7, 1.8, 1, 1, 29, -1.02, 5.36, 1, 1, 29, -1.01, 8.19, 1, 1, 29, 2.16, 9.74, 1, 2, 29, 5.67, 10.88, 0.98752, 30, -5.67, 9.87, 0.01248, 2, 29, 8.9, 10.14, 0.93346, 30, -2.38, 10.3, 0.06654, 2, 29, 11.69, 7.08, 0.72679, 30, 1.3, 8.4, 0.27321, 2, 29, 13.46, 3.51, 0.31255, 30, 4.2, 5.67, 0.68745, 2, 29, 15.18, 0.37, 0.03898, 30, 6.9, 3.32, 0.96102, 1, 30, 9.65, 0.01, 1, 1, 30, 7.02, -2.41, 1, 2, 29, 12.25, -1.74, 0.00213, 30, 4.88, 0.32, 0.99787, 2, 29, 7.77, -0.16, 0.15447, 30, 0.14, 0.25, 0.84553, 1, 29, 3.58, 0.18, 1, 2, 29, -0.03, -0.07, 0.99972, 30, -7.21, -2.37, 0.00028, 2, 29, 0.61, -4.29, 0.87282, 30, -5.14, -6.11, 0.12718, 2, 29, 4.83, -5.06, 0.5074, 30, -0.92, -5.36, 0.4926, 2, 29, 8.42, 4.51, 0.80498, 30, -0.88, 4.85, 0.19502, 1, 29, 2.8, 5.03, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 23, "height": 25}}, "hair_3": {"hair_3": {"type": "mesh", "uvs": [0.70542, 0.41985, 0.86583, 0.61235, 1, 0.74985, 0.93229, 0.91878, 0.76729, 0.99342, 0.58396, 0.96592, 0.38, 0.94628, 0.20125, 0.81271, 0.0775, 0.62021, 0, 0.40021, 0.1325, 0.27057, 0.29292, 0.17628, 0.41667, 0, 0.57937, 0, 0.63667, 0.21557, 0.56451, 0.53314, 0.29324, 0.60143, 0.14815, 0.49083, 0.42677, 0.59264, 0.64089, 0.4602, 0.77164, 0.70696, 0.61496, 0.7869, 0.44049, 0.8076, 0.2973, 0.75162, 0.30691, 0.40911, 0.44878, 0.27885, 0.53329, 0.20807], "triangles": [8, 17, 7, 8, 9, 17, 16, 17, 24, 9, 10, 17, 17, 10, 24, 10, 11, 24, 24, 11, 25, 5, 21, 4, 4, 21, 20, 6, 22, 5, 5, 22, 21, 7, 23, 6, 6, 23, 22, 7, 16, 23, 7, 17, 16, 23, 18, 22, 22, 15, 21, 22, 18, 15, 23, 16, 18, 16, 24, 18, 18, 24, 25, 4, 20, 3, 20, 1, 3, 3, 1, 2, 21, 19, 20, 19, 0, 20, 21, 15, 19, 20, 0, 1, 18, 25, 15, 25, 26, 15, 19, 26, 14, 19, 15, 26, 19, 14, 0, 11, 12, 25, 25, 12, 26, 26, 13, 14, 26, 12, 13], "vertices": [2, 31, 0.1, 0.44, 0.98, 32, -3.95, -1.3, 0.02, 2, 31, -1.91, 4.68, 0.6781, 32, -7.45, 1.83, 0.3219, 2, 31, -3.76, 7.95, 0.64337, 32, -10.42, 4.13, 0.35663, 2, 31, -1.18, 9.2, 0.61097, 32, -8.52, 6.28, 0.38903, 2, 31, 2.78, 8.15, 0.43234, 32, -4.47, 6.85, 0.56766, 2, 31, 6.41, 5.64, 0.08197, 32, -0.15, 5.94, 0.91803, 1, 32, 4.68, 5.08, 1, 2, 32, 8.72, 2.71, 0.7862, 33, 1.16, 3.96, 0.2138, 2, 32, 11.34, -0.32, 0.0742, 33, 4.92, 2.59, 0.9258, 1, 33, 7.81, 0.43, 1, 2, 31, 11.02, -8.17, 0.0153, 33, 5.54, -2.45, 0.9847, 3, 31, 7.02, -7.42, 0.26864, 32, 5.47, -5.87, 0.03342, 33, 2.48, -5.12, 0.69794, 3, 31, 3.22, -8.1, 0.65368, 32, 2.23, -7.97, 0.01627, 33, 0.66, -8.53, 0.33005, 2, 31, -0.17, -6.17, 0.82954, 33, -2.96, -10, 0.17046, 2, 31, 0.12, -2.87, 0.92947, 33, -5.37, -7.72, 0.07053, 2, 31, 3.83, 0.14, 0.94579, 32, -0.41, -0.13, 0.05421, 2, 32, 6.17, 0.04, 0.78416, 33, 0.23, 0.39, 0.21584, 2, 32, 9.45, -1.91, 0.00559, 33, 4.03, 0.27, 0.99441, 1, 32, 2.98, 0.3, 1, 2, 31, 1.73, 0.16, 0.99321, 32, -2.35, -0.93, 0.00679, 2, 31, 0.71, 4.72, 0.60622, 32, -5.05, 2.88, 0.39378, 2, 31, 4.53, 3.83, 0.16021, 32, -1.18, 3.54, 0.83979, 1, 32, 3.01, 3.33, 1, 2, 32, 6.33, 2.14, 0.99468, 33, -0.66, 2.3, 0.00532, 3, 31, 8.34, -4.42, 0.08984, 32, 5.53, -2.59, 0.09437, 33, 0.94, -2.23, 0.81578, 3, 31, 4.48, -4.32, 0.56952, 32, 1.93, -4, 0.1545, 33, -1.53, -5.2, 0.27598, 3, 31, 2.23, -4.18, 0.82348, 32, -0.2, -4.74, 0.02317, 33, -3.04, -6.88, 0.15335], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 29, "height": 17}}, "hair_4": {"hair_4": {"type": "mesh", "uvs": [0.99078, 0.97808, 0.96844, 0.76751, 0.90423, 0.58804, 0.80094, 0.39183, 0.67811, 0.26022, 0.53573, 0.15972, 0.38638, 0.17408, 0.2803, 0.07358, 0.17421, 0, 0.10861, 0.15972, 0.09186, 0.32243, 0, 0.2339, 0, 0.4349, 0.03044, 0.6359, 0.13932, 0.74597, 0.2803, 0.71247, 0.41988, 0.70051, 0.57621, 0.72922, 0.70742, 0.79143, 0.84003, 0.86322, 0.92796, 0.84168, 0.81351, 0.68375, 0.69626, 0.54258, 0.57482, 0.43968, 0.43663, 0.34158, 0.24261, 0.23629, 0.06813, 0.42293, 0.21469, 0.56172, 0.40453, 0.59043, 0.6809, 0.7029, 0.55109, 0.54736, 0.35428, 0.37029, 0.23284, 0.4325], "triangles": [14, 27, 15, 14, 13, 27, 15, 27, 28, 28, 32, 31, 32, 28, 27, 13, 26, 27, 13, 12, 26, 27, 26, 32, 26, 12, 10, 26, 10, 32, 32, 25, 31, 32, 10, 25, 10, 12, 11, 31, 6, 24, 31, 25, 6, 10, 9, 25, 9, 8, 25, 25, 7, 6, 25, 8, 7, 16, 30, 17, 15, 28, 16, 16, 28, 30, 28, 24, 30, 28, 31, 24, 30, 24, 23, 24, 6, 5, 19, 20, 0, 20, 1, 0, 18, 21, 19, 19, 21, 20, 1, 20, 2, 17, 29, 18, 18, 29, 21, 20, 21, 2, 17, 30, 29, 29, 22, 21, 29, 30, 22, 22, 3, 21, 21, 3, 2, 30, 23, 22, 23, 4, 22, 22, 4, 3, 24, 5, 23, 23, 5, 4], "vertices": [1, 25, -16.45, -3.51, 1, 1, 25, -11.92, -7.44, 1, 1, 25, -6.38, -9.44, 1, 1, 25, 0.92, -10.63, 1, 2, 25, 7.83, -9.83, 0.94548, 26, -2.69, -9.64, 0.05452, 3, 25, 14.92, -7.76, 0.46624, 26, 4.7, -9.57, 0.47494, 27, -2.65, -10.36, 0.05881, 3, 25, 20.27, -2.96, 0.04846, 26, 11.14, -6.4, 0.27291, 27, 2.98, -5.91, 0.67863, 3, 25, 26.01, -1.98, 5e-05, 26, 16.93, -7.01, 0.00218, 27, 8.76, -5.27, 0.99778, 1, 27, 14.11, -4.02, 1, 1, 27, 14.11, 1.45, 1, 1, 27, 12.14, 5.63, 1, 1, 27, 17.17, 6.15, 1, 2, 26, 25.37, 7.56, 0.00019, 27, 13.93, 10.75, 0.99981, 2, 26, 21.83, 12.17, 0.01733, 27, 9.49, 14.51, 0.98267, 2, 26, 15.81, 12.97, 0.09262, 27, 3.44, 14.01, 0.90738, 2, 26, 9.95, 9.46, 0.41906, 27, -1.55, 9.35, 0.58094, 3, 25, 9.81, 7.54, 0.00352, 26, 3.91, 6.54, 0.94401, 27, -6.83, 5.22, 0.05247, 2, 25, 3.45, 3.48, 0.79846, 26, -3.31, 4.36, 0.20154, 1, 25, -2.56, 0.91, 1, 1, 25, -8.79, -1.5, 1, 1, 25, -11.7, -4.61, 1, 1, 25, -4.65, -4.63, 1, 1, 25, 2.21, -4.2, 1, 2, 25, 8.56, -2.81, 0.86825, 26, -0.09, -3.08, 0.13175, 3, 25, 15.46, -0.81, 0.0775, 26, 7.09, -3.02, 0.78686, 27, -1.7, -3.46, 0.13564, 1, 27, 7.61, -0.51, 1, 2, 26, 22.49, 5.97, 0.00168, 27, 11.45, 8.59, 0.99832, 2, 26, 14.5, 6.81, 0.14191, 27, 3.46, 7.71, 0.85809, 2, 26, 5.79, 3.99, 0.93681, 27, -4.45, 3.12, 0.06319, 1, 25, -0.02, -0.23, 1, 2, 25, 7.57, 0.26, 0.44645, 26, -0.21, 0.14, 0.55355, 3, 25, 18.04, 2.29, 0.00158, 26, 10.42, -0.74, 0.03245, 27, 1.07, -0.53, 0.96597, 2, 26, 15.11, 3.14, 0.04056, 27, 4.83, 4.25, 0.95944], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 59, "height": 34}}, "hair_5": {"hair_5": {"type": "mesh", "uvs": [0.0189, 0.03542, 0.20419, 0.02256, 0.46625, 0.06756, 0.62772, 0.16399, 0.76802, 0.3022, 0.90302, 0.4822, 0.96919, 0.70077, 1, 0.96113, 0.90831, 0.98684, 0.78919, 0.85506, 0.63037, 0.72327, 0.50066, 0.64291, 0.34714, 0.51434, 0.21478, 0.3472, 0.10361, 0.19292, 0, 0.10292, 0.39399, 0.22166, 0.60911, 0.4607, 0.71666, 0.61595, 0.8851, 0.81555, 0.93178, 0.92645, 0.21134, 0.13788, 0.51575, 0.37445], "triangles": [21, 1, 2, 14, 0, 1, 14, 1, 21, 15, 0, 14, 16, 21, 2, 13, 21, 16, 14, 21, 13, 3, 16, 2, 17, 22, 3, 22, 16, 3, 4, 17, 3, 12, 13, 16, 12, 16, 22, 18, 17, 4, 11, 12, 22, 11, 22, 17, 18, 4, 5, 10, 11, 17, 18, 5, 6, 18, 10, 17, 19, 18, 6, 9, 18, 19, 10, 18, 9, 20, 19, 6, 20, 6, 7, 20, 9, 19, 8, 9, 20, 8, 20, 7], "vertices": [1, 24, 13.58, 0.08, 1, 1, 24, 7.89, -2.65, 1, 2, 23, 11.33, -4.85, 0.53647, 24, -0.82, -4.88, 0.46353, 2, 23, 5.49, -6.65, 0.99619, 24, -6.93, -4.47, 0.00381, 2, 22, 9.93, -7.49, 0.1768, 23, -0.64, -7.1, 0.8232, 2, 22, 3.18, -8.45, 0.76975, 23, -7.44, -6.58, 0.23025, 2, 22, -3.14, -6.86, 0.98297, 23, -13.27, -3.66, 0.01703, 1, 22, -9.76, -3.62, 1, 1, 22, -8.59, -0.63, 1, 1, 22, -3.26, 0.63, 1, 2, 22, 2.83, 3.02, 0.99983, 23, -5.31, 4.69, 0.00017, 2, 22, 7.17, 5.39, 0.70908, 23, -0.56, 6.08, 0.29092, 3, 22, 13.08, 7.68, 0.10631, 23, 5.71, 7.03, 0.8361, 24, -1.84, 8.23, 0.05758, 3, 22, 19.49, 8.76, 0.00109, 23, 12.19, 6.71, 0.29843, 24, 4.1, 5.62, 0.70048, 2, 23, 17.91, 6.15, 0.00367, 24, 9.24, 3.06, 0.99633, 1, 24, 13.46, 2.07, 1, 2, 23, 10.17, -0.03, 0.75602, 24, -0.2, 0.05, 0.24398, 1, 23, 0.25, -0.16, 1, 2, 22, 3.66, -1.1, 0.977, 23, -5.39, 0.49, 0.023, 2, 22, -4.19, -2.68, 0.99928, 23, -13.39, 0.64, 0.00072, 1, 22, -7.65, -2.25, 1, 2, 23, 16.3, 2.52, 0.00102, 24, 6.44, 0.24, 0.99898, 2, 22, 13.09, 0.73, 0.00322, 23, 4.22, 0.25, 0.99678], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 40, "height": 34}}, "hair_6": {"hair_6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-10.51, 2.7, 7.97, 10.36, 13.33, -2.57, -5.15, -10.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 24, "height": 17}}, "head": {"head": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [37.55, -11.28, -14.21, -16.34, -17.22, 14.51, 34.53, 19.57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 64, "height": 37}}, "head_2": {"head_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.63, -2.38, -4.15, -25.48, -18.21, -1.26, 21.57, 21.84], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 57, "height": 33}}, "head_3": {"head_3": {"type": "mesh", "uvs": [0.61605, 0.06455, 0.75439, 0.15116, 0.87008, 0.26303, 0.97572, 0.41099, 1, 0.66359, 0.93296, 0.84403, 0.86002, 0.9559, 0.65881, 0.97394, 0.45005, 0.94146, 0.28154, 0.86929, 0.16333, 0.75742, 0.06775, 0.60225, 0, 0.42542, 0.05518, 0.22333, 0.18596, 0.11146, 0.35699, 0.01764, 0.5079, 0.0032, 0.16081, 0.39294, 0.33687, 0.45068, 0.52048, 0.51203, 0.72169, 0.56977, 0.90781, 0.67442, 0.30921, 0.20168, 0.47269, 0.21612, 0.6412, 0.29912, 0.81223, 0.42542, 0.91787, 0.52646, 0.20609, 0.59142, 0.34945, 0.72133, 0.51293, 0.78629, 0.71666, 0.79712, 0.85499, 0.80433], "triangles": [6, 30, 31, 6, 7, 30, 8, 29, 7, 7, 29, 30, 6, 31, 5, 9, 28, 8, 8, 28, 29, 9, 10, 28, 31, 21, 5, 31, 30, 20, 30, 29, 20, 29, 19, 20, 29, 28, 19, 10, 27, 28, 10, 11, 27, 27, 18, 28, 28, 18, 19, 11, 17, 27, 11, 12, 17, 27, 17, 18, 12, 13, 17, 5, 21, 4, 21, 31, 20, 21, 20, 26, 21, 26, 4, 26, 20, 25, 26, 3, 4, 19, 24, 20, 20, 24, 25, 26, 25, 3, 18, 23, 19, 19, 23, 24, 17, 22, 18, 18, 22, 23, 24, 1, 25, 25, 2, 3, 25, 1, 2, 17, 14, 22, 17, 13, 14, 23, 0, 24, 24, 0, 1, 22, 15, 23, 23, 16, 0, 23, 15, 16, 22, 14, 15], "vertices": [1, 11, 19.46, 4.64, 1, 1, 11, 24.09, 2.8, 1, 2, 11, 27.99, 0.35, 0.95767, 12, 21.9, 14.52, 0.04233, 2, 11, 31.59, -2.94, 0.83585, 12, 26.54, 13.03, 0.16415, 2, 11, 32.58, -8.72, 0.64439, 12, 29.84, 8.18, 0.35561, 2, 11, 30.5, -12.94, 0.45191, 12, 29.71, 3.48, 0.54809, 2, 11, 28.18, -15.59, 0.26002, 12, 28.7, 0.1, 0.73998, 2, 11, 21.56, -16.22, 0.11815, 12, 22.94, -3.22, 0.88185, 2, 11, 14.65, -15.7, 0.01963, 12, 16.44, -5.62, 0.98037, 1, 12, 10.72, -6.61, 1, 1, 12, 6.08, -6.04, 1, 2, 11, 1.78, -8.31, 0.00614, 12, 1.67, -4.25, 0.99386, 2, 11, -0.59, -4.32, 0.33947, 12, -2.15, -1.6, 0.66053, 2, 11, 1.08, 0.38, 0.67281, 12, -2.58, 3.37, 0.32719, 1, 11, 5.31, 3.1, 1, 1, 11, 10.88, 5.44, 1, 1, 11, 15.85, 5.93, 1, 2, 11, 4.69, -3.4, 0.23036, 12, 2.27, 1.43, 0.76964, 2, 11, 10.54, -4.54, 0.33799, 12, 8.07, 2.82, 0.66201, 2, 11, 16.65, -5.75, 0.39766, 12, 14.12, 4.26, 0.60234, 2, 11, 23.33, -6.86, 0.54159, 12, 20.66, 6.02, 0.45841, 2, 11, 29.54, -9.06, 0.57908, 12, 27.23, 6.6, 0.42092, 1, 11, 9.44, 1.16, 1, 1, 11, 14.85, 1, 1, 2, 11, 20.47, -0.73, 0.98246, 12, 15.51, 10.42, 0.01754, 2, 11, 26.2, -3.44, 0.874, 12, 21.86, 10.33, 0.126, 2, 11, 29.76, -5.65, 0.76967, 12, 26.01, 9.8, 0.23033, 1, 12, 5.64, -2, 1, 1, 12, 11.21, -2.56, 1, 1, 12, 16.71, -1.5, 1, 2, 11, 23.33, -12.09, 0.19254, 12, 22.84, 1.27, 0.80746, 2, 11, 27.9, -12.11, 0.38295, 12, 27, 3.15, 0.61705], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 40, "height": 28}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [21.55, -4.11, -4.41, -5.6, -5.15, 7.38, 20.8, 8.86], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 16}}, "leg_L2": {"leg_L2": {"type": "mesh", "uvs": [0.43457, 0, 0.68357, 0.03791, 0.83957, 0.13003, 0.95357, 0.29123, 0.99557, 0.47547, 0.95657, 0.69297, 0.89657, 0.86953, 0.69557, 0.83626, 0.48857, 0.86697, 0.36557, 0.94117, 0.26657, 1, 0.14957, 0.91047, 0.07757, 0.73391, 0.02657, 0.542, 0.00557, 0.34497, 0.06857, 0.1505, 0.22457, 0.04814, 0.53965, 0.08142, 0.51561, 0.29124, 0.50657, 0.50106, 0.50954, 0.70577, 0.30552, 0.77995, 0.23057, 0.53173, 0.21861, 0.28096, 0.28465, 0.09418, 0.67764, 0.10191, 0.75861, 0.27847, 0.79157, 0.49598, 0.78854, 0.67765], "triangles": [10, 11, 9, 11, 21, 9, 9, 21, 8, 11, 12, 21, 6, 28, 5, 8, 20, 7, 8, 21, 20, 7, 20, 28, 20, 21, 22, 21, 12, 22, 12, 13, 22, 28, 20, 27, 19, 26, 27, 22, 19, 20, 27, 20, 19, 28, 27, 5, 5, 27, 4, 26, 19, 18, 13, 14, 22, 14, 23, 22, 22, 23, 19, 19, 23, 18, 27, 3, 4, 27, 26, 3, 14, 15, 23, 23, 24, 18, 18, 24, 17, 18, 25, 26, 18, 17, 25, 17, 24, 0, 26, 2, 3, 24, 15, 16, 24, 23, 15, 26, 25, 2, 25, 1, 2, 25, 17, 1, 24, 16, 0, 17, 0, 1, 7, 28, 6], "vertices": [1, 40, -2.07, -3.85, 1, 2, 41, -23.5, 7.02, 0.00038, 40, -1.18, 3.43, 0.99962, 2, 41, -19.96, 11.23, 0.00919, 40, 1.69, 8.13, 0.99081, 2, 41, -14.19, 14.01, 0.05029, 40, 6.98, 11.73, 0.94971, 2, 41, -7.84, 14.64, 0.19297, 40, 13.17, 13.3, 0.80703, 2, 41, -0.59, 12.82, 0.41006, 40, 20.61, 12.58, 0.58994, 2, 41, 5.23, 10.53, 0.64295, 40, 26.7, 11.18, 0.35705, 2, 41, 3.56, 4.83, 0.76239, 40, 25.9, 5.3, 0.23761, 2, 41, 4.04, -1.24, 0.62865, 40, 27.28, -0.64, 0.37135, 2, 41, 6.22, -5.03, 0.41897, 40, 30, -4.06, 0.58103, 2, 41, 7.94, -8.07, 0.19321, 40, 32.15, -6.81, 0.80679, 2, 41, 4.59, -11.17, 0.10418, 40, 29.3, -10.37, 0.89582, 2, 41, -1.58, -12.69, 0.03986, 40, 23.43, -12.79, 0.96014, 2, 41, -8.21, -13.55, 0.00313, 40, 17, -14.63, 0.99687, 1, 40, 10.34, -15.61, 1, 1, 40, 3.64, -14.16, 1, 1, 40, -0.09, -9.84, 1, 1, 40, 0.53, -0.65, 1, 1, 40, 7.69, -0.95, 1, 1, 40, 14.83, -0.81, 1, 1, 40, 21.77, -0.34, 1, 2, 41, 0.6, -6.25, 0.05887, 40, 24.62, -6.1, 0.94113, 1, 40, 16.32, -8.74, 1, 1, 40, 7.82, -9.57, 1, 1, 40, 1.38, -8.01, 1, 1, 40, 1, 3.38, 1, 2, 41, -15.15, 8.42, 0.0073, 40, 6.86, 6.06, 0.9927, 2, 41, -7.7, 8.68, 0.09738, 40, 14.19, 7.43, 0.90262, 2, 41, -1.56, 8.02, 0.39582, 40, 20.37, 7.69, 0.60418], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 35, "height": 41}}, "leg_L3": {"leg_L3": {"type": "mesh", "uvs": [0.27237, 0.04082, 0.07581, 0.14025, 0.01137, 0.30928, 0.03715, 0.4609, 0.14992, 0.58519, 0.2337, 0.7393, 0.35292, 0.8959, 0.56559, 0.97793, 0.86204, 0.98788, 0.99092, 0.83625, 0.98126, 0.69953, 0.89104, 0.56779, 0.87492, 0.38136, 0.85559, 0.20985, 0.77826, 0.06816, 0.57526, 0, 0.37488, 0.14352, 0.39645, 0.35219, 0.41794, 0.5534, 0.4296, 0.73976, 0.49245, 0.89099, 0.71507, 0.91692, 0.73264, 0.75274, 0.69813, 0.53918, 0.62815, 0.32336, 0.60338, 0.1172], "triangles": [25, 15, 14, 16, 0, 15, 16, 15, 25, 13, 25, 14, 16, 25, 24, 1, 0, 16, 1, 17, 2, 16, 17, 1, 13, 24, 25, 17, 16, 24, 24, 13, 12, 2, 17, 3, 23, 24, 12, 18, 17, 24, 18, 24, 23, 4, 3, 17, 23, 12, 11, 18, 4, 17, 5, 4, 18, 19, 18, 23, 5, 18, 19, 22, 23, 11, 19, 23, 22, 20, 19, 22, 6, 5, 19, 6, 19, 20, 22, 11, 10, 22, 10, 9, 21, 20, 22, 21, 22, 9, 7, 20, 21, 6, 20, 7, 8, 21, 9, 7, 21, 8], "vertices": [1, 40, 19.32, -2.87, 1, 2, 41, -1.24, -7.89, 0.0406, 40, 23.05, -8, 0.9594, 2, 41, 4.47, -10.23, 0.40502, 40, 29.04, -9.46, 0.59498, 2, 41, 9.82, -10.07, 0.72635, 40, 34.31, -8.51, 0.27365, 2, 41, 14.46, -7.49, 0.93517, 40, 38.51, -5.26, 0.06483, 2, 41, 20.05, -5.78, 0.99834, 40, 43.79, -2.75, 0.00166, 1, 41, 25.83, -3.14, 1, 2, 42, 2.67, -2.44, 0.93652, 41, 29.27, 2.28, 0.06348, 1, 42, 10.68, -2.4, 1, 2, 42, 13.9, 3.07, 0.93473, 41, 25.5, 14.21, 0.06527, 3, 42, 13.4, 7.84, 0.73407, 41, 20.72, 14.44, 0.26554, 40, 41.44, 17.35, 0.00039, 3, 42, 10.74, 12.33, 0.37913, 41, 15.88, 12.48, 0.60536, 40, 36.95, 14.7, 0.01551, 3, 42, 9.99, 18.82, 0.08984, 41, 9.35, 12.71, 0.77245, 40, 30.45, 13.95, 0.13771, 3, 42, 9.17, 24.79, 0.01589, 41, 3.32, 12.8, 0.56832, 40, 24.48, 13.14, 0.41579, 3, 42, 6.85, 29.64, 0.00124, 41, -1.82, 11.23, 0.29995, 40, 19.63, 10.82, 0.69881, 2, 41, -4.75, 6.02, 0.04913, 40, 17.5, 5.23, 0.95087, 1, 40, 22.78, 0.07, 1, 2, 41, 7.02, -0.04, 0.99987, 40, 30.05, 1, 0.00013, 2, 41, 14.08, -0.17, 0.9998, 40, 37.05, 1.91, 0.0002, 1, 41, 20.6, -0.52, 1, 2, 42, 0.55, 0.5, 0.61604, 41, 26.04, 0.63, 0.38396, 1, 42, 6.6, -0.11, 1, 2, 42, 6.79, 5.65, 0.64703, 41, 21.89, 7.57, 0.35297, 3, 42, 5.49, 13.07, 0.18079, 41, 14.36, 7.4, 0.80274, 40, 36.2, 9.44, 0.01647, 3, 42, 3.24, 20.52, 0.02053, 41, 6.65, 6.29, 0.8362, 40, 28.74, 7.2, 0.14327, 3, 42, 2.21, 27.7, 0.00039, 41, -0.6, 6.36, 0.24547, 40, 21.57, 6.19, 0.75414], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 32, "height": 43}}, "leg_L4": {"leg_L4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [20.36, -4.91, -5.64, -4.91, -5.64, 9.09, 20.36, 9.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 31, "height": 16}}, "leg_L5": {"leg_L5": {"type": "mesh", "uvs": [0.78433, 0.06195, 0.9186, 0.17692, 0.98915, 0.33447, 0.99826, 0.52821, 0.95274, 0.69427, 0.90722, 0.84969, 0.83895, 0.99233, 0.67509, 0.96253, 0.46571, 0.92633, 0.27226, 0.96253, 0.09702, 1, 0.04923, 0.8284, 0.02192, 0.62614, 0.04923, 0.42389, 0.11978, 0.22163, 0.26771, 0.04279, 0.50668, 0.00234, 0.66371, 0, 0.60659, 0.24538, 0.56205, 0.41859, 0.5195, 0.57687, 0.46439, 0.79284, 0.70208, 0.84429, 0.76849, 0.62387, 0.8629, 0.43919, 0.83638, 0.23947, 0.38215, 0.17242, 0.28544, 0.35501, 0.21199, 0.56489, 0.2257, 0.80954], "triangles": [4, 5, 23, 21, 20, 22, 22, 20, 23, 12, 28, 29, 21, 28, 20, 4, 23, 3, 12, 13, 28, 20, 19, 23, 23, 19, 24, 23, 24, 3, 24, 18, 25, 18, 24, 19, 28, 27, 20, 20, 27, 19, 28, 13, 27, 24, 2, 3, 24, 25, 2, 13, 14, 27, 27, 26, 19, 19, 26, 18, 27, 14, 26, 25, 1, 2, 26, 16, 18, 18, 0, 25, 18, 17, 0, 18, 16, 17, 25, 0, 1, 14, 15, 26, 26, 15, 16, 10, 29, 9, 10, 11, 29, 7, 22, 6, 6, 22, 5, 7, 8, 22, 8, 9, 21, 9, 29, 21, 8, 21, 22, 5, 22, 23, 11, 12, 29, 29, 28, 21], "vertices": [2, 44, -22.35, 10.5, 0.00024, 43, -1.89, 3.76, 0.99976, 2, 44, -18.62, 14.23, 0.01197, 43, 0.63, 8.4, 0.98803, 2, 44, -13.65, 16.06, 0.05826, 43, 4.87, 11.57, 0.94174, 2, 44, -7.64, 16.06, 0.19526, 43, 10.63, 13.29, 0.80474, 2, 44, -2.55, 14.52, 0.44182, 43, 15.94, 13.25, 0.55818, 2, 44, 2.2, 12.99, 0.74894, 43, 20.94, 13.14, 0.25106, 2, 44, 6.54, 10.81, 0.91604, 43, 25.71, 12.29, 0.08396, 2, 44, 5.4, 6.11, 0.96433, 43, 25.96, 7.46, 0.03567, 1, 44, 4.01, 0.09, 1, 2, 44, 4.89, -5.56, 0.9942, 43, 28.8, -3.87, 0.0058, 2, 44, 5.83, -10.69, 0.97414, 43, 31.16, -8.52, 0.02586, 2, 44, 0.45, -11.84, 0.76903, 43, 26.33, -11.16, 0.23097, 2, 44, -5.85, -12.36, 0.35334, 43, 20.44, -13.45, 0.64666, 2, 44, -12.08, -11.29, 0.0982, 43, 14.17, -14.2, 0.9018, 2, 44, -18.25, -8.97, 0.01004, 43, 7.59, -13.73, 0.98996, 1, 43, 1.17, -10.92, 1, 1, 43, -1.73, -4.5, 1, 1, 43, -2.9, -0.1, 1, 2, 44, -16.89, 5.1, 5e-05, 43, 4.88, 0.14, 0.99995, 2, 44, -11.59, 3.57, 0.00038, 43, 10.4, 0.19, 0.99962, 2, 44, -6.74, 2.13, 0.00175, 43, 15.46, 0.18, 0.99825, 2, 44, -0.12, 0.23, 0.65982, 43, 22.34, 0.26, 0.34018, 2, 44, 1.78, 7.05, 0.82766, 43, 22.22, 7.33, 0.17234, 2, 44, -4.97, 9.28, 0.2955, 43, 15.12, 7.54, 0.7045, 2, 44, -10.57, 12.26, 0.10134, 43, 8.9, 8.81, 0.89866, 2, 44, -16.78, 11.77, 0.01425, 43, 3.08, 6.56, 0.98575, 1, 43, 4.26, -6.72, 1, 2, 44, -13.91, -4.35, 0.01484, 43, 10.43, -8.07, 0.98516, 2, 44, -7.5, -6.77, 0.17321, 43, 17.26, -8.56, 0.82679, 2, 44, 0.09, -6.7, 0.75836, 43, 24.52, -6.33, 0.24164], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 35, "height": 39}}, "leg_L6": {"leg_L6": {"type": "mesh", "uvs": [0.3473, 0, 0.60838, 0, 0.82238, 0.04928, 0.91654, 0.18073, 0.89942, 0.34276, 0.92082, 0.52925, 0.98074, 0.69433, 0.99358, 0.84108, 0.8823, 0.94196, 0.66402, 0.99699, 0.4543, 0.97253, 0.29166, 0.90528, 0.24886, 0.7463, 0.1547, 0.60568, 0.05198, 0.45893, 0, 0.2755, 0.07766, 0.09208, 0.4208, 0.18558, 0.46152, 0.40024, 0.46492, 0.57459, 0.44164, 0.78217, 0.75157, 0.86942, 0.75354, 0.65846, 0.69578, 0.44048, 0.69884, 0.19284, 0.65977, 0.06688, 0.19383, 0.18826, 0.25531, 0.42465, 0.28767, 0.63307], "triangles": [21, 22, 6, 21, 6, 7, 8, 21, 7, 9, 10, 21, 9, 21, 8, 25, 1, 2, 17, 0, 1, 17, 1, 25, 26, 16, 0, 17, 26, 0, 24, 25, 2, 17, 25, 24, 15, 16, 26, 27, 26, 17, 24, 2, 3, 4, 24, 3, 18, 17, 24, 23, 18, 24, 27, 17, 18, 15, 26, 27, 4, 23, 24, 14, 15, 27, 23, 4, 5, 19, 18, 23, 27, 18, 19, 13, 14, 27, 28, 13, 27, 19, 28, 27, 22, 23, 5, 19, 23, 22, 22, 5, 6, 12, 13, 28, 20, 28, 19, 20, 19, 22, 12, 28, 20, 21, 20, 22, 11, 12, 20, 10, 20, 21, 11, 20, 10], "vertices": [1, 43, 16.8, -4.27, 1, 2, 44, -6.69, 4.01, 0.08608, 43, 14.97, 2, 0.91392, 3, 44, -4.94, 9.35, 0.46065, 43, 15.13, 7.62, 0.53934, 45, 8.27, 30.55, 1e-05, 3, 44, -0.32, 11.68, 0.73467, 43, 18.89, 11.17, 0.26128, 45, 10.72, 26, 0.00405, 3, 44, 5.35, 11.22, 0.9155, 43, 24.45, 12.34, 0.04434, 45, 10.41, 20.32, 0.04016, 2, 44, 11.88, 11.72, 0.76933, 45, 11.09, 13.8, 0.23067, 2, 44, 17.66, 13.19, 0.38791, 45, 12.71, 8.06, 0.61209, 2, 44, 22.8, 13.48, 0.09694, 45, 13.14, 2.93, 0.90306, 1, 45, 10.43, -0.66, 1, 1, 45, 5.02, -2.7, 1, 2, 44, 27.33, -0.02, 0.94455, 45, -0.24, -1.95, 0.05545, 1, 44, 24.95, -4.08, 1, 2, 44, 19.38, -5.12, 0.99979, 43, 42.57, 0.68, 0.00021, 2, 44, 14.45, -7.45, 0.97277, 43, 38.5, -2.95, 0.02723, 2, 44, 9.3, -9.99, 0.8197, 43, 34.29, -6.86, 0.1803, 2, 44, 2.87, -11.25, 0.45174, 43, 28.49, -9.9, 0.54826, 2, 44, -3.54, -9.28, 0.08721, 43, 21.78, -9.84, 0.91279, 2, 44, -0.22, -0.72, 0.2366, 43, 22.52, -0.68, 0.7634, 2, 44, 7.3, 0.26, 0.99973, 45, -0.49, 18.07, 0.00027, 2, 44, 13.4, 0.31, 0.99796, 45, -0.27, 11.98, 0.00204, 1, 44, 20.67, -0.3, 1, 2, 44, 23.76, 7.43, 0.07302, 45, 7.11, 1.81, 0.92698, 2, 44, 16.38, 7.51, 0.64241, 45, 7, 9.19, 0.35759, 3, 44, 8.74, 6.11, 0.9434, 43, 29.16, 8.41, 0.00035, 45, 5.4, 16.79, 0.05624, 3, 44, 0.07, 6.23, 0.71538, 43, 20.82, 6.06, 0.28267, 45, 5.29, 25.46, 0.00196, 2, 44, -4.34, 5.28, 0.25712, 43, 16.86, 3.89, 0.74288, 2, 44, -0.15, -6.39, 0.21811, 43, 24.2, -6.1, 0.78189, 2, 44, 8.13, -4.9, 0.89236, 43, 31.71, -2.31, 0.10764, 2, 44, 15.43, -4.13, 0.99468, 43, 38.49, 0.51, 0.00532], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 31, "height": 43}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [19.84, -5.26, -7.16, -5.26, -7.16, 7.74, 19.84, 7.74], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 32, "height": 16}}, "leg_R2": {"leg_R2": {"type": "mesh", "uvs": [0.56337, 0.11948, 0.79192, 0.12939, 1, 0.12939, 0.96773, 0.32096, 0.95015, 0.50924, 0.96421, 0.67108, 0.97828, 0.87917, 0.8306, 0.98487, 0.59502, 1, 0.42976, 0.91551, 0.29966, 0.70411, 0.16957, 0.40354, 0.08518, 0.23178, 0, 0, 0.14847, 0, 0.34186, 0.07654, 0.57521, 0.29235, 0.59628, 0.50616, 0.62519, 0.69591, 0.66771, 0.88096, 0.83937, 0.86685, 0.83569, 0.68118, 0.81254, 0.51077, 0.80659, 0.3054, 0.28072, 0.22259, 0.35621, 0.41758, 0.42829, 0.58302, 0.49188, 0.73603, 0.55433, 0.87919], "triangles": [21, 4, 5, 20, 21, 5, 19, 18, 21, 20, 5, 6, 21, 20, 19, 7, 19, 20, 7, 20, 6, 8, 28, 19, 8, 19, 7, 16, 0, 1, 23, 1, 2, 16, 1, 23, 3, 23, 2, 17, 16, 23, 25, 16, 17, 3, 22, 23, 22, 17, 23, 3, 4, 22, 26, 25, 17, 21, 22, 4, 18, 17, 22, 18, 22, 21, 26, 17, 18, 10, 11, 25, 10, 25, 26, 27, 26, 18, 10, 26, 27, 28, 27, 18, 19, 28, 18, 9, 10, 27, 9, 27, 28, 9, 28, 8, 24, 14, 15, 12, 13, 14, 12, 14, 24, 11, 12, 24, 24, 0, 16, 0, 24, 15, 25, 24, 16, 11, 24, 25], "vertices": [3, 37, 29.78, 2.38, 0.75619, 38, -5.35, 1.87, 0.24319, 39, 3.08, 25.62, 0.00062, 3, 37, 30.35, 9.45, 0.06546, 38, -4.66, 8.93, 0.91189, 39, 10.09, 24.53, 0.02265, 2, 38, -4.33, 15.37, 0.96331, 39, 16.5, 23.84, 0.03669, 2, 38, 1.94, 14.05, 0.91387, 39, 14.83, 17.66, 0.08613, 2, 38, 8.11, 13.18, 0.70477, 39, 13.62, 11.54, 0.29523, 2, 38, 13.47, 13.34, 0.33634, 39, 13.48, 6.18, 0.66366, 2, 38, 20.35, 13.42, 3e-05, 39, 13.17, -0.69, 0.99997, 1, 39, 8.24, -3.67, 1, 1, 39, 0.93, -3.38, 1, 1, 38, 20.67, -3.62, 1, 2, 37, 48.77, -6.46, 0.06452, 38, 13.49, -7.29, 0.93548, 2, 37, 38.72, -10.15, 0.78748, 38, 3.38, -10.8, 0.21252, 2, 37, 32.96, -12.57, 0.99409, 38, -2.42, -13.12, 0.00591, 1, 37, 25.23, -14.94, 1, 1, 37, 25.39, -10.34, 1, 1, 37, 28.12, -4.44, 1, 3, 37, 35.49, 2.55, 0.00489, 38, 0.36, 1.94, 0.9912, 39, 2.83, 19.91, 0.00391, 2, 38, 7.44, 2.23, 0.96971, 39, 2.72, 12.82, 0.03029, 2, 38, 13.74, 2.8, 0.8252, 39, 2.94, 6.5, 0.1748, 2, 38, 19.91, 3.8, 0.02023, 39, 3.59, 0.29, 0.97977, 2, 38, 19.72, 9.14, 0.00246, 39, 8.93, 0.18, 0.99754, 2, 38, 13.6, 9.35, 0.39439, 39, 9.48, 6.28, 0.60561, 2, 38, 7.94, 8.92, 0.76228, 39, 9.37, 11.95, 0.23772, 3, 37, 36.17, 9.7, 0.00584, 38, 1.16, 9.09, 0.93409, 39, 9.91, 18.71, 0.06007, 2, 37, 32.87, -6.5, 0.99492, 38, -2.41, -7.05, 0.00508, 2, 37, 39.38, -4.38, 0.53841, 38, 4.14, -5.05, 0.46159, 2, 37, 44.92, -2.34, 0.06576, 38, 9.71, -3.1, 0.93424, 2, 37, 50.03, -0.54, 0.00179, 38, 14.85, -1.39, 0.99821, 2, 38, 19.67, 0.3, 0.7612, 39, 0.1, 0.72, 0.2388], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 37, "height": 41}}, "leg_R3": {"leg_R3": {"type": "mesh", "uvs": [0.59691, 0.01014, 0.35579, 0.03101, 0.13118, 0.14465, 0.05191, 0.30235, 0.09815, 0.47397, 0.01888, 0.58528, 0, 0.73835, 0.11797, 0.85662, 0.29964, 0.95867, 0.57049, 0.99809, 0.82482, 0.91692, 0.95033, 0.79169, 0.94703, 0.60384, 0.97015, 0.45541, 0.98997, 0.31858, 0.92061, 0.19567, 0.82482, 0.0658, 0.51569, 0.5189, 0.51696, 0.31897, 0.5491, 0.10902, 0.50721, 0.75566, 0.51058, 0.91165, 0.20964, 0.782, 0.29667, 0.61678, 0.29636, 0.40049, 0.32924, 0.1836, 0.77795, 0.20002, 0.77094, 0.42291, 0.79504, 0.63346, 0.77144, 0.82525], "triangles": [8, 21, 9, 9, 21, 10, 21, 29, 10, 10, 29, 11, 21, 20, 29, 29, 28, 11, 7, 22, 8, 8, 22, 21, 22, 20, 21, 7, 6, 22, 29, 20, 28, 28, 12, 11, 22, 23, 20, 22, 6, 23, 20, 17, 28, 20, 23, 17, 6, 5, 23, 17, 27, 28, 28, 27, 12, 5, 4, 23, 4, 24, 23, 23, 24, 17, 12, 27, 13, 17, 18, 27, 17, 24, 18, 4, 3, 24, 13, 27, 14, 27, 26, 14, 26, 15, 14, 27, 18, 26, 24, 3, 25, 24, 25, 18, 25, 3, 2, 18, 19, 26, 18, 25, 19, 26, 16, 15, 26, 19, 16, 25, 1, 19, 25, 2, 1, 19, 0, 16, 19, 1, 0], "vertices": [1, 37, -4.88, -1.47, 1, 1, 37, -2.74, -9.19, 1, 1, 37, 3.63, -15.74, 1, 1, 37, 11.34, -17.25, 1, 1, 37, 19.1, -14.55, 1, 1, 37, 24.66, -16.37, 1, 1, 37, 31.86, -15.94, 1, 2, 37, 36.79, -11.27, 0.99693, 38, 1.43, -11.89, 0.00307, 2, 37, 40.66, -4.64, 0.65647, 38, 5.41, -5.33, 0.34353, 1, 38, 6.09, 3.78, 1, 2, 37, 36.18, 12.22, 0.1096, 38, 1.22, 11.6, 0.8904, 2, 37, 29.75, 15.45, 0.38378, 38, -5.16, 14.95, 0.61622, 2, 37, 21.03, 14.05, 0.78262, 38, -13.9, 13.69, 0.21738, 2, 37, 14.02, 13.78, 0.95154, 38, -20.91, 13.54, 0.04846, 2, 37, 7.56, 13.49, 0.99308, 38, -27.38, 13.36, 0.00692, 2, 37, 2.18, 10.38, 0.99989, 38, -32.81, 10.34, 0.00011, 1, 37, -3.39, 6.36, 1, 1, 37, 19.17, -0.61, 1, 1, 37, 9.87, -1.95, 1, 1, 37, -0.05, -2.35, 1, 2, 37, 30.22, 0.74, 0.96048, 38, -4.94, 0.23, 0.03952, 1, 38, 2.32, 1.29, 1, 1, 37, 32.88, -8.79, 1, 1, 37, 24.78, -7.09, 1, 1, 37, 14.72, -8.59, 1, 1, 37, 4.48, -9.01, 1, 1, 37, 3.07, 5.75, 1, 2, 37, 13.47, 7.06, 0.98018, 38, -21.58, 6.83, 0.01982, 2, 37, 23.14, 9.29, 0.76896, 38, -11.87, 8.9, 0.23104, 2, 37, 32.17, 9.84, 0.24558, 38, -2.83, 9.3, 0.75442], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 40, "height": 58}}, "leg_R4": {"leg_R4": {"type": "mesh", "uvs": [0.72985, 0, 0.48686, 0.02433, 0.29786, 0.09103, 0.20528, 0.25939, 0.12814, 0.49762, 0.02786, 0.63739, 0, 0.72315, 0.12043, 0.83433, 0.27857, 0.95186, 0.49071, 1, 0.72214, 0.9328, 0.92271, 0.79938, 0.98057, 0.61197, 0.99214, 0.40233, 0.97285, 0.16727, 0.90728, 0.06245, 0.58486, 0.19326, 0.54142, 0.41274, 0.4751, 0.62942, 0.35055, 0.82294, 0.62994, 0.8089, 0.76846, 0.64599, 0.82588, 0.45712, 0.81873, 0.21541, 0.43111, 0.1809, 0.31957, 0.38885, 0.27091, 0.57237, 0.17295, 0.7049], "triangles": [8, 19, 9, 8, 7, 19, 7, 27, 19, 7, 6, 27, 27, 26, 19, 6, 5, 27, 27, 5, 26, 9, 20, 10, 9, 19, 20, 10, 20, 11, 19, 18, 20, 19, 26, 18, 20, 21, 11, 20, 18, 21, 11, 21, 12, 18, 17, 21, 21, 22, 12, 21, 17, 22, 5, 4, 26, 17, 23, 22, 17, 16, 23, 26, 25, 18, 18, 25, 17, 12, 22, 13, 26, 4, 25, 4, 3, 25, 22, 23, 13, 25, 24, 17, 17, 24, 16, 23, 14, 13, 25, 3, 24, 3, 2, 24, 16, 0, 23, 23, 15, 14, 23, 0, 15, 24, 1, 16, 16, 1, 0, 24, 2, 1], "vertices": [1, 34, -4.25, -1.07, 1, 2, 34, -0.75, -6.96, 0.99979, 35, -25.08, -6.97, 0.00021, 2, 34, 3.46, -10.89, 0.99027, 35, -20.84, -10.86, 0.00973, 2, 34, 9.74, -10.96, 0.87416, 35, -14.56, -10.88, 0.12584, 2, 34, 18.03, -9.67, 0.60743, 35, -6.28, -9.52, 0.39257, 2, 34, 23.51, -10.33, 0.3104, 35, -0.8, -10.13, 0.6896, 2, 34, 26.49, -9.86, 0.09347, 35, 2.18, -9.65, 0.90653, 2, 34, 28.59, -5.26, 0.0343, 35, 4.25, -5.02, 0.9657, 2, 34, 30.46, 0.41, 0.21799, 35, 6.07, 0.65, 0.78201, 2, 34, 29.57, 6.5, 0.53993, 35, 5.13, 6.74, 0.46007, 2, 34, 24.87, 11.51, 0.86604, 35, 0.38, 11.71, 0.13396, 2, 34, 18.45, 14.83, 0.9889, 35, -6.06, 14.97, 0.0111, 1, 34, 11.97, 13.74, 1, 1, 34, 5.31, 11.16, 1, 1, 34, -1.78, 7.45, 1, 1, 34, -4.31, 4.33, 1, 1, 34, 3.4, -2.14, 1, 2, 34, 10.72, -0.24, 0.99985, 35, -13.66, -0.16, 0.00015, 1, 34, 18.21, 1.03, 1, 2, 34, 25.64, 0.49, 0.2328, 35, 1.25, 0.69, 0.7672, 2, 34, 22.05, 7.45, 0.98521, 35, -2.4, 7.63, 0.01479, 1, 34, 15.42, 8.77, 1, 1, 34, 8.89, 7.65, 1, 1, 34, 1.45, 4.16, 1, 2, 34, 4.75, -6.25, 0.99924, 35, -19.58, -6.21, 0.00076, 2, 34, 12.48, -6.26, 0.94466, 35, -11.86, -6.15, 0.05534, 2, 34, 18.74, -4.99, 0.72225, 35, -5.61, -4.84, 0.27775, 2, 34, 23.97, -5.68, 0.17046, 35, -0.37, -5.49, 0.82954], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 34, "height": 41}}, "leg_R5": {"leg_R5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.78, -4.96, -8.2, -5.91, -8.69, 7.08, 16.29, 8.03], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 15}}, "leg_R6": {"leg_R6": {"type": "mesh", "uvs": [0.5875, 0.02198, 0.75771, 0.15087, 0.85228, 0.26687, 0.89389, 0.4312, 0.95819, 0.59231, 0.98467, 0.76954, 0.87368, 0.91515, 0.66942, 0.98281, 0.40842, 0.96992, 0.27981, 0.91515, 0.1739, 0.7637, 0.11338, 0.58326, 0.03394, 0.42215, 0, 0.3287, 0.09825, 0.1257, 0.29494, 0.01615, 0.44381, 0.17335, 0.4043, 0.3758, 0.45688, 0.63326, 0.62125, 0.82962, 0.85945, 0.7773, 0.76638, 0.55734, 0.74223, 0.33407, 0.25635, 0.1829, 0.22634, 0.40665, 0.28999, 0.66198, 0.38239, 0.80609, 0.65352, 0.18925], "triangles": [21, 3, 4, 20, 21, 4, 20, 4, 5, 19, 21, 20, 6, 20, 5, 19, 20, 6, 7, 19, 6, 8, 19, 7, 23, 14, 15, 13, 14, 23, 17, 23, 16, 24, 13, 23, 17, 24, 23, 12, 13, 24, 11, 12, 24, 21, 17, 22, 21, 18, 17, 24, 17, 18, 25, 11, 24, 18, 25, 24, 10, 11, 25, 26, 25, 18, 10, 25, 26, 19, 18, 21, 26, 18, 19, 9, 10, 26, 8, 26, 19, 9, 26, 8, 16, 15, 0, 27, 16, 0, 23, 15, 16, 1, 27, 0, 22, 27, 1, 22, 1, 2, 22, 2, 3, 21, 22, 3, 17, 27, 22, 27, 17, 16], "vertices": [1, 34, 17.35, 1.3, 1, 3, 34, 20.64, 5.37, 0.98142, 35, -3.79, 5.54, 0.00236, 36, 9.44, 17.91, 0.01622, 3, 34, 23.67, 7.68, 0.82075, 35, -0.78, 7.88, 0.10332, 36, 11.01, 14.43, 0.07593, 3, 34, 28.06, 8.84, 0.41459, 35, 3.6, 9.07, 0.30205, 36, 11.14, 9.89, 0.28336, 3, 34, 32.34, 10.52, 0.11935, 35, 7.86, 10.78, 0.20219, 36, 11.8, 5.35, 0.67846, 3, 34, 37.09, 11.35, 0.0019, 35, 12.61, 11.65, 0.00378, 36, 11.52, 0.53, 0.99432, 1, 36, 8.29, -2.87, 1, 1, 36, 3.34, -3.81, 1, 2, 35, 18.73, -1.29, 0.48586, 36, -2.5, -2.37, 0.51414, 2, 35, 17.41, -4.33, 0.93788, 36, -5.14, -0.37, 0.06212, 1, 35, 13.46, -6.98, 1, 1, 35, 8.67, -8.63, 1, 2, 34, 28.72, -10.92, 0.02218, 35, 4.42, -10.69, 0.97782, 2, 34, 26.24, -11.82, 0.06206, 35, 1.95, -11.61, 0.93794, 2, 34, 20.66, -9.81, 0.30639, 35, -3.65, -9.65, 0.69361, 2, 34, 17.5, -5.43, 0.69105, 35, -6.85, -5.29, 0.30895, 2, 34, 21.58, -1.82, 0.78873, 35, -2.79, -1.64, 0.21127, 1, 35, 2.72, -2.25, 1, 1, 35, 9.59, -0.67, 1, 3, 34, 39.1, 3.07, 4e-05, 35, 14.68, 3.39, 0.04347, 36, 3.01, 0.46, 0.95649, 3, 34, 37.43, 8.48, 0.00418, 35, 12.97, 8.79, 0.01708, 36, 8.65, 0.85, 0.97874, 3, 34, 31.6, 6.07, 0.15751, 35, 7.16, 6.33, 0.43814, 36, 7.63, 7.08, 0.40434, 3, 34, 25.6, 5.24, 0.7023, 35, 1.17, 5.45, 0.21436, 36, 8.19, 13.11, 0.08333, 2, 34, 22.04, -6.11, 0.37055, 35, -2.3, -5.93, 0.62945, 2, 34, 28.1, -6.52, 0.0154, 35, 3.77, -6.29, 0.9846, 1, 35, 10.57, -4.46, 1, 1, 35, 14.34, -2.13, 1, 2, 34, 21.79, 3.02, 0.99166, 36, 6.89, 17.33, 0.00834], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 29, "height": 32}}, "tails": {"tails": {"type": "mesh", "uvs": [0.85559, 0.77718, 0.9531, 0.61191, 1, 0.37012, 0.92176, 0.09467, 0.69886, 0, 0.4899, 0.07324, 0.36103, 0.25994, 0.2461, 0.40379, 0.04062, 0.45888, 0, 0.60273, 0, 0.78942, 0.11724, 0.92103, 0.31228, 0.99142, 0.58045, 0.96694, 0.72324, 0.89042, 0.21476, 0.67618, 0.40631, 0.60273, 0.5491, 0.53233, 0.74065, 0.47112, 0.89041, 0.44358, 0.74065, 0.19261, 0.59438, 0.29667, 0.36103, 0.43133, 0.1451, 0.53233, 0.23914, 0.84758, 0.48293, 0.84452, 0.61527, 0.75882, 0.76852, 0.6517, 0.89389, 0.53846], "triangles": [10, 9, 15, 9, 23, 15, 22, 23, 7, 9, 8, 23, 23, 8, 7, 11, 24, 12, 12, 25, 13, 12, 24, 25, 14, 13, 26, 11, 10, 24, 13, 25, 26, 14, 26, 0, 10, 15, 24, 24, 15, 25, 15, 16, 25, 25, 16, 26, 26, 27, 0, 16, 17, 26, 26, 17, 27, 16, 23, 22, 23, 16, 15, 16, 22, 17, 17, 22, 21, 22, 7, 6, 0, 27, 1, 17, 18, 27, 27, 28, 1, 27, 18, 28, 1, 28, 2, 18, 19, 28, 28, 19, 2, 17, 21, 18, 19, 18, 20, 18, 21, 20, 19, 20, 2, 22, 6, 21, 20, 3, 2, 6, 5, 21, 21, 5, 20, 5, 4, 20, 20, 4, 3], "vertices": [2, 7, 18.14, 11.4, 0.44276, 8, -3.45, 11.43, 0.55724, 2, 7, 12, 11.28, 0.80394, 8, -8.47, 7.89, 0.19606, 2, 7, 4.34, 8.68, 0.99805, 8, -13.35, 1.43, 0.00195, 1, 7, -2.57, 2.36, 1, 2, 7, -2.24, -4.82, 1, 8, -11.21, -13.43, 0, 3, 7, 2.77, -8.99, 0.97402, 8, -4.71, -14.07, 0.02398, 9, -12.36, -16.81, 0.002, 3, 7, 9.97, -9.34, 0.69033, 8, 1.44, -10.32, 0.24233, 9, -7.14, -11.85, 0.06733, 3, 7, 15.73, -10.01, 0.16116, 8, 6.58, -7.64, 0.40246, 9, -2.67, -8.15, 0.43639, 3, 7, 20.16, -14.39, 0.0031, 8, 12.71, -8.76, 0.02734, 9, 3.55, -7.96, 0.96956, 1, 9, 5.94, -3.69, 1, 2, 8, 18.77, 0.38, 0.12578, 9, 7.56, 2.25, 0.87422, 2, 8, 17.75, 5.8, 0.70462, 9, 5.42, 7.34, 0.29538, 2, 8, 13.79, 10.46, 0.98565, 9, 0.58, 11.07, 0.01435, 2, 7, 27.45, 7.36, 0.04326, 8, 6.52, 13.32, 0.95674, 2, 7, 23.26, 9.8, 0.16952, 8, 1.68, 12.99, 0.83048, 2, 8, 11.52, -0.07, 0.59356, 9, 0.57, 0.29, 0.40644, 1, 8, 5.47, 0.33, 1, 1, 8, 0.73, 0.17, 1, 2, 7, 10.85, 3.65, 0.96729, 8, -5.13, 0.93, 0.03271, 2, 7, 7.98, 7.04, 0.97744, 8, -9.41, 2.12, 0.02256, 2, 7, 2.77, -0.72, 0.99992, 8, -9.36, -7.23, 8e-05, 3, 7, 7.81, -2.82, 0.96013, 8, -4.01, -6.13, 0.03538, 9, -13.35, -8.9, 0.00449, 3, 7, 14.94, -6.65, 0.26202, 8, 4.04, -5.3, 0.55855, 9, -5.65, -6.39, 0.17943, 3, 7, 20.85, -10.57, 0.00745, 8, 11.13, -5.22, 0.07369, 9, 1.27, -4.82, 0.91886, 2, 8, 13.49, 5.27, 0.90633, 9, 1.38, 5.93, 0.09367, 2, 7, 25.25, 2.95, 0.01936, 8, 7.17, 8.44, 0.98064, 2, 7, 20.93, 4.98, 0.14469, 8, 2.46, 7.69, 0.85531, 2, 7, 15.71, 7.2, 0.56921, 8, -3.11, 6.59, 0.43079, 2, 7, 10.69, 8.62, 0.88826, 8, -8.06, 4.95, 0.11174], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 35, "height": 39}}, "tails_2": {"tails_2": {"type": "mesh", "uvs": [0.86035, 0.67857, 0.97513, 0.505, 0.98296, 0.31428, 0.90209, 0.15786, 0.67253, 0.04643, 0.46122, 0.025, 0.23426, 0.07, 0.08557, 0.23714, 0.04122, 0.43214, 0.02818, 0.67, 0.11426, 0.83928, 0.21079, 0.95928, 0.456, 1, 0.456, 0.93143, 0.4247, 0.78143, 0.50296, 0.66143, 0.68035, 0.68714, 0.71427, 0.34857, 0.48731, 0.40857, 0.24209, 0.45786, 0.24209, 0.65286, 0.29949, 0.865, 0.59949, 0.18786, 0.32818, 0.27571, 0.48209, 0.55428, 0.82383, 0.50714], "triangles": [17, 3, 2, 2, 25, 17, 1, 25, 2, 16, 15, 24, 0, 25, 1, 25, 16, 24, 0, 16, 25, 22, 5, 4, 22, 4, 3, 23, 6, 5, 23, 5, 22, 7, 6, 23, 17, 22, 3, 18, 23, 22, 18, 22, 17, 19, 8, 7, 23, 19, 7, 19, 23, 18, 24, 19, 18, 20, 19, 24, 24, 17, 25, 17, 24, 18, 19, 9, 8, 20, 24, 15, 20, 9, 19, 14, 20, 15, 10, 9, 20, 21, 10, 20, 14, 21, 20, 21, 14, 13, 11, 10, 21, 11, 21, 13, 12, 11, 13], "vertices": [3, 10, 9.43, 12.88, 0.05427, 9, -0.58, 9.6, 0.0628, 8, 12.35, 9.28, 0.88293, 3, 10, 5.39, 16.66, 0.01308, 9, -4.41, 5.61, 0.00825, 8, 7.77, 6.18, 0.97867, 3, 10, 0.27, 18.17, 0.00086, 9, -5.98, 0.5, 0.09339, 8, 5.16, 1.52, 0.90575, 2, 9, -5.34, -4.21, 0.39598, 8, 4.8, -3.23, 0.60402, 2, 9, -1.07, -8.61, 0.72905, 8, 8.05, -8.43, 0.27095, 2, 9, 3.46, -10.47, 0.96899, 8, 12.09, -11.19, 0.03101, 3, 10, -10.67, 3.21, 0.01746, 9, 8.83, -10.63, 0.98227, 8, 17.3, -12.48, 0.00026, 2, 10, -6.99, -1.28, 0.25943, 9, 13.36, -7.01, 0.74057, 2, 10, -1.96, -3.63, 0.59277, 9, 15.78, -2.01, 0.40723, 2, 10, 4.41, -5.59, 0.90863, 9, 17.82, 4.34, 0.09137, 1, 10, 9.49, -4.86, 1, 1, 10, 13.3, -3.55, 1, 1, 10, 15.82, 1.63, 1, 3, 10, 13.96, 2.11, 0.98948, 9, 10.26, 13.99, 0.00658, 8, 23.87, 11.29, 0.00394, 3, 10, 9.71, 2.46, 0.83044, 9, 9.84, 9.75, 0.0955, 8, 22.58, 7.23, 0.07406, 3, 10, 6.91, 5.04, 0.5383, 9, 7.22, 6.98, 0.15005, 8, 19.43, 5.07, 0.31165, 3, 10, 8.63, 8.81, 0.2277, 9, 3.48, 8.75, 0.15173, 8, 16.14, 7.59, 0.62057, 1, 9, 0.23, -0.2, 1, 3, 10, -0.03, 6.47, 0.00047, 9, 5.71, 0.05, 0.99889, 8, 16.5, -1.38, 0.00064, 1, 9, 11.51, -0.1, 1, 1, 10, 5.18, -0.7, 1, 1, 10, 11.26, -0.91, 1, 2, 9, 1.59, -5.23, 0.97481, 8, 11.36, -5.68, 0.02519, 1, 9, 8.26, -4.5, 1, 3, 10, 3.89, 5.33, 0.29859, 9, 6.9, 3.96, 0.53805, 8, 18.48, 2.19, 0.16336, 3, 10, 4.58, 13.27, 0.01285, 9, -1.03, 4.75, 0.00743, 8, 10.89, 4.63, 0.97972], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 28, "height": 34}}, "yinying": {"yinying": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-23.19, -49.76, -23.19, 49.24, 23.81, 49.24, 23.81, -49.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 99, "height": 47}}}}], "animations": {"attack": {"slots": {"head_3": {"attachment": [{"time": 0.4333, "name": null}]}}, "bones": {"body": {"rotate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 6.8, "curve": 0.672, "c3": 0.75}, {"time": 0.8333, "angle": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -10.76, "y": -11.45, "curve": "stepped"}, {"time": 0.5333, "x": -10.76, "y": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 12.35, "y": -6.76, "curve": 0.672, "c3": 0.75}, {"time": 0.8333, "x": 11.65, "y": -6.76, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -2.07, "curve": 0.25, "c3": 0.75}, {"time": 1.4333}]}, "body3": {"rotate": [{"angle": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 7.17, "curve": "stepped"}, {"time": 0.8333, "angle": 7.17, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.09, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 0.43}], "translate": [{"y": 0.61, "curve": "stepped"}, {"time": 0.5333, "y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 4.34, "y": 3.44, "curve": "stepped"}, {"time": 0.8333, "x": 4.34, "y": 3.44, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 0.61}]}, "hair_11": {"rotate": [{"angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -16.33, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 6.41, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -7.38}]}, "yinying": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -13.68, "curve": "stepped"}, {"time": 0.5333, "x": -13.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 11.4, "curve": "stepped"}, {"time": 0.8333, "x": 11.4, "curve": 0.672, "c3": 0.75}, {"time": 1.1667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 1.187, "curve": "stepped"}, {"time": 0.5333, "y": 1.187, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 1.208, "curve": "stepped"}, {"time": 0.8333, "y": 1.208, "curve": 0.672, "c3": 0.75}, {"time": 1.1667}]}, "body5": {"rotate": [{"angle": -2.36}]}, "leg_R_h": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": -12.11, "y": 3.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": -24.22, "y": 0.76, "curve": "stepped"}, {"time": 0.5333, "x": -24.22, "y": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "leg_L_H": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -11.27, "y": 2.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -22.53, "y": -1.83, "curve": "stepped"}, {"time": 0.5333, "x": -22.53, "y": -1.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 2.47, "y": -0.25, "curve": "stepped"}, {"time": 0.8333, "x": 2.47, "y": -0.25, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "leg_R_q": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": -7.5, "y": 5.08, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -14.99, "y": 0.04, "curve": "stepped"}, {"time": 0.5333, "x": -14.99, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 20.56, "y": 0.74, "curve": "stepped"}, {"time": 0.9333, "x": 20.56, "y": 0.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.0667, "x": 10.28, "y": 4.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.2}]}, "leg_L_q": {"translate": [{"time": 0.0667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -2.32, "y": 5.44, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "x": -6.34, "curve": "stepped"}, {"time": 0.5333, "x": -6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 19.92, "y": -2.27, "curve": "stepped"}, {"time": 1.0333, "x": 19.92, "y": -2.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.1667, "x": 9.96, "y": 3.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.3}]}, "leg_L8": {"rotate": [{"angle": 14.03}]}, "leg_L6": {"rotate": [{"angle": -7.45}]}, "leg_L4": {"rotate": [{"angle": 15.18}]}, "leg_L2": {"rotate": [{"angle": -8.35}]}, "leg_R2": {"rotate": [{"angle": 10.3}]}, "leg_R3": {"rotate": [{"angle": -3.39}]}, "leg_R6": {"rotate": [{"angle": 3.39}]}, "hair_13": {"rotate": [{"angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -16.22, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -7.27}]}, "hair_14": {"rotate": [{"angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -13.87, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 8.87, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -4.92}]}, "hair_3": {"rotate": [{"angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -17.06, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 5.68, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -8.11}]}, "hair_9": {"rotate": [{"angle": 2.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.38, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 9.5, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 2.42}], "scale": [{"y": 0.945}]}, "hair_6": {"rotate": [{"angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -2.78, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 3.52, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -5.17}]}, "hair_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 2.62, "curve": 0.25, "c3": 0.75}, {"time": 0.7667}]}, "leg_R4": {"rotate": [{"angle": -1.42}]}, "hair_8": {"rotate": [{"angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -8.37}]}, "hair_7": {"rotate": [{"angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.34, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -5.02}]}, "hair_10": {"rotate": [{"angle": 6.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -12.35, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 13.47, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 6.38}], "scale": [{"y": 0.896}]}, "ear_R3": {"rotate": [{"angle": -3.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 18.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -28.53, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 11.8, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -3.98}]}, "head_2": {"rotate": [{"angle": -2.19, "curve": "stepped"}, {"time": 0.5333, "angle": -2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -24.25, "curve": 0.672, "c3": 0.75}, {"time": 0.8333, "angle": 8.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.19}]}, "hair4": {"rotate": [{"angle": 12.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 33.8, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -10.44, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 29.55, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 12.81}]}, "hair_5": {"rotate": [{"angle": -0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -6.58, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 5.65, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -0.32}]}, "tails": {"rotate": [{"angle": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -40.61, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 10.56, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -7.12}], "translate": [{"x": -0.13, "y": 0.04}]}, "head": {"rotate": [{"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 27.17, "curve": 0.672, "c3": 0.75}, {"time": 0.8333, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "ear_L4": {"rotate": [{"angle": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -35.96, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 14.2, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -0.09}]}, "body4": {"rotate": [{"angle": 2.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -8.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 2.89, "curve": 0.672, "c3": 0.75}, {"time": 0.8333, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.55, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 2.89}], "translate": [{"x": 0.03, "y": 0.67, "curve": "stepped"}, {"time": 0.5333, "x": 0.03, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 1.25, "y": 3.89, "curve": "stepped"}, {"time": 0.8333, "x": 1.25, "y": 3.89, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.03, "y": 0.67}]}, "hair3": {"rotate": [{"angle": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -14.24, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 25.75, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 9.01}]}, "tails3": {"rotate": [{"angle": -9.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -43.13, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -9.64}]}, "tails_3": {"rotate": [{"angle": -9.47, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 4.01, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -42.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -9.47}]}, "tails_2": {"rotate": [{"angle": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 56.92, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 20.86, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -12.15}]}, "ear_R4": {"rotate": [{"angle": -0.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 22.05, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -25.29, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 15.03, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -0.75}]}, "ear_L3": {"rotate": [{"angle": -2.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 18.59, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -38.65, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 11.51, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -2.78}]}, "hair_2": {"rotate": [{"angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -16.33, "curve": 0.672, "c3": 0.75}, {"time": 1.0333, "angle": 6.41, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -7.38}]}}}, "idle": {"slots": {"eye_L": {"color": [{"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "eye_R": {"color": [{"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}}, "bones": {"leg_L8": {"rotate": [{"angle": 14.03}]}, "leg_L6": {"rotate": [{"angle": -7.45}]}, "leg_L4": {"rotate": [{"angle": 15.18}]}, "leg_L2": {"rotate": [{"angle": -8.35}]}, "leg_R2": {"rotate": [{"angle": 10.3}]}, "leg_R3": {"rotate": [{"angle": -3.39}]}, "leg_R6": {"rotate": [{"angle": 3.39}]}, "leg_R4": {"rotate": [{"angle": -1.42}]}, "hair_14": {"rotate": [{"angle": -4.92, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 26.92, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -4.92}]}, "hair_13": {"rotate": [{"angle": -7.27, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 22.62, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": -7.27}]}, "hair_3": {"rotate": [{"angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.11}]}, "hair_11": {"rotate": [{"angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 27.37, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.38}]}, "hair_6": {"rotate": [{"angle": -5.17, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "angle": -10.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 8.59, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": -5.17}]}, "hair_10": {"rotate": [{"angle": 6.38, "curve": 0.345, "c2": 0.37, "c3": 0.682, "c4": 0.72}, {"time": 0.1333, "angle": 3.03, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 17.48, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 6.38}], "scale": [{"y": 0.896, "curve": 0.345, "c2": 0.37, "c3": 0.682, "c4": 0.72}, {"time": 0.1333, "y": 0.951, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": 0.716, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "y": 0.896}]}, "hair_9": {"rotate": [{"angle": 2.42, "curve": 0.355, "c2": 0.43, "c3": 0.693, "c4": 0.78}, {"time": 0.1333, "angle": 0.66, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 11.26, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 2.42}], "scale": [{"y": 0.945, "curve": 0.355, "c2": 0.43, "c3": 0.693, "c4": 0.78}, {"time": 0.1333, "y": 0.985, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "y": 0.744, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "y": 0.945}]}, "hair_8": {"rotate": [{"angle": -8.37, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -12.17, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 9.73, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -8.37}]}, "hair_7": {"rotate": [{"angle": -5.02, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -5.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 9.73, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -5.02}]}, "hair_5": {"rotate": [{"angle": -0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.32}]}, "hair4": {"rotate": [{"angle": 12.81, "curve": 0.327, "c2": 0.32, "c3": 0.673, "c4": 0.68}, {"time": 0.2333, "angle": 3.91, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.4333, "angle": -2.22, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.5333, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 20.55, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": 12.81}]}, "hair3": {"rotate": [{"angle": 9.01, "curve": 0.343, "c2": 0.36, "c3": 0.688, "c4": 0.73}, {"time": 0.2333, "angle": 0.56, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4333, "angle": -3.64, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 20.55, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": 9.01}]}, "ear_L4": {"rotate": [{"angle": -0.09, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.2, "angle": -5.01, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.3, "angle": -6.15, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 13.27, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -0.09}]}, "ear_L3": {"rotate": [{"angle": -2.78, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -6.15, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 13.27, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -2.78}]}, "ear_R4": {"rotate": [{"angle": -0.75, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.2, "angle": -6.15, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.3, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 13.91, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -0.75}]}, "ear_R3": {"rotate": [{"angle": -3.98, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 12.31, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -3.98}]}, "head_2": {"rotate": [{"angle": -2.19, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -10.08, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -2.19}]}, "tails_3": {"rotate": [{"angle": -9.47, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "angle": -22.65, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 5.17, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": -9.47}]}, "tails_2": {"rotate": [{"angle": -12.15, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": -20.2, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 5.17, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -12.15}]}, "tails3": {"rotate": [{"angle": -9.64, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -13.13, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 6.97, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -9.64}]}, "tails": {"rotate": [{"angle": -7.12, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -7.57, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -7.12}], "translate": [{"x": -0.13, "y": 0.04, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -2.3, "y": 0.71, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "x": -0.13, "y": 0.04}]}, "body5": {"rotate": [{"angle": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.93, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.36}]}, "body4": {"rotate": [{"angle": 2.89, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -4.81, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 2.89}], "translate": [{"x": 0.03, "y": 0.67, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "x": 0.03, "y": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": 0.01, "y": -0.35, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "x": 0.03, "y": 0.67}]}, "body3": {"rotate": [{"angle": 0.43, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.29, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 0.43}], "translate": [{"y": 0.61, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "y": 0.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "y": -0.88, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "y": 0.61}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.085, "y": 1.085, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hair_2": {"rotate": [{"angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 14.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.38}]}}}, "run": {"bones": {"leg_L5": {"rotate": [{"angle": -133.18}, {"time": 0.1333, "angle": -87.54}, {"time": 0.2333, "angle": 33.46}, {"time": 0.3333, "angle": -0.68}, {"time": 0.3667, "angle": -7.33}, {"time": 0.4, "angle": -106.88}, {"time": 0.4667, "angle": -133.18}]}, "leg_L8": {"rotate": [{"angle": -47.23}, {"time": 0.1333, "angle": -70.4}, {"time": 0.2333, "angle": 58.85}, {"time": 0.3333, "angle": 6.42}, {"time": 0.3667, "angle": 18.22}, {"time": 0.4, "angle": -21.71}, {"time": 0.4667, "angle": -47.23}]}, "leg_L6": {"rotate": [{"angle": -57.83}, {"time": 0.1333, "angle": -5.38}, {"time": 0.2333, "angle": 8.57}, {"time": 0.3333, "angle": -16.3}, {"time": 0.3667, "angle": -1.66}, {"time": 0.4, "angle": -42.35}, {"time": 0.4667, "angle": -57.83}], "translate": [{"x": -5.03, "y": -0.3}, {"time": 0.1333, "x": -11.19, "y": -0.94}, {"time": 0.2333, "x": -19.06, "y": -3.92}, {"time": 0.3333, "x": -10.61, "y": -9.42}, {"time": 0.3667, "x": -13.82, "y": -8.86}, {"time": 0.4, "x": -11.39, "y": -6.95}, {"time": 0.4667, "x": -5.03, "y": -0.3}]}, "leg_L": {"rotate": [{"angle": 51.84}, {"time": 0.1333, "angle": -2.71}, {"time": 0.2333, "angle": -106.88}, {"time": 0.4, "angle": 27.32}, {"time": 0.4667, "angle": 51.84}]}, "leg_L4": {"rotate": [{}, {"time": 0.1333, "angle": -21.85}, {"time": 0.2333, "angle": -126}, {"time": 0.4, "angle": -45.93}, {"time": 0.4667}]}, "leg_L2": {"rotate": [{"angle": 59.4}, {"time": 0.1333, "angle": 23.37}, {"time": 0.2333, "angle": 6.79}, {"time": 0.4, "angle": 49.9}, {"time": 0.4667, "angle": 59.4}], "translate": [{}, {"time": 0.1333, "x": -3.22, "y": -16.62}, {"time": 0.2333, "x": -1.87, "y": -11.47}, {"time": 0.4, "x": -12.4, "y": -7.45}, {"time": 0.4667}]}, "leg_R": {"rotate": [{"angle": 11.7}, {"time": 0.1333, "angle": 3.16}, {"time": 0.2333, "angle": -103.94}, {"time": 0.4, "angle": -74.48}, {"time": 0.4667, "angle": 11.7}]}, "leg_R2": {"rotate": [{"angle": -16.65}, {"time": 0.1333, "angle": -2.41}, {"time": 0.2333, "angle": -140.31}, {"time": 0.3333, "angle": -117.61}, {"time": 0.4667, "angle": -16.65}], "translate": [{"time": 0.2333}, {"time": 0.3333, "x": -7.39, "y": 1.16}, {"time": 0.4667}]}, "leg_R3": {"rotate": [{"angle": 58.21}, {"time": 0.1333, "angle": -15.16}, {"time": 0.2333, "angle": 34.16}, {"time": 0.3333, "angle": 47.53}, {"time": 0.4667, "angle": 58.21}], "translate": [{"x": -8.29, "y": -1.18}, {"time": 0.1333, "x": 7.65, "y": -16.25}, {"time": 0.2333, "x": -10.64, "y": -3.48}, {"time": 0.3333, "x": -7.04, "y": -7.12}, {"time": 0.4667, "x": -8.29, "y": -1.18}]}, "leg_R5": {"rotate": [{"angle": -125.76}, {"time": 0.1333, "angle": -71.61}, {"time": 0.2333, "angle": 13.23}, {"time": 0.4, "angle": -4.24}, {"time": 0.4667, "angle": -125.76}]}, "leg_R6": {"rotate": [{"angle": -34.66}, {"time": 0.1333, "angle": -89.08}, {"time": 0.2333, "angle": 1.91}, {"time": 0.4, "angle": -36.84}, {"time": 0.4667, "angle": -34.66}]}, "leg_R4": {"rotate": [{"angle": -37.97}, {"time": 0.1333, "angle": 20.39}, {"time": 0.2333, "angle": 45.19}, {"time": 0.3667, "angle": 60.97}, {"time": 0.4, "angle": 17.37}, {"time": 0.4667, "angle": -37.97}], "translate": [{"y": 1.78}, {"time": 0.1333, "x": -1.61, "y": 9.92}, {"time": 0.2333, "x": -8.71, "y": 7.6}, {"time": 0.3667, "x": -8.65, "y": 2.67}, {"time": 0.4, "x": -7.09, "y": -0.5}, {"time": 0.4667, "y": 1.78}]}, "hair_14": {"rotate": [{"angle": -13.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.54}]}, "hair_13": {"rotate": [{"angle": -13.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.54}]}, "hair_3": {"rotate": [{"angle": -13.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.54}]}, "hair_11": {"rotate": [{"angle": -13.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.54}]}, "hair_2": {"rotate": [{"angle": -13.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 4.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.54}]}, "hair_6": {"rotate": [{"angle": 7.22}, {"time": 0.2333, "angle": -0.88}, {"time": 0.4667}]}, "hair_10": {"rotate": [{"angle": -3.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -23.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.37}]}, "hair_9": {"rotate": [{"angle": 1.96, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 1.96}]}, "hair_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -0.88, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}], "translate": [{"x": -1.23, "y": -3.86}]}, "hair_8": {"rotate": [{"angle": 10.39}, {"time": 0.2333, "angle": -24.21}, {"time": 0.4667}]}, "hair_7": {"rotate": [{"angle": 21.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 2.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 21.56}]}, "hair_5": {"rotate": [{"angle": 10.39, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.39}]}, "hair4": {"rotate": [{"angle": 24.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 24.15}]}, "hair3": {"rotate": [{"angle": 24.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 24.15}]}, "hair": {"rotate": [{"angle": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.52}], "translate": [{"x": -1.23, "y": 2.77, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.02, "y": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -1.23, "y": 2.77}]}, "ear_L4": {"rotate": [{"angle": 16.42, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 26.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -17.96, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 16.42}]}, "ear_L3": {"rotate": [{"angle": 26.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 26.37}]}, "ear_R4": {"rotate": [{"angle": 16.42, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": 26.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -17.96, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": 16.42}]}, "ear_R3": {"rotate": [{"angle": 26.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 26.37}]}, "head": {"rotate": [{"angle": 7.86, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 18.33}]}, "tails_3": {"rotate": [{"angle": 9.56, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.2, "angle": -43.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 13.97, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.4667, "angle": 9.56}]}, "tails_2": {"rotate": [{"angle": -9.2, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.1333, "angle": -43.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 13.97, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.4667, "angle": -9.2}]}, "tails3": {"rotate": [{"angle": -30.34, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.0667, "angle": -43.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 13.97, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.4667, "angle": -30.34}]}, "tails": {"rotate": [{"angle": -43.16, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 13.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -43.16}]}, "body5": {"rotate": [{"time": 0.1333}, {"time": 0.2333, "angle": 7.6}, {"time": 0.4}], "translate": [{"x": -2.62, "y": 4.37}, {"time": 0.1333, "x": -1.57, "y": 2.02}, {"time": 0.2333, "x": -3.66, "y": -2.74}, {"time": 0.4, "x": -3.43, "y": -1.98}, {"time": 0.4667, "x": -2.62, "y": 4.37}]}, "body4": {"rotate": [{"angle": 4.31}, {"time": 0.1333, "angle": 35.72}, {"time": 0.2333, "angle": 23.6}, {"time": 0.4667, "angle": 4.31}], "translate": [{"x": 12.79, "y": 3.04}, {"time": 0.1333, "x": 12.65, "y": 1.56}, {"time": 0.2333, "x": 4.42, "y": -0.7}, {"time": 0.4667, "x": 12.79, "y": 3.04}]}, "body3": {"rotate": [{}, {"time": 0.1333, "angle": -30.8}, {"time": 0.2333, "angle": -17.62}, {"time": 0.4, "angle": -9.47}, {"time": 0.4667}]}, "body": {"rotate": [{"time": 0.1333}, {"time": 0.2333, "angle": 4.4}, {"time": 0.4, "angle": 9.6}, {"time": 0.4667}], "translate": [{"y": 14.66, "curve": "stepped"}, {"time": 0.1333, "y": 14.66}, {"time": 0.2333, "x": -0.97, "y": 12.47}, {"time": 0.4, "x": -4.57, "y": 4.37}, {"time": 0.4667, "y": 14.66}]}, "yinying": {"scale": [{"x": 1.11, "y": 1.11}, {"time": 0.1}, {"time": 0.3333, "x": 1.257, "y": 1.257}, {"time": 0.4667, "x": 1.11, "y": 1.11}]}, "head_2": {"rotate": [{"angle": -16.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -16.37}]}}, "ik": {"leg_L_H": [{"mix": 0}], "leg_L_q": [{"mix": 0}], "leg_R_h": [{"mix": 0}], "leg_R_q": [{"mix": 0}]}, "deform": {"default": {"body": {"body": [{"vertices": [-2.39454, -10.41206, -0.59207, 10.66753, -0.70796, 10.66067, -0.4169, -3.44218, -0.55571, 3.42254, -0.59287, 3.41634, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61799, -2.57576, -0.58996, -2.58224, 3.24979, 5.37622, -1.62842, -6.06735, -1.56239, -6.08446, 2.94333, 4.35615, -1.6174, -5.00227, -1.56295, -5.01939, 1.37418, 1.89426, -0.79389, -2.20142, -0.76992, -2.20982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.58053, -5.81835, 4.70526, 7.4174, 4.62437, 7.46817, -11.22521, -10.77996, 7.78885, 13.47401, 7.64196, 13.55811, -8.41312, -13.20225, 4.41454, 15.01981, 4.25107, 15.06711, -0.62001, -3.37797, -0.34276, 3.4173, 0, 0, 0, 0, -2.96116, -4.4257, 1.6152, 5.07409, 2.38905, 1.8593, 2.3687, 1.8852, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91924, 1.06783, -0.58643, -1.28114, -0.57247, -1.28738, -0.2526, -2.76977, -0.22249, -2.77224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.95315, -9.23217, 0.27232, 9.68923, 0.16702, 9.69178, -7.51515, -10.35866, 4.34183, 12.03864, 4.21076, 12.08529, -5.90096, -5.95439, 4.01464, 7.3593, 3.93443, 7.4026]}, {"time": 0.1333, "vertices": [6.11814, -10.89183, -7.93683, 6.33742, -9.05233, 8.90919, 1.64012, -3.92189, -2.53261, 2.67867, -2.70386, 3.34436, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61799, -2.57576, -0.58996, -2.58224, 3.24979, 5.37622, -1.62842, -6.06735, -1.56239, -6.08446, 2.94333, 4.35615, -1.6174, -5.00227, -1.56295, -5.01939, 1.37418, 1.89426, -0.79389, -2.20142, -0.76992, -2.20982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.58053, -5.81835, 4.70526, 7.4174, 4.62437, 7.46817, -8.7454, -8.84491, 6.80188, 10.48744, 5.75095, 11.04454, -3.33969, -10.04762, 1.94849, 9.57833, 0.17302, 10.70119, -0.62001, -3.37797, -0.34276, 3.4173, 0, 0, 0, 0, -2.96116, -4.4257, 1.6152, 5.07409, 2.38905, 1.8593, 2.3687, 1.8852, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91924, 1.06783, -0.58643, -1.28114, -0.57247, -1.28738, -0.2526, -2.76977, -0.22249, -2.77224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.95315, -9.23217, 0.27232, 9.68923, 0.16702, 9.69178, -3.78325, -6.61051, 3.32099, 6.84891, 1.58247, 7.4953, -3.38862, -2.8404, 3.65557, 3.37436, 2.31873, 3.74221]}, {"time": 0.2333}, {"time": 0.4, "offset": 54, "vertices": [0.75846, 2.55973, 0.42208, -2.63617, 0.19589, -2.66253, 0.56287, 5.05541, 1.6769, -4.80234, 1.26155, -4.92773, 0.01793, 1.83349, 0.77611, -1.66124, 0.63171, -1.72133, -0.5019, 1.17844, 0.96184, -0.84589, 0.88626, -0.92476]}, {"time": 0.4667, "vertices": [-2.39454, -10.41206, -0.59207, 10.66753, -0.70796, 10.66067, -0.4169, -3.44218, -0.55571, 3.42254, -0.59287, 3.41634, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.61799, -2.57576, -0.58996, -2.58224, 3.24979, 5.37622, -1.62842, -6.06735, -1.56239, -6.08446, 2.94333, 4.35615, -1.6174, -5.00227, -1.56295, -5.01939, 1.37418, 1.89426, -0.79389, -2.20142, -0.76992, -2.20982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.58053, -5.81835, 4.70526, 7.4174, 4.62437, 7.46817, -11.22521, -10.77996, 7.78885, 13.47401, 7.64196, 13.55811, -8.41312, -13.20225, 4.41454, 15.01981, 4.25107, 15.06711, -0.62001, -3.37797, -0.34276, 3.4173, 0, 0, 0, 0, -2.96116, -4.4257, 1.6152, 5.07409, 2.38905, 1.8593, 2.3687, 1.8852, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.91924, 1.06783, -0.58643, -1.28114, -0.57247, -1.28738, -0.2526, -2.76977, -0.22249, -2.77224, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.95315, -9.23217, 0.27232, 9.68923, 0.16702, 9.69178, -7.51515, -10.35866, 4.34183, 12.03864, 4.21076, 12.08529, -5.90096, -5.95439, 4.01464, 7.3593, 3.93443, 7.4026]}]}}}}, "skill": {"slots": {"head_3": {"attachment": [{"time": 0.4333, "name": null}]}}, "bones": {"body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -10.76, "y": -11.45}]}, "body3": {"rotate": [{"angle": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -11.45}], "translate": [{"y": 0.61}]}, "hair_11": {"rotate": [{"angle": -7.38, "curve": 0.247, "c3": 0.726, "c4": 0.89}, {"time": 0.5, "angle": 3.61}]}, "yinying": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -13.68}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "y": 1.187}]}, "body5": {"rotate": [{"angle": -2.36}]}, "leg_R_h": {"translate": [{"curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1, "x": -12.11, "y": 3.94, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": -24.22, "y": 0.76}]}, "leg_L_H": {"translate": [{"time": 0.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "x": -11.27, "y": 2.64, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "x": -22.53, "y": -1.83}]}, "leg_R_q": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2667, "x": -7.5, "y": 5.08, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "x": -14.99, "y": 0.04}]}, "leg_L_q": {"translate": [{"time": 0.0667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": -2.32, "y": 5.44, "curve": 0.314, "c2": 0.27, "c3": 0.657, "c4": 0.64}, {"time": 0.2667, "x": -6.34}]}, "leg_L8": {"rotate": [{"angle": 14.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 51.51}]}, "leg_L6": {"rotate": [{"angle": -7.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -46.01}]}, "leg_L4": {"rotate": [{"angle": 15.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 73.08}]}, "leg_L2": {"rotate": [{"angle": -8.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -32.72}]}, "leg_R2": {"rotate": [{"angle": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 69.07}]}, "leg_R3": {"rotate": [{"angle": -3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -29.49}]}, "leg_R6": {"rotate": [{"angle": 3.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 37.35}]}, "hair_13": {"rotate": [{"angle": -7.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.95}]}, "hair_14": {"rotate": [{"angle": -4.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.3}]}, "hair_3": {"rotate": [{"angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.11}]}, "hair_9": {"rotate": [{"angle": 2.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.04}], "scale": [{"y": 0.945}]}, "hair_6": {"rotate": [{"angle": -5.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8.75}]}, "hair_4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.62}]}, "leg_R4": {"rotate": [{"angle": -1.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -41.83}]}, "hair_8": {"rotate": [{"angle": -8.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.27}]}, "hair_7": {"rotate": [{"angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.08}]}, "hair_10": {"rotate": [{"angle": 6.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.01}], "scale": [{"y": 0.896}]}, "ear_R3": {"rotate": [{"angle": -3.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 8}]}, "head_2": {"rotate": [{"angle": -2.19}]}, "hair4": {"rotate": [{"angle": 12.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 33.8}]}, "hair_5": {"rotate": [{"angle": -0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.79}]}, "tails": {"rotate": [{"angle": -7.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.36}], "translate": [{"x": -0.13, "y": 0.04}]}, "ear_L4": {"rotate": [{"angle": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 21.28}]}, "body4": {"rotate": [{"angle": 2.89, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -4.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.99}], "translate": [{"x": 0.03, "y": 0.67}]}, "hair3": {"rotate": [{"angle": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 30}]}, "tails3": {"rotate": [{"angle": -9.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 7.1}]}, "tails_3": {"rotate": [{"angle": -9.47, "curve": 0.243, "c3": 0.654, "c4": 0.62}, {"time": 0.5, "angle": -17.26}]}, "tails_2": {"rotate": [{"angle": -12.15, "curve": 0.242, "c3": 0.67, "c4": 0.68}, {"time": 0.5, "angle": 34.45}]}, "ear_R4": {"rotate": [{"angle": -0.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.23}]}, "ear_L3": {"rotate": [{"angle": -2.78, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 18.59}]}, "hair_2": {"rotate": [{"angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 3.84}]}}}, "skill1": {"bones": {"body": {"translate": [{"x": -10.76, "y": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -10.76, "y": -8.62, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -10.76, "y": -11.45}]}, "body3": {"rotate": [{"angle": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -8.86, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -11.45}], "translate": [{"y": 0.61}]}, "hair_11": {"rotate": [{"angle": 3.61}]}, "yinying": {"translate": [{"x": -13.68}], "scale": [{"y": 1.187}]}, "body5": {"rotate": [{"angle": -2.36}]}, "leg_R_h": {"translate": [{"x": -24.22, "y": 0.76}]}, "leg_L_H": {"translate": [{"x": -22.53, "y": -1.83}]}, "leg_R_q": {"translate": [{"x": -14.99, "y": 0.04}]}, "leg_L_q": {"translate": [{"x": -6.34}]}, "leg_L8": {"rotate": [{"angle": 51.51}]}, "leg_L6": {"rotate": [{"angle": -46.01}]}, "leg_L4": {"rotate": [{"angle": 73.08}]}, "leg_L2": {"rotate": [{"angle": -32.72}]}, "leg_R2": {"rotate": [{"angle": 69.07}]}, "leg_R3": {"rotate": [{"angle": -29.49}]}, "leg_R6": {"rotate": [{"angle": 37.35}]}, "hair_13": {"rotate": [{"angle": 3.95}]}, "hair_14": {"rotate": [{"angle": 6.3}]}, "hair_3": {"rotate": [{"angle": 3.11}]}, "hair_9": {"rotate": [{"angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.77, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.04}], "scale": [{"y": 0.945}]}, "hair_6": {"rotate": [{"angle": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.47, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 8.75}]}, "hair_4": {"rotate": [{"angle": 2.62}]}, "leg_R4": {"rotate": [{"angle": -41.83}]}, "hair_8": {"rotate": [{"angle": -1.27}]}, "hair_7": {"rotate": [{"angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.08}]}, "hair_10": {"rotate": [{"angle": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.01}], "scale": [{"y": 0.896}]}, "ear_R3": {"rotate": [{"angle": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 23.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 8}]}, "head_2": {"rotate": [{"angle": -2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -14.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.19}]}, "hair4": {"rotate": [{"angle": 33.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 40.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 33.8}]}, "hair_5": {"rotate": [{"angle": 6.79, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.79}]}, "tails": {"rotate": [{"angle": 6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -22.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.36}], "translate": [{"x": -0.13, "y": 0.04}]}, "head": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.59, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "ear_L4": {"rotate": [{"angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 30.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 21.28}]}, "body4": {"rotate": [{"angle": -8.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.99}], "translate": [{"x": 0.03, "y": 0.67}]}, "hair3": {"rotate": [{"angle": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 36.93, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 30}]}, "tails3": {"rotate": [{"angle": 7.1, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 8.79, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -20.05, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 7.1}]}, "tails_3": {"rotate": [{"angle": -17.26, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": 0.67, "curve": 0.382, "c2": 0.58, "c3": 0.732}, {"time": 1.1333, "angle": -55.89, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -17.26}]}, "tails_2": {"rotate": [{"angle": 34.45, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": 45.08, "curve": 0.378, "c2": 0.6, "c3": 0.722}, {"time": 1.0667, "angle": -3.81, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 34.45}]}, "ear_R4": {"rotate": [{"angle": 11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 27.22, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.23}]}, "ear_L3": {"rotate": [{"angle": 18.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 27.84, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 18.59}]}, "hair_2": {"rotate": [{"angle": 3.84}]}}}, "skill2": {"bones": {"body": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 6.8, "curve": 0.672, "c3": 0.75}, {"time": 0.3, "angle": -18.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}], "translate": [{"x": -10.76, "y": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 12.35, "y": -6.76, "curve": 0.672, "c3": 0.75}, {"time": 0.3, "x": 11.65, "y": -6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -2.07, "curve": 0.25, "c3": 0.75}, {"time": 0.9}]}, "body3": {"rotate": [{"angle": -11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 7.17, "curve": "stepped"}, {"time": 0.3, "angle": 7.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 2.09, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 0.43}], "translate": [{"y": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 4.34, "y": 3.44, "curve": "stepped"}, {"time": 0.3, "x": 4.34, "y": 3.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "y": 0.61}]}, "hair_11": {"rotate": [{"angle": 3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.33, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 6.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.38}]}, "yinying": {"translate": [{"x": -13.68, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 11.4, "curve": "stepped"}, {"time": 0.3, "x": 11.4, "curve": 0.672, "c3": 0.75}, {"time": 0.6333}], "scale": [{"y": 1.187, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 1.208, "curve": "stepped"}, {"time": 0.3, "y": 1.208, "curve": 0.672, "c3": 0.75}, {"time": 0.6333}]}, "body5": {"rotate": [{"angle": -2.36}]}, "leg_R_h": {"translate": [{"x": -24.22, "y": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.1}]}, "leg_L_H": {"translate": [{"x": -22.53, "y": -1.83, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 2.47, "y": -0.25, "curve": "stepped"}, {"time": 0.3, "x": 2.47, "y": -0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "leg_R_q": {"translate": [{"x": -14.99, "y": 0.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 20.56, "y": 0.74, "curve": "stepped"}, {"time": 0.4, "x": 20.56, "y": 0.74, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "x": 10.28, "y": 4.8, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667}]}, "leg_L_q": {"translate": [{"x": -6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 19.92, "y": -2.27, "curve": "stepped"}, {"time": 0.5, "x": 19.92, "y": -2.27, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6333, "x": 9.96, "y": 3.29, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.7667}]}, "leg_L8": {"rotate": [{"angle": 51.51}]}, "leg_L6": {"rotate": [{"angle": -46.01}]}, "leg_L4": {"rotate": [{"angle": 73.08}]}, "leg_L2": {"rotate": [{"angle": -32.72}]}, "leg_R2": {"rotate": [{"angle": 69.07}]}, "leg_R3": {"rotate": [{"angle": -29.49}]}, "leg_R6": {"rotate": [{"angle": 37.35}]}, "hair_13": {"rotate": [{"angle": 3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.22, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.27}]}, "hair_14": {"rotate": [{"angle": 6.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -13.87, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 8.87, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -4.92}]}, "hair_3": {"rotate": [{"angle": 3.11, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.06, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 5.68, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -8.11}]}, "hair_9": {"rotate": [{"angle": 5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.38, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 9.5, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.42}], "scale": [{"y": 0.945}]}, "hair_6": {"rotate": [{"angle": 8.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -2.78, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 3.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -5.17}]}, "hair_4": {"rotate": [{"angle": 2.62, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}, "leg_R4": {"rotate": [{"angle": -41.83}]}, "hair_8": {"rotate": [{"angle": -1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -8.37}]}, "hair_7": {"rotate": [{"angle": 2.08, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -9.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -5.02}]}, "hair_10": {"rotate": [{"angle": 9.01, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -12.35, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 13.47, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 6.38}], "scale": [{"y": 0.896}]}, "ear_R3": {"rotate": [{"angle": 8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -28.53, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -3.98}]}, "head_2": {"rotate": [{"angle": -2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -24.25, "curve": 0.672, "c3": 0.75}, {"time": 0.3, "angle": 8.67, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.19}]}, "hair4": {"rotate": [{"angle": 33.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -10.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 29.55, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 12.81}]}, "hair_5": {"rotate": [{"angle": 6.79, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -6.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 5.65, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -0.32}]}, "tails": {"rotate": [{"angle": 6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -40.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.56, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.12}], "translate": [{"x": -0.13, "y": 0.04}]}, "head": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 27.17, "curve": 0.672, "c3": 0.75}, {"time": 0.3, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}, "ear_L4": {"rotate": [{"angle": 21.28, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -35.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 14.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -0.09}]}, "body4": {"rotate": [{"angle": -8.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 2.89, "curve": 0.672, "c3": 0.75}, {"time": 0.3, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 4.55, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 2.89}], "translate": [{"x": 0.03, "y": 0.67, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 1.25, "y": 3.89, "curve": "stepped"}, {"time": 0.3, "x": 1.25, "y": 3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.03, "y": 0.67}]}, "hair3": {"rotate": [{"angle": 30, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -14.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 25.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 9.01}]}, "tails3": {"rotate": [{"angle": 7.1, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.3, "angle": -43.13, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.04, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -9.64}]}, "tails_3": {"rotate": [{"angle": -17.26, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.2333, "angle": 4.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -42.97, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 8.21, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -9.47}]}, "tails_2": {"rotate": [{"angle": 34.45, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1667, "angle": 56.92, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -26.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 20.86, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -12.15}]}, "ear_R4": {"rotate": [{"angle": 11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -25.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 15.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -0.75}]}, "ear_L3": {"rotate": [{"angle": 18.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -38.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 11.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.78}]}, "hair_2": {"rotate": [{"angle": 3.84, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -16.33, "curve": 0.672, "c3": 0.75}, {"time": 0.5, "angle": 6.41, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.38}]}}}}}