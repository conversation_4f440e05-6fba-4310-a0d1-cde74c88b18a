{"skeleton": {"hash": "dCzpI0D3InSCxMN1QWN+ZcWogjo", "spine": "3.8.99", "x": -61.34, "y": -13.36, "width": 125, "height": 98.31, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/哀嚎洞穴/变异守护者"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 25.73}, {"name": "yinying", "parent": "bone", "x": 4.69, "y": 5.92, "scaleX": 1.2452}, {"name": "body_2", "parent": "bone", "length": 20.87, "rotation": 19.61, "x": -14.69, "y": 38.3}, {"name": "body_3", "parent": "body_2", "length": 22.69, "rotation": 37.17, "x": 20.87}, {"name": "head_2", "parent": "body_3", "length": 34.38, "rotation": -75.58, "x": 16.23, "y": -0.45}, {"name": "head", "parent": "body_3", "length": 41.78, "rotation": -53.01, "x": 17.93, "y": -0.15}, {"name": "body_5", "parent": "body_2", "length": 16.28, "rotation": 154.25, "x": 5.45, "y": -4.5}, {"name": "body_6", "parent": "body_5", "length": 14.92, "rotation": -10.82, "x": 16.28}, {"name": "body_7", "parent": "body_6", "length": 14.48, "rotation": -8.69, "x": 14.92}, {"name": "body_8", "parent": "body_7", "length": 14.68, "rotation": -23.68, "x": 14.48}, {"name": "leg_R2", "parent": "body_2", "length": 25.34, "rotation": -84.61, "x": -6.84, "y": 6.44}, {"name": "leg_R", "parent": "leg_R2", "length": 8.1, "rotation": -62.23, "x": 25.81, "y": -0.51}, {"name": "leg_R4", "parent": "leg_R", "length": 10.29, "rotation": -97.93, "x": 8.33, "y": 0.34, "transform": "noRotationOrReflection"}, {"name": "hand_R3", "parent": "body_2", "length": 12.16, "rotation": -111.73, "x": 15.73, "y": 5.42}, {"name": "hand_R2", "parent": "hand_R3", "length": 11.57, "rotation": 88.41, "x": 11.83, "y": 0.74}, {"name": "hand_R", "parent": "hand_R2", "length": 8.11, "rotation": -84.16, "x": 12.36, "y": -0.55}, {"name": "hand_L3", "parent": "body_2", "length": 10.8, "rotation": -100.02, "x": 32.74, "y": -7.16}, {"name": "hand_L2", "parent": "hand_L3", "length": 10.11, "rotation": 45.07, "x": 10.88, "y": 0.44}, {"name": "hand_L", "parent": "hand_L2", "length": 8.36, "rotation": -63.95, "x": 10.67, "y": -0.53}, {"name": "leg_L", "parent": "body_2", "length": 17.63, "rotation": -118.91, "x": 14.96, "y": -13.72}, {"name": "leg_L3", "parent": "leg_L", "length": 8.95, "rotation": 17.98, "x": 18, "y": 0.52}, {"name": "leg_L2", "parent": "leg_L3", "length": 17.66, "rotation": -7.52, "x": 8.28, "y": 0.56, "transform": "noRotationOrReflection"}, {"name": "leg_R3", "parent": "root", "x": -12.92, "y": 12.71, "color": "ff3f00ff"}, {"name": "leg_L4", "parent": "root", "x": 7.47, "y": 4, "color": "ff3f00ff"}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "leg_L3", "bone": "leg_L3", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L", "attachment": "leg_L"}, {"name": "body_3", "bone": "body_5", "attachment": "body_3"}, {"name": "hand_L3", "bone": "hand_L3", "attachment": "hand_L3"}, {"name": "hand_L2", "bone": "hand_L2", "attachment": "hand_L2"}, {"name": "hand_L", "bone": "hand_L", "attachment": "hand_L"}, {"name": "body_2", "bone": "body_2", "attachment": "body_2"}, {"name": "body", "bone": "body_3", "attachment": "body"}, {"name": "leg_R3", "bone": "leg_R4", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R2", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "hand_R3", "bone": "hand_R3", "attachment": "hand_R3"}, {"name": "hand_R2", "bone": "hand_R2", "attachment": "hand_R2"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}, {"name": "head_2", "bone": "head_2", "attachment": "head_2"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "biyan", "bone": "head", "color": "ffffff00", "attachment": "biyan"}], "ik": [{"name": "leg_L", "order": 1, "bones": ["leg_L", "leg_L3"], "target": "leg_L4"}, {"name": "leg_R", "bones": ["leg_R2", "leg_R"], "target": "leg_R3", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"x": 10.72, "y": 7.03, "rotation": -3.77, "width": 19, "height": 16}}, "body": {"body": {"x": 22.38, "y": -2.9, "rotation": -56.78, "width": 40, "height": 41}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0, 0.41339, 0.13879, 0.29994, 0.28279, 0.23303, 0.41879, 0.16612, 0.52013, 0.11958, 0.57079, 0.01194, 0.70146, 0, 0.83479, 0.04685, 0.92813, 0.14867, 0.98679, 0.25339, 1, 0.36685, 1, 0.47158, 0.95479, 0.57339, 0.89079, 0.68394, 0.83479, 0.78285, 0.74679, 0.86721, 0.63479, 0.9283, 0.51746, 0.97776, 0.40546, 1, 0.29346, 1, 0.16546, 0.97485, 0.06679, 0.90212, 0.00279, 0.79739, 0, 0.65776, 0, 0.52976, 0.10788, 0.65822, 0.23668, 0.64568, 0.40228, 0.58797, 0.53568, 0.51772, 0.64838, 0.46503, 0.75188, 0.3195, 0.81858, 0.16645], "triangles": [15, 29, 14, 14, 29, 13, 13, 29, 12, 29, 30, 12, 12, 30, 10, 10, 30, 31, 11, 12, 10, 31, 9, 10, 9, 31, 8, 3, 4, 29, 29, 4, 30, 6, 4, 5, 30, 4, 31, 31, 4, 6, 31, 7, 8, 31, 6, 7, 27, 18, 26, 17, 27, 16, 27, 28, 16, 16, 28, 15, 28, 29, 15, 27, 26, 1, 1, 2, 27, 27, 2, 28, 2, 3, 28, 28, 3, 29, 17, 18, 27, 20, 26, 19, 20, 21, 26, 26, 21, 25, 26, 24, 1, 21, 22, 25, 22, 23, 25, 26, 25, 24, 25, 23, 24, 24, 0, 1, 18, 19, 26], "vertices": [4, 8, 11.14, -8.71, 0.75773, 7, 25.58, -10.64, 0, 3, -12.97, 16.2, 0.23225, 4, -17.18, 33.36, 0.01002, 3, 8, 5, -17.1, 0.06055, 3, -3.03, 19.28, 0.82815, 4, -7.4, 29.81, 0.1113, 2, 3, 6.34, 19.85, 0.72838, 4, 0.42, 24.6, 0.27162, 2, 3, 15.26, 20.58, 0.50875, 4, 7.97, 19.79, 0.49125, 2, 3, 21.85, 20.95, 0.2772, 4, 13.44, 16.1, 0.7228, 3, 8, -15.18, -39.81, 0.00654, 3, 26.7, 25.51, 0.11064, 4, 20.06, 16.8, 0.88282, 3, 8, -22.49, -42.73, 0.00101, 3, 34.31, 23.49, 0.03418, 4, 24.9, 10.6, 0.96481, 3, 8, -30.89, -42.6, 4e-05, 3, 40.98, 18.38, 0.00738, 4, 27.13, 2.5, 0.99258, 2, 3, 44.37, 11.23, 4e-05, 4, 25.51, -5.26, 0.99996, 3, 7, -32.34, -25.72, 0.00042, 3, 45.76, 4.62, 0.00036, 4, 22.62, -11.36, 0.99922, 3, 7, -33.8, -19.6, 0.00423, 3, 44.41, -1.53, 0.00618, 4, 17.83, -15.44, 0.98959, 3, 7, -34.41, -13.88, 0.01959, 3, 42.48, -6.95, 0.03216, 4, 13.01, -18.59, 0.94825, 2, 3, 38.04, -11.32, 0.11369, 4, 6.84, -19.39, 0.88631, 2, 3, 32.38, -15.76, 0.24957, 4, -0.35, -19.51, 0.75043, 2, 3, 27.39, -19.75, 0.42757, 4, -6.74, -19.68, 0.57243, 2, 3, 20.86, -22.35, 0.59589, 4, -13.51, -17.8, 0.40411, 3, 7, -15.31, 13.44, 0.064, 3, 13.4, -23.26, 0.69195, 4, -20.01, -14.02, 0.24405, 4, 8, -27.61, 11.93, 0.00591, 7, -8.6, 16.9, 0.008, 3, 5.86, -23.46, 0.8512, 4, -26.14, -9.62, 0.13489, 3, 8, -21.54, 15.06, 0.24238, 3, -0.88, -22.36, 0.72964, 4, -30.84, -4.67, 0.02798, 2, 8, -15.11, 17.02, 0.92364, 3, -7.21, -20.1, 0.07636, 3, 8, -7.36, 17.94, 0.17244, 7, 12.41, 19, 0.82753, 3, -13.98, -16.22, 3e-05, 3, 8, -0.53, 15.84, 0.37215, 7, 18.73, 15.65, 0.6278, 3, -18.22, -10.46, 6e-05, 3, 8, 4.82, 11.45, 0.64484, 7, 23.16, 10.34, 0.35504, 3, -19.9, -3.75, 0.00011, 3, 8, 7.22, 4.15, 0.85146, 7, 24.15, 2.72, 0.13648, 3, -17.48, 3.54, 0.01206, 4, 8, 9.27, -2.58, 0.91854, 7, 24.9, -4.28, 0.01342, 3, -15.12, 10.17, 0.06776, 4, -22.53, 29.85, 0.00029, 3, 8, 1.02, 2.29, 0.77732, 7, 17.71, 2.05, 0.22267, 3, -11.39, 1.34, 1e-05, 1, 7, 10.1, 0.54, 1, 2, 7, 0.56, -3.68, 0.1442, 3, 6.54, -0.94, 0.8558, 1, 3, 15.38, 0.01, 1, 2, 7, -13.4, -11.98, 0.00187, 4, 1.76, -0.74, 0.99813, 1, 4, 11.86, -1.56, 1, 1, 4, 21.09, -0.29, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 60, "height": 55}}, "body_3": {"body_3": {"type": "mesh", "uvs": [0, 0, 0, 0.15849, 0.0217, 0.31166, 0.06586, 0.46483, 0.12719, 0.618, 0.20569, 0.75202, 0.28664, 0.86307, 0.39948, 0.932, 0.51233, 0.98944, 0.64479, 1, 0.78217, 1, 0.89992, 0.93583, 0.97842, 0.79798, 1, 0.61034, 1, 0.39973, 0.95879, 0.26188, 0.87293, 0.17764, 0.77972, 0.15083, 0.62517, 0.13934, 0.54176, 0.16232, 0.44609, 0.19295, 0.31853, 0.17381, 0.21795, 0.13168, 0.12964, 0.07042, 0.0585, 0.02829, 0.09775, 0.15849, 0.15908, 0.28103, 0.27437, 0.37676, 0.36023, 0.45334, 0.50742, 0.50695, 0.58347, 0.55673, 0.62762, 0.57971, 0.73311, 0.5912, 0.86558, 0.58737], "triangles": [25, 24, 23, 1, 0, 24, 1, 24, 25, 22, 25, 23, 26, 25, 22, 2, 1, 25, 2, 25, 26, 3, 2, 26, 27, 22, 21, 26, 22, 27, 27, 21, 28, 27, 4, 3, 27, 3, 26, 5, 4, 27, 5, 27, 28, 28, 21, 20, 29, 20, 19, 28, 20, 29, 29, 19, 30, 6, 5, 28, 7, 28, 29, 7, 29, 30, 6, 28, 7, 30, 19, 18, 32, 31, 18, 30, 18, 31, 33, 17, 16, 33, 16, 15, 33, 15, 14, 17, 32, 18, 33, 32, 17, 33, 14, 13, 12, 33, 13, 11, 33, 12, 8, 7, 30, 8, 30, 31, 9, 8, 31, 32, 9, 31, 32, 33, 11, 10, 9, 32, 11, 10, 32], "vertices": [1, 10, 15.26, -0.19, 1, 2, 9, 25.56, -0.45, 0.02976, 10, 10.33, 4.04, 0.97024, 3, 8, 36.96, 1.3, 0.00051, 9, 21.59, 4.61, 0.25181, 10, 4.66, 7.08, 0.74768, 3, 8, 32.42, 6.48, 0.02632, 9, 16.33, 9.05, 0.55247, 10, -1.94, 9.03, 0.42121, 4, 7, 44.76, 6.11, 0.0011, 8, 26.84, 11.34, 0.13676, 9, 10.07, 13.01, 0.74451, 10, -9.26, 10.15, 0.11763, 4, 7, 39.18, 11.03, 0.01489, 8, 20.43, 15.13, 0.33199, 9, 3.16, 15.79, 0.64625, 10, -16.7, 9.92, 0.00686, 3, 7, 33.54, 15, 0.08092, 8, 14.14, 17.98, 0.53094, 9, -3.48, 17.66, 0.38814, 3, 7, 26.06, 17.04, 0.2428, 8, 6.41, 18.58, 0.58368, 9, -11.21, 17.08, 0.17352, 3, 7, 18.63, 18.61, 0.50197, 8, -1.18, 18.72, 0.44828, 9, -18.74, 16.08, 0.04975, 3, 7, 10.15, 18.14, 0.76171, 8, -9.42, 16.66, 0.23108, 9, -26.57, 12.8, 0.00721, 3, 7, 1.41, 17.2, 0.93204, 8, -17.83, 14.1, 0.06793, 9, -34.5, 8.99, 3e-05, 2, 7, -5.8, 13.77, 0.99242, 8, -24.27, 9.39, 0.00758, 2, 7, -10.19, 7.62, 0.99998, 8, -27.43, 2.51, 2e-05, 2, 7, -10.74, -0.18, 0.99859, 8, -26.5, -5.25, 0.00141, 2, 7, -9.82, -8.76, 0.9902, 8, -23.99, -13.51, 0.0098, 2, 7, -6.59, -14.1, 0.96316, 8, -19.82, -18.14, 0.03684, 2, 7, -0.76, -16.95, 0.89911, 8, -13.55, -19.85, 0.10089, 3, 7, 5.29, -17.4, 0.71838, 8, -7.52, -19.16, 0.28029, 9, -19.29, -22.33, 0.00132, 3, 7, 15.17, -16.81, 0.48103, 8, 2.07, -16.72, 0.50281, 9, -10.17, -18.47, 0.01616, 3, 7, 20.38, -15.31, 0.22457, 8, 6.9, -14.27, 0.6667, 9, -5.77, -15.31, 0.10873, 4, 7, 26.33, -13.4, 0.08036, 8, 12.4, -11.28, 0.51735, 9, -0.79, -11.53, 0.39252, 10, -9.35, -16.69, 0.00977, 4, 7, 34.53, -13.31, 0.01142, 8, 20.43, -9.65, 0.26801, 9, 6.91, -8.7, 0.5604, 10, -3.43, -11.01, 0.16017, 3, 8, 27.09, -9.42, 0.03866, 9, 13.46, -7.48, 0.47985, 10, 2.07, -7.25, 0.48148, 3, 8, 33.23, -10.18, 0.00022, 9, 19.64, -7.29, 0.19474, 10, 7.66, -4.6, 0.80504, 2, 9, 24.5, -6.88, 0.01202, 10, 11.94, -2.28, 0.98798, 2, 9, 19.92, -3.16, 0.00365, 10, 6.25, -0.7, 0.99635, 2, 9, 14.21, -0.33, 0.62058, 10, -0.11, -0.4, 0.37942, 1, 9, 5.86, 0.02, 1, 2, 8, 14.54, 0.54, 0.76803, 9, -0.45, 0.47, 0.23197, 2, 7, 21.05, -1.02, 0.00062, 8, 4.89, -0.11, 0.99938, 2, 7, 16, 0.49, 0.88107, 8, -0.36, 0.42, 0.11893, 2, 7, 13.09, 1.12, 0.88107, 8, -3.34, 0.5, 0.11893, 1, 7, 6.32, 0.87, 1, 1, 7, -2.09, -0.2, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 60, 62], "width": 64, "height": 41}}, "hand_L": {"hand_L": {"x": 2.65, "y": -1.64, "rotation": 99.29, "width": 17, "height": 18}}, "hand_L2": {"hand_L2": {"x": 5.25, "y": 0.11, "rotation": 35.34, "width": 19, "height": 14}}, "hand_L3": {"hand_L3": {"x": 6.6, "y": -0.51, "rotation": 80.41, "width": 11, "height": 18}}, "hand_R": {"hand_R": {"x": 4.53, "y": -0.42, "rotation": 87.88, "width": 20, "height": 24}}, "hand_R2": {"hand_R2": {"x": 3.56, "y": -0.17, "rotation": 3.72, "width": 23, "height": 13}}, "hand_R3": {"hand_R3": {"x": 7.8, "y": -1.37, "rotation": 92.12, "width": 14, "height": 20}}, "head": {"head": {"x": 19.07, "y": 4.48, "rotation": -3.77, "width": 60, "height": 38}}, "head_2": {"head_2": {"x": 16.26, "y": 1.63, "rotation": 18.79, "width": 53, "height": 26}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0, 0.87385, 0.12938, 0.73507, 0.18283, 0.55246, 0.12556, 0.30412, 0.25156, 0.0996, 0.4692, 0, 0.73265, 0, 1, 0.28951, 1, 0.56707, 1, 0.78985, 0.99229, 0.94324, 0.79756, 1, 0.46156, 1, 0.20193, 1, 0.0072, 1, 0.60062, 0.10429, 0.55229, 0.34898, 0.5031, 0.60092, 0.46357, 0.86863], "triangles": [12, 13, 18, 11, 12, 18, 9, 10, 11, 15, 5, 6, 15, 4, 5, 16, 4, 15, 3, 4, 16, 2, 3, 16, 15, 6, 7, 7, 16, 15, 8, 16, 7, 17, 2, 16, 17, 16, 8, 17, 8, 9, 17, 1, 2, 11, 18, 17, 18, 1, 17, 17, 9, 11, 13, 14, 0, 13, 1, 18, 13, 0, 1], "vertices": [2, 20, 19.97, -9.95, 0.99995, 21, -3.59, -10.03, 5e-05, 1, 20, 16.36, -7.66, 1, 1, 20, 12.02, -7.18, 1, 1, 20, 6.59, -9.34, 1, 1, 20, 1.5, -7.37, 1, 2, 20, -1.54, -3.01, 0.99992, 21, -18.65, 6.81, 8e-05, 2, 20, -2.47, 2.71, 0.97623, 21, -16.57, 12.23, 0.02377, 2, 20, 3.15, 9.59, 0.87371, 21, -8.25, 15.33, 0.12629, 2, 20, 9.45, 10.62, 0.64934, 21, -2.29, 13.04, 0.35066, 2, 20, 14.5, 11.45, 0.39838, 21, 2.5, 11.2, 0.60162, 2, 20, 18.01, 11.85, 0.19484, 21, 5.73, 9.78, 0.80516, 2, 20, 19.99, 7.83, 0.14192, 21, 5.41, 5.31, 0.85808, 2, 20, 21.19, 0.54, 0.39615, 21, 2.76, -1.59, 0.60385, 2, 20, 22.11, -5.1, 0.70215, 21, 0.71, -6.92, 0.29785, 2, 20, 22.8, -9.33, 0.97952, 21, -0.82, -10.92, 0.02048, 2, 20, 0.36, 0.23, 0.99997, 21, -15.37, 8.65, 3e-05, 2, 20, 6.09, 0.09, 0.99975, 21, -10.5, 5.64, 0.00025, 1, 20, 11.98, -0.04, 1, 2, 20, 18.2, 0.09, 0.39767, 21, -0.05, -0.47, 0.60233], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 22, "height": 23}}, "leg_L2": {"leg_L2": {"type": "mesh", "uvs": [0, 0.65305, 0.16037, 0.31705, 0.36969, 0, 0.55463, 0, 0.73319, 0, 0.93032, 0.1333, 0.99739, 0.53755, 1, 0.8893, 0.87951, 1, 0.66206, 1, 0.45071, 1, 0.28407, 1, 0.12758, 1, 0, 1, 0.26388, 0.52387, 0.45069, 0.49817, 0.6746, 0.53877, 0.87837, 0.60143], "triangles": [1, 12, 0, 0, 12, 13, 1, 14, 12, 9, 17, 8, 8, 17, 7, 9, 16, 17, 9, 10, 16, 11, 15, 10, 10, 15, 16, 11, 14, 15, 17, 6, 7, 16, 4, 17, 17, 5, 6, 17, 4, 5, 15, 3, 16, 16, 3, 4, 15, 14, 2, 15, 2, 3, 12, 14, 11, 14, 1, 2], "vertices": [2, 21, 5.87, -7.76, 0.56263, 22, -8.19, -2.8, 0.43737, 2, 21, 4.72, -1.46, 0.68081, 22, -3.69, 1.75, 0.31919, 2, 21, 4.5, 6.06, 0.34748, 22, 2.34, 6.25, 0.65252, 2, 21, 7.25, 11.09, 0.11818, 22, 8.03, 6.88, 0.88182, 1, 22, 13.54, 7.49, 1, 1, 22, 19.79, 6.58, 1, 1, 22, 22.39, 1.99, 1, 2, 21, 23.24, 18.08, 2e-05, 22, 22.93, -2.2, 0.99998, 2, 21, 22.61, 14.16, 2e-05, 22, 19.37, -3.93, 0.99998, 2, 21, 19.38, 8.25, 2e-05, 22, 12.67, -4.68, 0.99998, 1, 22, 6.16, -5.4, 1, 1, 22, 1.02, -5.97, 1, 1, 22, -3.8, -6.51, 1, 1, 22, -7.73, -6.94, 1, 2, 21, 8.44, 0.16, 0.75028, 22, -0.23, -0.36, 0.24972, 1, 22, 5.49, 0.59, 1, 1, 22, 12.44, 0.87, 1, 1, 22, 18.81, 0.82, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 31, "height": 12}}, "leg_L3": {"leg_L3": {"type": "mesh", "uvs": [0.00959, 0.3071, 0.1997, 0.11257, 0.38538, 0, 0.6639, 0, 0.86285, 0.18331, 0.9999, 0.44415, 1, 0.78015, 0.84959, 0.93488, 0.66833, 1, 0.37654, 1, 0.14222, 0.83762, 0.04496, 0.55468, 0.48509, 0.20235, 0.55866, 0.47968, 0.68063, 0.83424], "triangles": [8, 14, 7, 8, 9, 14, 14, 9, 13, 7, 14, 6, 9, 10, 13, 13, 11, 12, 11, 13, 10, 6, 14, 5, 14, 13, 5, 13, 4, 5, 13, 12, 4, 12, 11, 0, 1, 12, 0, 1, 2, 12, 12, 3, 4, 12, 2, 3], "vertices": [1, 21, -1.6, -10.34, 1, 2, 20, 17.08, -7.22, 0.32022, 21, -4.71, -6.22, 0.67978, 2, 20, 13.79, -4.73, 0.58812, 21, -6.29, -2.41, 0.41188, 2, 20, 11.84, 0.18, 0.74703, 21, -5.49, 2.83, 0.25297, 2, 20, 13.68, 4.98, 0.47473, 21, -1.48, 6.04, 0.52527, 2, 20, 17.33, 9.23, 0.20883, 21, 3.81, 7.86, 0.79117, 2, 20, 23.26, 11.59, 0.04992, 21, 10.13, 6.9, 0.95008, 2, 20, 27.05, 10.02, 0.00332, 21, 12.6, 3.63, 0.99668, 2, 20, 29.47, 7.27, 0.00131, 21, 13.3, 0.04, 0.99869, 2, 20, 31.51, 2.12, 0.00131, 21, 12.47, -5.44, 0.99869, 1, 21, 8.75, -9.37, 1, 1, 21, 3.15, -10.39, 1, 1, 20, 16.66, -1.56, 1, 2, 20, 21.05, 1.69, 0.01018, 21, 3.22, -0.53, 0.98982, 1, 21, 10.23, 0.75, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 19, "height": 19}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.14882, 0.01379, 0.0643, 0.10846, 0, 0.25046, 0, 0.54392, 0.09473, 0.78059, 0.26715, 1, 0.57482, 1, 0.78444, 0.88472, 0.98729, 0.60072, 1, 0.36406, 0.84868, 0.14159, 0.69653, 0, 0.46325, 0, 0.25025, 0, 0.70102, 0.268, 0.51048, 0.40262, 0.33411, 0.59568], "triangles": [5, 16, 6, 16, 15, 6, 6, 15, 7, 5, 4, 16, 15, 14, 7, 1, 13, 16, 1, 0, 13, 16, 4, 3, 2, 1, 16, 15, 16, 13, 16, 3, 2, 7, 14, 8, 8, 14, 9, 9, 14, 10, 15, 13, 12, 14, 12, 11, 14, 15, 12, 14, 11, 10], "vertices": [2, 11, 19.34, -6.06, 0.48683, 12, 1.05, -8.46, 0.51317, 2, 11, 20.1, -8.2, 0.3692, 12, 3.26, -9.01, 0.6308, 2, 11, 21.67, -10.18, 0.25603, 12, 5.77, -8.8, 0.74397, 2, 11, 25.82, -11.63, 0.09426, 12, 9.28, -6.13, 0.90574, 3, 11, 29.83, -10.92, 0.01316, 12, 10.9, -2.4, 0.76566, 13, 1.74, -3.33, 0.22117, 3, 11, 34.13, -8.58, 0, 12, 11.33, 2.47, 0.00012, 13, 3.46, 1.25, 0.99988, 3, 11, 36.26, -2.48, 0.03063, 12, 7.42, 7.62, 0.29854, 13, 1.08, 7.26, 0.67083, 3, 11, 36.07, 2.24, 0.18937, 12, 3.38, 10.07, 0.44751, 13, -2.16, 10.71, 0.36311, 3, 11, 33.45, 7.67, 0.51614, 12, -2.59, 10.89, 0.33607, 13, -7.69, 13.1, 0.14779, 3, 11, 30.19, 9.09, 0.65345, 12, -5.58, 8.95, 0.25066, 13, -11.08, 12.03, 0.09588, 3, 11, 25.99, 7.19, 0.86317, 12, -6.31, 4.4, 0.10038, 13, -13.01, 7.85, 0.03645, 3, 11, 22.94, 4.87, 0.99374, 12, -6.07, 0.57, 0.00218, 13, -13.81, 4.1, 0.00409, 1, 11, 21.32, 0.24, 1, 2, 11, 19.85, -3.98, 0.62515, 12, -0.4, -6.89, 0.37485, 3, 11, 26.76, 3.64, 0.82147, 12, -2.93, 3.08, 0.14601, 13, -10.11, 5.67, 0.03252, 3, 11, 27.35, -0.81, 0.03045, 12, 1.1, 1.12, 0.9503, 13, -6.75, 2.69, 0.01925, 2, 11, 28.87, -5.26, 0.00042, 12, 5.65, -0.08, 0.99958], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 21, "height": 15}}, "leg_R2": {"leg_R2": {"type": "mesh", "uvs": [0.01224, 0.16423, 0.16901, 0.02923, 0.39111, 0, 0.63933, 0, 0.8353, 0.17267, 0.95724, 0.43845, 1, 0.61564, 1, 0.81392, 0.89627, 0.97423, 0.67417, 1, 0.48256, 0.99533, 0.31272, 0.86454, 0.17337, 0.71267, 0.05579, 0.51017, 0, 0.32033, 0.33866, 0.21583, 0.47536, 0.39619, 0.60404, 0.57119, 0.74095, 0.81121], "triangles": [15, 1, 2, 0, 1, 15, 14, 0, 15, 3, 15, 2, 16, 3, 4, 16, 15, 3, 17, 16, 4, 13, 14, 15, 13, 15, 16, 5, 17, 4, 17, 5, 6, 12, 13, 16, 12, 16, 17, 18, 17, 6, 18, 6, 7, 11, 12, 17, 11, 17, 18, 8, 18, 7, 10, 11, 18, 9, 10, 18, 9, 18, 8], "vertices": [2, 11, -1.51, -8.86, 0.99956, 12, -8.13, -27.39, 0.00044, 1, 11, -3.37, -2.63, 1, 1, 11, -1.31, 4, 1, 1, 11, 1.94, 10.98, 1, 1, 11, 9.52, 14.15, 1, 1, 11, 18.82, 13.98, 1, 2, 11, 24.52, 12.79, 0.99287, 12, -11.79, 6.27, 0.00713, 2, 11, 30.27, 10.1, 0.92389, 12, -6.38, 9.58, 0.07611, 2, 11, 33.56, 5.02, 0.64946, 12, -0.32, 9.52, 0.35055, 2, 11, 31.4, -1.57, 0.32742, 12, 3.97, 4.07, 0.67258, 2, 11, 28.76, -6.89, 0.22916, 12, 6.94, -1.07, 0.77084, 2, 11, 22.74, -9.89, 0.45095, 12, 6.12, -7.75, 0.54905, 2, 11, 16.51, -11.75, 0.77204, 12, 4.23, -13.97, 0.22796, 2, 11, 9.09, -12.32, 0.93883, 12, 0.61, -20.46, 0.06117, 2, 11, 2.86, -11.32, 0.99148, 12, -3.67, -25.1, 0.00852, 1, 11, 4.26, -0.39, 1, 1, 11, 11.29, 1.01, 1, 1, 11, 18.05, 2.26, 1, 2, 11, 26.8, 2.86, 0.98195, 12, -2.26, 2.69, 0.01805], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 31, "height": 32}}, "leg_R3": {"leg_R3": {"type": "mesh", "uvs": [0.0792, 0.1657, 0.28116, 0, 0.48312, 0, 0.70562, 0.10379, 0.80489, 0.29727, 0.80147, 0.47527, 0.92127, 0.59909, 1, 0.7384, 1, 0.95509, 0.87677, 1, 0.69193, 1, 0.4781, 1, 0.26929, 1, 0.08787, 0.98218, 0, 0.85062, 0, 0.59136, 0.00914, 0.36305, 0.47465, 0.19279, 0.44386, 0.43658, 0.42676, 0.62619, 0.45758, 0.76936], "triangles": [18, 0, 17, 8, 9, 7, 7, 9, 10, 12, 20, 11, 7, 10, 11, 6, 20, 5, 20, 7, 11, 19, 20, 12, 12, 13, 14, 7, 20, 6, 12, 14, 19, 14, 15, 19, 20, 19, 5, 5, 19, 18, 19, 15, 18, 15, 16, 18, 5, 18, 4, 4, 18, 17, 16, 0, 18, 4, 17, 3, 17, 0, 1, 17, 2, 3, 17, 1, 2], "vertices": [2, 12, 9.37, -10.84, 0.3853, 13, -2.01, -11.05, 0.6147, 2, 12, 3.78, -7.55, 0.69386, 13, -6.51, -6.37, 0.30614, 2, 12, 1.68, -2.73, 0.85478, 13, -7.23, -1.17, 0.14522, 2, 12, 1.56, 3.52, 0.65032, 13, -5.66, 4.89, 0.34968, 2, 12, 4.62, 7.67, 0.37225, 13, -1.61, 8.06, 0.62775, 2, 12, 8.41, 9.22, 0.15036, 13, 2.46, 8.53, 0.84964, 2, 12, 9.77, 13.21, 0.03353, 13, 4.85, 12.01, 0.96647, 2, 12, 11.9, 16.36, 0.00303, 13, 7.74, 14.48, 0.99697, 1, 13, 12.67, 15.17, 1, 1, 13, 14.14, 12.14, 1, 2, 12, 20.61, 11.42, 0.0016, 13, 14.8, 7.38, 0.9984, 2, 12, 22.83, 6.32, 0.0016, 13, 15.57, 1.87, 0.9984, 2, 12, 24.99, 1.34, 0.0016, 13, 16.32, -3.51, 0.9984, 1, 13, 16.56, -8.23, 1, 1, 13, 13.88, -10.91, 1, 1, 13, 7.97, -11.74, 1, 2, 12, 14.25, -10.7, 0.064, 13, 2.74, -12.23, 0.936, 1, 12, 5.84, -1.17, 1, 2, 12, 11.3, 0.34, 0.06389, 13, 2.86, -0.8, 0.93611, 2, 12, 15.47, 1.67, 0.00806, 13, 7.24, -0.64, 0.99194, 1, 13, 10.39, 0.61, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 26, "height": 23}}, "yinying": {"yinying": {"x": -0.71, "y": -1.28, "width": 76, "height": 36}}}}], "animations": {"attack": {"bones": {"hand_R2": {"rotate": [{"angle": 3.02, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.3333, "angle": -8.64, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.5, "angle": -5.88, "curve": 0.844, "c3": 0.75}, {"time": 0.7667, "angle": 2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 3.02}]}, "body_2": {"rotate": [{"angle": 3.46, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "angle": 11.9, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.4333, "angle": -4.94, "curve": 0.844, "c3": 0.75}, {"time": 0.7667, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 3.46}], "translate": [{"curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "x": -2.93, "y": -5.62, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.4333, "x": 23.01, "curve": 0.844, "c3": 0.75}, {"time": 0.7667}]}, "body_3": {"rotate": [{"angle": 3.68, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "angle": 36.86, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.4333, "angle": -8.74, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -0.55, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 3.68}], "translate": [{"curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "x": -1.53, "y": -1.24, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.4333, "x": 7.47, "y": -3.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "scale": [{"curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "x": 0.96, "y": 0.96, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.4333}]}, "head_2": {"rotate": [{"angle": -6.27, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.3667, "angle": -11.67, "curve": 0, "c2": 0.44, "c3": 0.75}, {"time": 0.6333}, {"time": 0.7667, "angle": -2.26}, {"time": 0.9333, "angle": -6.27}]}, "head": {"rotate": [{"angle": 1.84, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.3667, "angle": 25.82, "curve": 0, "c2": 0.44, "c3": 0.75}, {"time": 0.6333}, {"time": 0.7667, "angle": -2.26}, {"time": 0.9333, "angle": 1.84}]}, "body_5": {"rotate": [{"angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "angle": -2.64}], "translate": [{"x": 1.12, "y": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 1.12, "y": 2.63}], "scale": [{"x": 0.931, "y": 0.931, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": 0.931, "y": 0.931}]}, "leg_R2": {"rotate": [{"angle": 7.95}]}, "leg_R": {"rotate": [{"angle": 5.78}]}, "hand_R3": {"rotate": [{"angle": 3.38, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.3333, "angle": 61.72, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.5, "angle": -5.88, "curve": 0.844, "c3": 0.75}, {"time": 0.7667, "angle": 2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 3.38}]}, "hand_R": {"rotate": [{"angle": 2.31, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.4, "angle": 22.33, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.5667, "angle": -5.88, "curve": 0.844, "c3": 0.75}, {"time": 0.8333, "angle": 2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 2.31}]}, "hand_L3": {"rotate": [{"angle": 0.73, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.3333, "angle": 61.83, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.5, "angle": -5.88, "curve": 0.844, "c3": 0.75}, {"time": 0.7667, "angle": 2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.73}], "translate": [{"curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.3333, "x": -0.97, "y": 7.39, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.5}]}, "hand_L2": {"rotate": [{"angle": -0.73, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.3333, "angle": 22.33, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.5, "angle": -5.88, "curve": 0.844, "c3": 0.75}, {"time": 0.7667, "angle": 2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.73}]}, "hand_L": {"rotate": [{"angle": -2.03, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.4, "angle": 22.33, "curve": 0, "c2": 0.58, "c3": 0.75}, {"time": 0.5667, "angle": -5.88, "curve": 0.844, "c3": 0.75}, {"time": 0.8333, "angle": 2.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.03}]}, "leg_L": {"rotate": [{"angle": 12.53, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "angle": 8.83, "curve": "stepped"}, {"time": 0.7667, "angle": 8.83}, {"time": 0.9333, "angle": 12.53}], "translate": [{"x": -0.48, "y": -1.12, "curve": 0, "c2": 0.87, "c3": 0.75}, {"time": 0.2667, "x": 0.51, "y": 0.57, "curve": 0, "c2": 0.39, "c3": 0.429, "c4": 0.73}, {"time": 0.3333, "x": 5.38, "y": 4.91, "curve": 0.333, "c2": 0.62, "c3": 0.792}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7667}, {"time": 0.9333, "x": -0.48, "y": -1.12}]}, "leg_L3": {"rotate": [{"angle": -8.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.76, "curve": "stepped"}, {"time": 0.7667, "angle": 2.76}, {"time": 0.9333, "angle": -8.69}]}, "leg_L2": {"rotate": [{"time": 0.2667, "curve": 0, "c2": 0.39, "c3": 0.429, "c4": 0.73}, {"time": 0.3333, "angle": -17.15, "curve": 0.333, "c2": 0.62, "c3": 0.792}, {"time": 0.4333}]}, "leg_L4": {"translate": [{"time": 0.2667, "curve": 0, "c2": 0.39, "c3": 0.429, "c4": 0.73}, {"time": 0.3333, "x": 13.6, "y": 5.15, "curve": 0.333, "c2": 0.62, "c3": 0.792}, {"time": 0.4333, "x": 14.36, "y": -1.51, "curve": 0.844, "c3": 0.75}, {"time": 0.7667}]}, "body_6": {"rotate": [{"angle": -2.25, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.2333, "angle": -20.06, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 26.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.57, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 28.84, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.25}], "translate": [{"curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.2333, "x": -5.54, "y": -1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}], "scale": [{"x": 0.935, "y": 0.935}]}, "body_7": {"rotate": [{"angle": -1.5, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2333, "angle": -20.06, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 26.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.57, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 28.84, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -1.5}], "scale": [{"x": 0.943, "y": 0.943}]}, "body_8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -20.06, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 26.85, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.57, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 28.84, "curve": 0.25, "c3": 0.75}, {"time": 0.9333}]}}, "deform": {"default": {"leg_R2": {"leg_R2": [{"vertices": [5.00901, 2.11725, 0.60399, 5.40457, 1.34183, 1.95707, 0.95848, 1.39794, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.02628, 2.13347, -1.35862, 1.93887, 2.5638, 3.22117, -1.55506, 3.81203, 4.62425, 2.79214, -0.17285, 5.39916, 0.67357, 1.18223]}]}}}}, "idle": {"bones": {"leg_L3": {"rotate": [{"angle": -8.69}]}, "leg_L": {"rotate": [{"angle": 12.53}], "translate": [{"x": -0.48, "y": -1.12, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.48, "y": -1.12}]}, "hand_L": {"rotate": [{"angle": -2.03, "curve": 0.294, "c2": 0.21, "c3": 0.683, "c4": 0.72}, {"time": 0.5, "angle": 5.31, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.7, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -2.95, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 1.6667, "angle": -2.03}]}, "hand_L2": {"rotate": [{"angle": -0.73, "curve": 0.328, "c2": 0.31, "c3": 0.717, "c4": 0.84}, {"time": 0.5, "angle": 6.45, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.6, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -2.95, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 1.6667, "angle": -0.73}]}, "hand_L3": {"rotate": [{"angle": 0.73, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -2.95, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 0.73}]}, "hand_R": {"rotate": [{"angle": 2.31, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 3.38, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -2.81, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 2.31}]}, "hand_R2": {"rotate": [{"angle": 3.02, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 3.38, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -2.81, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 3.02}]}, "hand_R3": {"rotate": [{"angle": 3.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.38}]}, "leg_R": {"rotate": [{"angle": 5.78}]}, "leg_R2": {"rotate": [{"angle": 7.95}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.47, "y": -1.31, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "body_8": {"rotate": [{"angle": -0.57, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.89, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -0.57}], "scale": [{"x": 0.953, "y": 0.953, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "x": 0.931, "y": 0.931, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "x": 0.953, "y": 0.953}]}, "body_7": {"rotate": [{"angle": -1.5, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 3.89, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -1.5}], "scale": [{"x": 0.943, "y": 0.943, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "x": 0.931, "y": 0.931, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "x": 0.943, "y": 0.943}]}, "body_6": {"rotate": [{"angle": -2.25, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 3.89, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -2.25}], "scale": [{"x": 0.935, "y": 0.935, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "x": 0.931, "y": 0.931, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "x": 0.935, "y": 0.935}]}, "body_5": {"rotate": [{"angle": -2.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.89, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.64}], "translate": [{"x": 1.12, "y": 2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.12, "y": 2.63}], "scale": [{"x": 0.931, "y": 0.931, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.931, "y": 0.931}]}, "head": {"rotate": [{"angle": 1.84, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": 2.35, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 1.84}]}, "head_2": {"rotate": [{"angle": -6.27, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -8.01, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -6.27}]}, "body_3": {"rotate": [{"angle": 3.68, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 4.93, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.68, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 3.68}]}, "body_2": {"rotate": [{"angle": 3.46, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.61, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.46}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.42, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}}, "deform": {"default": {"leg_R2": {"leg_R2": [{"vertices": [5.00901, 2.11725, 0.60399, 5.40457, 1.34183, 1.95707, 0.95848, 1.39794, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.02628, 2.13347, -1.35862, 1.93887, 2.5638, 3.22117, -1.55506, 3.81203, 4.62425, 2.79214, -0.17285, 5.39916, 0.67357, 1.18223], "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "vertices": [5.00901, 2.11725, 0.60399, 5.40457, 1.34183, 1.95707, 0.95848, 1.39794, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.02628, 2.13347, -1.35862, 1.93887, 2.5638, 3.22117, -1.55506, 3.81203, 4.62425, 2.79214, -0.17285, 5.39916, 0.67357, 1.18223]}]}}}}, "run": {"slots": {"leg_R2": {"attachment": [{"name": null}]}}, "bones": {"leg_L4": {"translate": [{"x": 16.1, "y": 5.23}, {"time": 0.1667, "x": -25.71, "y": 2.49}, {"time": 0.3333, "x": -37.28, "y": 16.57}, {"time": 0.5, "x": 7.99, "y": 8.28}, {"time": 0.6667, "x": 16.1, "y": 5.23}]}, "leg_L2": {"rotate": [{}, {"time": 0.1667, "angle": -2.31}, {"time": 0.3333, "angle": -53.05}, {"time": 0.5, "angle": 58.94}, {"time": 0.6667}]}, "leg_L": {"rotate": [{"angle": 8.83}, {"time": 0.3333, "angle": -73.93}, {"time": 0.6667, "angle": 8.83}], "translate": [{"x": 1.58, "y": -1.06}, {"time": 0.3333, "x": -14.82, "y": 7.13}, {"time": 0.5, "x": -7.24, "y": 8.48}, {"time": 0.6667, "x": 1.58, "y": -1.06}]}, "leg_R3": {"translate": [{"x": -14.52, "y": 8.13}, {"time": 0.1667, "x": 11.62, "y": 10.54}, {"time": 0.3333, "x": 21.12, "y": 2.92}, {"time": 0.5, "x": 5.96, "y": -2.1}, {"time": 0.6667, "x": -14.52, "y": 8.13}]}, "leg_R2": {"rotate": [{"angle": -61.61}, {"time": 0.3333, "angle": -73.66}, {"time": 0.6667, "angle": -61.61}], "translate": [{"x": 0.54, "y": 5.74}, {"time": 0.1667, "x": -15.64, "y": -4.59}, {"time": 0.3333, "x": -4.34, "y": -13.25}, {"time": 0.5, "x": 7.24, "y": -2.81}, {"time": 0.6667, "x": 0.54, "y": 5.74}], "scale": [{}, {"time": 0.1667, "x": 0.919}, {"time": 0.3333, "x": 0.975}, {"time": 0.5, "x": 0.877}, {"time": 0.6667}]}, "leg_R4": {"rotate": [{"angle": -94.62}, {"time": 0.1667, "angle": 5.61}, {"time": 0.3333, "angle": 39.21}, {"time": 0.3667, "angle": 15.45}, {"time": 0.5, "angle": 0.78}, {"time": 0.5667, "angle": -61.5}, {"time": 0.6667, "angle": -94.62}]}, "body_2": {"rotate": [{"angle": 2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -5.11, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 2.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -3.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": 2.97}], "translate": [{"y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -6.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 1.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -6.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "body_3": {"rotate": [{"angle": 1.19, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 10.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -3.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -4.46, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 1.19}]}, "head": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"x": 0.964}]}, "head_2": {"rotate": [{"angle": -6.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.18}], "scale": [{"x": 0.964}]}, "body_5": {"rotate": [{"angle": -11.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.17}], "scale": [{"x": 0.924, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 1.066, "y": 1.066, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.924, "y": 0.924}]}, "body_6": {"rotate": [{"angle": -9.61, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -11.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.81, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -9.61}], "scale": [{"x": 0.942, "y": 0.942, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 0.924, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.066, "y": 1.066, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "x": 0.942, "y": 0.942}]}, "body_7": {"rotate": [{"angle": -6.76, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -11.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 0.81, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -6.76}], "scale": [{"x": 0.976, "y": 0.976, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 0.924, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.066, "y": 1.066, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 0.976, "y": 0.976}]}, "body_8": {"rotate": [{"angle": -3.59, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": -11.17, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 0.81, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -3.59}], "scale": [{"x": 1.014, "y": 1.014, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "x": 0.924, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.066, "y": 1.066, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 1.014, "y": 1.014}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.892, "y": 0.892, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.892, "y": 0.892, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "hand_R3": {"rotate": [{"angle": -2.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -13.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -2.42}]}, "hand_L3": {"rotate": [{"angle": 8.11, "curve": 0.282, "c2": 0.15, "c3": 0.641, "c4": 0.58}, {"time": 0.1333, "angle": -2.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -13.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 9.04, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 8.11}], "translate": [{"x": 1.23, "y": 6.37}]}, "hand_R2": {"rotate": [{"angle": -2.42}, {"time": 0.1667, "angle": -13.88}, {"time": 0.5, "angle": 9.04}, {"time": 0.6667, "angle": -2.42}]}, "hand_R": {"rotate": [{"angle": -2.42}, {"time": 0.1667, "angle": -13.88}, {"time": 0.5, "angle": 9.04}, {"time": 0.6667, "angle": -2.42}]}, "hand_L2": {"rotate": [{"angle": 6.75}, {"time": 0.1333, "angle": -2.42}, {"time": 0.3, "angle": -13.88}, {"time": 0.6333, "angle": 9.04}, {"time": 0.6667, "angle": 6.75}]}, "hand_L": {"rotate": [{"angle": 6.75}, {"time": 0.1333, "angle": -2.42}, {"time": 0.3, "angle": -13.88}, {"time": 0.6333, "angle": 9.04}, {"time": 0.6667, "angle": 6.75}]}}, "deform": {"default": {"body_2": {"body_2": [{"offset": 8, "vertices": [-2.24388, 1.59911, 0.84948, -2.62118, -0.90692, -2.60187, 0.38564, -1.92086, -0.85335, -1.76357, 0.35108, -1.01681, -0.33464, -1.02233, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.63691, -2.08907, 0.23349, -2.17148, 0.73306, 2.0573, 1.11901, -1.9421, 0.73461, -2.1176, 0.2583, 2.22647, 1.1036, -2.86889, 0.54553, -3.02502, 0.82282, 2.96165, 0.51035, -1.67393, 0.18709, -1.73997, 0.58739, 1.64848, 1.46408, 0.95863, 5.59124, -6.21491, 4.09922, -7.59964, 0.11162, 8.85354, 1.63927, -3.20755, 0.04871, -4.14283, 0.97828, 4.06613, 1.08773, 3.16071, -1.05363, -6.44197, 5.38765, 2.35164, 4.53202, 4.1695, 4.28384, 3.85203]}, {"time": 0.3333, "offset": 8, "vertices": [-1.12194, 0.79955, 0.42474, -1.31059, -0.45346, -1.30093, 0.19282, -0.96043, -0.42668, -0.88179, 0.17554, -0.5084, -0.16732, -0.51117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.31845, -1.04453, 0.11675, -1.08574, 0.36653, 1.02865, 0.5595, -0.97105, 0.3673, -1.0588, 0.12915, 1.11324, 0.5518, -1.43444, 0.27277, -1.51251, 0.41141, 1.48082, 0.25517, -0.83697, 0.09355, -0.86998, 0.29369, 0.82424, 0.73204, 0.47931, -0.55593, 2.66821, 0.16999, 2.94325, -1.95344, -2.31362, -2.54401, 5.14844, -5.91235, 7.02106, 1.74278, -9.14436, 3.00212, -7.02887, -7.10047, 1.07243, 2.04922, -6.96013, 0.34936, -5.91067, 0.05366, -4.51873]}, {"time": 0.6667, "offset": 8, "vertices": [-2.24388, 1.59911, 0.84948, -2.62118, -0.90692, -2.60187, 0.38564, -1.92086, -0.85335, -1.76357, 0.35108, -1.01681, -0.33464, -1.02233, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.63691, -2.08907, 0.23349, -2.17148, 0.73306, 2.0573, 1.11901, -1.9421, 0.73461, -2.1176, 0.2583, 2.22647, 1.1036, -2.86889, 0.54553, -3.02502, 0.82282, 2.96165, 0.51035, -1.67393, 0.18709, -1.73997, 0.58739, 1.64848, 1.46408, 0.95863, 5.59124, -6.21491, 4.09922, -7.59964, 0.11162, 8.85354, 1.63927, -3.20755, 0.04871, -4.14283, 0.97828, 4.06613, 1.08773, 3.16071, -1.05363, -6.44197, 5.38765, 2.35164, 4.53202, 4.1695, 4.28384, 3.85203]}]}, "leg_R2": {"leg_R2": [{"vertices": [-2.09269, 14.79199, -14.86213, -1.51642, 0.65585, 15.00345, 3.22189, 13.55091, 5.82125, 11.57431, 6.4541, 8.14734, 5.54631, 4.30338, 4.52937, 2.02735, -1.84984, 4.60468, 2.7525, -0.2229, 0.32967, 2.74172, -0.25415, -1.10364, 1.09293, -0.29689, -2.71854, 1.03478, -1.13963, -2.67634, -3.93409, 3.30263, -3.45299, -3.80282, -3.81139, 5.69172, -5.83551, -3.58739, -4.21206, 7.87257, -8.03028, -3.90305, -4.19855, 10.74288, -10.89792, -3.77801, -3.45727, 13.20033, -13.32469, -2.94181, 0.89856, 11.63546, 0.84763, 8.59737, 0.75664, 5.68093, 0.20727, 1.98593, -1.97638, 0.28423]}, {"time": 0.1667, "vertices": [18.17151, 20.99669, 5.40243, 4.68825, 23.78492, 20.08207, 28.21341, 15.9275, 32.45372, 10.70802, 32.05343, 3.54258, 28.3675, -3.70805, 25.23598, -7.68743, 18.85715, -5.11012, 20.94988, -10.82717, 18.52743, -7.86257, 16.09296, -10.74017, 17.4404, -9.93345, 11.58473, -6.98998, 13.164, -10.70112, 7.95887, -3.9512, 8.44034, -11.05668, 7.06166, 1.3163, 5.0379, -7.96285, 8.07351, 7.21412, 4.25565, -4.56152, 10.97253, 13.7406, 4.27355, -0.78032, 14.49633, 18.46978, 4.62928, 2.3276, 22.63267, 13.22442, 21.05328, 7.16967, 19.45288, 1.37788, 16.56353, -5.68889, 14.38025, -7.39062]}, {"time": 0.3333, "vertices": [-6.43845, 8.90881, -19.20763, -7.39965, -4.40936, 9.17844, -2.44479, 8.20584, -0.43392, 6.84562, 0.17689, 4.32783, -0.33869, 1.43708, -0.9967, -0.31308, -7.37565, 2.26424, -2.27084, -2.19331, -4.69339, 0.77128, -4.7965, -3.16556, -3.44918, -2.35884, -6.69274, -1.25005, -5.1136, -4.9612, -7.31503, 1.00844, -6.83368, -6.09705, -6.76796, 2.82078, -8.79181, -6.45837, -7.23326, 4.07147, -11.0512, -7.70417, -7.61909, 5.92529, -14.31818, -8.59565, -7.348, 7.68398, -17.21515, -8.45822, -4.09055, 6.68839, -4.00332, 4.43109, -3.95078, 2.26247, -4.20394, -0.51691, -6.38732, -2.21865]}, {"time": 0.5, "vertices": [-5.03915, 9.35295, -19.04525, -5.94061, -2.44845, 9.58333, -0.00881, 8.24093, 2.46827, 6.40407, 3.10484, 3.17524, 2.2915, -0.46494, 1.36481, -2.64206, -4.28835, 3.47642, -0.28476, -4.91112, -1.40322, 1.48249, -3.24506, -5.97494, -0.25184, -2.082, -5.46023, -3.62118, -2.44627, -4.79784, -6.34204, -0.93529, -4.6623, -5.99728, -5.8884, 1.41395, -7.14903, -6.22598, -6.46186, 3.16826, -9.91256, -7.36611, -6.77339, 5.61718, -13.69318, -7.948, -6.27361, 7.84857, -16.92927, -7.40964, -2.1809, 6.40576, -2.19438, 3.53631, -2.24706, 0.7813, -2.71177, -2.7264, -3.76226, -1.75238]}, {"time": 0.6667, "vertices": [-2.09269, 14.79199, -14.86213, -1.51642, 0.65585, 15.00345, 3.22189, 13.55091, 5.82125, 11.57431, 6.4541, 8.14734, 5.54631, 4.30338, 4.52937, 2.02735, -1.84984, 4.60468, 2.7525, -0.2229, 0.32967, 2.74172, -0.25415, -1.10364, 1.09293, -0.29689, -2.71854, 1.03478, -1.13963, -2.67634, -3.93409, 3.30263, -3.45299, -3.80282, -3.81139, 5.69172, -5.83551, -3.58739, -4.21206, 7.87257, -8.03028, -3.90305, -4.19855, 10.74288, -10.89792, -3.77801, -3.45727, 13.20033, -13.32469, -2.94181, 0.89856, 11.63546, 0.84763, 8.59737, 0.75664, 5.68093, 0.20727, 1.98593, -1.97638, 0.28423]}]}}}}}}