{"skeleton": {"hash": "JItbck2ntyoULp+qAARLOXIgEUo", "spine": "3.8.99", "x": -70.55, "y": -21.15, "width": 127.84, "height": 123.42, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/阿拉希高地/风元素"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 38.77, "scaleX": 1.1, "scaleY": 1.1}, {"name": "yinying", "parent": "bone", "x": 1.21, "y": 1.73}, {"name": "body_4", "parent": "bone", "length": 15.66, "rotation": 89.81, "x": 1.07, "y": 0.42}, {"name": "body_6", "parent": "body_4", "length": 11.14, "rotation": 2.68, "x": 16.33, "y": 0.08, "color": "0081ffff"}, {"name": "body_3", "parent": "body_6", "length": 9.69, "rotation": 0.37, "x": 11.75, "y": -0.03, "color": "00ff8bff"}, {"name": "body_2", "parent": "body_3", "length": 16.28, "rotation": 8.28, "x": 9.93, "y": -0.13, "color": "ffde00ff"}, {"name": "body", "parent": "body_2", "length": 15.32, "rotation": -0.21, "x": 9.33, "y": -1.47, "color": "ff0000ff"}, {"name": "head", "parent": "body", "length": 17.65, "rotation": -11.45, "x": 6.18, "y": -6.23}, {"name": "hand_L2", "parent": "body", "length": 17.31, "rotation": 173.36, "x": 4.27, "y": -30.48}, {"name": "hand_L", "parent": "hand_L2", "length": 22.12, "rotation": -11.79, "x": 18.27, "y": 0.34}, {"name": "hand_R2", "parent": "body", "x": 10.18, "y": 37.39}, {"name": "hand_R", "parent": "hand_R2", "length": 30.61, "rotation": -75.21, "x": -0.02, "y": 0.02, "transform": "noRotationOrReflection"}, {"name": "head_4", "parent": "head", "length": 11.67, "rotation": 45.96, "x": 24.57, "y": -13.92}, {"name": "head_3", "parent": "head", "length": 14.6, "rotation": 41.45, "x": 21.34, "y": -1.72}, {"name": "head_2", "parent": "head", "length": 10.58, "rotation": 43.18, "x": 22.76, "y": 10.76}, {"name": "body2", "parent": "body", "length": 5.19, "rotation": -99.68, "x": 5.53, "y": -0.75, "color": "ff0000ff"}, {"name": "body3", "parent": "body2", "rotation": 99.68, "x": -37.35, "y": 20, "color": "ff0000ff"}, {"name": "body4", "parent": "body2", "rotation": 99.68, "x": -10.14, "y": 4.96, "color": "ff0000ff"}, {"name": "body5", "parent": "body2", "rotation": 99.68, "x": 19.35, "y": 1.93, "color": "ff0000ff"}, {"name": "body6", "parent": "body2", "rotation": 99.68, "x": 38.9, "y": 15.23, "color": "ff0000ff"}, {"name": "body_22", "parent": "body_2", "length": 6.55, "rotation": -99.78, "x": 4.44, "y": 1.47, "color": "ffde00ff"}, {"name": "body_5", "parent": "body_22", "rotation": 99.57, "x": -25.31, "y": 10.91, "color": "ffde00ff"}, {"name": "body_8", "parent": "body_22", "rotation": 99.78, "x": -5.71, "y": 2.56, "color": "ffde00ff"}, {"name": "body_9", "parent": "body_22", "rotation": 99.78, "x": 14.37, "y": 1.25, "color": "ffde00ff"}, {"name": "body_10", "parent": "body_22", "rotation": 99.78, "x": 30.58, "y": 6.93, "color": "ffde00ff"}, {"name": "body_21", "parent": "body_3", "length": 3, "rotation": -92.48, "x": 2.58, "y": 0.5, "color": "00ff8bff"}, {"name": "body_11", "parent": "body_21", "rotation": 92.48, "x": -19.2, "y": 6.85, "color": "00ff8bff"}, {"name": "body_12", "parent": "body_21", "rotation": 92.48, "x": -1.51, "y": -0.13, "color": "00ff8bff"}, {"name": "body_13", "parent": "body_21", "rotation": 92.48, "x": 20, "y": 6.49, "color": "00ff8bff"}, {"name": "body_20", "parent": "body_6", "length": 2.04, "rotation": -92.49, "x": 6.93, "y": 0.98, "color": "0081ffff"}, {"name": "body_14", "parent": "body_20", "rotation": 92.49, "x": -14.79, "y": 3.83, "color": "0081ffff"}, {"name": "body_15", "parent": "body_20", "rotation": 92.49, "x": -0.4, "y": -0.27, "color": "0081ffff"}, {"name": "body_16", "parent": "body_20", "rotation": 92.49, "x": 14.69, "y": 4.3, "color": "0081ffff"}, {"name": "body7", "parent": "body", "length": 5.79, "rotation": -103.27, "x": 21.28, "y": -5.29, "color": "3500ffff"}, {"name": "body_7", "parent": "body7", "rotation": 103.27, "x": 29.77, "y": 4.18, "color": "3500ffff"}, {"name": "body_17", "parent": "body7", "rotation": 103.27, "x": 9.1, "y": 0.9, "color": "3500ffff"}, {"name": "body_18", "parent": "body7", "rotation": 103.27, "x": -11.53, "y": 1.4, "color": "3500ffff"}, {"name": "body_19", "parent": "body7", "rotation": 103.27, "x": -31.11, "y": 5.85, "color": "3500ffff"}, {"name": "body8", "parent": "body", "length": 5.19, "rotation": -99.68, "x": 5.53, "y": -0.75, "color": "ff0000ff"}, {"name": "body9", "parent": "body8", "rotation": 99.68, "x": -37.35, "y": 20, "color": "ff0000ff"}, {"name": "body10", "parent": "body8", "rotation": 99.68, "x": -10.14, "y": 4.96, "color": "ff0000ff"}, {"name": "body11", "parent": "body8", "rotation": 99.68, "x": 19.35, "y": 1.93, "color": "ff0000ff"}, {"name": "body12", "parent": "body8", "rotation": 99.68, "x": 38.9, "y": 15.23, "color": "ff0000ff"}, {"name": "body13", "parent": "body", "length": 5.19, "rotation": -99.68, "x": 5.53, "y": -0.75, "color": "ff0000ff"}, {"name": "body14", "parent": "body13", "rotation": 99.68, "x": -37.35, "y": 20, "color": "ff0000ff"}, {"name": "body15", "parent": "body13", "rotation": 99.68, "x": -10.14, "y": 4.96, "color": "ff0000ff"}, {"name": "body16", "parent": "body13", "rotation": 99.68, "x": 19.35, "y": 1.93, "color": "ff0000ff"}, {"name": "body17", "parent": "body13", "rotation": 99.68, "x": 38.9, "y": 15.23, "color": "ff0000ff"}, {"name": "body18", "parent": "body", "length": 5.19, "rotation": -99.68, "x": 5.53, "y": -0.75, "color": "ff0000ff"}, {"name": "body19", "parent": "body18", "rotation": 99.68, "x": -37.35, "y": 20, "color": "ff0000ff"}, {"name": "body20", "parent": "body18", "rotation": 99.68, "x": -10.14, "y": 4.96, "color": "ff0000ff"}, {"name": "body21", "parent": "body18", "rotation": 99.68, "x": 19.35, "y": 1.93, "color": "ff0000ff"}, {"name": "body22", "parent": "body18", "rotation": 99.68, "x": 38.9, "y": 15.23, "color": "ff0000ff"}, {"name": "body23", "parent": "body", "length": 5.19, "rotation": -99.68, "x": 5.53, "y": -0.75, "color": "ff0000ff"}, {"name": "body24", "parent": "body23", "rotation": 99.68, "x": -37.35, "y": 20, "color": "ff0000ff"}, {"name": "body25", "parent": "body23", "rotation": 99.68, "x": -10.14, "y": 4.96, "color": "ff0000ff"}, {"name": "body26", "parent": "body23", "rotation": 99.68, "x": 19.35, "y": 1.93, "color": "ff0000ff"}, {"name": "body27", "parent": "body23", "rotation": 99.68, "x": 38.9, "y": 15.23, "color": "ff0000ff"}, {"name": "body28", "parent": "body", "length": 5.19, "rotation": -99.68, "x": 5.53, "y": -0.75, "color": "ff0000ff"}, {"name": "body29", "parent": "body28", "rotation": 99.68, "x": -37.35, "y": 20, "color": "ff0000ff"}, {"name": "body30", "parent": "body28", "rotation": 99.68, "x": -10.14, "y": 4.96, "color": "ff0000ff"}, {"name": "body31", "parent": "body28", "rotation": 99.68, "x": 19.35, "y": 1.93, "color": "ff0000ff"}, {"name": "body32", "parent": "body28", "rotation": 99.68, "x": 38.9, "y": 15.23, "color": "ff0000ff"}, {"name": "body_23", "parent": "body_2", "length": 6.55, "rotation": -99.78, "x": 4.44, "y": 1.47, "color": "ffde00ff"}, {"name": "body_24", "parent": "body_23", "rotation": 99.57, "x": -25.31, "y": 10.91, "color": "ffde00ff"}, {"name": "body_25", "parent": "body_23", "rotation": 99.78, "x": -5.71, "y": 2.56, "color": "ffde00ff"}, {"name": "body_26", "parent": "body_23", "rotation": 99.78, "x": 14.37, "y": 1.25, "color": "ffde00ff"}, {"name": "body_27", "parent": "body_23", "rotation": 99.78, "x": 30.58, "y": 6.93, "color": "ffde00ff"}, {"name": "body_28", "parent": "body_2", "length": 6.55, "rotation": -99.78, "x": 4.44, "y": 1.47, "color": "ffde00ff"}, {"name": "body_29", "parent": "body_28", "rotation": 99.57, "x": -25.31, "y": 10.91, "color": "ffde00ff"}, {"name": "body_30", "parent": "body_28", "rotation": 99.78, "x": -5.71, "y": 2.56, "color": "ffde00ff"}, {"name": "body_31", "parent": "body_28", "rotation": 99.78, "x": 14.37, "y": 1.25, "color": "ffde00ff"}, {"name": "body_32", "parent": "body_28", "rotation": 99.78, "x": 30.58, "y": 6.93, "color": "ffde00ff"}, {"name": "body_33", "parent": "body_2", "length": 6.55, "rotation": -99.78, "x": 4.44, "y": 1.47, "color": "ffde00ff"}, {"name": "body_34", "parent": "body_33", "rotation": 99.57, "x": -25.31, "y": 10.91, "color": "ffde00ff"}, {"name": "body_35", "parent": "body_33", "rotation": 99.78, "x": -5.71, "y": 2.56, "color": "ffde00ff"}, {"name": "body_36", "parent": "body_33", "rotation": 99.78, "x": 14.37, "y": 1.25, "color": "ffde00ff"}, {"name": "body_37", "parent": "body_33", "rotation": 99.78, "x": 30.58, "y": 6.93, "color": "ffde00ff"}, {"name": "body_38", "parent": "body_2", "length": 6.55, "rotation": -99.78, "x": 4.44, "y": 1.47, "color": "ffde00ff"}, {"name": "body_39", "parent": "body_38", "rotation": 99.57, "x": -25.31, "y": 10.91, "color": "ffde00ff"}, {"name": "body_40", "parent": "body_38", "rotation": 99.78, "x": -5.71, "y": 2.56, "color": "ffde00ff"}, {"name": "body_41", "parent": "body_38", "rotation": 99.78, "x": 14.37, "y": 1.25, "color": "ffde00ff"}, {"name": "body_42", "parent": "body_38", "rotation": 99.78, "x": 30.58, "y": 6.93, "color": "ffde00ff"}, {"name": "body_43", "parent": "body_2", "length": 6.55, "rotation": -99.78, "x": 4.44, "y": 1.47, "color": "ffde00ff"}, {"name": "body_44", "parent": "body_43", "rotation": 99.57, "x": -25.31, "y": 10.91, "color": "ffde00ff"}, {"name": "body_45", "parent": "body_43", "rotation": 99.78, "x": -5.71, "y": 2.56, "color": "ffde00ff"}, {"name": "body_46", "parent": "body_43", "rotation": 99.78, "x": 14.37, "y": 1.25, "color": "ffde00ff"}, {"name": "body_47", "parent": "body_43", "rotation": 99.78, "x": 30.58, "y": 6.93, "color": "ffde00ff"}, {"name": "body_48", "parent": "body_3", "length": 3, "rotation": -92.48, "x": 2.58, "y": 0.5, "color": "00ff8bff"}, {"name": "body_49", "parent": "body_48", "rotation": 92.48, "x": -19.2, "y": 6.85, "color": "00ff8bff"}, {"name": "body_50", "parent": "body_48", "rotation": 92.48, "x": -1.51, "y": -0.13, "color": "00ff8bff"}, {"name": "body_51", "parent": "body_48", "rotation": 92.48, "x": 20, "y": 6.49, "color": "00ff8bff"}, {"name": "body_52", "parent": "body_3", "length": 3, "rotation": -92.48, "x": 2.58, "y": 0.5, "color": "00ff8bff"}, {"name": "body_53", "parent": "body_52", "rotation": 92.48, "x": -19.2, "y": 6.85, "color": "00ff8bff"}, {"name": "body_54", "parent": "body_52", "rotation": 92.48, "x": -1.51, "y": -0.13, "color": "00ff8bff"}, {"name": "body_55", "parent": "body_52", "rotation": 92.48, "x": 20, "y": 6.49, "color": "00ff8bff"}, {"name": "body_56", "parent": "body_3", "length": 3, "rotation": -92.48, "x": 2.58, "y": 0.5, "color": "00ff8bff"}, {"name": "body_57", "parent": "body_56", "rotation": 92.48, "x": -19.2, "y": 6.85, "color": "00ff8bff"}, {"name": "body_58", "parent": "body_56", "rotation": 92.48, "x": -1.51, "y": -0.13, "color": "00ff8bff"}, {"name": "body_59", "parent": "body_56", "rotation": 92.48, "x": 20, "y": 6.49, "color": "00ff8bff"}, {"name": "body_60", "parent": "body_3", "length": 3, "rotation": -92.48, "x": 2.58, "y": 0.5, "color": "00ff8bff"}, {"name": "body_61", "parent": "body_60", "rotation": 92.48, "x": -19.2, "y": 6.85, "color": "00ff8bff"}, {"name": "body_62", "parent": "body_60", "rotation": 92.48, "x": -1.51, "y": -0.13, "color": "00ff8bff"}, {"name": "body_63", "parent": "body_60", "rotation": 92.48, "x": 20, "y": 6.49, "color": "00ff8bff"}, {"name": "body_64", "parent": "body_3", "length": 3, "rotation": -92.48, "x": 2.58, "y": 0.5, "color": "00ff8bff"}, {"name": "body_65", "parent": "body_64", "rotation": 92.48, "x": -19.2, "y": 6.85, "color": "00ff8bff"}, {"name": "body_66", "parent": "body_64", "rotation": 92.48, "x": -1.51, "y": -0.13, "color": "00ff8bff"}, {"name": "body_67", "parent": "body_64", "rotation": 92.48, "x": 20, "y": 6.49, "color": "00ff8bff"}, {"name": "hand_R3", "parent": "body", "x": 10.18, "y": 37.39}, {"name": "body33", "parent": "body", "length": 5.79, "rotation": -103.27, "x": 21.28, "y": -5.29, "color": "3500ffff"}, {"name": "body_68", "parent": "body33", "rotation": 103.27, "x": 29.77, "y": 4.18, "color": "3500ffff"}, {"name": "body_69", "parent": "body33", "rotation": 103.27, "x": 9.1, "y": 0.9, "color": "3500ffff"}, {"name": "body_70", "parent": "body33", "rotation": 103.27, "x": -11.53, "y": 1.4, "color": "3500ffff"}, {"name": "body_71", "parent": "body33", "rotation": 103.27, "x": -31.11, "y": 5.85, "color": "3500ffff"}, {"name": "body34", "parent": "body", "length": 5.79, "rotation": -103.27, "x": 21.28, "y": -5.29, "color": "3500ffff"}, {"name": "body_72", "parent": "body34", "rotation": 103.27, "x": 29.77, "y": 4.18, "color": "3500ffff"}, {"name": "body_73", "parent": "body34", "rotation": 103.27, "x": 9.1, "y": 0.9, "color": "3500ffff"}, {"name": "body_74", "parent": "body34", "rotation": 103.27, "x": -11.53, "y": 1.4, "color": "3500ffff"}, {"name": "body_75", "parent": "body34", "rotation": 103.27, "x": -31.11, "y": 5.85, "color": "3500ffff"}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "hand_L2", "bone": "hand_L2", "attachment": "hand_L2"}, {"name": "hand_L", "bone": "hand_L", "attachment": "hand_L"}, {"name": "body_5", "bone": "body_19", "attachment": "body_5"}, {"name": "body_18", "bone": "body_75", "attachment": "body_5"}, {"name": "body_17", "bone": "body_71", "attachment": "body_5"}, {"name": "head_4", "bone": "head_4", "attachment": "head_4"}, {"name": "head_3", "bone": "head_3", "attachment": "head_3"}, {"name": "head_2", "bone": "head_2", "attachment": "head_2"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye_close_R", "bone": "bone"}, {"name": "eye_close_L", "bone": "bone"}, {"name": "body_4", "bone": "body_4", "attachment": "body_4"}, {"name": "body_3", "bone": "body_13", "attachment": "body_3"}, {"name": "body_6", "bone": "body_16", "attachment": "body_6"}, {"name": "body_16", "bone": "body_67", "attachment": "body_3"}, {"name": "body_15", "bone": "body_63", "attachment": "body_3"}, {"name": "body_14", "bone": "body_59", "attachment": "body_3"}, {"name": "body_13", "bone": "body_55", "attachment": "body_3"}, {"name": "body_12", "bone": "body_51", "attachment": "body_3"}, {"name": "body_2", "bone": "body_10", "attachment": "body_2"}, {"name": "body_11", "bone": "body_47", "attachment": "body_2"}, {"name": "body_10", "bone": "body_42", "attachment": "body_2"}, {"name": "body_9", "bone": "body_37", "attachment": "body_2"}, {"name": "body_8", "bone": "body_32", "attachment": "body_2"}, {"name": "body_7", "bone": "body_27", "attachment": "body_2"}, {"name": "body", "bone": "body6", "attachment": "body"}, {"name": "body2", "bone": "body12", "attachment": "body"}, {"name": "body3", "bone": "body17", "attachment": "body"}, {"name": "body4", "bone": "body22", "attachment": "body"}, {"name": "body5", "bone": "body27", "attachment": "body"}, {"name": "body6", "bone": "body32", "attachment": "body"}, {"name": "hand_R2", "bone": "hand_R2", "attachment": "hand_R2"}, {"name": "hand_R3", "bone": "hand_R3", "attachment": "hand_R2"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}, {"name": "wind_2", "bone": "bone"}, {"name": "wind", "bone": "bone"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.10195, 0.02207, 0.03823, 0.11657, 0.0046, 0.30556, 0.02584, 0.49456, 0.06478, 0.64506, 0.12497, 0.77806, 0.23117, 0.87606, 0.21368, 0.74525, 0.28827, 0.81525, 0.36666, 0.88775, 0.47287, 0.96275, 0.59931, 1, 0.72574, 0.98525, 0.80154, 0.93775, 0.90522, 0.81275, 0.97223, 0.61775, 1, 0.40525, 0.9697, 0.23525, 0.8989, 0.08275, 0.86729, 0.28775, 0.79648, 0.41525, 0.71809, 0.52275, 0.61441, 0.57025, 0.50315, 0.54525, 0.40706, 0.48025, 0.33752, 0.39525, 0.25407, 0.26275, 0.17315, 0.25025, 0.13775, 0.17275, 0.14281, 0, 0.10867, 0.38275, 0.22499, 0.54025, 0.38051, 0.66525, 0.52591, 0.73275, 0.68016, 0.76525, 0.79522, 0.71275, 0.89763, 0.54025, 0.94441, 0.36275], "triangles": [14, 36, 15, 15, 36, 16, 16, 36, 37, 20, 19, 36, 36, 19, 37, 37, 17, 16, 37, 19, 17, 19, 18, 17, 11, 34, 12, 11, 33, 34, 12, 34, 13, 34, 35, 13, 13, 35, 14, 35, 36, 14, 33, 22, 34, 34, 21, 35, 34, 22, 21, 35, 20, 36, 35, 21, 20, 10, 33, 11, 10, 9, 33, 9, 32, 33, 9, 8, 32, 32, 8, 31, 8, 7, 31, 32, 23, 33, 33, 23, 22, 31, 25, 32, 32, 24, 23, 32, 25, 24, 31, 26, 25, 5, 7, 6, 7, 5, 31, 31, 5, 4, 4, 30, 31, 4, 3, 30, 30, 27, 31, 31, 27, 26, 3, 2, 30, 30, 2, 28, 30, 28, 27, 28, 2, 1, 1, 0, 28, 28, 0, 29], "vertices": [1, 17, 15.46, -6.63, 1, 1, 17, 12.73, -0.25, 1, 2, 17, 5.35, 4.56, 0.99396, 18, 24.75, 28.86, 0.00604, 2, 17, -3.17, 4.73, 0.94348, 18, 16.23, 29.03, 0.05652, 2, 17, -10.41, 3.01, 0.83589, 18, 8.99, 27.31, 0.16411, 2, 17, -17.33, -0.69, 0.69966, 18, 2.07, 23.61, 0.30034, 2, 17, -23.71, -8.64, 0.5852, 18, -4.31, 15.65, 0.4148, 2, 17, -17.76, -8.53, 0.4128, 18, 1.64, 15.77, 0.5872, 3, 17, -22.29, -14.1, 0.22576, 18, -2.89, 10.19, 0.76438, 19, 5.06, 38.76, 0.00986, 3, 17, -27, -19.97, 0.05737, 18, -7.6, 4.32, 0.84469, 19, 0.34, 32.89, 0.09794, 3, 17, -32.4, -28.17, 0.00402, 18, -13, -3.87, 0.68284, 19, -5.05, 24.69, 0.31314, 3, 18, -17.2, -14.17, 0.38659, 19, -9.25, 14.39, 0.61305, 20, -19.07, 35.9, 0.00035, 3, 18, -19.17, -25.01, 0.14321, 19, -11.23, 3.55, 0.84076, 20, -21.05, 25.06, 0.01603, 3, 18, -18.7, -31.91, 0.02507, 19, -10.76, -3.35, 0.85187, 20, -20.58, 18.15, 0.12305, 3, 18, -15.5, -41.98, 0.00187, 19, -7.55, -13.42, 0.63295, 20, -17.37, 8.09, 0.36518, 2, 19, -0.6, -21.11, 0.31861, 20, -10.42, 0.39, 0.68139, 2, 19, 7.91, -25.68, 0.09231, 20, -1.91, -4.17, 0.90769, 2, 19, 15.8, -24.89, 0.00146, 20, 5.98, -3.38, 0.99854, 2, 19, 23.78, -20.49, 0.02348, 20, 13.96, 1.01, 0.97652, 2, 19, 15.67, -15.68, 0.18007, 20, 5.85, 5.82, 0.81993, 3, 18, 3.73, -36.93, 0.00892, 19, 11.68, -8.37, 0.46776, 20, 1.86, 13.13, 0.52332, 3, 18, 0.76, -29.18, 0.1131, 19, 8.7, -0.62, 0.67343, 20, -1.12, 20.88, 0.21347, 4, 17, -18.54, -44.22, 0.00017, 18, 0.86, -19.93, 0.36628, 19, 8.81, 8.64, 0.59683, 20, -1.01, 30.14, 0.03672, 3, 17, -15.18, -35.08, 0.02819, 18, 4.22, -10.78, 0.65712, 19, 12.17, 17.78, 0.31469, 3, 17, -10.42, -27.64, 0.13431, 18, 8.98, -3.34, 0.78015, 19, 16.93, 25.22, 0.08554, 3, 17, -5.35, -22.64, 0.34364, 18, 14.05, 1.65, 0.6508, 19, 22, 30.21, 0.00556, 2, 17, 2.04, -16.97, 0.60072, 18, 21.44, 7.33, 0.39928, 2, 17, 4.24, -10.26, 0.81891, 18, 23.64, 14.03, 0.18109, 2, 17, 8.28, -8.08, 0.94274, 18, 27.68, 16.22, 0.05726, 2, 17, 15.56, -10.31, 0.99098, 18, 34.96, 13.99, 0.00902, 2, 17, -0.1, -3.43, 0.94587, 18, 19.3, 20.87, 0.05413, 2, 17, -9.23, -11.62, 0.53696, 18, 10.17, 12.68, 0.46304, 2, 17, -17.78, -23.46, 0.02843, 18, 1.62, 0.83, 0.97157, 2, 18, -4.26, -10.75, 0.6322, 19, 3.69, 17.81, 0.3678, 2, 18, -8.83, -23.45, 0.11579, 19, -0.88, 5.11, 0.88421, 2, 19, -1.01, -5.16, 0.86326, 20, -10.83, 16.34, 0.13674, 2, 19, 4.25, -15.62, 0.31012, 20, -5.57, 5.89, 0.68988, 1, 20, 1.05, 0.08, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 87, "height": 44}}, "body2": {"body": {"type": "mesh", "uvs": [0.10195, 0.02207, 0.03823, 0.11657, 0.0046, 0.30556, 0.02584, 0.49456, 0.06478, 0.64506, 0.12497, 0.77806, 0.23117, 0.87606, 0.21368, 0.74525, 0.28827, 0.81525, 0.36666, 0.88775, 0.47287, 0.96275, 0.59931, 1, 0.72574, 0.98525, 0.80154, 0.93775, 0.90522, 0.81275, 0.97223, 0.61775, 1, 0.40525, 0.9697, 0.23525, 0.8989, 0.08275, 0.86729, 0.28775, 0.79648, 0.41525, 0.71809, 0.52275, 0.61441, 0.57025, 0.50315, 0.54525, 0.40706, 0.48025, 0.33752, 0.39525, 0.25407, 0.26275, 0.17315, 0.25025, 0.13775, 0.17275, 0.14281, 0, 0.10867, 0.38275, 0.22499, 0.54025, 0.38051, 0.66525, 0.52591, 0.73275, 0.68016, 0.76525, 0.79522, 0.71275, 0.89763, 0.54025, 0.94441, 0.36275], "triangles": [14, 36, 15, 15, 36, 16, 16, 36, 37, 20, 19, 36, 36, 19, 37, 37, 17, 16, 37, 19, 17, 19, 18, 17, 11, 34, 12, 11, 33, 34, 12, 34, 13, 34, 35, 13, 13, 35, 14, 35, 36, 14, 33, 22, 34, 34, 21, 35, 34, 22, 21, 35, 20, 36, 35, 21, 20, 10, 33, 11, 10, 9, 33, 9, 32, 33, 9, 8, 32, 32, 8, 31, 8, 7, 31, 32, 23, 33, 33, 23, 22, 31, 25, 32, 32, 24, 23, 32, 25, 24, 31, 26, 25, 5, 7, 6, 7, 5, 31, 31, 5, 4, 4, 30, 31, 4, 3, 30, 30, 27, 31, 31, 27, 26, 3, 2, 30, 30, 2, 28, 30, 28, 27, 28, 2, 1, 1, 0, 28, 28, 0, 29], "vertices": [1, 40, 15.46, -6.63, 1, 1, 40, 12.73, -0.25, 1, 2, 40, 5.35, 4.56, 0.99396, 41, 24.75, 28.86, 0.00604, 2, 40, -3.17, 4.73, 0.94348, 41, 16.23, 29.03, 0.05652, 2, 40, -10.41, 3.01, 0.83589, 41, 8.99, 27.31, 0.16411, 2, 40, -17.33, -0.69, 0.69966, 41, 2.07, 23.61, 0.30034, 2, 40, -23.71, -8.64, 0.5852, 41, -4.31, 15.65, 0.4148, 2, 40, -17.76, -8.53, 0.4128, 41, 1.64, 15.77, 0.5872, 3, 40, -22.29, -14.1, 0.22576, 41, -2.89, 10.19, 0.76438, 42, 5.06, 38.76, 0.00986, 3, 40, -27, -19.97, 0.05737, 41, -7.6, 4.32, 0.84469, 42, 0.34, 32.89, 0.09794, 3, 40, -32.4, -28.17, 0.00402, 41, -13, -3.87, 0.68284, 42, -5.05, 24.69, 0.31314, 3, 41, -17.2, -14.17, 0.38659, 42, -9.25, 14.39, 0.61305, 43, -19.07, 35.9, 0.00035, 3, 41, -19.17, -25.01, 0.14321, 42, -11.23, 3.55, 0.84076, 43, -21.05, 25.06, 0.01603, 3, 41, -18.7, -31.91, 0.02507, 42, -10.76, -3.35, 0.85187, 43, -20.58, 18.15, 0.12305, 3, 41, -15.5, -41.98, 0.00187, 42, -7.55, -13.42, 0.63295, 43, -17.37, 8.09, 0.36518, 2, 42, -0.6, -21.11, 0.31861, 43, -10.42, 0.39, 0.68139, 2, 42, 7.91, -25.68, 0.09231, 43, -1.91, -4.17, 0.90769, 2, 42, 15.8, -24.89, 0.00146, 43, 5.98, -3.38, 0.99854, 2, 42, 23.78, -20.49, 0.02348, 43, 13.96, 1.01, 0.97652, 2, 42, 15.67, -15.68, 0.18007, 43, 5.85, 5.82, 0.81993, 3, 41, 3.73, -36.93, 0.00892, 42, 11.68, -8.37, 0.46776, 43, 1.86, 13.13, 0.52332, 3, 41, 0.76, -29.18, 0.1131, 42, 8.7, -0.62, 0.67343, 43, -1.12, 20.88, 0.21347, 4, 40, -18.54, -44.22, 0.00017, 41, 0.86, -19.93, 0.36628, 42, 8.81, 8.64, 0.59683, 43, -1.01, 30.14, 0.03672, 3, 40, -15.18, -35.08, 0.02819, 41, 4.22, -10.78, 0.65712, 42, 12.17, 17.78, 0.31469, 3, 40, -10.42, -27.64, 0.13431, 41, 8.98, -3.34, 0.78015, 42, 16.93, 25.22, 0.08554, 3, 40, -5.35, -22.64, 0.34364, 41, 14.05, 1.65, 0.6508, 42, 22, 30.21, 0.00556, 2, 40, 2.04, -16.97, 0.60072, 41, 21.44, 7.33, 0.39928, 2, 40, 4.24, -10.26, 0.81891, 41, 23.64, 14.03, 0.18109, 2, 40, 8.28, -8.08, 0.94274, 41, 27.68, 16.22, 0.05726, 2, 40, 15.56, -10.31, 0.99098, 41, 34.96, 13.99, 0.00902, 2, 40, -0.1, -3.43, 0.94587, 41, 19.3, 20.87, 0.05413, 2, 40, -9.23, -11.62, 0.53696, 41, 10.17, 12.68, 0.46304, 2, 40, -17.78, -23.46, 0.02843, 41, 1.62, 0.83, 0.97157, 2, 41, -4.26, -10.75, 0.6322, 42, 3.69, 17.81, 0.3678, 2, 41, -8.83, -23.45, 0.11579, 42, -0.88, 5.11, 0.88421, 2, 42, -1.01, -5.16, 0.86326, 43, -10.83, 16.34, 0.13674, 2, 42, 4.25, -15.62, 0.31012, 43, -5.57, 5.89, 0.68988, 1, 43, 1.05, 0.08, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 87, "height": 44}}, "body3": {"body": {"type": "mesh", "uvs": [0.10195, 0.02207, 0.03823, 0.11657, 0.0046, 0.30556, 0.02584, 0.49456, 0.06478, 0.64506, 0.12497, 0.77806, 0.23117, 0.87606, 0.21368, 0.74525, 0.28827, 0.81525, 0.36666, 0.88775, 0.47287, 0.96275, 0.59931, 1, 0.72574, 0.98525, 0.80154, 0.93775, 0.90522, 0.81275, 0.97223, 0.61775, 1, 0.40525, 0.9697, 0.23525, 0.8989, 0.08275, 0.86729, 0.28775, 0.79648, 0.41525, 0.71809, 0.52275, 0.61441, 0.57025, 0.50315, 0.54525, 0.40706, 0.48025, 0.33752, 0.39525, 0.25407, 0.26275, 0.17315, 0.25025, 0.13775, 0.17275, 0.14281, 0, 0.10867, 0.38275, 0.22499, 0.54025, 0.38051, 0.66525, 0.52591, 0.73275, 0.68016, 0.76525, 0.79522, 0.71275, 0.89763, 0.54025, 0.94441, 0.36275], "triangles": [14, 36, 15, 15, 36, 16, 16, 36, 37, 20, 19, 36, 36, 19, 37, 37, 17, 16, 37, 19, 17, 19, 18, 17, 11, 34, 12, 11, 33, 34, 12, 34, 13, 34, 35, 13, 13, 35, 14, 35, 36, 14, 33, 22, 34, 34, 21, 35, 34, 22, 21, 35, 20, 36, 35, 21, 20, 10, 33, 11, 10, 9, 33, 9, 32, 33, 9, 8, 32, 32, 8, 31, 8, 7, 31, 32, 23, 33, 33, 23, 22, 31, 25, 32, 32, 24, 23, 32, 25, 24, 31, 26, 25, 5, 7, 6, 7, 5, 31, 31, 5, 4, 4, 30, 31, 4, 3, 30, 30, 27, 31, 31, 27, 26, 3, 2, 30, 30, 2, 28, 30, 28, 27, 28, 2, 1, 1, 0, 28, 28, 0, 29], "vertices": [1, 45, 15.46, -6.63, 1, 1, 45, 12.73, -0.25, 1, 2, 45, 5.35, 4.56, 0.99396, 46, 24.75, 28.86, 0.00604, 2, 45, -3.17, 4.73, 0.94348, 46, 16.23, 29.03, 0.05652, 2, 45, -10.41, 3.01, 0.83589, 46, 8.99, 27.31, 0.16411, 2, 45, -17.33, -0.69, 0.69966, 46, 2.07, 23.61, 0.30034, 2, 45, -23.71, -8.64, 0.5852, 46, -4.31, 15.65, 0.4148, 2, 45, -17.76, -8.53, 0.4128, 46, 1.64, 15.77, 0.5872, 3, 45, -22.29, -14.1, 0.22576, 46, -2.89, 10.19, 0.76438, 47, 5.06, 38.76, 0.00986, 3, 45, -27, -19.97, 0.05737, 46, -7.6, 4.32, 0.84469, 47, 0.34, 32.89, 0.09794, 3, 45, -32.4, -28.17, 0.00402, 46, -13, -3.87, 0.68284, 47, -5.05, 24.69, 0.31314, 3, 46, -17.2, -14.17, 0.38659, 47, -9.25, 14.39, 0.61305, 48, -19.07, 35.9, 0.00035, 3, 46, -19.17, -25.01, 0.14321, 47, -11.23, 3.55, 0.84076, 48, -21.05, 25.06, 0.01603, 3, 46, -18.7, -31.91, 0.02507, 47, -10.76, -3.35, 0.85187, 48, -20.58, 18.15, 0.12305, 3, 46, -15.5, -41.98, 0.00187, 47, -7.55, -13.42, 0.63295, 48, -17.37, 8.09, 0.36518, 2, 47, -0.6, -21.11, 0.31861, 48, -10.42, 0.39, 0.68139, 2, 47, 7.91, -25.68, 0.09231, 48, -1.91, -4.17, 0.90769, 2, 47, 15.8, -24.89, 0.00146, 48, 5.98, -3.38, 0.99854, 2, 47, 23.78, -20.49, 0.02348, 48, 13.96, 1.01, 0.97652, 2, 47, 15.67, -15.68, 0.18007, 48, 5.85, 5.82, 0.81993, 3, 46, 3.73, -36.93, 0.00892, 47, 11.68, -8.37, 0.46776, 48, 1.86, 13.13, 0.52332, 3, 46, 0.76, -29.18, 0.1131, 47, 8.7, -0.62, 0.67343, 48, -1.12, 20.88, 0.21347, 4, 45, -18.54, -44.22, 0.00017, 46, 0.86, -19.93, 0.36628, 47, 8.81, 8.64, 0.59683, 48, -1.01, 30.14, 0.03672, 3, 45, -15.18, -35.08, 0.02819, 46, 4.22, -10.78, 0.65712, 47, 12.17, 17.78, 0.31469, 3, 45, -10.42, -27.64, 0.13431, 46, 8.98, -3.34, 0.78015, 47, 16.93, 25.22, 0.08554, 3, 45, -5.35, -22.64, 0.34364, 46, 14.05, 1.65, 0.6508, 47, 22, 30.21, 0.00556, 2, 45, 2.04, -16.97, 0.60072, 46, 21.44, 7.33, 0.39928, 2, 45, 4.24, -10.26, 0.81891, 46, 23.64, 14.03, 0.18109, 2, 45, 8.28, -8.08, 0.94274, 46, 27.68, 16.22, 0.05726, 2, 45, 15.56, -10.31, 0.99098, 46, 34.96, 13.99, 0.00902, 2, 45, -0.1, -3.43, 0.94587, 46, 19.3, 20.87, 0.05413, 2, 45, -9.23, -11.62, 0.53696, 46, 10.17, 12.68, 0.46304, 2, 45, -17.78, -23.46, 0.02843, 46, 1.62, 0.83, 0.97157, 2, 46, -4.26, -10.75, 0.6322, 47, 3.69, 17.81, 0.3678, 2, 46, -8.83, -23.45, 0.11579, 47, -0.88, 5.11, 0.88421, 2, 47, -1.01, -5.16, 0.86326, 48, -10.83, 16.34, 0.13674, 2, 47, 4.25, -15.62, 0.31012, 48, -5.57, 5.89, 0.68988, 1, 48, 1.05, 0.08, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 87, "height": 44}}, "body4": {"body": {"type": "mesh", "uvs": [0.10195, 0.02207, 0.03823, 0.11657, 0.0046, 0.30556, 0.02584, 0.49456, 0.06478, 0.64506, 0.12497, 0.77806, 0.23117, 0.87606, 0.21368, 0.74525, 0.28827, 0.81525, 0.36666, 0.88775, 0.47287, 0.96275, 0.59931, 1, 0.72574, 0.98525, 0.80154, 0.93775, 0.90522, 0.81275, 0.97223, 0.61775, 1, 0.40525, 0.9697, 0.23525, 0.8989, 0.08275, 0.86729, 0.28775, 0.79648, 0.41525, 0.71809, 0.52275, 0.61441, 0.57025, 0.50315, 0.54525, 0.40706, 0.48025, 0.33752, 0.39525, 0.25407, 0.26275, 0.17315, 0.25025, 0.13775, 0.17275, 0.14281, 0, 0.10867, 0.38275, 0.22499, 0.54025, 0.38051, 0.66525, 0.52591, 0.73275, 0.68016, 0.76525, 0.79522, 0.71275, 0.89763, 0.54025, 0.94441, 0.36275], "triangles": [14, 36, 15, 15, 36, 16, 16, 36, 37, 20, 19, 36, 36, 19, 37, 37, 17, 16, 37, 19, 17, 19, 18, 17, 11, 34, 12, 11, 33, 34, 12, 34, 13, 34, 35, 13, 13, 35, 14, 35, 36, 14, 33, 22, 34, 34, 21, 35, 34, 22, 21, 35, 20, 36, 35, 21, 20, 10, 33, 11, 10, 9, 33, 9, 32, 33, 9, 8, 32, 32, 8, 31, 8, 7, 31, 32, 23, 33, 33, 23, 22, 31, 25, 32, 32, 24, 23, 32, 25, 24, 31, 26, 25, 5, 7, 6, 7, 5, 31, 31, 5, 4, 4, 30, 31, 4, 3, 30, 30, 27, 31, 31, 27, 26, 3, 2, 30, 30, 2, 28, 30, 28, 27, 28, 2, 1, 1, 0, 28, 28, 0, 29], "vertices": [1, 50, 15.46, -6.63, 1, 1, 50, 12.73, -0.25, 1, 2, 50, 5.35, 4.56, 0.99396, 51, 24.75, 28.86, 0.00604, 2, 50, -3.17, 4.73, 0.94348, 51, 16.23, 29.03, 0.05652, 2, 50, -10.41, 3.01, 0.83589, 51, 8.99, 27.31, 0.16411, 2, 50, -17.33, -0.69, 0.69966, 51, 2.07, 23.61, 0.30034, 2, 50, -23.71, -8.64, 0.5852, 51, -4.31, 15.65, 0.4148, 2, 50, -17.76, -8.53, 0.4128, 51, 1.64, 15.77, 0.5872, 3, 50, -22.29, -14.1, 0.22576, 51, -2.89, 10.19, 0.76438, 52, 5.06, 38.76, 0.00986, 3, 50, -27, -19.97, 0.05737, 51, -7.6, 4.32, 0.84469, 52, 0.34, 32.89, 0.09794, 3, 50, -32.4, -28.17, 0.00402, 51, -13, -3.87, 0.68284, 52, -5.05, 24.69, 0.31314, 3, 51, -17.2, -14.17, 0.38659, 52, -9.25, 14.39, 0.61305, 53, -19.07, 35.9, 0.00035, 3, 51, -19.17, -25.01, 0.14321, 52, -11.23, 3.55, 0.84076, 53, -21.05, 25.06, 0.01603, 3, 51, -18.7, -31.91, 0.02507, 52, -10.76, -3.35, 0.85187, 53, -20.58, 18.15, 0.12305, 3, 51, -15.5, -41.98, 0.00187, 52, -7.55, -13.42, 0.63295, 53, -17.37, 8.09, 0.36518, 2, 52, -0.6, -21.11, 0.31861, 53, -10.42, 0.39, 0.68139, 2, 52, 7.91, -25.68, 0.09231, 53, -1.91, -4.17, 0.90769, 2, 52, 15.8, -24.89, 0.00146, 53, 5.98, -3.38, 0.99854, 2, 52, 23.78, -20.49, 0.02348, 53, 13.96, 1.01, 0.97652, 2, 52, 15.67, -15.68, 0.18007, 53, 5.85, 5.82, 0.81993, 3, 51, 3.73, -36.93, 0.00892, 52, 11.68, -8.37, 0.46776, 53, 1.86, 13.13, 0.52332, 3, 51, 0.76, -29.18, 0.1131, 52, 8.7, -0.62, 0.67343, 53, -1.12, 20.88, 0.21347, 4, 50, -18.54, -44.22, 0.00017, 51, 0.86, -19.93, 0.36628, 52, 8.81, 8.64, 0.59683, 53, -1.01, 30.14, 0.03672, 3, 50, -15.18, -35.08, 0.02819, 51, 4.22, -10.78, 0.65712, 52, 12.17, 17.78, 0.31469, 3, 50, -10.42, -27.64, 0.13431, 51, 8.98, -3.34, 0.78015, 52, 16.93, 25.22, 0.08554, 3, 50, -5.35, -22.64, 0.34364, 51, 14.05, 1.65, 0.6508, 52, 22, 30.21, 0.00556, 2, 50, 2.04, -16.97, 0.60072, 51, 21.44, 7.33, 0.39928, 2, 50, 4.24, -10.26, 0.81891, 51, 23.64, 14.03, 0.18109, 2, 50, 8.28, -8.08, 0.94274, 51, 27.68, 16.22, 0.05726, 2, 50, 15.56, -10.31, 0.99098, 51, 34.96, 13.99, 0.00902, 2, 50, -0.1, -3.43, 0.94587, 51, 19.3, 20.87, 0.05413, 2, 50, -9.23, -11.62, 0.53696, 51, 10.17, 12.68, 0.46304, 2, 50, -17.78, -23.46, 0.02843, 51, 1.62, 0.83, 0.97157, 2, 51, -4.26, -10.75, 0.6322, 52, 3.69, 17.81, 0.3678, 2, 51, -8.83, -23.45, 0.11579, 52, -0.88, 5.11, 0.88421, 2, 52, -1.01, -5.16, 0.86326, 53, -10.83, 16.34, 0.13674, 2, 52, 4.25, -15.62, 0.31012, 53, -5.57, 5.89, 0.68988, 1, 53, 1.05, 0.08, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 87, "height": 44}}, "body5": {"body": {"type": "mesh", "uvs": [0.10195, 0.02207, 0.03823, 0.11657, 0.0046, 0.30556, 0.02584, 0.49456, 0.06478, 0.64506, 0.12497, 0.77806, 0.23117, 0.87606, 0.21368, 0.74525, 0.28827, 0.81525, 0.36666, 0.88775, 0.47287, 0.96275, 0.59931, 1, 0.72574, 0.98525, 0.80154, 0.93775, 0.90522, 0.81275, 0.97223, 0.61775, 1, 0.40525, 0.9697, 0.23525, 0.8989, 0.08275, 0.86729, 0.28775, 0.79648, 0.41525, 0.71809, 0.52275, 0.61441, 0.57025, 0.50315, 0.54525, 0.40706, 0.48025, 0.33752, 0.39525, 0.25407, 0.26275, 0.17315, 0.25025, 0.13775, 0.17275, 0.14281, 0, 0.10867, 0.38275, 0.22499, 0.54025, 0.38051, 0.66525, 0.52591, 0.73275, 0.68016, 0.76525, 0.79522, 0.71275, 0.89763, 0.54025, 0.94441, 0.36275], "triangles": [14, 36, 15, 15, 36, 16, 16, 36, 37, 20, 19, 36, 36, 19, 37, 37, 17, 16, 37, 19, 17, 19, 18, 17, 11, 34, 12, 11, 33, 34, 12, 34, 13, 34, 35, 13, 13, 35, 14, 35, 36, 14, 33, 22, 34, 34, 21, 35, 34, 22, 21, 35, 20, 36, 35, 21, 20, 10, 33, 11, 10, 9, 33, 9, 32, 33, 9, 8, 32, 32, 8, 31, 8, 7, 31, 32, 23, 33, 33, 23, 22, 31, 25, 32, 32, 24, 23, 32, 25, 24, 31, 26, 25, 5, 7, 6, 7, 5, 31, 31, 5, 4, 4, 30, 31, 4, 3, 30, 30, 27, 31, 31, 27, 26, 3, 2, 30, 30, 2, 28, 30, 28, 27, 28, 2, 1, 1, 0, 28, 28, 0, 29], "vertices": [1, 55, 15.46, -6.63, 1, 1, 55, 12.73, -0.25, 1, 2, 55, 5.35, 4.56, 0.99396, 56, 24.75, 28.86, 0.00604, 2, 55, -3.17, 4.73, 0.94348, 56, 16.23, 29.03, 0.05652, 2, 55, -10.41, 3.01, 0.83589, 56, 8.99, 27.31, 0.16411, 2, 55, -17.33, -0.69, 0.69966, 56, 2.07, 23.61, 0.30034, 2, 55, -23.71, -8.64, 0.5852, 56, -4.31, 15.65, 0.4148, 2, 55, -17.76, -8.53, 0.4128, 56, 1.64, 15.77, 0.5872, 3, 55, -22.29, -14.1, 0.22576, 56, -2.89, 10.19, 0.76438, 57, 5.06, 38.76, 0.00986, 3, 55, -27, -19.97, 0.05737, 56, -7.6, 4.32, 0.84469, 57, 0.34, 32.89, 0.09794, 3, 55, -32.4, -28.17, 0.00402, 56, -13, -3.87, 0.68284, 57, -5.05, 24.69, 0.31314, 3, 56, -17.2, -14.17, 0.38659, 57, -9.25, 14.39, 0.61305, 58, -19.07, 35.9, 0.00035, 3, 56, -19.17, -25.01, 0.14321, 57, -11.23, 3.55, 0.84076, 58, -21.05, 25.06, 0.01603, 3, 56, -18.7, -31.91, 0.02507, 57, -10.76, -3.35, 0.85187, 58, -20.58, 18.15, 0.12305, 3, 56, -15.5, -41.98, 0.00187, 57, -7.55, -13.42, 0.63295, 58, -17.37, 8.09, 0.36518, 2, 57, -0.6, -21.11, 0.31861, 58, -10.42, 0.39, 0.68139, 2, 57, 7.91, -25.68, 0.09231, 58, -1.91, -4.17, 0.90769, 2, 57, 15.8, -24.89, 0.00146, 58, 5.98, -3.38, 0.99854, 2, 57, 23.78, -20.49, 0.02348, 58, 13.96, 1.01, 0.97652, 2, 57, 15.67, -15.68, 0.18007, 58, 5.85, 5.82, 0.81993, 3, 56, 3.73, -36.93, 0.00892, 57, 11.68, -8.37, 0.46776, 58, 1.86, 13.13, 0.52332, 3, 56, 0.76, -29.18, 0.1131, 57, 8.7, -0.62, 0.67343, 58, -1.12, 20.88, 0.21347, 4, 55, -18.54, -44.22, 0.00017, 56, 0.86, -19.93, 0.36628, 57, 8.81, 8.64, 0.59683, 58, -1.01, 30.14, 0.03672, 3, 55, -15.18, -35.08, 0.02819, 56, 4.22, -10.78, 0.65712, 57, 12.17, 17.78, 0.31469, 3, 55, -10.42, -27.64, 0.13431, 56, 8.98, -3.34, 0.78015, 57, 16.93, 25.22, 0.08554, 3, 55, -5.35, -22.64, 0.34364, 56, 14.05, 1.65, 0.6508, 57, 22, 30.21, 0.00556, 2, 55, 2.04, -16.97, 0.60072, 56, 21.44, 7.33, 0.39928, 2, 55, 4.24, -10.26, 0.81891, 56, 23.64, 14.03, 0.18109, 2, 55, 8.28, -8.08, 0.94274, 56, 27.68, 16.22, 0.05726, 2, 55, 15.56, -10.31, 0.99098, 56, 34.96, 13.99, 0.00902, 2, 55, -0.1, -3.43, 0.94587, 56, 19.3, 20.87, 0.05413, 2, 55, -9.23, -11.62, 0.53696, 56, 10.17, 12.68, 0.46304, 2, 55, -17.78, -23.46, 0.02843, 56, 1.62, 0.83, 0.97157, 2, 56, -4.26, -10.75, 0.6322, 57, 3.69, 17.81, 0.3678, 2, 56, -8.83, -23.45, 0.11579, 57, -0.88, 5.11, 0.88421, 2, 57, -1.01, -5.16, 0.86326, 58, -10.83, 16.34, 0.13674, 2, 57, 4.25, -15.62, 0.31012, 58, -5.57, 5.89, 0.68988, 1, 58, 1.05, 0.08, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 87, "height": 44}}, "body6": {"body": {"type": "mesh", "uvs": [0.10195, 0.02207, 0.03823, 0.11657, 0.0046, 0.30556, 0.02584, 0.49456, 0.06478, 0.64506, 0.12497, 0.77806, 0.23117, 0.87606, 0.21368, 0.74525, 0.28827, 0.81525, 0.36666, 0.88775, 0.47287, 0.96275, 0.59931, 1, 0.72574, 0.98525, 0.80154, 0.93775, 0.90522, 0.81275, 0.97223, 0.61775, 1, 0.40525, 0.9697, 0.23525, 0.8989, 0.08275, 0.86729, 0.28775, 0.79648, 0.41525, 0.71809, 0.52275, 0.61441, 0.57025, 0.50315, 0.54525, 0.40706, 0.48025, 0.33752, 0.39525, 0.25407, 0.26275, 0.17315, 0.25025, 0.13775, 0.17275, 0.14281, 0, 0.10867, 0.38275, 0.22499, 0.54025, 0.38051, 0.66525, 0.52591, 0.73275, 0.68016, 0.76525, 0.79522, 0.71275, 0.89763, 0.54025, 0.94441, 0.36275], "triangles": [14, 36, 15, 15, 36, 16, 16, 36, 37, 20, 19, 36, 36, 19, 37, 37, 17, 16, 37, 19, 17, 19, 18, 17, 11, 34, 12, 11, 33, 34, 12, 34, 13, 34, 35, 13, 13, 35, 14, 35, 36, 14, 33, 22, 34, 34, 21, 35, 34, 22, 21, 35, 20, 36, 35, 21, 20, 10, 33, 11, 10, 9, 33, 9, 32, 33, 9, 8, 32, 32, 8, 31, 8, 7, 31, 32, 23, 33, 33, 23, 22, 31, 25, 32, 32, 24, 23, 32, 25, 24, 31, 26, 25, 5, 7, 6, 7, 5, 31, 31, 5, 4, 4, 30, 31, 4, 3, 30, 30, 27, 31, 31, 27, 26, 3, 2, 30, 30, 2, 28, 30, 28, 27, 28, 2, 1, 1, 0, 28, 28, 0, 29], "vertices": [1, 60, 15.46, -6.63, 1, 1, 60, 12.73, -0.25, 1, 2, 60, 5.35, 4.56, 0.99396, 61, 24.75, 28.86, 0.00604, 2, 60, -3.17, 4.73, 0.94348, 61, 16.23, 29.03, 0.05652, 2, 60, -10.41, 3.01, 0.83589, 61, 8.99, 27.31, 0.16411, 2, 60, -17.33, -0.69, 0.69966, 61, 2.07, 23.61, 0.30034, 2, 60, -23.71, -8.64, 0.5852, 61, -4.31, 15.65, 0.4148, 2, 60, -17.76, -8.53, 0.4128, 61, 1.64, 15.77, 0.5872, 3, 60, -22.29, -14.1, 0.22576, 61, -2.89, 10.19, 0.76438, 62, 5.06, 38.76, 0.00986, 3, 60, -27, -19.97, 0.05737, 61, -7.6, 4.32, 0.84469, 62, 0.34, 32.89, 0.09794, 3, 60, -32.4, -28.17, 0.00402, 61, -13, -3.87, 0.68284, 62, -5.05, 24.69, 0.31314, 3, 61, -17.2, -14.17, 0.38659, 62, -9.25, 14.39, 0.61305, 63, -19.07, 35.9, 0.00035, 3, 61, -19.17, -25.01, 0.14321, 62, -11.23, 3.55, 0.84076, 63, -21.05, 25.06, 0.01603, 3, 61, -18.7, -31.91, 0.02507, 62, -10.76, -3.35, 0.85187, 63, -20.58, 18.15, 0.12305, 3, 61, -15.5, -41.98, 0.00187, 62, -7.55, -13.42, 0.63295, 63, -17.37, 8.09, 0.36518, 2, 62, -0.6, -21.11, 0.31861, 63, -10.42, 0.39, 0.68139, 2, 62, 7.91, -25.68, 0.09231, 63, -1.91, -4.17, 0.90769, 2, 62, 15.8, -24.89, 0.00146, 63, 5.98, -3.38, 0.99854, 2, 62, 23.78, -20.49, 0.02348, 63, 13.96, 1.01, 0.97652, 2, 62, 15.67, -15.68, 0.18007, 63, 5.85, 5.82, 0.81993, 3, 61, 3.73, -36.93, 0.00892, 62, 11.68, -8.37, 0.46776, 63, 1.86, 13.13, 0.52332, 3, 61, 0.76, -29.18, 0.1131, 62, 8.7, -0.62, 0.67343, 63, -1.12, 20.88, 0.21347, 4, 60, -18.54, -44.22, 0.00017, 61, 0.86, -19.93, 0.36628, 62, 8.81, 8.64, 0.59683, 63, -1.01, 30.14, 0.03672, 3, 60, -15.18, -35.08, 0.02819, 61, 4.22, -10.78, 0.65712, 62, 12.17, 17.78, 0.31469, 3, 60, -10.42, -27.64, 0.13431, 61, 8.98, -3.34, 0.78015, 62, 16.93, 25.22, 0.08554, 3, 60, -5.35, -22.64, 0.34364, 61, 14.05, 1.65, 0.6508, 62, 22, 30.21, 0.00556, 2, 60, 2.04, -16.97, 0.60072, 61, 21.44, 7.33, 0.39928, 2, 60, 4.24, -10.26, 0.81891, 61, 23.64, 14.03, 0.18109, 2, 60, 8.28, -8.08, 0.94274, 61, 27.68, 16.22, 0.05726, 2, 60, 15.56, -10.31, 0.99098, 61, 34.96, 13.99, 0.00902, 2, 60, -0.1, -3.43, 0.94587, 61, 19.3, 20.87, 0.05413, 2, 60, -9.23, -11.62, 0.53696, 61, 10.17, 12.68, 0.46304, 2, 60, -17.78, -23.46, 0.02843, 61, 1.62, 0.83, 0.97157, 2, 61, -4.26, -10.75, 0.6322, 62, 3.69, 17.81, 0.3678, 2, 61, -8.83, -23.45, 0.11579, 62, -0.88, 5.11, 0.88421, 2, 62, -1.01, -5.16, 0.86326, 63, -10.83, 16.34, 0.13674, 2, 62, 4.25, -15.62, 0.31012, 63, -5.57, 5.89, 0.68988, 1, 63, 1.05, 0.08, 1], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 87, "height": 44}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.11829, 0.01246, 0.05173, 0.10841, 0.0118, 0.29646, 0.01623, 0.5037, 0.09167, 0.71862, 0.19817, 0.88749, 0.32242, 0.98727, 0.49992, 0.98727, 0.64858, 0.92203, 0.80833, 0.83759, 0.90817, 0.66105, 1, 0.32716, 0.86379, 0.35403, 0.69517, 0.35787, 0.53542, 0.32332, 0.40451, 0.25424, 0.27583, 0.16214, 0.20039, 0.03933, 0.12495, 0.28495, 0.23367, 0.48451, 0.39342, 0.63803, 0.68408, 0.65338, 0.86601, 0.54592], "triangles": [9, 22, 10, 22, 13, 12, 10, 22, 11, 22, 12, 11, 21, 8, 7, 8, 21, 9, 9, 21, 22, 13, 22, 21, 20, 14, 21, 21, 14, 13, 7, 6, 20, 6, 5, 20, 21, 7, 20, 5, 19, 20, 5, 4, 19, 20, 15, 14, 20, 19, 15, 19, 16, 15, 4, 3, 19, 3, 18, 19, 3, 2, 18, 19, 18, 16, 2, 1, 18, 1, 0, 18, 18, 17, 16, 18, 0, 17], "vertices": [2, 22, 12.1, -6.13, 0.92517, 23, 23.63, 11.71, 0.07483, 2, 22, 9.42, -1.28, 0.97368, 23, 20.97, 16.58, 0.02632, 2, 22, 3.08, 2.55, 0.96267, 23, 14.64, 20.43, 0.03733, 2, 22, -4.51, 3.73, 0.80676, 23, 7.06, 21.64, 0.19324, 3, 22, -13.23, 0.5, 0.53577, 23, -1.67, 18.44, 0.46253, 24, 3.03, 38.01, 0.00169, 3, 22, -20.66, -5.01, 0.24602, 23, -9.12, 12.96, 0.72477, 24, -4.42, 32.53, 0.0292, 3, 22, -25.79, -12.12, 0.07436, 23, -14.28, 5.87, 0.76162, 24, -9.58, 25.44, 0.16402, 4, 22, -27.95, -23.27, 0.01202, 23, -16.48, -5.28, 0.56163, 24, -11.77, 14.29, 0.42486, 25, -14.62, 31.23, 0.00149, 3, 23, -15.95, -15.08, 0.26926, 24, -11.24, 4.49, 0.66868, 25, -14.09, 21.43, 0.06206, 3, 23, -14.86, -25.71, 0.07074, 24, -10.15, -6.14, 0.66796, 25, -13, 10.79, 0.2613, 3, 23, -9.68, -33.25, 0.00144, 24, -4.98, -13.68, 0.40542, 25, -7.83, 3.26, 0.59314, 2, 24, 6.01, -21.83, 0.20024, 25, 3.16, -4.89, 0.79976, 3, 23, 2.01, -32.66, 0.01937, 24, 6.72, -13.08, 0.33775, 25, 3.87, 3.85, 0.64288, 4, 22, -7.45, -39.96, 0.00201, 23, 3.96, -22.04, 0.19198, 24, 8.66, -2.47, 0.49646, 25, 5.81, 14.47, 0.30956, 4, 22, -4.26, -30.16, 0.05221, 23, 7.19, -12.26, 0.44385, 24, 11.89, 7.32, 0.46156, 25, 9.04, 24.25, 0.04237, 4, 22, -0.16, -22.42, 0.24958, 23, 11.31, -4.53, 0.55993, 24, 16.02, 15.04, 0.19047, 25, 13.17, 31.98, 1e-05, 3, 22, 4.75, -14.98, 0.53241, 23, 16.25, 2.89, 0.43583, 24, 20.95, 22.46, 0.03176, 3, 22, 10.13, -11.1, 0.79497, 23, 21.64, 6.75, 0.20452, 24, 26.34, 26.32, 0.00051, 2, 22, 2.12, -4.64, 0.89783, 23, 13.66, 13.25, 0.10217, 2, 22, -6.45, -10.07, 0.4052, 23, 5.07, 7.85, 0.5948, 3, 22, -13.97, -19.03, 0.00103, 23, -2.48, -1.09, 0.92649, 24, 2.22, 18.48, 0.07248, 3, 23, -6.63, -19.23, 0.02453, 24, -1.93, 0.34, 0.96398, 25, -4.78, 17.28, 0.01149, 2, 24, -0.28, -11.85, 0.34014, 25, -3.13, 5.08, 0.65986], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 64, "height": 37}}, "body_3": {"body_3": {"type": "mesh", "uvs": [0.00295, 0.03257, 0.02504, 0.38448, 0.13233, 0.64996, 0.28064, 0.94013, 0.50784, 1, 0.77606, 0.92161, 0.91491, 0.62526, 1, 0.20543, 1, 0, 0.79815, 0.12517, 0.61829, 0.16839, 0.45104, 0.16839, 0.25855, 0.05726, 0.11024, 0, 0.1702, 0.25483, 0.29011, 0.47091, 0.46366, 0.61909, 0.62144, 0.60056, 0.78869, 0.45856, 0.89597, 0.30422], "triangles": [19, 9, 8, 7, 19, 8, 18, 10, 9, 18, 9, 19, 6, 19, 7, 18, 19, 6, 5, 18, 6, 15, 12, 11, 17, 10, 18, 16, 11, 10, 16, 10, 17, 15, 11, 16, 5, 17, 18, 3, 2, 15, 3, 15, 16, 4, 16, 17, 4, 17, 5, 3, 16, 4, 14, 13, 12, 14, 1, 0, 14, 0, 13, 14, 12, 15, 2, 1, 14, 2, 14, 15], "vertices": [2, 27, 4.92, 2.82, 0.97323, 28, 12.66, 20.2, 0.02677, 2, 27, -3.21, 2.23, 0.84186, 28, 4.53, 19.61, 0.15814, 2, 27, -9.55, -2.28, 0.56021, 28, -1.81, 15.09, 0.43979, 3, 27, -16.55, -8.62, 0.24271, 28, -8.81, 8.76, 0.73983, 29, -14.49, 30.53, 0.01746, 3, 27, -18.43, -18.76, 0.0529, 28, -10.7, -1.39, 0.80361, 29, -16.38, 20.39, 0.1435, 3, 27, -17.24, -30.9, 0.00121, 28, -9.5, -13.53, 0.61639, 29, -15.18, 8.24, 0.3824, 2, 28, -3, -20.11, 0.30173, 29, -8.68, 1.66, 0.69827, 2, 28, 6.45, -24.42, 0.09443, 29, 0.77, -2.64, 0.90557, 2, 28, 11.17, -24.65, 0.07158, 29, 5.49, -2.88, 0.92842, 3, 27, 1.01, -32.81, 0.00042, 28, 8.75, -15.44, 0.30492, 29, 3.07, 6.34, 0.69466, 3, 27, 0.42, -24.68, 0.04292, 28, 8.16, -7.3, 0.58605, 29, 2.48, 14.47, 0.37104, 3, 27, 0.8, -17.16, 0.26306, 28, 8.53, 0.21, 0.62766, 29, 2.85, 21.99, 0.10928, 3, 27, 3.78, -8.64, 0.58383, 28, 11.52, 8.74, 0.40647, 29, 5.84, 30.51, 0.00971, 2, 27, 5.43, -2.04, 0.87466, 28, 13.17, 15.34, 0.12534, 2, 27, -0.56, -4.44, 0.81021, 28, 7.18, 12.93, 0.18979, 2, 27, -5.79, -9.58, 0.35855, 28, 1.95, 7.79, 0.64145, 3, 27, -9.58, -17.21, 0.00484, 28, -1.85, 0.16, 0.9887, 29, -7.53, 21.94, 0.00646, 2, 28, -1.78, -6.95, 0.78514, 29, -7.46, 14.82, 0.21486, 2, 28, 1.11, -14.63, 0.37317, 29, -4.57, 7.14, 0.62683, 2, 28, 4.41, -19.63, 0.07504, 29, -1.27, 2.14, 0.92496], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 45, "height": 23}}, "body_4": {"body_4": {"x": 11.87, "y": -0.88, "rotation": -89.81, "width": 25, "height": 28}}, "body_5": {"body_5": {"type": "mesh", "uvs": [0.13002, 0.02462, 0.03705, 0.1868, 0, 0.40302, 0.05797, 0.59468, 0.20673, 0.81091, 0.35083, 0.91902, 0.5577, 0.99765, 0.74132, 0.94851, 0.86218, 0.85022, 0.96678, 0.6684, 1, 0.41285, 0.96446, 0.13765, 0.79943, 0, 0.82964, 0.17205, 0.83197, 0.31457, 0.74597, 0.19171, 0.65067, 0.13274, 0.61116, 0.21628, 0.62278, 0.03937, 0.52516, 0.07868, 0.42521, 0.14257, 0.44846, 0, 0.35083, 0.00497, 0.28343, 0.09342, 0.19278, 0.22611, 0.18116, 0.07868, 0.06959, 0.36862, 0.1951, 0.44234, 0.34154, 0.49148, 0.46937, 0.50622, 0.60883, 0.51114, 0.76456, 0.46691, 0.89705, 0.35388], "triangles": [7, 31, 8, 9, 8, 31, 9, 31, 32, 32, 31, 14, 9, 32, 10, 31, 15, 14, 32, 11, 10, 11, 32, 13, 32, 14, 13, 13, 12, 11, 5, 29, 6, 6, 30, 7, 6, 29, 30, 7, 30, 31, 31, 17, 15, 17, 31, 30, 17, 16, 15, 30, 29, 17, 29, 19, 17, 29, 20, 19, 17, 19, 18, 4, 28, 5, 5, 28, 29, 3, 27, 4, 4, 27, 28, 28, 20, 29, 28, 24, 23, 20, 23, 22, 20, 28, 23, 28, 27, 24, 20, 22, 21, 3, 26, 27, 3, 2, 26, 26, 24, 27, 2, 1, 26, 25, 24, 0, 0, 24, 1, 1, 24, 26], "vertices": [2, 38, 10.43, -8.03, 0.90826, 37, 19.26, 10, 0.09174, 2, 38, 6.55, 0, 0.98884, 37, 15.37, 18.03, 0.01116, 1, 38, -0.15, 4.45, 1, 2, 38, -7.69, 1.87, 0.80867, 37, 1.14, 19.91, 0.19133, 3, 38, -17.65, -7.03, 0.20507, 37, -8.82, 11, 0.78368, 36, -3.6, 30.97, 0.01125, 3, 38, -23.85, -16.49, 0.01022, 37, -15.03, 1.54, 0.76246, 36, -9.8, 21.51, 0.22732, 3, 37, -21.33, -12.68, 0.2232, 36, -16.1, 7.29, 0.73784, 35, -14.55, 28.16, 0.03896, 3, 37, -22.88, -26.29, 0.01162, 36, -17.65, -6.32, 0.65085, 35, -16.1, 14.55, 0.33753, 2, 36, -16.43, -15.82, 0.32872, 35, -14.88, 5.05, 0.67128, 2, 36, -12.08, -24.85, 0.0514, 35, -10.53, -3.98, 0.9486, 1, 35, -2.42, -8.49, 1, 1, 35, 7.56, -8.22, 1, 1, 35, 15.13, 2.51, 1, 2, 36, 7.2, -19.11, 0.00514, 35, 8.75, 1.76, 0.99486, 2, 36, 2.31, -18.09, 0.14956, 35, 3.86, 2.78, 0.85044, 2, 36, 8, -12.93, 0.64509, 35, 9.55, 7.94, 0.35491, 3, 37, 6.45, -26.54, 0.01629, 36, 11.68, -6.57, 0.8273, 35, 13.23, 14.3, 0.15642, 3, 37, 4.3, -23, 0.09008, 36, 9.53, -3.03, 0.85291, 35, 11.08, 17.83, 0.05701, 3, 37, 10.12, -25.31, 0.16144, 36, 15.34, -5.34, 0.83587, 35, 16.89, 15.53, 0.00268, 3, 37, 10.49, -17.96, 0.27449, 36, 15.72, 2.01, 0.72542, 35, 17.26, 22.88, 9e-05, 3, 38, 1.25, -28.28, 0.00226, 37, 10.07, -10.25, 0.69024, 36, 15.3, 9.72, 0.3075, 2, 37, 14.51, -13.1, 0.79858, 36, 19.74, 6.87, 0.20142, 3, 38, 7.23, -24.07, 0.02087, 37, 16.05, -6.04, 0.83401, 36, 21.28, 13.93, 0.14512, 3, 38, 5.4, -18.49, 0.10118, 37, 14.23, -0.46, 0.83051, 36, 19.45, 19.51, 0.06831, 3, 38, 2.48, -10.87, 0.58443, 37, 11.31, 7.16, 0.41394, 36, 16.53, 27.13, 0.00164, 2, 38, 7.7, -11.26, 0.86223, 37, 16.52, 6.77, 0.13777, 2, 38, -0.2, -0.84, 0.98166, 37, 8.62, 17.2, 0.01834, 2, 38, -4.91, -9.25, 0.44461, 37, 3.91, 8.78, 0.55539, 2, 37, -0.33, -1.34, 0.9577, 36, 4.9, 18.63, 0.0423, 2, 37, -3.07, -10.41, 0.47098, 36, 2.16, 9.57, 0.52902, 2, 36, -0.46, -0.42, 0.99086, 35, 1.09, 20.45, 0.00914, 2, 36, -1.68, -11.98, 0.47917, 35, -0.13, 8.88, 0.52083, 1, 35, 1.39, -1.58, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 74, "height": 35}}, "body_6": {"body_6": {"type": "mesh", "uvs": [0, 0, 0.05012, 0.44041, 0.18697, 0.68499, 0.38206, 0.91211, 0.5917, 0.99946, 0.76641, 0.8364, 0.91782, 0.55105, 1, 0.25988, 1, 0, 0.76641, 0.091, 0.53347, 0.09682, 0.29762, 0.07935, 0.14621, 0.02694, 0.13456, 0.28317, 0.3675, 0.44041, 0.60335, 0.50446, 0.81008, 0.40546, 0.92073, 0.25405], "triangles": [17, 9, 8, 17, 8, 7, 16, 9, 17, 6, 16, 17, 6, 17, 7, 5, 16, 6, 14, 11, 10, 15, 10, 9, 15, 9, 16, 14, 10, 15, 5, 15, 16, 3, 14, 15, 2, 14, 3, 4, 3, 15, 4, 15, 5, 13, 0, 12, 13, 12, 11, 13, 11, 14, 1, 0, 13, 2, 13, 14, 1, 13, 2], "vertices": [1, 31, 3.93, 2.31, 1, 2, 31, -3.62, 0.94, 0.92025, 32, 1.09, 15.14, 0.07975, 2, 31, -7.98, -3.53, 0.52123, 32, -3.26, 10.67, 0.47877, 3, 31, -12.12, -9.99, 0.10104, 32, -7.41, 4.21, 0.8966, 33, -11.31, 19.49, 0.00236, 3, 31, -13.92, -17.05, 0.00101, 32, -9.2, -2.85, 0.90843, 33, -13.1, 12.43, 0.09056, 2, 32, -6.69, -8.9, 0.68036, 33, -10.59, 6.38, 0.31964, 2, 32, -2.07, -14.26, 0.25877, 33, -5.97, 1.02, 0.74123, 2, 32, 2.76, -17.26, 0.00365, 33, -1.15, -1.98, 0.99635, 1, 33, 3.27, -2.18, 1, 2, 32, 5.97, -9.45, 0.34939, 33, 2.07, 5.83, 0.65061, 3, 31, 1.5, -15.73, 0.03459, 32, 6.21, -1.54, 0.8675, 33, 2.31, 13.74, 0.09791, 2, 31, 2.14, -7.74, 0.51901, 32, 6.86, 6.46, 0.48099, 2, 31, 3.26, -2.63, 0.91785, 32, 7.97, 11.57, 0.08215, 2, 31, -1.08, -2.05, 0.89179, 32, 3.64, 12.15, 0.10821, 2, 31, -4.09, -9.84, 0.23187, 32, 0.62, 4.36, 0.76813, 2, 32, -0.81, -3.61, 0.84958, 33, -4.72, 11.67, 0.15042, 2, 32, 0.56, -10.7, 0.35241, 33, -3.34, 4.57, 0.64759, 2, 32, 2.97, -14.57, 0.04335, 33, -0.93, 0.7, 0.95665], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 34, "height": 17}}, "body_7": {"body_2": {"type": "mesh", "uvs": [0.11829, 0.01246, 0.05173, 0.10841, 0.0118, 0.29646, 0.01623, 0.5037, 0.09167, 0.71862, 0.19817, 0.88749, 0.32242, 0.98727, 0.49992, 0.98727, 0.64858, 0.92203, 0.80833, 0.83759, 0.90817, 0.66105, 1, 0.32716, 0.86379, 0.35403, 0.69517, 0.35787, 0.53542, 0.32332, 0.40451, 0.25424, 0.27583, 0.16214, 0.20039, 0.03933, 0.12495, 0.28495, 0.23367, 0.48451, 0.39342, 0.63803, 0.68408, 0.65338, 0.86601, 0.54592], "triangles": [9, 22, 10, 22, 13, 12, 10, 22, 11, 22, 12, 11, 21, 8, 7, 8, 21, 9, 9, 21, 22, 13, 22, 21, 20, 14, 21, 21, 14, 13, 7, 6, 20, 6, 5, 20, 21, 7, 20, 5, 19, 20, 5, 4, 19, 20, 15, 14, 20, 19, 15, 19, 16, 15, 4, 3, 19, 3, 18, 19, 3, 2, 18, 19, 18, 16, 2, 1, 18, 1, 0, 18, 18, 17, 16, 18, 0, 17], "vertices": [2, 65, 12.1, -6.13, 0.92517, 66, 23.63, 11.71, 0.07483, 2, 65, 9.42, -1.28, 0.97368, 66, 20.97, 16.58, 0.02632, 2, 65, 3.08, 2.55, 0.96267, 66, 14.64, 20.43, 0.03733, 2, 65, -4.51, 3.73, 0.80676, 66, 7.06, 21.64, 0.19324, 3, 65, -13.23, 0.5, 0.53577, 66, -1.67, 18.44, 0.46253, 67, 3.03, 38.01, 0.00169, 3, 65, -20.66, -5.01, 0.24602, 66, -9.12, 12.96, 0.72477, 67, -4.42, 32.53, 0.0292, 3, 65, -25.79, -12.12, 0.07436, 66, -14.28, 5.87, 0.76162, 67, -9.58, 25.44, 0.16402, 4, 65, -27.95, -23.27, 0.01202, 66, -16.48, -5.28, 0.56163, 67, -11.77, 14.29, 0.42486, 68, -14.62, 31.23, 0.00149, 3, 66, -15.95, -15.08, 0.26926, 67, -11.24, 4.49, 0.66868, 68, -14.09, 21.43, 0.06206, 3, 66, -14.86, -25.71, 0.07074, 67, -10.15, -6.14, 0.66796, 68, -13, 10.79, 0.2613, 3, 66, -9.68, -33.25, 0.00144, 67, -4.98, -13.68, 0.40542, 68, -7.83, 3.26, 0.59314, 2, 67, 6.01, -21.83, 0.20024, 68, 3.16, -4.89, 0.79976, 3, 66, 2.01, -32.66, 0.01937, 67, 6.72, -13.08, 0.33775, 68, 3.87, 3.85, 0.64288, 4, 65, -7.45, -39.96, 0.00201, 66, 3.96, -22.04, 0.19198, 67, 8.66, -2.47, 0.49646, 68, 5.81, 14.47, 0.30956, 4, 65, -4.26, -30.16, 0.05221, 66, 7.19, -12.26, 0.44385, 67, 11.89, 7.32, 0.46156, 68, 9.04, 24.25, 0.04237, 4, 65, -0.16, -22.42, 0.24958, 66, 11.31, -4.53, 0.55993, 67, 16.02, 15.04, 0.19047, 68, 13.17, 31.98, 1e-05, 3, 65, 4.75, -14.98, 0.53241, 66, 16.25, 2.89, 0.43583, 67, 20.95, 22.46, 0.03176, 3, 65, 10.13, -11.1, 0.79497, 66, 21.64, 6.75, 0.20452, 67, 26.34, 26.32, 0.00051, 2, 65, 2.12, -4.64, 0.89783, 66, 13.66, 13.25, 0.10217, 2, 65, -6.45, -10.07, 0.4052, 66, 5.07, 7.85, 0.5948, 3, 65, -13.97, -19.03, 0.00103, 66, -2.48, -1.09, 0.92649, 67, 2.22, 18.48, 0.07248, 3, 66, -6.63, -19.23, 0.02453, 67, -1.93, 0.34, 0.96398, 68, -4.78, 17.28, 0.01149, 2, 67, -0.28, -11.85, 0.34014, 68, -3.13, 5.08, 0.65986], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 64, "height": 37}}, "body_8": {"body_2": {"type": "mesh", "uvs": [0.11829, 0.01246, 0.05173, 0.10841, 0.0118, 0.29646, 0.01623, 0.5037, 0.09167, 0.71862, 0.19817, 0.88749, 0.32242, 0.98727, 0.49992, 0.98727, 0.64858, 0.92203, 0.80833, 0.83759, 0.90817, 0.66105, 1, 0.32716, 0.86379, 0.35403, 0.69517, 0.35787, 0.53542, 0.32332, 0.40451, 0.25424, 0.27583, 0.16214, 0.20039, 0.03933, 0.12495, 0.28495, 0.23367, 0.48451, 0.39342, 0.63803, 0.68408, 0.65338, 0.86601, 0.54592], "triangles": [9, 22, 10, 22, 13, 12, 10, 22, 11, 22, 12, 11, 21, 8, 7, 8, 21, 9, 9, 21, 22, 13, 22, 21, 20, 14, 21, 21, 14, 13, 7, 6, 20, 6, 5, 20, 21, 7, 20, 5, 19, 20, 5, 4, 19, 20, 15, 14, 20, 19, 15, 19, 16, 15, 4, 3, 19, 3, 18, 19, 3, 2, 18, 19, 18, 16, 2, 1, 18, 1, 0, 18, 18, 17, 16, 18, 0, 17], "vertices": [2, 70, 12.1, -6.13, 0.92517, 71, 23.63, 11.71, 0.07483, 2, 70, 9.42, -1.28, 0.97368, 71, 20.97, 16.58, 0.02632, 2, 70, 3.08, 2.55, 0.96267, 71, 14.64, 20.43, 0.03733, 2, 70, -4.51, 3.73, 0.80676, 71, 7.06, 21.64, 0.19324, 3, 70, -13.23, 0.5, 0.53577, 71, -1.67, 18.44, 0.46253, 72, 3.03, 38.01, 0.00169, 3, 70, -20.66, -5.01, 0.24602, 71, -9.12, 12.96, 0.72477, 72, -4.42, 32.53, 0.0292, 3, 70, -25.79, -12.12, 0.07436, 71, -14.28, 5.87, 0.76162, 72, -9.58, 25.44, 0.16402, 4, 70, -27.95, -23.27, 0.01202, 71, -16.48, -5.28, 0.56163, 72, -11.77, 14.29, 0.42486, 73, -14.62, 31.23, 0.00149, 3, 71, -15.95, -15.08, 0.26926, 72, -11.24, 4.49, 0.66868, 73, -14.09, 21.43, 0.06206, 3, 71, -14.86, -25.71, 0.07074, 72, -10.15, -6.14, 0.66796, 73, -13, 10.79, 0.2613, 3, 71, -9.68, -33.25, 0.00144, 72, -4.98, -13.68, 0.40542, 73, -7.83, 3.26, 0.59314, 2, 72, 6.01, -21.83, 0.20024, 73, 3.16, -4.89, 0.79976, 3, 71, 2.01, -32.66, 0.01937, 72, 6.72, -13.08, 0.33775, 73, 3.87, 3.85, 0.64288, 4, 70, -7.45, -39.96, 0.00201, 71, 3.96, -22.04, 0.19198, 72, 8.66, -2.47, 0.49646, 73, 5.81, 14.47, 0.30956, 4, 70, -4.26, -30.16, 0.05221, 71, 7.19, -12.26, 0.44385, 72, 11.89, 7.32, 0.46156, 73, 9.04, 24.25, 0.04237, 4, 70, -0.16, -22.42, 0.24958, 71, 11.31, -4.53, 0.55993, 72, 16.02, 15.04, 0.19047, 73, 13.17, 31.98, 1e-05, 3, 70, 4.75, -14.98, 0.53241, 71, 16.25, 2.89, 0.43583, 72, 20.95, 22.46, 0.03176, 3, 70, 10.13, -11.1, 0.79497, 71, 21.64, 6.75, 0.20452, 72, 26.34, 26.32, 0.00051, 2, 70, 2.12, -4.64, 0.89783, 71, 13.66, 13.25, 0.10217, 2, 70, -6.45, -10.07, 0.4052, 71, 5.07, 7.85, 0.5948, 3, 70, -13.97, -19.03, 0.00103, 71, -2.48, -1.09, 0.92649, 72, 2.22, 18.48, 0.07248, 3, 71, -6.63, -19.23, 0.02453, 72, -1.93, 0.34, 0.96398, 73, -4.78, 17.28, 0.01149, 2, 72, -0.28, -11.85, 0.34014, 73, -3.13, 5.08, 0.65986], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 64, "height": 37}}, "body_9": {"body_2": {"type": "mesh", "uvs": [0.11829, 0.01246, 0.05173, 0.10841, 0.0118, 0.29646, 0.01623, 0.5037, 0.09167, 0.71862, 0.19817, 0.88749, 0.32242, 0.98727, 0.49992, 0.98727, 0.64858, 0.92203, 0.80833, 0.83759, 0.90817, 0.66105, 1, 0.32716, 0.86379, 0.35403, 0.69517, 0.35787, 0.53542, 0.32332, 0.40451, 0.25424, 0.27583, 0.16214, 0.20039, 0.03933, 0.12495, 0.28495, 0.23367, 0.48451, 0.39342, 0.63803, 0.68408, 0.65338, 0.86601, 0.54592], "triangles": [9, 22, 10, 22, 13, 12, 10, 22, 11, 22, 12, 11, 21, 8, 7, 8, 21, 9, 9, 21, 22, 13, 22, 21, 20, 14, 21, 21, 14, 13, 7, 6, 20, 6, 5, 20, 21, 7, 20, 5, 19, 20, 5, 4, 19, 20, 15, 14, 20, 19, 15, 19, 16, 15, 4, 3, 19, 3, 18, 19, 3, 2, 18, 19, 18, 16, 2, 1, 18, 1, 0, 18, 18, 17, 16, 18, 0, 17], "vertices": [2, 75, 12.1, -6.13, 0.92517, 76, 23.63, 11.71, 0.07483, 2, 75, 9.42, -1.28, 0.97368, 76, 20.97, 16.58, 0.02632, 2, 75, 3.08, 2.55, 0.96267, 76, 14.64, 20.43, 0.03733, 2, 75, -4.51, 3.73, 0.80676, 76, 7.06, 21.64, 0.19324, 3, 75, -13.23, 0.5, 0.53577, 76, -1.67, 18.44, 0.46253, 77, 3.03, 38.01, 0.00169, 3, 75, -20.66, -5.01, 0.24602, 76, -9.12, 12.96, 0.72477, 77, -4.42, 32.53, 0.0292, 3, 75, -25.79, -12.12, 0.07436, 76, -14.28, 5.87, 0.76162, 77, -9.58, 25.44, 0.16402, 4, 75, -27.95, -23.27, 0.01202, 76, -16.48, -5.28, 0.56163, 77, -11.77, 14.29, 0.42486, 78, -14.62, 31.23, 0.00149, 3, 76, -15.95, -15.08, 0.26926, 77, -11.24, 4.49, 0.66868, 78, -14.09, 21.43, 0.06206, 3, 76, -14.86, -25.71, 0.07074, 77, -10.15, -6.14, 0.66796, 78, -13, 10.79, 0.2613, 3, 76, -9.68, -33.25, 0.00144, 77, -4.98, -13.68, 0.40542, 78, -7.83, 3.26, 0.59314, 2, 77, 6.01, -21.83, 0.20024, 78, 3.16, -4.89, 0.79976, 3, 76, 2.01, -32.66, 0.01937, 77, 6.72, -13.08, 0.33775, 78, 3.87, 3.85, 0.64288, 4, 75, -7.45, -39.96, 0.00201, 76, 3.96, -22.04, 0.19198, 77, 8.66, -2.47, 0.49646, 78, 5.81, 14.47, 0.30956, 4, 75, -4.26, -30.16, 0.05221, 76, 7.19, -12.26, 0.44385, 77, 11.89, 7.32, 0.46156, 78, 9.04, 24.25, 0.04237, 4, 75, -0.16, -22.42, 0.24958, 76, 11.31, -4.53, 0.55993, 77, 16.02, 15.04, 0.19047, 78, 13.17, 31.98, 1e-05, 3, 75, 4.75, -14.98, 0.53241, 76, 16.25, 2.89, 0.43583, 77, 20.95, 22.46, 0.03176, 3, 75, 10.13, -11.1, 0.79497, 76, 21.64, 6.75, 0.20452, 77, 26.34, 26.32, 0.00051, 2, 75, 2.12, -4.64, 0.89783, 76, 13.66, 13.25, 0.10217, 2, 75, -6.45, -10.07, 0.4052, 76, 5.07, 7.85, 0.5948, 3, 75, -13.97, -19.03, 0.00103, 76, -2.48, -1.09, 0.92649, 77, 2.22, 18.48, 0.07248, 3, 76, -6.63, -19.23, 0.02453, 77, -1.93, 0.34, 0.96398, 78, -4.78, 17.28, 0.01149, 2, 77, -0.28, -11.85, 0.34014, 78, -3.13, 5.08, 0.65986], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 64, "height": 37}}, "body_10": {"body_2": {"type": "mesh", "uvs": [0.11829, 0.01246, 0.05173, 0.10841, 0.0118, 0.29646, 0.01623, 0.5037, 0.09167, 0.71862, 0.19817, 0.88749, 0.32242, 0.98727, 0.49992, 0.98727, 0.64858, 0.92203, 0.80833, 0.83759, 0.90817, 0.66105, 1, 0.32716, 0.86379, 0.35403, 0.69517, 0.35787, 0.53542, 0.32332, 0.40451, 0.25424, 0.27583, 0.16214, 0.20039, 0.03933, 0.12495, 0.28495, 0.23367, 0.48451, 0.39342, 0.63803, 0.68408, 0.65338, 0.86601, 0.54592], "triangles": [9, 22, 10, 22, 13, 12, 10, 22, 11, 22, 12, 11, 21, 8, 7, 8, 21, 9, 9, 21, 22, 13, 22, 21, 20, 14, 21, 21, 14, 13, 7, 6, 20, 6, 5, 20, 21, 7, 20, 5, 19, 20, 5, 4, 19, 20, 15, 14, 20, 19, 15, 19, 16, 15, 4, 3, 19, 3, 18, 19, 3, 2, 18, 19, 18, 16, 2, 1, 18, 1, 0, 18, 18, 17, 16, 18, 0, 17], "vertices": [2, 80, 12.1, -6.13, 0.92517, 81, 23.63, 11.71, 0.07483, 2, 80, 9.42, -1.28, 0.97368, 81, 20.97, 16.58, 0.02632, 2, 80, 3.08, 2.55, 0.96267, 81, 14.64, 20.43, 0.03733, 2, 80, -4.51, 3.73, 0.80676, 81, 7.06, 21.64, 0.19324, 3, 80, -13.23, 0.5, 0.53577, 81, -1.67, 18.44, 0.46253, 82, 3.03, 38.01, 0.00169, 3, 80, -20.66, -5.01, 0.24602, 81, -9.12, 12.96, 0.72477, 82, -4.42, 32.53, 0.0292, 3, 80, -25.79, -12.12, 0.07436, 81, -14.28, 5.87, 0.76162, 82, -9.58, 25.44, 0.16402, 4, 80, -27.95, -23.27, 0.01202, 81, -16.48, -5.28, 0.56163, 82, -11.77, 14.29, 0.42486, 83, -14.62, 31.23, 0.00149, 3, 81, -15.95, -15.08, 0.26926, 82, -11.24, 4.49, 0.66868, 83, -14.09, 21.43, 0.06206, 3, 81, -14.86, -25.71, 0.07074, 82, -10.15, -6.14, 0.66796, 83, -13, 10.79, 0.2613, 3, 81, -9.68, -33.25, 0.00144, 82, -4.98, -13.68, 0.40542, 83, -7.83, 3.26, 0.59314, 2, 82, 6.01, -21.83, 0.20024, 83, 3.16, -4.89, 0.79976, 3, 81, 2.01, -32.66, 0.01937, 82, 6.72, -13.08, 0.33775, 83, 3.87, 3.85, 0.64288, 4, 80, -7.45, -39.96, 0.00201, 81, 3.96, -22.04, 0.19198, 82, 8.66, -2.47, 0.49646, 83, 5.81, 14.47, 0.30956, 4, 80, -4.26, -30.16, 0.05221, 81, 7.19, -12.26, 0.44385, 82, 11.89, 7.32, 0.46156, 83, 9.04, 24.25, 0.04237, 4, 80, -0.16, -22.42, 0.24958, 81, 11.31, -4.53, 0.55993, 82, 16.02, 15.04, 0.19047, 83, 13.17, 31.98, 1e-05, 3, 80, 4.75, -14.98, 0.53241, 81, 16.25, 2.89, 0.43583, 82, 20.95, 22.46, 0.03176, 3, 80, 10.13, -11.1, 0.79497, 81, 21.64, 6.75, 0.20452, 82, 26.34, 26.32, 0.00051, 2, 80, 2.12, -4.64, 0.89783, 81, 13.66, 13.25, 0.10217, 2, 80, -6.45, -10.07, 0.4052, 81, 5.07, 7.85, 0.5948, 3, 80, -13.97, -19.03, 0.00103, 81, -2.48, -1.09, 0.92649, 82, 2.22, 18.48, 0.07248, 3, 81, -6.63, -19.23, 0.02453, 82, -1.93, 0.34, 0.96398, 83, -4.78, 17.28, 0.01149, 2, 82, -0.28, -11.85, 0.34014, 83, -3.13, 5.08, 0.65986], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 64, "height": 37}}, "body_11": {"body_2": {"type": "mesh", "uvs": [0.11829, 0.01246, 0.05173, 0.10841, 0.0118, 0.29646, 0.01623, 0.5037, 0.09167, 0.71862, 0.19817, 0.88749, 0.32242, 0.98727, 0.49992, 0.98727, 0.64858, 0.92203, 0.80833, 0.83759, 0.90817, 0.66105, 1, 0.32716, 0.86379, 0.35403, 0.69517, 0.35787, 0.53542, 0.32332, 0.40451, 0.25424, 0.27583, 0.16214, 0.20039, 0.03933, 0.12495, 0.28495, 0.23367, 0.48451, 0.39342, 0.63803, 0.68408, 0.65338, 0.86601, 0.54592], "triangles": [9, 22, 10, 22, 13, 12, 10, 22, 11, 22, 12, 11, 21, 8, 7, 8, 21, 9, 9, 21, 22, 13, 22, 21, 20, 14, 21, 21, 14, 13, 7, 6, 20, 6, 5, 20, 21, 7, 20, 5, 19, 20, 5, 4, 19, 20, 15, 14, 20, 19, 15, 19, 16, 15, 4, 3, 19, 3, 18, 19, 3, 2, 18, 19, 18, 16, 2, 1, 18, 1, 0, 18, 18, 17, 16, 18, 0, 17], "vertices": [2, 85, 12.1, -6.13, 0.92517, 86, 23.63, 11.71, 0.07483, 2, 85, 9.42, -1.28, 0.97368, 86, 20.97, 16.58, 0.02632, 2, 85, 3.08, 2.55, 0.96267, 86, 14.64, 20.43, 0.03733, 2, 85, -4.51, 3.73, 0.80676, 86, 7.06, 21.64, 0.19324, 3, 85, -13.23, 0.5, 0.53577, 86, -1.67, 18.44, 0.46253, 87, 3.03, 38.01, 0.00169, 3, 85, -20.66, -5.01, 0.24602, 86, -9.12, 12.96, 0.72477, 87, -4.42, 32.53, 0.0292, 3, 85, -25.79, -12.12, 0.07436, 86, -14.28, 5.87, 0.76162, 87, -9.58, 25.44, 0.16402, 4, 85, -27.95, -23.27, 0.01202, 86, -16.48, -5.28, 0.56163, 87, -11.77, 14.29, 0.42486, 88, -14.62, 31.23, 0.00149, 3, 86, -15.95, -15.08, 0.26926, 87, -11.24, 4.49, 0.66868, 88, -14.09, 21.43, 0.06206, 3, 86, -14.86, -25.71, 0.07074, 87, -10.15, -6.14, 0.66796, 88, -13, 10.79, 0.2613, 3, 86, -9.68, -33.25, 0.00144, 87, -4.98, -13.68, 0.40542, 88, -7.83, 3.26, 0.59314, 2, 87, 6.01, -21.83, 0.20024, 88, 3.16, -4.89, 0.79976, 3, 86, 2.01, -32.66, 0.01937, 87, 6.72, -13.08, 0.33775, 88, 3.87, 3.85, 0.64288, 4, 85, -7.45, -39.96, 0.00201, 86, 3.96, -22.04, 0.19198, 87, 8.66, -2.47, 0.49646, 88, 5.81, 14.47, 0.30956, 4, 85, -4.26, -30.16, 0.05221, 86, 7.19, -12.26, 0.44385, 87, 11.89, 7.32, 0.46156, 88, 9.04, 24.25, 0.04237, 4, 85, -0.16, -22.42, 0.24958, 86, 11.31, -4.53, 0.55993, 87, 16.02, 15.04, 0.19047, 88, 13.17, 31.98, 1e-05, 3, 85, 4.75, -14.98, 0.53241, 86, 16.25, 2.89, 0.43583, 87, 20.95, 22.46, 0.03176, 3, 85, 10.13, -11.1, 0.79497, 86, 21.64, 6.75, 0.20452, 87, 26.34, 26.32, 0.00051, 2, 85, 2.12, -4.64, 0.89783, 86, 13.66, 13.25, 0.10217, 2, 85, -6.45, -10.07, 0.4052, 86, 5.07, 7.85, 0.5948, 3, 85, -13.97, -19.03, 0.00103, 86, -2.48, -1.09, 0.92649, 87, 2.22, 18.48, 0.07248, 3, 86, -6.63, -19.23, 0.02453, 87, -1.93, 0.34, 0.96398, 88, -4.78, 17.28, 0.01149, 2, 87, -0.28, -11.85, 0.34014, 88, -3.13, 5.08, 0.65986], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 64, "height": 37}}, "body_12": {"body_3": {"type": "mesh", "uvs": [0.00295, 0.03257, 0.02504, 0.38448, 0.13233, 0.64996, 0.28064, 0.94013, 0.50784, 1, 0.77606, 0.92161, 0.91491, 0.62526, 1, 0.20543, 1, 0, 0.79815, 0.12517, 0.61829, 0.16839, 0.45104, 0.16839, 0.25855, 0.05726, 0.11024, 0, 0.1702, 0.25483, 0.29011, 0.47091, 0.46366, 0.61909, 0.62144, 0.60056, 0.78869, 0.45856, 0.89597, 0.30422], "triangles": [19, 9, 8, 7, 19, 8, 18, 10, 9, 18, 9, 19, 6, 19, 7, 18, 19, 6, 5, 18, 6, 15, 12, 11, 17, 10, 18, 16, 11, 10, 16, 10, 17, 15, 11, 16, 5, 17, 18, 3, 2, 15, 3, 15, 16, 4, 16, 17, 4, 17, 5, 3, 16, 4, 14, 13, 12, 14, 1, 0, 14, 0, 13, 14, 12, 15, 2, 1, 14, 2, 14, 15], "vertices": [2, 90, 4.92, 2.82, 0.97323, 91, 12.66, 20.2, 0.02677, 2, 90, -3.21, 2.23, 0.84186, 91, 4.53, 19.61, 0.15814, 2, 90, -9.55, -2.28, 0.56021, 91, -1.81, 15.09, 0.43979, 3, 90, -16.55, -8.62, 0.24271, 91, -8.81, 8.76, 0.73983, 92, -14.49, 30.53, 0.01746, 3, 90, -18.43, -18.76, 0.0529, 91, -10.7, -1.39, 0.80361, 92, -16.38, 20.39, 0.1435, 3, 90, -17.24, -30.9, 0.00121, 91, -9.5, -13.53, 0.61639, 92, -15.18, 8.24, 0.3824, 2, 91, -3, -20.11, 0.30173, 92, -8.68, 1.66, 0.69827, 2, 91, 6.45, -24.42, 0.09443, 92, 0.77, -2.64, 0.90557, 2, 91, 11.17, -24.65, 0.07158, 92, 5.49, -2.88, 0.92842, 3, 90, 1.01, -32.81, 0.00042, 91, 8.75, -15.44, 0.30492, 92, 3.07, 6.34, 0.69466, 3, 90, 0.42, -24.68, 0.04292, 91, 8.16, -7.3, 0.58605, 92, 2.48, 14.47, 0.37104, 3, 90, 0.8, -17.16, 0.26306, 91, 8.53, 0.21, 0.62766, 92, 2.85, 21.99, 0.10928, 3, 90, 3.78, -8.64, 0.58383, 91, 11.52, 8.74, 0.40647, 92, 5.84, 30.51, 0.00971, 2, 90, 5.43, -2.04, 0.87466, 91, 13.17, 15.34, 0.12534, 2, 90, -0.56, -4.44, 0.81021, 91, 7.18, 12.93, 0.18979, 2, 90, -5.79, -9.58, 0.35855, 91, 1.95, 7.79, 0.64145, 3, 90, -9.58, -17.21, 0.00484, 91, -1.85, 0.16, 0.9887, 92, -7.53, 21.94, 0.00646, 2, 91, -1.78, -6.95, 0.78514, 92, -7.46, 14.82, 0.21486, 2, 91, 1.11, -14.63, 0.37317, 92, -4.57, 7.14, 0.62683, 2, 91, 4.41, -19.63, 0.07504, 92, -1.27, 2.14, 0.92496], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 45, "height": 23}}, "body_13": {"body_3": {"type": "mesh", "uvs": [0.00295, 0.03257, 0.02504, 0.38448, 0.13233, 0.64996, 0.28064, 0.94013, 0.50784, 1, 0.77606, 0.92161, 0.91491, 0.62526, 1, 0.20543, 1, 0, 0.79815, 0.12517, 0.61829, 0.16839, 0.45104, 0.16839, 0.25855, 0.05726, 0.11024, 0, 0.1702, 0.25483, 0.29011, 0.47091, 0.46366, 0.61909, 0.62144, 0.60056, 0.78869, 0.45856, 0.89597, 0.30422], "triangles": [19, 9, 8, 7, 19, 8, 18, 10, 9, 18, 9, 19, 6, 19, 7, 18, 19, 6, 5, 18, 6, 15, 12, 11, 17, 10, 18, 16, 11, 10, 16, 10, 17, 15, 11, 16, 5, 17, 18, 3, 2, 15, 3, 15, 16, 4, 16, 17, 4, 17, 5, 3, 16, 4, 14, 13, 12, 14, 1, 0, 14, 0, 13, 14, 12, 15, 2, 1, 14, 2, 14, 15], "vertices": [2, 94, 4.92, 2.82, 0.97323, 95, 12.66, 20.2, 0.02677, 2, 94, -3.21, 2.23, 0.84186, 95, 4.53, 19.61, 0.15814, 2, 94, -9.55, -2.28, 0.56021, 95, -1.81, 15.09, 0.43979, 3, 94, -16.55, -8.62, 0.24271, 95, -8.81, 8.76, 0.73983, 96, -14.49, 30.53, 0.01746, 3, 94, -18.43, -18.76, 0.0529, 95, -10.7, -1.39, 0.80361, 96, -16.38, 20.39, 0.1435, 3, 94, -17.24, -30.9, 0.00121, 95, -9.5, -13.53, 0.61639, 96, -15.18, 8.24, 0.3824, 2, 95, -3, -20.11, 0.30173, 96, -8.68, 1.66, 0.69827, 2, 95, 6.45, -24.42, 0.09443, 96, 0.77, -2.64, 0.90557, 2, 95, 11.17, -24.65, 0.07158, 96, 5.49, -2.88, 0.92842, 3, 94, 1.01, -32.81, 0.00042, 95, 8.75, -15.44, 0.30492, 96, 3.07, 6.34, 0.69466, 3, 94, 0.42, -24.68, 0.04292, 95, 8.16, -7.3, 0.58605, 96, 2.48, 14.47, 0.37104, 3, 94, 0.8, -17.16, 0.26306, 95, 8.53, 0.21, 0.62766, 96, 2.85, 21.99, 0.10928, 3, 94, 3.78, -8.64, 0.58383, 95, 11.52, 8.74, 0.40647, 96, 5.84, 30.51, 0.00971, 2, 94, 5.43, -2.04, 0.87466, 95, 13.17, 15.34, 0.12534, 2, 94, -0.56, -4.44, 0.81021, 95, 7.18, 12.93, 0.18979, 2, 94, -5.79, -9.58, 0.35855, 95, 1.95, 7.79, 0.64145, 3, 94, -9.58, -17.21, 0.00484, 95, -1.85, 0.16, 0.9887, 96, -7.53, 21.94, 0.00646, 2, 95, -1.78, -6.95, 0.78514, 96, -7.46, 14.82, 0.21486, 2, 95, 1.11, -14.63, 0.37317, 96, -4.57, 7.14, 0.62683, 2, 95, 4.41, -19.63, 0.07504, 96, -1.27, 2.14, 0.92496], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 45, "height": 23}}, "body_14": {"body_3": {"type": "mesh", "uvs": [0.00295, 0.03257, 0.02504, 0.38448, 0.13233, 0.64996, 0.28064, 0.94013, 0.50784, 1, 0.77606, 0.92161, 0.91491, 0.62526, 1, 0.20543, 1, 0, 0.79815, 0.12517, 0.61829, 0.16839, 0.45104, 0.16839, 0.25855, 0.05726, 0.11024, 0, 0.1702, 0.25483, 0.29011, 0.47091, 0.46366, 0.61909, 0.62144, 0.60056, 0.78869, 0.45856, 0.89597, 0.30422], "triangles": [19, 9, 8, 7, 19, 8, 18, 10, 9, 18, 9, 19, 6, 19, 7, 18, 19, 6, 5, 18, 6, 15, 12, 11, 17, 10, 18, 16, 11, 10, 16, 10, 17, 15, 11, 16, 5, 17, 18, 3, 2, 15, 3, 15, 16, 4, 16, 17, 4, 17, 5, 3, 16, 4, 14, 13, 12, 14, 1, 0, 14, 0, 13, 14, 12, 15, 2, 1, 14, 2, 14, 15], "vertices": [2, 98, 4.92, 2.82, 0.97323, 99, 12.66, 20.2, 0.02677, 2, 98, -3.21, 2.23, 0.84186, 99, 4.53, 19.61, 0.15814, 2, 98, -9.55, -2.28, 0.56021, 99, -1.81, 15.09, 0.43979, 3, 98, -16.55, -8.62, 0.24271, 99, -8.81, 8.76, 0.73983, 100, -14.49, 30.53, 0.01746, 3, 98, -18.43, -18.76, 0.0529, 99, -10.7, -1.39, 0.80361, 100, -16.38, 20.39, 0.1435, 3, 98, -17.24, -30.9, 0.00121, 99, -9.5, -13.53, 0.61639, 100, -15.18, 8.24, 0.3824, 2, 99, -3, -20.11, 0.30173, 100, -8.68, 1.66, 0.69827, 2, 99, 6.45, -24.42, 0.09443, 100, 0.77, -2.64, 0.90557, 2, 99, 11.17, -24.65, 0.07158, 100, 5.49, -2.88, 0.92842, 3, 98, 1.01, -32.81, 0.00042, 99, 8.75, -15.44, 0.30492, 100, 3.07, 6.34, 0.69466, 3, 98, 0.42, -24.68, 0.04292, 99, 8.16, -7.3, 0.58605, 100, 2.48, 14.47, 0.37104, 3, 98, 0.8, -17.16, 0.26306, 99, 8.53, 0.21, 0.62766, 100, 2.85, 21.99, 0.10928, 3, 98, 3.78, -8.64, 0.58383, 99, 11.52, 8.74, 0.40647, 100, 5.84, 30.51, 0.00971, 2, 98, 5.43, -2.04, 0.87466, 99, 13.17, 15.34, 0.12534, 2, 98, -0.56, -4.44, 0.81021, 99, 7.18, 12.93, 0.18979, 2, 98, -5.79, -9.58, 0.35855, 99, 1.95, 7.79, 0.64145, 3, 98, -9.58, -17.21, 0.00484, 99, -1.85, 0.16, 0.9887, 100, -7.53, 21.94, 0.00646, 2, 99, -1.78, -6.95, 0.78514, 100, -7.46, 14.82, 0.21486, 2, 99, 1.11, -14.63, 0.37317, 100, -4.57, 7.14, 0.62683, 2, 99, 4.41, -19.63, 0.07504, 100, -1.27, 2.14, 0.92496], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 45, "height": 23}}, "body_15": {"body_3": {"type": "mesh", "uvs": [0.00295, 0.03257, 0.02504, 0.38448, 0.13233, 0.64996, 0.28064, 0.94013, 0.50784, 1, 0.77606, 0.92161, 0.91491, 0.62526, 1, 0.20543, 1, 0, 0.79815, 0.12517, 0.61829, 0.16839, 0.45104, 0.16839, 0.25855, 0.05726, 0.11024, 0, 0.1702, 0.25483, 0.29011, 0.47091, 0.46366, 0.61909, 0.62144, 0.60056, 0.78869, 0.45856, 0.89597, 0.30422], "triangles": [19, 9, 8, 7, 19, 8, 18, 10, 9, 18, 9, 19, 6, 19, 7, 18, 19, 6, 5, 18, 6, 15, 12, 11, 17, 10, 18, 16, 11, 10, 16, 10, 17, 15, 11, 16, 5, 17, 18, 3, 2, 15, 3, 15, 16, 4, 16, 17, 4, 17, 5, 3, 16, 4, 14, 13, 12, 14, 1, 0, 14, 0, 13, 14, 12, 15, 2, 1, 14, 2, 14, 15], "vertices": [2, 102, 4.92, 2.82, 0.97323, 103, 12.66, 20.2, 0.02677, 2, 102, -3.21, 2.23, 0.84186, 103, 4.53, 19.61, 0.15814, 2, 102, -9.55, -2.28, 0.56021, 103, -1.81, 15.09, 0.43979, 3, 102, -16.55, -8.62, 0.24271, 103, -8.81, 8.76, 0.73983, 104, -14.49, 30.53, 0.01746, 3, 102, -18.43, -18.76, 0.0529, 103, -10.7, -1.39, 0.80361, 104, -16.38, 20.39, 0.1435, 3, 102, -17.24, -30.9, 0.00121, 103, -9.5, -13.53, 0.61639, 104, -15.18, 8.24, 0.3824, 2, 103, -3, -20.11, 0.30173, 104, -8.68, 1.66, 0.69827, 2, 103, 6.45, -24.42, 0.09443, 104, 0.77, -2.64, 0.90557, 2, 103, 11.17, -24.65, 0.07158, 104, 5.49, -2.88, 0.92842, 3, 102, 1.01, -32.81, 0.00042, 103, 8.75, -15.44, 0.30492, 104, 3.07, 6.34, 0.69466, 3, 102, 0.42, -24.68, 0.04292, 103, 8.16, -7.3, 0.58605, 104, 2.48, 14.47, 0.37104, 3, 102, 0.8, -17.16, 0.26306, 103, 8.53, 0.21, 0.62766, 104, 2.85, 21.99, 0.10928, 3, 102, 3.78, -8.64, 0.58383, 103, 11.52, 8.74, 0.40647, 104, 5.84, 30.51, 0.00971, 2, 102, 5.43, -2.04, 0.87466, 103, 13.17, 15.34, 0.12534, 2, 102, -0.56, -4.44, 0.81021, 103, 7.18, 12.93, 0.18979, 2, 102, -5.79, -9.58, 0.35855, 103, 1.95, 7.79, 0.64145, 3, 102, -9.58, -17.21, 0.00484, 103, -1.85, 0.16, 0.9887, 104, -7.53, 21.94, 0.00646, 2, 103, -1.78, -6.95, 0.78514, 104, -7.46, 14.82, 0.21486, 2, 103, 1.11, -14.63, 0.37317, 104, -4.57, 7.14, 0.62683, 2, 103, 4.41, -19.63, 0.07504, 104, -1.27, 2.14, 0.92496], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 45, "height": 23}}, "body_16": {"body_3": {"type": "mesh", "uvs": [0.00295, 0.03257, 0.02504, 0.38448, 0.13233, 0.64996, 0.28064, 0.94013, 0.50784, 1, 0.77606, 0.92161, 0.91491, 0.62526, 1, 0.20543, 1, 0, 0.79815, 0.12517, 0.61829, 0.16839, 0.45104, 0.16839, 0.25855, 0.05726, 0.11024, 0, 0.1702, 0.25483, 0.29011, 0.47091, 0.46366, 0.61909, 0.62144, 0.60056, 0.78869, 0.45856, 0.89597, 0.30422], "triangles": [19, 9, 8, 7, 19, 8, 18, 10, 9, 18, 9, 19, 6, 19, 7, 18, 19, 6, 5, 18, 6, 15, 12, 11, 17, 10, 18, 16, 11, 10, 16, 10, 17, 15, 11, 16, 5, 17, 18, 3, 2, 15, 3, 15, 16, 4, 16, 17, 4, 17, 5, 3, 16, 4, 14, 13, 12, 14, 1, 0, 14, 0, 13, 14, 12, 15, 2, 1, 14, 2, 14, 15], "vertices": [2, 106, 4.92, 2.82, 0.97323, 107, 12.66, 20.2, 0.02677, 2, 106, -3.21, 2.23, 0.84186, 107, 4.53, 19.61, 0.15814, 2, 106, -9.55, -2.28, 0.56021, 107, -1.81, 15.09, 0.43979, 3, 106, -16.55, -8.62, 0.24271, 107, -8.81, 8.76, 0.73983, 108, -14.49, 30.53, 0.01746, 3, 106, -18.43, -18.76, 0.0529, 107, -10.7, -1.39, 0.80361, 108, -16.38, 20.39, 0.1435, 3, 106, -17.24, -30.9, 0.00121, 107, -9.5, -13.53, 0.61639, 108, -15.18, 8.24, 0.3824, 2, 107, -3, -20.11, 0.30173, 108, -8.68, 1.66, 0.69827, 2, 107, 6.45, -24.42, 0.09443, 108, 0.77, -2.64, 0.90557, 2, 107, 11.17, -24.65, 0.07158, 108, 5.49, -2.88, 0.92842, 3, 106, 1.01, -32.81, 0.00042, 107, 8.75, -15.44, 0.30492, 108, 3.07, 6.34, 0.69466, 3, 106, 0.42, -24.68, 0.04292, 107, 8.16, -7.3, 0.58605, 108, 2.48, 14.47, 0.37104, 3, 106, 0.8, -17.16, 0.26306, 107, 8.53, 0.21, 0.62766, 108, 2.85, 21.99, 0.10928, 3, 106, 3.78, -8.64, 0.58383, 107, 11.52, 8.74, 0.40647, 108, 5.84, 30.51, 0.00971, 2, 106, 5.43, -2.04, 0.87466, 107, 13.17, 15.34, 0.12534, 2, 106, -0.56, -4.44, 0.81021, 107, 7.18, 12.93, 0.18979, 2, 106, -5.79, -9.58, 0.35855, 107, 1.95, 7.79, 0.64145, 3, 106, -9.58, -17.21, 0.00484, 107, -1.85, 0.16, 0.9887, 108, -7.53, 21.94, 0.00646, 2, 107, -1.78, -6.95, 0.78514, 108, -7.46, 14.82, 0.21486, 2, 107, 1.11, -14.63, 0.37317, 108, -4.57, 7.14, 0.62683, 2, 107, 4.41, -19.63, 0.07504, 108, -1.27, 2.14, 0.92496], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 45, "height": 23}}, "body_17": {"body_5": {"type": "mesh", "uvs": [0.13002, 0.02462, 0.03705, 0.1868, 0, 0.40302, 0.05797, 0.59468, 0.20673, 0.81091, 0.35083, 0.91902, 0.5577, 0.99765, 0.74132, 0.94851, 0.86218, 0.85022, 0.96678, 0.6684, 1, 0.41285, 0.96446, 0.13765, 0.79943, 0, 0.82964, 0.17205, 0.83197, 0.31457, 0.74597, 0.19171, 0.65067, 0.13274, 0.61116, 0.21628, 0.62278, 0.03937, 0.52516, 0.07868, 0.42521, 0.14257, 0.44846, 0, 0.35083, 0.00497, 0.28343, 0.09342, 0.19278, 0.22611, 0.18116, 0.07868, 0.06959, 0.36862, 0.1951, 0.44234, 0.34154, 0.49148, 0.46937, 0.50622, 0.60883, 0.51114, 0.76456, 0.46691, 0.89705, 0.35388], "triangles": [7, 31, 8, 9, 8, 31, 9, 31, 32, 32, 31, 14, 9, 32, 10, 31, 15, 14, 32, 11, 10, 11, 32, 13, 32, 14, 13, 13, 12, 11, 5, 29, 6, 6, 30, 7, 6, 29, 30, 7, 30, 31, 31, 17, 15, 17, 31, 30, 17, 16, 15, 30, 29, 17, 29, 19, 17, 29, 20, 19, 17, 19, 18, 4, 28, 5, 5, 28, 29, 3, 27, 4, 4, 27, 28, 28, 20, 29, 28, 24, 23, 20, 23, 22, 20, 28, 23, 28, 27, 24, 20, 22, 21, 3, 26, 27, 3, 2, 26, 26, 24, 27, 2, 1, 26, 25, 24, 0, 0, 24, 1, 1, 24, 26], "vertices": [2, 114, 10.43, -8.03, 0.90826, 113, 19.26, 10, 0.09174, 2, 114, 6.55, 0, 0.98884, 113, 15.37, 18.03, 0.01116, 1, 114, -0.15, 4.45, 1, 2, 114, -7.69, 1.87, 0.80867, 113, 1.14, 19.91, 0.19133, 3, 114, -17.65, -7.03, 0.20507, 113, -8.82, 11, 0.78368, 112, -3.6, 30.97, 0.01125, 3, 114, -23.85, -16.49, 0.01022, 113, -15.03, 1.54, 0.76246, 112, -9.8, 21.51, 0.22732, 3, 113, -21.33, -12.68, 0.2232, 112, -16.1, 7.29, 0.73784, 111, -14.55, 28.16, 0.03896, 3, 113, -22.88, -26.29, 0.01162, 112, -17.65, -6.32, 0.65085, 111, -16.1, 14.55, 0.33753, 2, 112, -16.43, -15.82, 0.32872, 111, -14.88, 5.05, 0.67128, 2, 112, -12.08, -24.85, 0.0514, 111, -10.53, -3.98, 0.9486, 1, 111, -2.42, -8.49, 1, 1, 111, 7.56, -8.22, 1, 1, 111, 15.13, 2.51, 1, 2, 112, 7.2, -19.11, 0.00514, 111, 8.75, 1.76, 0.99486, 2, 112, 2.31, -18.09, 0.14956, 111, 3.86, 2.78, 0.85044, 2, 112, 8, -12.93, 0.64509, 111, 9.55, 7.94, 0.35491, 3, 113, 6.45, -26.54, 0.01629, 112, 11.68, -6.57, 0.8273, 111, 13.23, 14.3, 0.15642, 3, 113, 4.3, -23, 0.09008, 112, 9.53, -3.03, 0.85291, 111, 11.08, 17.83, 0.05701, 3, 113, 10.12, -25.31, 0.16144, 112, 15.34, -5.34, 0.83587, 111, 16.89, 15.53, 0.00268, 3, 113, 10.49, -17.96, 0.27449, 112, 15.72, 2.01, 0.72542, 111, 17.26, 22.88, 9e-05, 3, 114, 1.25, -28.28, 0.00226, 113, 10.07, -10.25, 0.69024, 112, 15.3, 9.72, 0.3075, 2, 113, 14.51, -13.1, 0.79858, 112, 19.74, 6.87, 0.20142, 3, 114, 7.23, -24.07, 0.02087, 113, 16.05, -6.04, 0.83401, 112, 21.28, 13.93, 0.14512, 3, 114, 5.4, -18.49, 0.10118, 113, 14.23, -0.46, 0.83051, 112, 19.45, 19.51, 0.06831, 3, 114, 2.48, -10.87, 0.58443, 113, 11.31, 7.16, 0.41394, 112, 16.53, 27.13, 0.00164, 2, 114, 7.7, -11.26, 0.86223, 113, 16.52, 6.77, 0.13777, 2, 114, -0.2, -0.84, 0.98166, 113, 8.62, 17.2, 0.01834, 2, 114, -4.91, -9.25, 0.44461, 113, 3.91, 8.78, 0.55539, 2, 113, -0.33, -1.34, 0.9577, 112, 4.9, 18.63, 0.0423, 2, 113, -3.07, -10.41, 0.47098, 112, 2.16, 9.57, 0.52902, 2, 112, -0.46, -0.42, 0.99086, 111, 1.09, 20.45, 0.00914, 2, 112, -1.68, -11.98, 0.47917, 111, -0.13, 8.88, 0.52083, 1, 111, 1.39, -1.58, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 74, "height": 35}}, "body_18": {"body_5": {"type": "mesh", "uvs": [0.13002, 0.02462, 0.03705, 0.1868, 0, 0.40302, 0.05797, 0.59468, 0.20673, 0.81091, 0.35083, 0.91902, 0.5577, 0.99765, 0.74132, 0.94851, 0.86218, 0.85022, 0.96678, 0.6684, 1, 0.41285, 0.96446, 0.13765, 0.79943, 0, 0.82964, 0.17205, 0.83197, 0.31457, 0.74597, 0.19171, 0.65067, 0.13274, 0.61116, 0.21628, 0.62278, 0.03937, 0.52516, 0.07868, 0.42521, 0.14257, 0.44846, 0, 0.35083, 0.00497, 0.28343, 0.09342, 0.19278, 0.22611, 0.18116, 0.07868, 0.06959, 0.36862, 0.1951, 0.44234, 0.34154, 0.49148, 0.46937, 0.50622, 0.60883, 0.51114, 0.76456, 0.46691, 0.89705, 0.35388], "triangles": [7, 31, 8, 9, 8, 31, 9, 31, 32, 32, 31, 14, 9, 32, 10, 31, 15, 14, 32, 11, 10, 11, 32, 13, 32, 14, 13, 13, 12, 11, 5, 29, 6, 6, 30, 7, 6, 29, 30, 7, 30, 31, 31, 17, 15, 17, 31, 30, 17, 16, 15, 30, 29, 17, 29, 19, 17, 29, 20, 19, 17, 19, 18, 4, 28, 5, 5, 28, 29, 3, 27, 4, 4, 27, 28, 28, 20, 29, 28, 24, 23, 20, 23, 22, 20, 28, 23, 28, 27, 24, 20, 22, 21, 3, 26, 27, 3, 2, 26, 26, 24, 27, 2, 1, 26, 25, 24, 0, 0, 24, 1, 1, 24, 26], "vertices": [2, 119, 10.43, -8.03, 0.90826, 118, 19.26, 10, 0.09174, 2, 119, 6.55, 0, 0.98884, 118, 15.37, 18.03, 0.01116, 1, 119, -0.15, 4.45, 1, 2, 119, -7.69, 1.87, 0.80867, 118, 1.14, 19.91, 0.19133, 3, 119, -17.65, -7.03, 0.20507, 118, -8.82, 11, 0.78368, 117, -3.6, 30.97, 0.01125, 3, 119, -23.85, -16.49, 0.01022, 118, -15.03, 1.54, 0.76246, 117, -9.8, 21.51, 0.22732, 3, 118, -21.33, -12.68, 0.2232, 117, -16.1, 7.29, 0.73784, 116, -14.55, 28.16, 0.03896, 3, 118, -22.88, -26.29, 0.01162, 117, -17.65, -6.32, 0.65085, 116, -16.1, 14.55, 0.33753, 2, 117, -16.43, -15.82, 0.32872, 116, -14.88, 5.05, 0.67128, 2, 117, -12.08, -24.85, 0.0514, 116, -10.53, -3.98, 0.9486, 1, 116, -2.42, -8.49, 1, 1, 116, 7.56, -8.22, 1, 1, 116, 15.13, 2.51, 1, 2, 117, 7.2, -19.11, 0.00514, 116, 8.75, 1.76, 0.99486, 2, 117, 2.31, -18.09, 0.14956, 116, 3.86, 2.78, 0.85044, 2, 117, 8, -12.93, 0.64509, 116, 9.55, 7.94, 0.35491, 3, 118, 6.45, -26.54, 0.01629, 117, 11.68, -6.57, 0.8273, 116, 13.23, 14.3, 0.15642, 3, 118, 4.3, -23, 0.09008, 117, 9.53, -3.03, 0.85291, 116, 11.08, 17.83, 0.05701, 3, 118, 10.12, -25.31, 0.16144, 117, 15.34, -5.34, 0.83587, 116, 16.89, 15.53, 0.00268, 3, 118, 10.49, -17.96, 0.27449, 117, 15.72, 2.01, 0.72542, 116, 17.26, 22.88, 9e-05, 3, 119, 1.25, -28.28, 0.00226, 118, 10.07, -10.25, 0.69024, 117, 15.3, 9.72, 0.3075, 2, 118, 14.51, -13.1, 0.79858, 117, 19.74, 6.87, 0.20142, 3, 119, 7.23, -24.07, 0.02087, 118, 16.05, -6.04, 0.83401, 117, 21.28, 13.93, 0.14512, 3, 119, 5.4, -18.49, 0.10118, 118, 14.23, -0.46, 0.83051, 117, 19.45, 19.51, 0.06831, 3, 119, 2.48, -10.87, 0.58443, 118, 11.31, 7.16, 0.41394, 117, 16.53, 27.13, 0.00164, 2, 119, 7.7, -11.26, 0.86223, 118, 16.52, 6.77, 0.13777, 2, 119, -0.2, -0.84, 0.98166, 118, 8.62, 17.2, 0.01834, 2, 119, -4.91, -9.25, 0.44461, 118, 3.91, 8.78, 0.55539, 2, 118, -0.33, -1.34, 0.9577, 117, 4.9, 18.63, 0.0423, 2, 118, -3.07, -10.41, 0.47098, 117, 2.16, 9.57, 0.52902, 2, 117, -0.46, -0.42, 0.99086, 116, 1.09, 20.45, 0.00914, 2, 117, -1.68, -11.98, 0.47917, 116, -0.13, 8.88, 0.52083, 1, 116, 1.39, -1.58, 1], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50], "width": 74, "height": 35}}, "eye_close_L": {"eye_close_L": {"x": 12.5, "y": 70.29, "width": 16, "height": 16}}, "eye_close_R": {"eye_close_R": {"x": -5.5, "y": 71.79, "width": 18, "height": 17}}, "hand_L": {"hand_L": {"x": 4.74, "y": 1.37, "rotation": 94.73, "width": 39, "height": 50}}, "hand_L2": {"hand_L2": {"x": 12.15, "y": 1.07, "rotation": 82.94, "width": 31, "height": 34}}, "hand_R": {"hand_R": {"x": 13.96, "y": -2.23, "rotation": 75.21, "width": 44, "height": 53}}, "hand_R2": {"hand_R2": {"x": 4.18, "y": 0.63, "rotation": -103.71, "width": 46, "height": 39}}, "hand_R3": {"hand_R2": {"x": 4.18, "y": 0.63, "rotation": -103.71, "width": 46, "height": 39}}, "head": {"head": {"x": 13.6, "y": 3.4, "rotation": -92.26, "width": 50, "height": 37}}, "head_2": {"head_2": {"x": 4.51, "y": 0.09, "rotation": -135.44, "width": 22, "height": 17}}, "head_3": {"head_3": {"x": 6.29, "y": -0.75, "rotation": -133.71, "width": 34, "height": 21}}, "head_4": {"head_4": {"x": 4.42, "y": 0.12, "rotation": -138.22, "width": 19, "height": 18}}, "wind": {"wind": {"x": 17, "y": 2.79, "width": 25, "height": 15}}, "wind_2": {"wind_2": {"x": -12.5, "y": 3.29, "width": 34, "height": 18}}, "yinying": {"yinying": {"x": 0.32, "y": -1.96, "width": 80, "height": 38}}}}], "animations": {"attack": {"slots": {"body": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body4": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "body5": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff7f"}]}, "body6": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffff7f"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_2": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_3": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_5": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body_8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_9": {"color": [{"color": "ffffff9f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff9f"}]}, "body_10": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "body_11": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff7f"}]}, "body_12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body_13": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_14": {"color": [{"color": "ffffff9f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff9f"}]}, "body_15": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "body_16": {"color": [{"color": "ffffff8d"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff8d"}]}, "body_17": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_18": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "hand_R2": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "hand_R3": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff"}]}, "head_2": {"color": [{"color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "head_3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "head_4": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff"}]}}, "bones": {"body6": {"translate": [{"x": -5.09, "y": -1.45}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 1, "x": -5.09, "y": -1.45}], "scale": [{}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 1}]}, "body5": {"translate": [{"x": -6.43, "y": -0.48}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 1, "x": -6.43, "y": -0.48}], "scale": [{}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 1}]}, "body4": {"rotate": [{}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 1}], "translate": [{"x": -9.48, "y": 2.07}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 1, "x": -9.48, "y": 2.07}], "scale": [{}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 1}]}, "body3": {"rotate": [{"angle": -7.52}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 1, "angle": -7.52}], "translate": [{"x": 1.02, "y": 0.78}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 1, "x": 1.02, "y": 0.78}]}, "body9": {"rotate": [{"angle": 7.57, "curve": "stepped"}, {"time": 0.1667, "angle": -7.52}, {"time": 0.8333, "angle": 7.57}], "translate": [{"x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.1667, "x": 1.02, "y": 0.78}, {"time": 0.8333, "x": 13.12, "y": -11.14}]}, "body10": {"rotate": [{"angle": 22.83, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "angle": 22.83}], "translate": [{"x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.1667, "x": -9.48, "y": 2.07}, {"time": 0.8333, "x": 34.93, "y": -4.74}], "scale": [{"x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.998, "y": 0.998}]}, "body11": {"translate": [{"x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.1667, "x": -6.43, "y": -0.48}, {"time": 0.8333, "x": 16.68, "y": 9.19}], "scale": [{"x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.638, "y": 0.638}]}, "body12": {"translate": [{"x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.1667, "x": -5.09, "y": -1.45}, {"time": 0.8333, "x": -0.42, "y": 0.66}], "scale": [{"x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.611, "y": 0.611}]}, "body14": {"rotate": [{"angle": 7.57, "curve": "stepped"}, {"time": 0.1667, "angle": 7.57, "curve": "stepped"}, {"time": 0.3333, "angle": -7.52}, {"time": 1, "angle": 7.57}], "translate": [{"x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.1667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.3333, "x": 1.02, "y": 0.78}, {"time": 1, "x": 13.12, "y": -11.14}]}, "body15": {"rotate": [{"angle": 22.83, "curve": "stepped"}, {"time": 0.1667, "angle": 22.83, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "angle": 22.83}], "translate": [{"x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.1667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.3333, "x": -9.48, "y": 2.07}, {"time": 1, "x": 34.93, "y": -4.74}], "scale": [{"x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.1667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.998, "y": 0.998}]}, "body16": {"translate": [{"x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.1667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.3333, "x": -6.43, "y": -0.48}, {"time": 1, "x": 16.68, "y": 9.19}], "scale": [{"x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.1667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.638, "y": 0.638}]}, "body17": {"translate": [{"x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.1667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.3333, "x": -5.09, "y": -1.45}, {"time": 1, "x": -0.42, "y": 0.66}], "scale": [{"x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.1667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.611, "y": 0.611}]}, "body19": {"rotate": [{"angle": 0.03}, {"time": 0.3333, "angle": 7.57, "curve": "stepped"}, {"time": 0.5, "angle": 7.57, "curve": "stepped"}, {"time": 0.6667, "angle": -7.52}, {"time": 1, "angle": 0.03}], "translate": [{"x": 7.07, "y": -5.18}, {"time": 0.3333, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.5, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.6667, "x": 1.02, "y": 0.78}, {"time": 1, "x": 7.07, "y": -5.18}]}, "body20": {"rotate": [{"angle": 11.42}, {"time": 0.3333, "angle": 22.83, "curve": "stepped"}, {"time": 0.5, "angle": 22.83, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "angle": 11.42}], "translate": [{"x": 12.72, "y": -1.34}, {"time": 0.3333, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.5, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.6667, "x": -9.48, "y": 2.07}, {"time": 1, "x": 12.72, "y": -1.34}], "scale": [{"x": 0.999, "y": 0.999}, {"time": 0.3333, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.5, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.999, "y": 0.999}]}, "body21": {"translate": [{"x": 5.12, "y": 4.36}, {"time": 0.3333, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.5, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.6667, "x": -6.43, "y": -0.48}, {"time": 1, "x": 5.12, "y": 4.36}], "scale": [{"x": 0.819, "y": 0.819}, {"time": 0.3333, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.5, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.819, "y": 0.819}]}, "body22": {"translate": [{"x": -2.76, "y": -0.4}, {"time": 0.3333, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.5, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.6667, "x": -5.09, "y": -1.45}, {"time": 1, "x": -2.76, "y": -0.4}], "scale": [{"x": 0.805, "y": 0.805}, {"time": 0.3333, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.5, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.805, "y": 0.805}]}, "body24": {"rotate": [{"angle": -3.74}, {"time": 0.1667, "angle": 0.03}, {"time": 0.5, "angle": 7.57, "curve": "stepped"}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 0.8333, "angle": -7.52}, {"time": 1, "angle": -3.74}], "translate": [{"x": 4.05, "y": -2.2}, {"time": 0.1667, "x": 7.07, "y": -5.18}, {"time": 0.5, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.8333, "x": 1.02, "y": 0.78}, {"time": 1, "x": 4.05, "y": -2.2}]}, "body25": {"rotate": [{"angle": 5.71}, {"time": 0.1667, "angle": 11.42}, {"time": 0.5, "angle": 22.83, "curve": "stepped"}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "angle": 5.71}], "translate": [{"x": 1.62, "y": 0.37}, {"time": 0.1667, "x": 12.72, "y": -1.34}, {"time": 0.5, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.8333, "x": -9.48, "y": 2.07}, {"time": 1, "x": 1.62, "y": 0.37}], "scale": [{"x": 0.999, "y": 0.999}, {"time": 0.1667, "x": 0.999, "y": 0.999}, {"time": 0.5, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.999, "y": 0.999}]}, "body26": {"translate": [{"x": -0.65, "y": 1.94}, {"time": 0.1667, "x": 5.12, "y": 4.36}, {"time": 0.5, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.8333, "x": -6.43, "y": -0.48}, {"time": 1, "x": -0.65, "y": 1.94}], "scale": [{"x": 0.91, "y": 0.91}, {"time": 0.1667, "x": 0.819, "y": 0.819}, {"time": 0.5, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.91, "y": 0.91}]}, "body27": {"translate": [{"x": -3.92, "y": -0.92}, {"time": 0.1667, "x": -2.76, "y": -0.4}, {"time": 0.5, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.8333, "x": -5.09, "y": -1.45}, {"time": 1, "x": -3.92, "y": -0.92}], "scale": [{"x": 0.903, "y": 0.903}, {"time": 0.1667, "x": 0.805, "y": 0.805}, {"time": 0.5, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.903, "y": 0.903}]}, "body29": {"rotate": [{"angle": -7.52}, {"time": 0.1667, "angle": -3.74}, {"time": 0.3333, "angle": 0.03}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 0.8333, "angle": 7.57, "curve": "stepped"}, {"time": 1, "angle": -7.52}], "translate": [{"x": 1.02, "y": 0.78}, {"time": 0.1667, "x": 4.05, "y": -2.2}, {"time": 0.3333, "x": 7.07, "y": -5.18}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.8333, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 1, "x": 1.02, "y": 0.78}]}, "body30": {"rotate": [{}, {"time": 0.1667, "angle": 5.71}, {"time": 0.3333, "angle": 11.42}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 0.8333, "angle": 22.83, "curve": "stepped"}, {"time": 1}], "translate": [{"x": -9.48, "y": 2.07}, {"time": 0.1667, "x": 1.62, "y": 0.37}, {"time": 0.3333, "x": 12.72, "y": -1.34}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.8333, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 1, "x": -9.48, "y": 2.07}], "scale": [{}, {"time": 0.1667, "x": 0.999, "y": 0.999}, {"time": 0.3333, "x": 0.999, "y": 0.999}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.8333, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 1}]}, "body31": {"translate": [{"x": -6.43, "y": -0.48}, {"time": 0.1667, "x": -0.65, "y": 1.94}, {"time": 0.3333, "x": 5.12, "y": 4.36}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.8333, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 1, "x": -6.43, "y": -0.48}], "scale": [{}, {"time": 0.1667, "x": 0.91, "y": 0.91}, {"time": 0.3333, "x": 0.819, "y": 0.819}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.8333, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 1}]}, "body32": {"translate": [{"x": -5.09, "y": -1.45}, {"time": 0.1667, "x": -3.92, "y": -0.92}, {"time": 0.3333, "x": -2.76, "y": -0.4}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.8333, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 1, "x": -5.09, "y": -1.45}], "scale": [{}, {"time": 0.1667, "x": 0.903, "y": 0.903}, {"time": 0.3333, "x": 0.805, "y": 0.805}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.8333, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 1}]}, "body_5": {"translate": [{"x": -0.58, "y": -0.55}, {"time": 0.6667, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 1, "x": -0.58, "y": -0.55}]}, "body_8": {"translate": [{"x": -15.24, "y": 3.01}, {"time": 0.6667, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 1, "x": -15.24, "y": 3.01}], "scale": [{}, {"time": 0.6667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 1}]}, "body_9": {"translate": [{"x": -21.47, "y": -0.6}, {"time": 0.6667, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 1, "x": -21.47, "y": -0.6}], "scale": [{"x": 1.528, "y": 1.528}, {"time": 0.6667, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 1, "x": 1.528, "y": 1.528}]}, "body_10": {"translate": [{"x": -15.65, "y": -4.09}, {"time": 0.6667, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 1, "x": -15.65, "y": -4.09}], "scale": [{"x": 1.121, "y": 1.121}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 1.121, "y": 1.121}]}, "body_24": {"translate": [{"x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.1667, "x": -0.58, "y": -0.55}, {"time": 0.8333, "x": 3.24, "y": -4.05}]}, "body_25": {"translate": [{"x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.1667, "x": -15.24, "y": 3.01}, {"time": 0.8333, "x": 7.4, "y": -3.2}], "scale": [{"x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 1.056, "y": 1.056}]}, "body_26": {"translate": [{"x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.1667, "x": -21.47, "y": -0.6}, {"time": 0.8333, "x": 7.06, "y": 2.54}], "scale": [{"x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.1667, "x": 1.528, "y": 1.528}, {"time": 0.8333, "x": 0.562, "y": 0.562}]}, "body_27": {"translate": [{"x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.1667, "x": -15.65, "y": -4.09}, {"time": 0.8333, "x": 1.45, "y": 0.76}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "x": 1.121, "y": 1.121}, {"time": 0.8333}]}, "body_29": {"translate": [{"x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.3333, "x": -0.58, "y": -0.55}, {"time": 1, "x": 3.24, "y": -4.05}]}, "body_30": {"translate": [{"x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.3333, "x": -15.24, "y": 3.01}, {"time": 1, "x": 7.4, "y": -3.2}], "scale": [{"x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 1.056, "y": 1.056}]}, "body_31": {"translate": [{"x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.3333, "x": -21.47, "y": -0.6}, {"time": 1, "x": 7.06, "y": 2.54}], "scale": [{"x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.3333, "x": 1.528, "y": 1.528}, {"time": 1, "x": 0.562, "y": 0.562}]}, "body_32": {"translate": [{"x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.3333, "x": -15.65, "y": -4.09}, {"time": 1, "x": 1.45, "y": 0.76}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "x": 1.121, "y": 1.121}, {"time": 1}]}, "body_34": {"translate": [{"x": 2.29, "y": -3.18}, {"time": 0.1667, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.5, "x": -0.58, "y": -0.55}, {"time": 1, "x": 2.29, "y": -3.18}]}, "body_35": {"translate": [{"x": 1.74, "y": -1.65}, {"time": 0.1667, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.5, "x": -15.24, "y": 3.01}, {"time": 1, "x": 1.74, "y": -1.65}], "scale": [{"x": 1.042, "y": 1.042}, {"time": 0.1667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.5}, {"time": 1, "x": 1.042, "y": 1.042}]}, "body_36": {"translate": [{"x": -0.07, "y": 1.75}, {"time": 0.1667, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.5, "x": -21.47, "y": -0.6}, {"time": 1, "x": -0.07, "y": 1.75}], "scale": [{"x": 0.803, "y": 0.803}, {"time": 0.1667, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.5, "x": 1.528, "y": 1.528}, {"time": 1, "x": 0.803, "y": 0.803}]}, "body_37": {"translate": [{"x": -2.83, "y": -0.45}, {"time": 0.1667, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.5, "x": -15.65, "y": -4.09}, {"time": 1, "x": -2.83, "y": -0.45}], "scale": [{"x": 1.03, "y": 1.03}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.03, "y": 1.03}]}, "body_39": {"translate": [{"x": 1.33, "y": -2.3}, {"time": 0.3333, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.6667, "x": -0.58, "y": -0.55}, {"time": 1, "x": 1.33, "y": -2.3}]}, "body_40": {"translate": [{"x": -3.92, "y": -0.09}, {"time": 0.3333, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.6667, "x": -15.24, "y": 3.01}, {"time": 1, "x": -3.92, "y": -0.09}], "scale": [{"x": 1.028, "y": 1.028}, {"time": 0.3333, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 1.028, "y": 1.028}]}, "body_41": {"translate": [{"x": -7.21, "y": 0.97}, {"time": 0.3333, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.6667, "x": -21.47, "y": -0.6}, {"time": 1, "x": -7.21, "y": 0.97}], "scale": [{"x": 1.045, "y": 1.045}, {"time": 0.3333, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.6667, "x": 1.528, "y": 1.528}, {"time": 1, "x": 1.045, "y": 1.045}]}, "body_42": {"translate": [{"x": -7.1, "y": -1.66}, {"time": 0.3333, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.6667, "x": -15.65, "y": -4.09}, {"time": 1, "x": -7.1, "y": -1.66}], "scale": [{"x": 1.06, "y": 1.06}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.06, "y": 1.06}]}, "body_44": {"translate": [{"x": 0.38, "y": -1.43}, {"time": 0.5, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.8333, "x": -0.58, "y": -0.55}, {"time": 1, "x": 0.38, "y": -1.43}]}, "body_45": {"translate": [{"x": -9.58, "y": 1.46}, {"time": 0.5, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.8333, "x": -15.24, "y": 3.01}, {"time": 1, "x": -9.58, "y": 1.46}], "scale": [{"x": 1.014, "y": 1.014}, {"time": 0.5, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 1.014, "y": 1.014}]}, "body_46": {"translate": [{"x": -14.34, "y": 0.18}, {"time": 0.5, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.8333, "x": -21.47, "y": -0.6}, {"time": 1, "x": -14.34, "y": 0.18}], "scale": [{"x": 1.286, "y": 1.286}, {"time": 0.5, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.8333, "x": 1.528, "y": 1.528}, {"time": 1, "x": 1.286, "y": 1.286}]}, "body_47": {"translate": [{"x": -11.38, "y": -2.88}, {"time": 0.5, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.8333, "x": -15.65, "y": -4.09}, {"time": 1, "x": -11.38, "y": -2.88}], "scale": [{"x": 1.091, "y": 1.091}, {"time": 0.5, "curve": "stepped"}, {"time": 0.8333, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.091, "y": 1.091}]}, "body_13": {"translate": [{"x": -16.43, "y": -4.41}, {"time": 0.6667, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 1, "x": -16.43, "y": -4.41}], "scale": [{"x": 1.272, "y": 1.272}, {"time": 0.6667, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 1, "x": 1.272, "y": 1.272}]}, "body_12": {"translate": [{"x": -8.73, "y": 3.82}, {"time": 0.6667, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 1, "x": -8.73, "y": 3.82}], "scale": [{"x": 0.951, "y": 0.951}, {"time": 0.6667, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 1, "x": 0.951, "y": 0.951}]}, "body_11": {"translate": [{}, {"time": 0.6667, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 1}]}, "body_49": {"translate": [{"x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 11.47, "y": -4.95}]}, "body_50": {"translate": [{"x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.1667, "x": -8.73, "y": 3.82}, {"time": 0.8333, "x": 7.13, "y": 1.23}], "scale": [{"x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.1667, "x": 0.951, "y": 0.951}, {"time": 0.8333, "x": 0.9, "y": 0.9}]}, "body_51": {"translate": [{"x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.1667, "x": -16.43, "y": -4.41}, {"time": 0.8333, "x": -1.6, "y": -0.26}], "scale": [{"x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.1667, "x": 1.272, "y": 1.272}, {"time": 0.8333, "x": 0.863, "y": 0.863}]}, "body_53": {"translate": [{"x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 11.47, "y": -4.95}]}, "body_54": {"translate": [{"x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.3333, "x": -8.73, "y": 3.82}, {"time": 1, "x": 7.13, "y": 1.23}], "scale": [{"x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.3333, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.9, "y": 0.9}]}, "body_55": {"translate": [{"x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.3333, "x": -16.43, "y": -4.41}, {"time": 1, "x": -1.6, "y": -0.26}], "scale": [{"x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.3333, "x": 1.272, "y": 1.272}, {"time": 1, "x": 0.863, "y": 0.863}]}, "body_57": {"translate": [{"x": 8.6, "y": -3.71}, {"time": 0.1667, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.5}, {"time": 1, "x": 8.6, "y": -3.71}]}, "body_58": {"translate": [{"x": 3.16, "y": 1.88}, {"time": 0.1667, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.5, "x": -8.73, "y": 3.82}, {"time": 1, "x": 3.16, "y": 1.88}], "scale": [{"x": 0.913, "y": 0.913}, {"time": 0.1667, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.5, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.913, "y": 0.913}]}, "body_59": {"translate": [{"x": -5.31, "y": -1.3}, {"time": 0.1667, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.5, "x": -16.43, "y": -4.41}, {"time": 1, "x": -5.31, "y": -1.3}], "scale": [{"x": 0.965, "y": 0.965}, {"time": 0.1667, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.5, "x": 1.272, "y": 1.272}, {"time": 1, "x": 0.965, "y": 0.965}]}, "body_61": {"translate": [{"x": 5.73, "y": -2.48}, {"time": 0.3333, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 5.73, "y": -2.48}]}, "body_62": {"translate": [{"x": -0.8, "y": 2.52}, {"time": 0.3333, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.6667, "x": -8.73, "y": 3.82}, {"time": 1, "x": -0.8, "y": 2.52}], "scale": [{"x": 0.925, "y": 0.925}, {"time": 0.3333, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.6667, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.925, "y": 0.925}]}, "body_63": {"translate": [{"x": -9.02, "y": -2.33}, {"time": 0.3333, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.6667, "x": -16.43, "y": -4.41}, {"time": 1, "x": -9.02, "y": -2.33}], "scale": [{"x": 1.067, "y": 1.067}, {"time": 0.3333, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.6667, "x": 1.272, "y": 1.272}, {"time": 1, "x": 1.067, "y": 1.067}]}, "body_65": {"translate": [{"x": 2.87, "y": -1.24}, {"time": 0.5, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 2.87, "y": -1.24}]}, "body_66": {"translate": [{"x": -4.77, "y": 3.17}, {"time": 0.5, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.8333, "x": -8.73, "y": 3.82}, {"time": 1, "x": -4.77, "y": 3.17}], "scale": [{"x": 0.938, "y": 0.938}, {"time": 0.5, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.8333, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.938, "y": 0.938}]}, "body_67": {"translate": [{"x": -12.72, "y": -3.37}, {"time": 0.5, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.8333, "x": -16.43, "y": -4.41}, {"time": 1, "x": -12.72, "y": -3.37}], "scale": [{"x": 1.169, "y": 1.169}, {"time": 0.5, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.8333, "x": 1.272, "y": 1.272}, {"time": 1, "x": 1.169, "y": 1.169}]}, "body_19": {"translate": [{"x": 16.79, "y": 1.11}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 16.79, "y": 1.11}]}, "body_18": {"translate": [{"x": 24.13, "y": 2.31}, {"time": 0.6667, "x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 1, "x": 24.13, "y": 2.31}]}, "body_17": {"translate": [{"x": 16.37, "y": 1.82}, {"time": 0.6667, "x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 1, "x": 16.37, "y": 1.82}]}, "body_7": {"translate": [{"x": 0.97, "y": -1.28}, {"time": 0.6667, "x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 1, "x": 0.97, "y": -1.28}]}, "body_4": {"rotate": [{"angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 8.13, "curve": 0, "c2": 0.43, "c3": 0, "c4": 0.76}, {"time": 0.3, "angle": -12.74, "curve": 0.048, "c2": 1, "c3": 0.238}, {"time": 0.4, "angle": -22.9, "curve": 0.773, "c3": 0.75}, {"time": 0.7667, "angle": 4.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.92}]}, "body_6": {"rotate": [{"angle": 4.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.26, "curve": 0, "c2": 0.43, "c3": 0, "c4": 0.76}, {"time": 0.3, "angle": 7.07, "curve": 0.048, "c2": 1, "c3": 0.238}, {"time": 0.4, "angle": -2.07, "curve": 0.773, "c3": 0.75}, {"time": 0.7667, "angle": 7.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.05}]}, "body_3": {"rotate": [{"angle": 1.13, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -5.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2667, "angle": 10, "curve": 0, "c2": 0.43, "c3": 0, "c4": 0.76}, {"time": 0.3, "angle": 1.94, "curve": 0.048, "c2": 1, "c3": 0.238}, {"time": 0.4, "angle": -7.49, "curve": 0.674, "c3": 0.843, "c4": 0.65}, {"time": 0.6667, "angle": -8.72, "curve": 0.298, "c2": 0.59, "c3": 0.588}, {"time": 0.8333, "angle": 4.23, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.13}]}, "body_2": {"rotate": [{"angle": -1.65, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -8.16, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2667, "angle": 7.21, "curve": 0, "c2": 0.43, "c3": 0, "c4": 0.76}, {"time": 0.3, "angle": -0.75, "curve": 0.048, "c2": 1, "c3": 0.238}, {"time": 0.4, "angle": -10.17, "curve": 0.579, "c3": 0.833, "c4": 0.52}, {"time": 0.6, "angle": -14.46, "curve": 0.372, "c2": 0.52, "c3": 0.582}, {"time": 0.8333, "angle": 1.31, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.65}], "scale": [{"curve": 0.25, "c3": 0}, {"time": 0.2667, "x": 1.075, "y": 1.075, "curve": 0, "c2": 0.3, "c3": 0}, {"time": 0.4}]}, "body": {"rotate": [{"angle": -2.57, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -9.08, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2667, "angle": 6.3, "curve": 0, "c2": 0.43, "c3": 0, "c4": 0.76}, {"time": 0.3, "angle": -2, "curve": 0.048, "c2": 1, "c3": 0.238}, {"time": 0.4, "angle": -11.44, "curve": 0.579, "c3": 0.833, "c4": 0.52}, {"time": 0.6, "angle": -15.52, "curve": 0.381, "c2": 0.36, "c3": 0.691, "c4": 0.72}, {"time": 0.7, "angle": -21, "curve": 0.296, "c2": 0.6, "c3": 0.59}, {"time": 0.9, "angle": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.57}], "scale": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": 1.123, "y": 1.123, "curve": 0, "c2": 0.75, "c3": 0.434}, {"time": 0.4333}]}, "hand_R2": {"rotate": [{}, {"time": 0.8333, "angle": 119.45, "curve": "stepped"}, {"time": 1}], "translate": [{}, {"time": 0.2667, "x": 10.15, "y": -9.68, "curve": 0, "c2": 1, "c3": 0.196}, {"time": 0.4, "x": -20.09, "y": -33.63, "curve": 0.819, "c2": 0.01, "c3": 0.75}, {"time": 0.7667}]}, "hand_R3": {"rotate": [{"angle": 76.45}, {"time": 0.3, "angle": 119.45, "curve": "stepped"}, {"time": 0.4667}, {"time": 1, "angle": 76.45}], "translate": [{}, {"time": 0.2667, "x": 10.15, "y": -9.68, "curve": 0, "c2": 1, "c3": 0.196}, {"time": 0.4, "x": -20.09, "y": -33.63, "curve": 0.819, "c2": 0.01, "c3": 0.75}, {"time": 0.7667}]}, "hand_R": {"rotate": [{"angle": -27.46, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.2667, "angle": -161.62, "curve": 0, "c2": 1, "c3": 0}, {"time": 0.4, "angle": 33.8, "curve": 0.773, "c3": 0.75}, {"time": 0.7667, "angle": -46.94, "curve": 0.331, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 1, "angle": -27.46}], "translate": [{"time": 0.2667, "curve": 0, "c2": 1, "c3": 0}, {"time": 0.4, "x": -4.55, "y": 0.12, "curve": 0.773, "c3": 0.75}, {"time": 0.7667}]}, "hand_L2": {"rotate": [{"angle": 9.51, "curve": 0.308, "c3": 0.643, "c4": 0.35}, {"time": 0.0667, "angle": 49.41, "curve": 0.301, "c2": 0.19, "c3": 0.64, "c4": 0.55}, {"time": 0.2, "angle": -131.57, "curve": 0, "c2": 1, "c3": 0}, {"time": 0.4, "angle": 128.45, "curve": 0.773, "c3": 0.75}, {"time": 0.7667, "angle": 2.74, "curve": 0.378, "c2": 0.61, "c3": 0.722}, {"time": 1, "angle": 9.51}], "translate": [{"curve": 0.273, "c3": 0.62, "c4": 0.41}, {"time": 0.2, "x": 21.47, "y": 12.64, "curve": 0, "c2": 1, "c3": 0}, {"time": 0.4, "x": -11.01, "y": 15.55, "curve": 0.773, "c3": 0.75}, {"time": 0.7667}]}, "hand_L": {"rotate": [{"angle": 4.66, "curve": 0.342, "c2": 0.36, "c3": 0.676, "c4": 0.7}, {"time": 0.2, "angle": -32.3, "curve": 0, "c2": 1, "c3": 0}, {"time": 0.4, "angle": -17.71, "curve": 0.773, "c3": 0.75}, {"time": 0.7667, "angle": 0.04, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1, "angle": 4.66}]}, "head": {"translate": [{"x": 2.59, "y": -0.53}, {"time": 0.5, "x": -1.5, "y": 0.3}, {"time": 1, "x": 2.59, "y": -0.53}]}, "body_68": {"translate": [{"x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 0.3333, "x": 0.97, "y": -1.28}, {"time": 1, "x": -14.56, "y": 0.86}]}, "body_69": {"translate": [{"x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 0.3333, "x": 16.37, "y": 1.82}, {"time": 1, "x": -10.85, "y": 1.57}]}, "body_70": {"translate": [{"x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 0.3333, "x": 24.13, "y": 2.31}, {"time": 1, "x": -5.9, "y": 0.79}]}, "body_71": {"translate": [{"curve": "stepped"}, {"time": 0.3333, "x": 16.79, "y": 1.11}, {"time": 1}]}, "body_72": {"translate": [{"x": -6.79, "y": -0.21}, {"time": 0.3333, "x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 0.6667, "x": 0.97, "y": -1.28}, {"time": 1, "x": -6.79, "y": -0.21}]}, "body_73": {"translate": [{"x": 2.76, "y": 1.69}, {"time": 0.3333, "x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 0.6667, "x": 16.37, "y": 1.82}, {"time": 1, "x": 2.76, "y": 1.69}]}, "body_74": {"translate": [{"x": 9.12, "y": 1.55}, {"time": 0.3333, "x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 0.6667, "x": 24.13, "y": 2.31}, {"time": 1, "x": 9.12, "y": 1.55}]}, "body_75": {"translate": [{"x": 8.4, "y": 0.55}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "x": 16.79, "y": 1.11}, {"time": 1, "x": 8.4, "y": 0.55}]}, "head_4": {"translate": [{"x": -1.18, "y": 14.92}, {"time": 0.3333, "x": -1.57, "y": 29.39, "curve": "stepped"}, {"time": 0.6667, "x": 2.63, "y": -4.31}, {"time": 0.8333, "x": 1.46, "y": 5.76}, {"time": 1, "x": -1.18, "y": 14.92}], "scale": [{}, {"time": 0.3333, "x": 0.735, "y": 0.735, "curve": "stepped"}, {"time": 0.6667, "x": 0.469, "y": 0.397}, {"time": 1}]}, "head_3": {"translate": [{"x": -2.04, "y": 14.2, "curve": "stepped"}, {"time": 0.3333, "x": 4.48, "y": -15.7}, {"time": 0.6667, "x": -2.89, "y": -1.78}, {"time": 1, "x": -2.04, "y": 14.2}], "scale": [{"x": 0.786, "y": 0.786, "curve": "stepped"}, {"time": 0.3333, "x": 0.327, "y": 0.241}, {"time": 0.6667}, {"time": 1, "x": 0.786, "y": 0.786}]}, "head_2": {"translate": [{"x": 2.35, "y": -26.64}, {"time": 0.3333, "x": 0.51, "y": -9.13}, {"time": 0.6667, "x": -0.63, "y": 2.66, "curve": "stepped"}, {"time": 1, "x": 2.35, "y": -26.64}], "scale": [{"x": 0.509, "y": 0.509}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 0.509, "y": 0.509}]}}}, "idle": {"slots": {"body": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body4": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "body5": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff7f"}]}, "body6": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffff7f"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_2": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_3": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_5": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body_8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_9": {"color": [{"color": "ffffff9f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff9f"}]}, "body_10": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "body_11": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff7f"}]}, "body_12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body_13": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_14": {"color": [{"color": "ffffff9f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff9f"}]}, "body_15": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "body_16": {"color": [{"color": "ffffff8d"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff8d"}]}, "body_17": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_18": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "hand_R2": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "hand_R3": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff"}]}, "head_2": {"color": [{"color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "head_3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "head_4": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff"}]}}, "bones": {"body6": {"translate": [{"x": -5.09, "y": -1.45}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 1, "x": -5.09, "y": -1.45}], "scale": [{}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 1}]}, "body5": {"translate": [{"x": -6.43, "y": -0.48}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 1, "x": -6.43, "y": -0.48}], "scale": [{}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 1}]}, "body4": {"rotate": [{}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 1}], "translate": [{"x": -9.48, "y": 2.07}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 1, "x": -9.48, "y": 2.07}], "scale": [{}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 1}]}, "body3": {"rotate": [{"angle": -7.52}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 1, "angle": -7.52}], "translate": [{"x": 1.02, "y": 0.78}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 1, "x": 1.02, "y": 0.78}]}, "body9": {"rotate": [{"angle": 7.57, "curve": "stepped"}, {"time": 0.1667, "angle": -7.52}, {"time": 0.8333, "angle": 7.57}], "translate": [{"x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.1667, "x": 1.02, "y": 0.78}, {"time": 0.8333, "x": 13.12, "y": -11.14}]}, "body10": {"rotate": [{"angle": 22.83, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "angle": 22.83}], "translate": [{"x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.1667, "x": -9.48, "y": 2.07}, {"time": 0.8333, "x": 34.93, "y": -4.74}], "scale": [{"x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.998, "y": 0.998}]}, "body11": {"translate": [{"x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.1667, "x": -6.43, "y": -0.48}, {"time": 0.8333, "x": 16.68, "y": 9.19}], "scale": [{"x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.638, "y": 0.638}]}, "body12": {"translate": [{"x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.1667, "x": -5.09, "y": -1.45}, {"time": 0.8333, "x": -0.42, "y": 0.66}], "scale": [{"x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.611, "y": 0.611}]}, "body14": {"rotate": [{"angle": 7.57, "curve": "stepped"}, {"time": 0.1667, "angle": 7.57, "curve": "stepped"}, {"time": 0.3333, "angle": -7.52}, {"time": 1, "angle": 7.57}], "translate": [{"x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.1667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.3333, "x": 1.02, "y": 0.78}, {"time": 1, "x": 13.12, "y": -11.14}]}, "body15": {"rotate": [{"angle": 22.83, "curve": "stepped"}, {"time": 0.1667, "angle": 22.83, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "angle": 22.83}], "translate": [{"x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.1667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.3333, "x": -9.48, "y": 2.07}, {"time": 1, "x": 34.93, "y": -4.74}], "scale": [{"x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.1667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.998, "y": 0.998}]}, "body16": {"translate": [{"x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.1667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.3333, "x": -6.43, "y": -0.48}, {"time": 1, "x": 16.68, "y": 9.19}], "scale": [{"x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.1667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.638, "y": 0.638}]}, "body17": {"translate": [{"x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.1667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.3333, "x": -5.09, "y": -1.45}, {"time": 1, "x": -0.42, "y": 0.66}], "scale": [{"x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.1667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.611, "y": 0.611}]}, "body19": {"rotate": [{"angle": 0.03}, {"time": 0.3333, "angle": 7.57, "curve": "stepped"}, {"time": 0.5, "angle": 7.57, "curve": "stepped"}, {"time": 0.6667, "angle": -7.52}, {"time": 1, "angle": 0.03}], "translate": [{"x": 7.07, "y": -5.18}, {"time": 0.3333, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.5, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.6667, "x": 1.02, "y": 0.78}, {"time": 1, "x": 7.07, "y": -5.18}]}, "body20": {"rotate": [{"angle": 11.42}, {"time": 0.3333, "angle": 22.83, "curve": "stepped"}, {"time": 0.5, "angle": 22.83, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "angle": 11.42}], "translate": [{"x": 12.72, "y": -1.34}, {"time": 0.3333, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.5, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.6667, "x": -9.48, "y": 2.07}, {"time": 1, "x": 12.72, "y": -1.34}], "scale": [{"x": 0.999, "y": 0.999}, {"time": 0.3333, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.5, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.999, "y": 0.999}]}, "body21": {"translate": [{"x": 5.12, "y": 4.36}, {"time": 0.3333, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.5, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.6667, "x": -6.43, "y": -0.48}, {"time": 1, "x": 5.12, "y": 4.36}], "scale": [{"x": 0.819, "y": 0.819}, {"time": 0.3333, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.5, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.819, "y": 0.819}]}, "body22": {"translate": [{"x": -2.76, "y": -0.4}, {"time": 0.3333, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.5, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.6667, "x": -5.09, "y": -1.45}, {"time": 1, "x": -2.76, "y": -0.4}], "scale": [{"x": 0.805, "y": 0.805}, {"time": 0.3333, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.5, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.805, "y": 0.805}]}, "body24": {"rotate": [{"angle": -3.74}, {"time": 0.1667, "angle": 0.03}, {"time": 0.5, "angle": 7.57, "curve": "stepped"}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 0.8333, "angle": -7.52}, {"time": 1, "angle": -3.74}], "translate": [{"x": 4.05, "y": -2.2}, {"time": 0.1667, "x": 7.07, "y": -5.18}, {"time": 0.5, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.8333, "x": 1.02, "y": 0.78}, {"time": 1, "x": 4.05, "y": -2.2}]}, "body25": {"rotate": [{"angle": 5.71}, {"time": 0.1667, "angle": 11.42}, {"time": 0.5, "angle": 22.83, "curve": "stepped"}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "angle": 5.71}], "translate": [{"x": 1.62, "y": 0.37}, {"time": 0.1667, "x": 12.72, "y": -1.34}, {"time": 0.5, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.8333, "x": -9.48, "y": 2.07}, {"time": 1, "x": 1.62, "y": 0.37}], "scale": [{"x": 0.999, "y": 0.999}, {"time": 0.1667, "x": 0.999, "y": 0.999}, {"time": 0.5, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.999, "y": 0.999}]}, "body26": {"translate": [{"x": -0.65, "y": 1.94}, {"time": 0.1667, "x": 5.12, "y": 4.36}, {"time": 0.5, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.8333, "x": -6.43, "y": -0.48}, {"time": 1, "x": -0.65, "y": 1.94}], "scale": [{"x": 0.91, "y": 0.91}, {"time": 0.1667, "x": 0.819, "y": 0.819}, {"time": 0.5, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.91, "y": 0.91}]}, "body27": {"translate": [{"x": -3.92, "y": -0.92}, {"time": 0.1667, "x": -2.76, "y": -0.4}, {"time": 0.5, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.8333, "x": -5.09, "y": -1.45}, {"time": 1, "x": -3.92, "y": -0.92}], "scale": [{"x": 0.903, "y": 0.903}, {"time": 0.1667, "x": 0.805, "y": 0.805}, {"time": 0.5, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.903, "y": 0.903}]}, "body29": {"rotate": [{"angle": -7.52}, {"time": 0.1667, "angle": -3.74}, {"time": 0.3333, "angle": 0.03}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 0.8333, "angle": 7.57, "curve": "stepped"}, {"time": 1, "angle": -7.52}], "translate": [{"x": 1.02, "y": 0.78}, {"time": 0.1667, "x": 4.05, "y": -2.2}, {"time": 0.3333, "x": 7.07, "y": -5.18}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.8333, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 1, "x": 1.02, "y": 0.78}]}, "body30": {"rotate": [{}, {"time": 0.1667, "angle": 5.71}, {"time": 0.3333, "angle": 11.42}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 0.8333, "angle": 22.83, "curve": "stepped"}, {"time": 1}], "translate": [{"x": -9.48, "y": 2.07}, {"time": 0.1667, "x": 1.62, "y": 0.37}, {"time": 0.3333, "x": 12.72, "y": -1.34}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.8333, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 1, "x": -9.48, "y": 2.07}], "scale": [{}, {"time": 0.1667, "x": 0.999, "y": 0.999}, {"time": 0.3333, "x": 0.999, "y": 0.999}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.8333, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 1}]}, "body31": {"translate": [{"x": -6.43, "y": -0.48}, {"time": 0.1667, "x": -0.65, "y": 1.94}, {"time": 0.3333, "x": 5.12, "y": 4.36}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.8333, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 1, "x": -6.43, "y": -0.48}], "scale": [{}, {"time": 0.1667, "x": 0.91, "y": 0.91}, {"time": 0.3333, "x": 0.819, "y": 0.819}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.8333, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 1}]}, "body32": {"translate": [{"x": -5.09, "y": -1.45}, {"time": 0.1667, "x": -3.92, "y": -0.92}, {"time": 0.3333, "x": -2.76, "y": -0.4}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.8333, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 1, "x": -5.09, "y": -1.45}], "scale": [{}, {"time": 0.1667, "x": 0.903, "y": 0.903}, {"time": 0.3333, "x": 0.805, "y": 0.805}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.8333, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 1}]}, "body_5": {"translate": [{"x": -0.58, "y": -0.55}, {"time": 0.6667, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 1, "x": -0.58, "y": -0.55}]}, "body_8": {"translate": [{"x": -15.24, "y": 3.01}, {"time": 0.6667, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 1, "x": -15.24, "y": 3.01}], "scale": [{}, {"time": 0.6667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 1}]}, "body_9": {"translate": [{"x": -21.47, "y": -0.6}, {"time": 0.6667, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 1, "x": -21.47, "y": -0.6}], "scale": [{"x": 1.528, "y": 1.528}, {"time": 0.6667, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 1, "x": 1.528, "y": 1.528}]}, "body_10": {"translate": [{"x": -15.65, "y": -4.09}, {"time": 0.6667, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 1, "x": -15.65, "y": -4.09}], "scale": [{"x": 1.121, "y": 1.121}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 1.121, "y": 1.121}]}, "body_24": {"translate": [{"x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.1667, "x": -0.58, "y": -0.55}, {"time": 0.8333, "x": 3.24, "y": -4.05}]}, "body_25": {"translate": [{"x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.1667, "x": -15.24, "y": 3.01}, {"time": 0.8333, "x": 7.4, "y": -3.2}], "scale": [{"x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 1.056, "y": 1.056}]}, "body_26": {"translate": [{"x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.1667, "x": -21.47, "y": -0.6}, {"time": 0.8333, "x": 7.06, "y": 2.54}], "scale": [{"x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.1667, "x": 1.528, "y": 1.528}, {"time": 0.8333, "x": 0.562, "y": 0.562}]}, "body_27": {"translate": [{"x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.1667, "x": -15.65, "y": -4.09}, {"time": 0.8333, "x": 1.45, "y": 0.76}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "x": 1.121, "y": 1.121}, {"time": 0.8333}]}, "body_29": {"translate": [{"x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.3333, "x": -0.58, "y": -0.55}, {"time": 1, "x": 3.24, "y": -4.05}]}, "body_30": {"translate": [{"x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.3333, "x": -15.24, "y": 3.01}, {"time": 1, "x": 7.4, "y": -3.2}], "scale": [{"x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 1.056, "y": 1.056}]}, "body_31": {"translate": [{"x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.3333, "x": -21.47, "y": -0.6}, {"time": 1, "x": 7.06, "y": 2.54}], "scale": [{"x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.3333, "x": 1.528, "y": 1.528}, {"time": 1, "x": 0.562, "y": 0.562}]}, "body_32": {"translate": [{"x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.3333, "x": -15.65, "y": -4.09}, {"time": 1, "x": 1.45, "y": 0.76}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "x": 1.121, "y": 1.121}, {"time": 1}]}, "body_34": {"translate": [{"x": 2.29, "y": -3.18}, {"time": 0.1667, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.5, "x": -0.58, "y": -0.55}, {"time": 1, "x": 2.29, "y": -3.18}]}, "body_35": {"translate": [{"x": 1.74, "y": -1.65}, {"time": 0.1667, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.5, "x": -15.24, "y": 3.01}, {"time": 1, "x": 1.74, "y": -1.65}], "scale": [{"x": 1.042, "y": 1.042}, {"time": 0.1667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.5}, {"time": 1, "x": 1.042, "y": 1.042}]}, "body_36": {"translate": [{"x": -0.07, "y": 1.75}, {"time": 0.1667, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.5, "x": -21.47, "y": -0.6}, {"time": 1, "x": -0.07, "y": 1.75}], "scale": [{"x": 0.803, "y": 0.803}, {"time": 0.1667, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.5, "x": 1.528, "y": 1.528}, {"time": 1, "x": 0.803, "y": 0.803}]}, "body_37": {"translate": [{"x": -2.83, "y": -0.45}, {"time": 0.1667, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.5, "x": -15.65, "y": -4.09}, {"time": 1, "x": -2.83, "y": -0.45}], "scale": [{"x": 1.03, "y": 1.03}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.03, "y": 1.03}]}, "body_39": {"translate": [{"x": 1.33, "y": -2.3}, {"time": 0.3333, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.6667, "x": -0.58, "y": -0.55}, {"time": 1, "x": 1.33, "y": -2.3}]}, "body_40": {"translate": [{"x": -3.92, "y": -0.09}, {"time": 0.3333, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.6667, "x": -15.24, "y": 3.01}, {"time": 1, "x": -3.92, "y": -0.09}], "scale": [{"x": 1.028, "y": 1.028}, {"time": 0.3333, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 1.028, "y": 1.028}]}, "body_41": {"translate": [{"x": -7.21, "y": 0.97}, {"time": 0.3333, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.6667, "x": -21.47, "y": -0.6}, {"time": 1, "x": -7.21, "y": 0.97}], "scale": [{"x": 1.045, "y": 1.045}, {"time": 0.3333, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.6667, "x": 1.528, "y": 1.528}, {"time": 1, "x": 1.045, "y": 1.045}]}, "body_42": {"translate": [{"x": -7.1, "y": -1.66}, {"time": 0.3333, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.6667, "x": -15.65, "y": -4.09}, {"time": 1, "x": -7.1, "y": -1.66}], "scale": [{"x": 1.06, "y": 1.06}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.06, "y": 1.06}]}, "body_44": {"translate": [{"x": 0.38, "y": -1.43}, {"time": 0.5, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.8333, "x": -0.58, "y": -0.55}, {"time": 1, "x": 0.38, "y": -1.43}]}, "body_45": {"translate": [{"x": -9.58, "y": 1.46}, {"time": 0.5, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.8333, "x": -15.24, "y": 3.01}, {"time": 1, "x": -9.58, "y": 1.46}], "scale": [{"x": 1.014, "y": 1.014}, {"time": 0.5, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 1.014, "y": 1.014}]}, "body_46": {"translate": [{"x": -14.34, "y": 0.18}, {"time": 0.5, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.8333, "x": -21.47, "y": -0.6}, {"time": 1, "x": -14.34, "y": 0.18}], "scale": [{"x": 1.286, "y": 1.286}, {"time": 0.5, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.8333, "x": 1.528, "y": 1.528}, {"time": 1, "x": 1.286, "y": 1.286}]}, "body_47": {"translate": [{"x": -11.38, "y": -2.88}, {"time": 0.5, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.8333, "x": -15.65, "y": -4.09}, {"time": 1, "x": -11.38, "y": -2.88}], "scale": [{"x": 1.091, "y": 1.091}, {"time": 0.5, "curve": "stepped"}, {"time": 0.8333, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.091, "y": 1.091}]}, "body_13": {"translate": [{"x": -16.43, "y": -4.41}, {"time": 0.6667, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 1, "x": -16.43, "y": -4.41}], "scale": [{"x": 1.272, "y": 1.272}, {"time": 0.6667, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 1, "x": 1.272, "y": 1.272}]}, "body_12": {"translate": [{"x": -8.73, "y": 3.82}, {"time": 0.6667, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 1, "x": -8.73, "y": 3.82}], "scale": [{"x": 0.951, "y": 0.951}, {"time": 0.6667, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 1, "x": 0.951, "y": 0.951}]}, "body_11": {"translate": [{}, {"time": 0.6667, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 1}]}, "body_49": {"translate": [{"x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 11.47, "y": -4.95}]}, "body_50": {"translate": [{"x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.1667, "x": -8.73, "y": 3.82}, {"time": 0.8333, "x": 7.13, "y": 1.23}], "scale": [{"x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.1667, "x": 0.951, "y": 0.951}, {"time": 0.8333, "x": 0.9, "y": 0.9}]}, "body_51": {"translate": [{"x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.1667, "x": -16.43, "y": -4.41}, {"time": 0.8333, "x": -1.6, "y": -0.26}], "scale": [{"x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.1667, "x": 1.272, "y": 1.272}, {"time": 0.8333, "x": 0.863, "y": 0.863}]}, "body_53": {"translate": [{"x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 11.47, "y": -4.95}]}, "body_54": {"translate": [{"x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.3333, "x": -8.73, "y": 3.82}, {"time": 1, "x": 7.13, "y": 1.23}], "scale": [{"x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.3333, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.9, "y": 0.9}]}, "body_55": {"translate": [{"x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.3333, "x": -16.43, "y": -4.41}, {"time": 1, "x": -1.6, "y": -0.26}], "scale": [{"x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.3333, "x": 1.272, "y": 1.272}, {"time": 1, "x": 0.863, "y": 0.863}]}, "body_57": {"translate": [{"x": 8.6, "y": -3.71}, {"time": 0.1667, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.5}, {"time": 1, "x": 8.6, "y": -3.71}]}, "body_58": {"translate": [{"x": 3.16, "y": 1.88}, {"time": 0.1667, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.5, "x": -8.73, "y": 3.82}, {"time": 1, "x": 3.16, "y": 1.88}], "scale": [{"x": 0.913, "y": 0.913}, {"time": 0.1667, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.5, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.913, "y": 0.913}]}, "body_59": {"translate": [{"x": -5.31, "y": -1.3}, {"time": 0.1667, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.5, "x": -16.43, "y": -4.41}, {"time": 1, "x": -5.31, "y": -1.3}], "scale": [{"x": 0.965, "y": 0.965}, {"time": 0.1667, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.5, "x": 1.272, "y": 1.272}, {"time": 1, "x": 0.965, "y": 0.965}]}, "body_61": {"translate": [{"x": 5.73, "y": -2.48}, {"time": 0.3333, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 5.73, "y": -2.48}]}, "body_62": {"translate": [{"x": -0.8, "y": 2.52}, {"time": 0.3333, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.6667, "x": -8.73, "y": 3.82}, {"time": 1, "x": -0.8, "y": 2.52}], "scale": [{"x": 0.925, "y": 0.925}, {"time": 0.3333, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.6667, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.925, "y": 0.925}]}, "body_63": {"translate": [{"x": -9.02, "y": -2.33}, {"time": 0.3333, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.6667, "x": -16.43, "y": -4.41}, {"time": 1, "x": -9.02, "y": -2.33}], "scale": [{"x": 1.067, "y": 1.067}, {"time": 0.3333, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.6667, "x": 1.272, "y": 1.272}, {"time": 1, "x": 1.067, "y": 1.067}]}, "body_65": {"translate": [{"x": 2.87, "y": -1.24}, {"time": 0.5, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 2.87, "y": -1.24}]}, "body_66": {"translate": [{"x": -4.77, "y": 3.17}, {"time": 0.5, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.8333, "x": -8.73, "y": 3.82}, {"time": 1, "x": -4.77, "y": 3.17}], "scale": [{"x": 0.938, "y": 0.938}, {"time": 0.5, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.8333, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.938, "y": 0.938}]}, "body_67": {"translate": [{"x": -12.72, "y": -3.37}, {"time": 0.5, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.8333, "x": -16.43, "y": -4.41}, {"time": 1, "x": -12.72, "y": -3.37}], "scale": [{"x": 1.169, "y": 1.169}, {"time": 0.5, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.8333, "x": 1.272, "y": 1.272}, {"time": 1, "x": 1.169, "y": 1.169}]}, "body_19": {"translate": [{"x": 16.79, "y": 1.11}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 16.79, "y": 1.11}]}, "body_18": {"translate": [{"x": 24.13, "y": 2.31}, {"time": 0.6667, "x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 1, "x": 24.13, "y": 2.31}]}, "body_17": {"translate": [{"x": 16.37, "y": 1.82}, {"time": 0.6667, "x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 1, "x": 16.37, "y": 1.82}]}, "body_7": {"translate": [{"x": 0.97, "y": -1.28}, {"time": 0.6667, "x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 1, "x": 0.97, "y": -1.28}]}, "body_4": {"rotate": [{"angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.92}]}, "body_6": {"rotate": [{"angle": 4.05, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.76, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 4.05}]}, "body_3": {"rotate": [{"angle": 1.13, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -2.76, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 1.13}]}, "body_2": {"rotate": [{"angle": -1.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -1.65}]}, "body": {"rotate": [{"angle": -2.57, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": -2.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 5.78, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": -2.57}]}, "hand_R2": {"rotate": [{}, {"time": 0.8333, "angle": 119.45, "curve": "stepped"}, {"time": 1}]}, "hand_R3": {"rotate": [{"angle": 76.45}, {"time": 0.3, "angle": 119.45, "curve": "stepped"}, {"time": 0.4667}, {"time": 1, "angle": 76.45}]}, "hand_R": {"rotate": [{"angle": -27.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -27.46}]}, "hand_L2": {"rotate": [{"angle": 9.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.51}]}, "hand_L": {"rotate": [{"angle": 4.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 9.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 4.66}]}, "head": {"translate": [{"x": 2.59, "y": -0.53}, {"time": 0.5, "x": -1.5, "y": 0.3}, {"time": 1, "x": 2.59, "y": -0.53}]}, "body_68": {"translate": [{"x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 0.3333, "x": 0.97, "y": -1.28}, {"time": 1, "x": -14.56, "y": 0.86}]}, "body_69": {"translate": [{"x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 0.3333, "x": 16.37, "y": 1.82}, {"time": 1, "x": -10.85, "y": 1.57}]}, "body_70": {"translate": [{"x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 0.3333, "x": 24.13, "y": 2.31}, {"time": 1, "x": -5.9, "y": 0.79}]}, "body_71": {"translate": [{"curve": "stepped"}, {"time": 0.3333, "x": 16.79, "y": 1.11}, {"time": 1}]}, "body_72": {"translate": [{"x": -6.79, "y": -0.21}, {"time": 0.3333, "x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 0.6667, "x": 0.97, "y": -1.28}, {"time": 1, "x": -6.79, "y": -0.21}]}, "body_73": {"translate": [{"x": 2.76, "y": 1.69}, {"time": 0.3333, "x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 0.6667, "x": 16.37, "y": 1.82}, {"time": 1, "x": 2.76, "y": 1.69}]}, "body_74": {"translate": [{"x": 9.12, "y": 1.55}, {"time": 0.3333, "x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 0.6667, "x": 24.13, "y": 2.31}, {"time": 1, "x": 9.12, "y": 1.55}]}, "body_75": {"translate": [{"x": 8.4, "y": 0.55}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "x": 16.79, "y": 1.11}, {"time": 1, "x": 8.4, "y": 0.55}]}, "head_4": {"translate": [{"x": -1.18, "y": 14.92}, {"time": 0.3333, "x": -1.57, "y": 29.39, "curve": "stepped"}, {"time": 0.6667, "x": 2.63, "y": -4.31}, {"time": 0.8333, "x": 1.46, "y": 5.76}, {"time": 1, "x": -1.18, "y": 14.92}], "scale": [{}, {"time": 0.3333, "x": 0.735, "y": 0.735, "curve": "stepped"}, {"time": 0.6667, "x": 0.469, "y": 0.397}, {"time": 1}]}, "head_3": {"translate": [{"x": -2.04, "y": 14.2, "curve": "stepped"}, {"time": 0.3333, "x": 4.48, "y": -15.7}, {"time": 0.6667, "x": -2.89, "y": -1.78}, {"time": 1, "x": -2.04, "y": 14.2}], "scale": [{"x": 0.786, "y": 0.786, "curve": "stepped"}, {"time": 0.3333, "x": 0.327, "y": 0.241}, {"time": 0.6667}, {"time": 1, "x": 0.786, "y": 0.786}]}, "head_2": {"translate": [{"x": 2.35, "y": -26.64}, {"time": 0.3333, "x": 0.51, "y": -9.13}, {"time": 0.6667, "x": -0.63, "y": 2.66, "curve": "stepped"}, {"time": 1, "x": 2.35, "y": -26.64}], "scale": [{"x": 0.509, "y": 0.509}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 0.509, "y": 0.509}]}}}, "run": {"slots": {"body": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body4": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "body5": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff7f"}]}, "body6": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffff7f"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_2": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_3": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_5": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "body_7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body_8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_9": {"color": [{"color": "ffffff9f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff9f"}]}, "body_10": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "body_11": {"color": [{"color": "ffffff7f"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff7f"}]}, "body_12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "body_13": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_14": {"color": [{"color": "ffffff9f"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff9f"}]}, "body_15": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "body_16": {"color": [{"color": "ffffff8d"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffff8d"}]}, "body_17": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "body_18": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}]}, "hand_R2": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "hand_R3": {"color": [{"time": 0.0667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff"}]}, "head_2": {"color": [{"color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}]}, "head_3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "head_4": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff"}]}}, "bones": {"body6": {"translate": [{"x": -5.09, "y": -1.45}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 1, "x": -5.09, "y": -1.45}], "scale": [{}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 1}]}, "body5": {"translate": [{"x": -6.43, "y": -0.48}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 1, "x": -6.43, "y": -0.48}], "scale": [{}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 1}]}, "body4": {"rotate": [{}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 1}], "translate": [{"x": -9.48, "y": 2.07}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 1, "x": -9.48, "y": 2.07}], "scale": [{}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 1}]}, "body3": {"rotate": [{"angle": -7.52}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 1, "angle": -7.52}], "translate": [{"x": 1.02, "y": 0.78}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 1, "x": 1.02, "y": 0.78}]}, "body9": {"rotate": [{"angle": 7.57, "curve": "stepped"}, {"time": 0.1667, "angle": -7.52}, {"time": 0.8333, "angle": 7.57}], "translate": [{"x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.1667, "x": 1.02, "y": 0.78}, {"time": 0.8333, "x": 13.12, "y": -11.14}]}, "body10": {"rotate": [{"angle": 22.83, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "angle": 22.83}], "translate": [{"x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.1667, "x": -9.48, "y": 2.07}, {"time": 0.8333, "x": 34.93, "y": -4.74}], "scale": [{"x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.998, "y": 0.998}]}, "body11": {"translate": [{"x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.1667, "x": -6.43, "y": -0.48}, {"time": 0.8333, "x": 16.68, "y": 9.19}], "scale": [{"x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.638, "y": 0.638}]}, "body12": {"translate": [{"x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.1667, "x": -5.09, "y": -1.45}, {"time": 0.8333, "x": -0.42, "y": 0.66}], "scale": [{"x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 0.611, "y": 0.611}]}, "body14": {"rotate": [{"angle": 7.57, "curve": "stepped"}, {"time": 0.1667, "angle": 7.57, "curve": "stepped"}, {"time": 0.3333, "angle": -7.52}, {"time": 1, "angle": 7.57}], "translate": [{"x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.1667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.3333, "x": 1.02, "y": 0.78}, {"time": 1, "x": 13.12, "y": -11.14}]}, "body15": {"rotate": [{"angle": 22.83, "curve": "stepped"}, {"time": 0.1667, "angle": 22.83, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "angle": 22.83}], "translate": [{"x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.1667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.3333, "x": -9.48, "y": 2.07}, {"time": 1, "x": 34.93, "y": -4.74}], "scale": [{"x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.1667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.998, "y": 0.998}]}, "body16": {"translate": [{"x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.1667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.3333, "x": -6.43, "y": -0.48}, {"time": 1, "x": 16.68, "y": 9.19}], "scale": [{"x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.1667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.638, "y": 0.638}]}, "body17": {"translate": [{"x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.1667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.3333, "x": -5.09, "y": -1.45}, {"time": 1, "x": -0.42, "y": 0.66}], "scale": [{"x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.1667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 0.611, "y": 0.611}]}, "body19": {"rotate": [{"angle": 0.03}, {"time": 0.3333, "angle": 7.57, "curve": "stepped"}, {"time": 0.5, "angle": 7.57, "curve": "stepped"}, {"time": 0.6667, "angle": -7.52}, {"time": 1, "angle": 0.03}], "translate": [{"x": 7.07, "y": -5.18}, {"time": 0.3333, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.5, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.6667, "x": 1.02, "y": 0.78}, {"time": 1, "x": 7.07, "y": -5.18}]}, "body20": {"rotate": [{"angle": 11.42}, {"time": 0.3333, "angle": 22.83, "curve": "stepped"}, {"time": 0.5, "angle": 22.83, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "angle": 11.42}], "translate": [{"x": 12.72, "y": -1.34}, {"time": 0.3333, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.5, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.6667, "x": -9.48, "y": 2.07}, {"time": 1, "x": 12.72, "y": -1.34}], "scale": [{"x": 0.999, "y": 0.999}, {"time": 0.3333, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.5, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.999, "y": 0.999}]}, "body21": {"translate": [{"x": 5.12, "y": 4.36}, {"time": 0.3333, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.5, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.6667, "x": -6.43, "y": -0.48}, {"time": 1, "x": 5.12, "y": 4.36}], "scale": [{"x": 0.819, "y": 0.819}, {"time": 0.3333, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.5, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.819, "y": 0.819}]}, "body22": {"translate": [{"x": -2.76, "y": -0.4}, {"time": 0.3333, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.5, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.6667, "x": -5.09, "y": -1.45}, {"time": 1, "x": -2.76, "y": -0.4}], "scale": [{"x": 0.805, "y": 0.805}, {"time": 0.3333, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.5, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 0.805, "y": 0.805}]}, "body24": {"rotate": [{"angle": -3.74}, {"time": 0.1667, "angle": 0.03}, {"time": 0.5, "angle": 7.57, "curve": "stepped"}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 0.8333, "angle": -7.52}, {"time": 1, "angle": -3.74}], "translate": [{"x": 4.05, "y": -2.2}, {"time": 0.1667, "x": 7.07, "y": -5.18}, {"time": 0.5, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.8333, "x": 1.02, "y": 0.78}, {"time": 1, "x": 4.05, "y": -2.2}]}, "body25": {"rotate": [{"angle": 5.71}, {"time": 0.1667, "angle": 11.42}, {"time": 0.5, "angle": 22.83, "curve": "stepped"}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "angle": 5.71}], "translate": [{"x": 1.62, "y": 0.37}, {"time": 0.1667, "x": 12.72, "y": -1.34}, {"time": 0.5, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.8333, "x": -9.48, "y": 2.07}, {"time": 1, "x": 1.62, "y": 0.37}], "scale": [{"x": 0.999, "y": 0.999}, {"time": 0.1667, "x": 0.999, "y": 0.999}, {"time": 0.5, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.999, "y": 0.999}]}, "body26": {"translate": [{"x": -0.65, "y": 1.94}, {"time": 0.1667, "x": 5.12, "y": 4.36}, {"time": 0.5, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.8333, "x": -6.43, "y": -0.48}, {"time": 1, "x": -0.65, "y": 1.94}], "scale": [{"x": 0.91, "y": 0.91}, {"time": 0.1667, "x": 0.819, "y": 0.819}, {"time": 0.5, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.91, "y": 0.91}]}, "body27": {"translate": [{"x": -3.92, "y": -0.92}, {"time": 0.1667, "x": -2.76, "y": -0.4}, {"time": 0.5, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.8333, "x": -5.09, "y": -1.45}, {"time": 1, "x": -3.92, "y": -0.92}], "scale": [{"x": 0.903, "y": 0.903}, {"time": 0.1667, "x": 0.805, "y": 0.805}, {"time": 0.5, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 0.903, "y": 0.903}]}, "body29": {"rotate": [{"angle": -7.52}, {"time": 0.1667, "angle": -3.74}, {"time": 0.3333, "angle": 0.03}, {"time": 0.6667, "angle": 7.57, "curve": "stepped"}, {"time": 0.8333, "angle": 7.57, "curve": "stepped"}, {"time": 1, "angle": -7.52}], "translate": [{"x": 1.02, "y": 0.78}, {"time": 0.1667, "x": 4.05, "y": -2.2}, {"time": 0.3333, "x": 7.07, "y": -5.18}, {"time": 0.6667, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 0.8333, "x": 13.12, "y": -11.14, "curve": "stepped"}, {"time": 1, "x": 1.02, "y": 0.78}]}, "body30": {"rotate": [{}, {"time": 0.1667, "angle": 5.71}, {"time": 0.3333, "angle": 11.42}, {"time": 0.6667, "angle": 22.83, "curve": "stepped"}, {"time": 0.8333, "angle": 22.83, "curve": "stepped"}, {"time": 1}], "translate": [{"x": -9.48, "y": 2.07}, {"time": 0.1667, "x": 1.62, "y": 0.37}, {"time": 0.3333, "x": 12.72, "y": -1.34}, {"time": 0.6667, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 0.8333, "x": 34.93, "y": -4.74, "curve": "stepped"}, {"time": 1, "x": -9.48, "y": 2.07}], "scale": [{}, {"time": 0.1667, "x": 0.999, "y": 0.999}, {"time": 0.3333, "x": 0.999, "y": 0.999}, {"time": 0.6667, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 0.8333, "x": 0.998, "y": 0.998, "curve": "stepped"}, {"time": 1}]}, "body31": {"translate": [{"x": -6.43, "y": -0.48}, {"time": 0.1667, "x": -0.65, "y": 1.94}, {"time": 0.3333, "x": 5.12, "y": 4.36}, {"time": 0.6667, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 0.8333, "x": 16.68, "y": 9.19, "curve": "stepped"}, {"time": 1, "x": -6.43, "y": -0.48}], "scale": [{}, {"time": 0.1667, "x": 0.91, "y": 0.91}, {"time": 0.3333, "x": 0.819, "y": 0.819}, {"time": 0.6667, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 0.8333, "x": 0.638, "y": 0.638, "curve": "stepped"}, {"time": 1}]}, "body32": {"translate": [{"x": -5.09, "y": -1.45}, {"time": 0.1667, "x": -3.92, "y": -0.92}, {"time": 0.3333, "x": -2.76, "y": -0.4}, {"time": 0.6667, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 0.8333, "x": -0.42, "y": 0.66, "curve": "stepped"}, {"time": 1, "x": -5.09, "y": -1.45}], "scale": [{}, {"time": 0.1667, "x": 0.903, "y": 0.903}, {"time": 0.3333, "x": 0.805, "y": 0.805}, {"time": 0.6667, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 0.8333, "x": 0.611, "y": 0.611, "curve": "stepped"}, {"time": 1}]}, "body_5": {"translate": [{"x": -0.58, "y": -0.55}, {"time": 0.6667, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 1, "x": -0.58, "y": -0.55}]}, "body_8": {"translate": [{"x": -15.24, "y": 3.01}, {"time": 0.6667, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 1, "x": -15.24, "y": 3.01}], "scale": [{}, {"time": 0.6667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 1}]}, "body_9": {"translate": [{"x": -21.47, "y": -0.6}, {"time": 0.6667, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 1, "x": -21.47, "y": -0.6}], "scale": [{"x": 1.528, "y": 1.528}, {"time": 0.6667, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 1, "x": 1.528, "y": 1.528}]}, "body_10": {"translate": [{"x": -15.65, "y": -4.09}, {"time": 0.6667, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 1, "x": -15.65, "y": -4.09}], "scale": [{"x": 1.121, "y": 1.121}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 1.121, "y": 1.121}]}, "body_24": {"translate": [{"x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.1667, "x": -0.58, "y": -0.55}, {"time": 0.8333, "x": 3.24, "y": -4.05}]}, "body_25": {"translate": [{"x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.1667, "x": -15.24, "y": 3.01}, {"time": 0.8333, "x": 7.4, "y": -3.2}], "scale": [{"x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 1.056, "y": 1.056}]}, "body_26": {"translate": [{"x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.1667, "x": -21.47, "y": -0.6}, {"time": 0.8333, "x": 7.06, "y": 2.54}], "scale": [{"x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.1667, "x": 1.528, "y": 1.528}, {"time": 0.8333, "x": 0.562, "y": 0.562}]}, "body_27": {"translate": [{"x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.1667, "x": -15.65, "y": -4.09}, {"time": 0.8333, "x": 1.45, "y": 0.76}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "x": 1.121, "y": 1.121}, {"time": 0.8333}]}, "body_29": {"translate": [{"x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.3333, "x": -0.58, "y": -0.55}, {"time": 1, "x": 3.24, "y": -4.05}]}, "body_30": {"translate": [{"x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.3333, "x": -15.24, "y": 3.01}, {"time": 1, "x": 7.4, "y": -3.2}], "scale": [{"x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 1.056, "y": 1.056}]}, "body_31": {"translate": [{"x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.3333, "x": -21.47, "y": -0.6}, {"time": 1, "x": 7.06, "y": 2.54}], "scale": [{"x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.3333, "x": 1.528, "y": 1.528}, {"time": 1, "x": 0.562, "y": 0.562}]}, "body_32": {"translate": [{"x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.3333, "x": -15.65, "y": -4.09}, {"time": 1, "x": 1.45, "y": 0.76}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "x": 1.121, "y": 1.121}, {"time": 1}]}, "body_34": {"translate": [{"x": 2.29, "y": -3.18}, {"time": 0.1667, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.5, "x": -0.58, "y": -0.55}, {"time": 1, "x": 2.29, "y": -3.18}]}, "body_35": {"translate": [{"x": 1.74, "y": -1.65}, {"time": 0.1667, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.5, "x": -15.24, "y": 3.01}, {"time": 1, "x": 1.74, "y": -1.65}], "scale": [{"x": 1.042, "y": 1.042}, {"time": 0.1667, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.5}, {"time": 1, "x": 1.042, "y": 1.042}]}, "body_36": {"translate": [{"x": -0.07, "y": 1.75}, {"time": 0.1667, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.5, "x": -21.47, "y": -0.6}, {"time": 1, "x": -0.07, "y": 1.75}], "scale": [{"x": 0.803, "y": 0.803}, {"time": 0.1667, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.5, "x": 1.528, "y": 1.528}, {"time": 1, "x": 0.803, "y": 0.803}]}, "body_37": {"translate": [{"x": -2.83, "y": -0.45}, {"time": 0.1667, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.5, "x": -15.65, "y": -4.09}, {"time": 1, "x": -2.83, "y": -0.45}], "scale": [{"x": 1.03, "y": 1.03}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.5, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.03, "y": 1.03}]}, "body_39": {"translate": [{"x": 1.33, "y": -2.3}, {"time": 0.3333, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.6667, "x": -0.58, "y": -0.55}, {"time": 1, "x": 1.33, "y": -2.3}]}, "body_40": {"translate": [{"x": -3.92, "y": -0.09}, {"time": 0.3333, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.6667, "x": -15.24, "y": 3.01}, {"time": 1, "x": -3.92, "y": -0.09}], "scale": [{"x": 1.028, "y": 1.028}, {"time": 0.3333, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 1.028, "y": 1.028}]}, "body_41": {"translate": [{"x": -7.21, "y": 0.97}, {"time": 0.3333, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.6667, "x": -21.47, "y": -0.6}, {"time": 1, "x": -7.21, "y": 0.97}], "scale": [{"x": 1.045, "y": 1.045}, {"time": 0.3333, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.6667, "x": 1.528, "y": 1.528}, {"time": 1, "x": 1.045, "y": 1.045}]}, "body_42": {"translate": [{"x": -7.1, "y": -1.66}, {"time": 0.3333, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.6667, "x": -15.65, "y": -4.09}, {"time": 1, "x": -7.1, "y": -1.66}], "scale": [{"x": 1.06, "y": 1.06}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.06, "y": 1.06}]}, "body_44": {"translate": [{"x": 0.38, "y": -1.43}, {"time": 0.5, "x": 3.24, "y": -4.05, "curve": "stepped"}, {"time": 0.8333, "x": -0.58, "y": -0.55}, {"time": 1, "x": 0.38, "y": -1.43}]}, "body_45": {"translate": [{"x": -9.58, "y": 1.46}, {"time": 0.5, "x": 7.4, "y": -3.2, "curve": "stepped"}, {"time": 0.8333, "x": -15.24, "y": 3.01}, {"time": 1, "x": -9.58, "y": 1.46}], "scale": [{"x": 1.014, "y": 1.014}, {"time": 0.5, "x": 1.056, "y": 1.056, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 1.014, "y": 1.014}]}, "body_46": {"translate": [{"x": -14.34, "y": 0.18}, {"time": 0.5, "x": 7.06, "y": 2.54, "curve": "stepped"}, {"time": 0.8333, "x": -21.47, "y": -0.6}, {"time": 1, "x": -14.34, "y": 0.18}], "scale": [{"x": 1.286, "y": 1.286}, {"time": 0.5, "x": 0.562, "y": 0.562, "curve": "stepped"}, {"time": 0.8333, "x": 1.528, "y": 1.528}, {"time": 1, "x": 1.286, "y": 1.286}]}, "body_47": {"translate": [{"x": -11.38, "y": -2.88}, {"time": 0.5, "x": 1.45, "y": 0.76, "curve": "stepped"}, {"time": 0.8333, "x": -15.65, "y": -4.09}, {"time": 1, "x": -11.38, "y": -2.88}], "scale": [{"x": 1.091, "y": 1.091}, {"time": 0.5, "curve": "stepped"}, {"time": 0.8333, "x": 1.121, "y": 1.121}, {"time": 1, "x": 1.091, "y": 1.091}]}, "body_13": {"translate": [{"x": -16.43, "y": -4.41}, {"time": 0.6667, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 1, "x": -16.43, "y": -4.41}], "scale": [{"x": 1.272, "y": 1.272}, {"time": 0.6667, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 1, "x": 1.272, "y": 1.272}]}, "body_12": {"translate": [{"x": -8.73, "y": 3.82}, {"time": 0.6667, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 1, "x": -8.73, "y": 3.82}], "scale": [{"x": 0.951, "y": 0.951}, {"time": 0.6667, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 1, "x": 0.951, "y": 0.951}]}, "body_11": {"translate": [{}, {"time": 0.6667, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 1}]}, "body_49": {"translate": [{"x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.8333, "x": 11.47, "y": -4.95}]}, "body_50": {"translate": [{"x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.1667, "x": -8.73, "y": 3.82}, {"time": 0.8333, "x": 7.13, "y": 1.23}], "scale": [{"x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.1667, "x": 0.951, "y": 0.951}, {"time": 0.8333, "x": 0.9, "y": 0.9}]}, "body_51": {"translate": [{"x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.1667, "x": -16.43, "y": -4.41}, {"time": 0.8333, "x": -1.6, "y": -0.26}], "scale": [{"x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.1667, "x": 1.272, "y": 1.272}, {"time": 0.8333, "x": 0.863, "y": 0.863}]}, "body_53": {"translate": [{"x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.3333}, {"time": 1, "x": 11.47, "y": -4.95}]}, "body_54": {"translate": [{"x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.3333, "x": -8.73, "y": 3.82}, {"time": 1, "x": 7.13, "y": 1.23}], "scale": [{"x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.3333, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.9, "y": 0.9}]}, "body_55": {"translate": [{"x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.3333, "x": -16.43, "y": -4.41}, {"time": 1, "x": -1.6, "y": -0.26}], "scale": [{"x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.3333, "x": 1.272, "y": 1.272}, {"time": 1, "x": 0.863, "y": 0.863}]}, "body_57": {"translate": [{"x": 8.6, "y": -3.71}, {"time": 0.1667, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.5}, {"time": 1, "x": 8.6, "y": -3.71}]}, "body_58": {"translate": [{"x": 3.16, "y": 1.88}, {"time": 0.1667, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.5, "x": -8.73, "y": 3.82}, {"time": 1, "x": 3.16, "y": 1.88}], "scale": [{"x": 0.913, "y": 0.913}, {"time": 0.1667, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.5, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.913, "y": 0.913}]}, "body_59": {"translate": [{"x": -5.31, "y": -1.3}, {"time": 0.1667, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.5, "x": -16.43, "y": -4.41}, {"time": 1, "x": -5.31, "y": -1.3}], "scale": [{"x": 0.965, "y": 0.965}, {"time": 0.1667, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.5, "x": 1.272, "y": 1.272}, {"time": 1, "x": 0.965, "y": 0.965}]}, "body_61": {"translate": [{"x": 5.73, "y": -2.48}, {"time": 0.3333, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.6667}, {"time": 1, "x": 5.73, "y": -2.48}]}, "body_62": {"translate": [{"x": -0.8, "y": 2.52}, {"time": 0.3333, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.6667, "x": -8.73, "y": 3.82}, {"time": 1, "x": -0.8, "y": 2.52}], "scale": [{"x": 0.925, "y": 0.925}, {"time": 0.3333, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.6667, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.925, "y": 0.925}]}, "body_63": {"translate": [{"x": -9.02, "y": -2.33}, {"time": 0.3333, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.6667, "x": -16.43, "y": -4.41}, {"time": 1, "x": -9.02, "y": -2.33}], "scale": [{"x": 1.067, "y": 1.067}, {"time": 0.3333, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.6667, "x": 1.272, "y": 1.272}, {"time": 1, "x": 1.067, "y": 1.067}]}, "body_65": {"translate": [{"x": 2.87, "y": -1.24}, {"time": 0.5, "x": 11.47, "y": -4.95, "curve": "stepped"}, {"time": 0.8333}, {"time": 1, "x": 2.87, "y": -1.24}]}, "body_66": {"translate": [{"x": -4.77, "y": 3.17}, {"time": 0.5, "x": 7.13, "y": 1.23, "curve": "stepped"}, {"time": 0.8333, "x": -8.73, "y": 3.82}, {"time": 1, "x": -4.77, "y": 3.17}], "scale": [{"x": 0.938, "y": 0.938}, {"time": 0.5, "x": 0.9, "y": 0.9, "curve": "stepped"}, {"time": 0.8333, "x": 0.951, "y": 0.951}, {"time": 1, "x": 0.938, "y": 0.938}]}, "body_67": {"translate": [{"x": -12.72, "y": -3.37}, {"time": 0.5, "x": -1.6, "y": -0.26, "curve": "stepped"}, {"time": 0.8333, "x": -16.43, "y": -4.41}, {"time": 1, "x": -12.72, "y": -3.37}], "scale": [{"x": 1.169, "y": 1.169}, {"time": 0.5, "x": 0.863, "y": 0.863, "curve": "stepped"}, {"time": 0.8333, "x": 1.272, "y": 1.272}, {"time": 1, "x": 1.169, "y": 1.169}]}, "body_19": {"translate": [{"x": 16.79, "y": 1.11}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 16.79, "y": 1.11}]}, "body_18": {"translate": [{"x": 24.13, "y": 2.31}, {"time": 0.6667, "x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 1, "x": 24.13, "y": 2.31}]}, "body_17": {"translate": [{"x": 16.37, "y": 1.82}, {"time": 0.6667, "x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 1, "x": 16.37, "y": 1.82}]}, "body_7": {"translate": [{"x": 0.97, "y": -1.28}, {"time": 0.6667, "x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 1, "x": 0.97, "y": -1.28}]}, "body_4": {"rotate": [{"angle": 1.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.92}]}, "body_6": {"rotate": [{"angle": 4.05, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -2.76, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 4.05}]}, "body_3": {"rotate": [{"angle": 1.13, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -2.76, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 1.13}]}, "body_2": {"rotate": [{"angle": -1.65, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "angle": 5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.76, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": -1.65}]}, "body": {"rotate": [{"angle": -2.57, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 0.0333, "angle": -2.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 5.78, "curve": 0.246, "c3": 0.723, "c4": 0.88}, {"time": 1, "angle": -2.57}]}, "hand_R2": {"rotate": [{}, {"time": 0.8333, "angle": 119.45, "curve": "stepped"}, {"time": 1}]}, "hand_R3": {"rotate": [{"angle": 76.45}, {"time": 0.3, "angle": 119.45, "curve": "stepped"}, {"time": 0.4667}, {"time": 1, "angle": 76.45}]}, "hand_R": {"rotate": [{"angle": -27.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -27.46}]}, "hand_L2": {"rotate": [{"angle": 9.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.59, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 9.51}]}, "hand_L": {"rotate": [{"angle": 4.66, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1667, "angle": 9.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1, "angle": 4.66}]}, "head": {"translate": [{"x": 2.59, "y": -0.53}, {"time": 0.5, "x": -1.5, "y": 0.3}, {"time": 1, "x": 2.59, "y": -0.53}]}, "body_68": {"translate": [{"x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 0.3333, "x": 0.97, "y": -1.28}, {"time": 1, "x": -14.56, "y": 0.86}]}, "body_69": {"translate": [{"x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 0.3333, "x": 16.37, "y": 1.82}, {"time": 1, "x": -10.85, "y": 1.57}]}, "body_70": {"translate": [{"x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 0.3333, "x": 24.13, "y": 2.31}, {"time": 1, "x": -5.9, "y": 0.79}]}, "body_71": {"translate": [{"curve": "stepped"}, {"time": 0.3333, "x": 16.79, "y": 1.11}, {"time": 1}]}, "body_72": {"translate": [{"x": -6.79, "y": -0.21}, {"time": 0.3333, "x": -14.56, "y": 0.86, "curve": "stepped"}, {"time": 0.6667, "x": 0.97, "y": -1.28}, {"time": 1, "x": -6.79, "y": -0.21}]}, "body_73": {"translate": [{"x": 2.76, "y": 1.69}, {"time": 0.3333, "x": -10.85, "y": 1.57, "curve": "stepped"}, {"time": 0.6667, "x": 16.37, "y": 1.82}, {"time": 1, "x": 2.76, "y": 1.69}]}, "body_74": {"translate": [{"x": 9.12, "y": 1.55}, {"time": 0.3333, "x": -5.9, "y": 0.79, "curve": "stepped"}, {"time": 0.6667, "x": 24.13, "y": 2.31}, {"time": 1, "x": 9.12, "y": 1.55}]}, "body_75": {"translate": [{"x": 8.4, "y": 0.55}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "x": 16.79, "y": 1.11}, {"time": 1, "x": 8.4, "y": 0.55}]}, "head_4": {"translate": [{"x": -1.18, "y": 14.92}, {"time": 0.3333, "x": -1.57, "y": 29.39, "curve": "stepped"}, {"time": 0.6667, "x": 2.63, "y": -4.31}, {"time": 0.8333, "x": 1.46, "y": 5.76}, {"time": 1, "x": -1.18, "y": 14.92}], "scale": [{}, {"time": 0.3333, "x": 0.735, "y": 0.735, "curve": "stepped"}, {"time": 0.6667, "x": 0.469, "y": 0.397}, {"time": 1}]}, "head_3": {"translate": [{"x": -2.04, "y": 14.2, "curve": "stepped"}, {"time": 0.3333, "x": 4.48, "y": -15.7}, {"time": 0.6667, "x": -2.89, "y": -1.78}, {"time": 1, "x": -2.04, "y": 14.2}], "scale": [{"x": 0.786, "y": 0.786, "curve": "stepped"}, {"time": 0.3333, "x": 0.327, "y": 0.241}, {"time": 0.6667}, {"time": 1, "x": 0.786, "y": 0.786}]}, "head_2": {"translate": [{"x": 2.35, "y": -26.64}, {"time": 0.3333, "x": 0.51, "y": -9.13}, {"time": 0.6667, "x": -0.63, "y": 2.66, "curve": "stepped"}, {"time": 1, "x": 2.35, "y": -26.64}], "scale": [{"x": 0.509, "y": 0.509}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1, "x": 0.509, "y": 0.509}]}}}}}