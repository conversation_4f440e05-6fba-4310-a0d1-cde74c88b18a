{"skeleton": {"hash": "CbjrL4amGCmLoaTIPDmbQmCAAHw", "spine": "3.8.99", "x": -41.37, "y": -18.8, "width": 91.86, "height": 80.25, "images": "./images/", "audio": "D:/spine动效/怪物/野猪"}, "bones": [{"name": "root", "scaleX": 0.4, "scaleY": 0.4}, {"name": "zong", "parent": "root"}, {"name": "trunk", "parent": "zong", "x": -39.76, "y": 65.63}, {"name": "trunk2", "parent": "trunk", "length": 37.32, "rotation": 0.61, "x": 6.35, "y": -1.19}, {"name": "trunk3", "parent": "trunk2", "length": 32.71, "rotation": 4.96, "x": 37.32}, {"name": "trunk4", "parent": "trunk3", "length": 19.03, "rotation": 17.78, "x": 32.31, "y": 0.04}, {"name": "head", "parent": "trunk4", "length": 35.73, "rotation": -8.55, "x": 16.95, "y": 2.2}, {"name": "leg_R3", "parent": "trunk3", "length": 37.24, "rotation": -53.42, "x": -11.82, "y": 9.84}, {"name": "leg_R4", "parent": "leg_R3", "length": 22.43, "rotation": -61.35, "x": 37.24}, {"name": "leg_R6", "parent": "zong", "length": 21.42, "rotation": -75.19, "x": 8.32, "y": 25.89, "color": "ff3f00ff"}, {"name": "leg_R7", "parent": "leg_R6", "length": 14.64, "rotation": 67.72, "x": 21.19, "y": 0.06}, {"name": "leg_L2", "parent": "trunk3", "length": 34.04, "rotation": -47.74, "x": 19.77, "y": 7.72}, {"name": "leg_L3", "parent": "leg_L2", "length": 25.12, "rotation": -57.65, "x": 34.04}, {"name": "leg_L4", "parent": "zong", "length": 19.93, "rotation": -78.29, "x": 43.79, "y": 27.08, "color": "ff3f00ff"}, {"name": "leg_L5", "parent": "leg_L4", "length": 14.1, "rotation": 66.6, "x": 19.74, "y": 0.25}, {"name": "trunk5", "parent": "trunk", "length": 25.47, "rotation": -164.28, "x": -2.24, "y": -0.71}, {"name": "tails", "parent": "trunk5", "length": 18.18, "rotation": 28.21, "x": 19.07, "y": -3.39}, {"name": "tails2", "parent": "tails", "length": 15.19, "rotation": 10.63, "x": 18.18}, {"name": "tails3", "parent": "tails2", "length": 17.47, "rotation": 11.31, "x": 15.19}, {"name": "tails4", "parent": "tails3", "length": 12.53, "rotation": 9.82, "x": 17.47}, {"name": "leg_R", "parent": "trunk", "length": 31.75, "rotation": -66.12, "x": -19.18, "y": 12.94}, {"name": "leg_R2", "parent": "leg_R", "length": 28.88, "rotation": -47.69, "x": 31.75}, {"name": "leg_R8", "parent": "zong", "length": 22.69, "rotation": -74.8, "x": -57.75, "y": 24.31, "color": "ff3f00ff"}, {"name": "leg_R9", "parent": "leg_R8", "length": 10.31, "rotation": 55.94, "x": 22.69}, {"name": "leg_L", "parent": "trunk", "length": 35.52, "rotation": -57.59, "x": -2.52, "y": 13.41}, {"name": "leg_L6", "parent": "leg_L", "length": 24.49, "rotation": -53.49, "x": 35.52}, {"name": "leg_L7", "parent": "zong", "length": 22.27, "rotation": -79.53, "x": -32.28, "y": 27.16, "color": "ff3f00ff"}, {"name": "leg_L8", "parent": "leg_L7", "length": 7.45, "rotation": 62.83, "x": 22.22, "y": -0.23}, {"name": "hairiness_6", "parent": "trunk4", "length": 28.76, "rotation": 126.58, "x": 27.2, "y": 40.01}, {"name": "hairiness_7", "parent": "hairiness_6", "length": 21.86, "rotation": 16.2, "x": 28.76}, {"name": "hairiness_8", "parent": "hairiness_7", "length": 20.12, "rotation": 22.11, "x": 21.86}, {"name": "hairiness_9", "parent": "hairiness_8", "length": 18.24, "rotation": 4.2, "x": 20.12}, {"name": "hairiness_5", "parent": "trunk4", "length": 18.76, "rotation": 107.68, "x": 33.22, "y": 41.4}, {"name": "hairiness_10", "parent": "hairiness_5", "length": 13.31, "rotation": 12.77, "x": 18.76}, {"name": "hairiness_11", "parent": "hairiness_10", "length": 19.22, "rotation": 10.33, "x": 13.52, "y": 0.15}, {"name": "hairiness_13", "parent": "trunk3", "length": 28.42, "rotation": 151.65, "x": 18.24, "y": 39.93}, {"name": "hairiness_14", "parent": "hairiness_13", "length": 18.37, "rotation": 26.05, "x": 28.42}, {"name": "hairiness_15", "parent": "hairiness_14", "length": 21.56, "rotation": 17.37, "x": 18.37}, {"name": "hairiness_16", "parent": "trunk2", "length": 21.1, "rotation": 165.01, "x": 24.92, "y": 28.81}, {"name": "hairiness_17", "parent": "hairiness_16", "length": 18.81, "rotation": 27.26, "x": 21.1}, {"name": "ear_R", "parent": "head", "length": 14.41, "rotation": 92.84, "x": -12.44, "y": 33.65}, {"name": "ear_R2", "parent": "ear_R", "length": 14.34, "rotation": -0.79, "x": 14.41}, {"name": "ear_L", "parent": "head", "length": 13.68, "rotation": 66.45, "x": 9.85, "y": 32.5}, {"name": "ear_L2", "parent": "ear_L", "length": 11.83, "rotation": -1.38, "x": 13.68}, {"name": "hairiness_2", "parent": "head", "length": 15.03, "rotation": 169.96, "x": -34.41, "y": 24.62}, {"name": "hairiness_3", "parent": "hairiness_2", "length": 12.34, "rotation": 1.04, "x": 15.03}, {"name": "hairiness", "parent": "head", "length": 12.77, "rotation": -164.48, "x": -30.87, "y": 6.25}, {"name": "hairiness2", "parent": "hairiness", "length": 12.37, "rotation": 11.95, "x": 12.95, "y": -0.11}, {"name": "hairiness_4", "parent": "head", "length": 9.09, "rotation": -120.75, "x": -24.24, "y": -10.99}, {"name": "hairiness_12", "parent": "hairiness_4", "length": 8.55, "rotation": -2.49, "x": 9.09}, {"name": "hairiness_19", "parent": "head", "length": 12.17, "rotation": -121.7, "x": -4.19, "y": -20.59}, {"name": "hairiness_20", "parent": "trunk4", "length": 16.6, "rotation": -120.89, "x": 16.88, "y": -18.87}, {"name": "hairiness_21", "parent": "hairiness_20", "length": 15.69, "rotation": -9.5, "x": 16.6}, {"name": "hairiness_22", "parent": "hairiness_21", "length": 13.03, "rotation": -18.13, "x": 15.69}, {"name": "yinying", "parent": "root", "length": 201.83, "rotation": -0.17, "x": -100.87, "y": 0.24}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "tails", "bone": "tails", "attachment": "tails"}, {"name": "leg_L", "bone": "leg_L", "attachment": "leg_L"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "hairiness_7", "bone": "hairiness_13", "attachment": "hairiness_7"}, {"name": "hairiness_6", "bone": "hairiness_6", "attachment": "hairiness_6"}, {"name": "hairiness_5", "bone": "hairiness_5", "attachment": "hairiness_5"}, {"name": "trunk", "bone": "trunk", "attachment": "trunk"}, {"name": "hairiness_4", "bone": "hairiness_20", "attachment": "hairiness_4"}, {"name": "leg_R4", "bone": "leg_R6", "attachment": "leg_R4"}, {"name": "leg_R3", "bone": "leg_R3", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R8", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "ear_L", "bone": "ear_L", "attachment": "ear_L"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "hairiness_3", "bone": "hairiness_4", "attachment": "hairiness_3"}, {"name": "hairiness_2", "bone": "hairiness_2", "attachment": "hairiness_2"}, {"name": "hairiness", "bone": "hairiness", "attachment": "hairiness"}, {"name": "ear_R", "bone": "ear_R", "attachment": "ear_R"}, {"name": "biyan", "bone": "head"}], "ik": [{"name": "leg_L4", "order": 3, "bones": ["leg_L2", "leg_L3"], "target": "leg_L4", "bendPositive": false}, {"name": "leg_L7", "order": 1, "bones": ["leg_L", "leg_L6"], "target": "leg_L7", "bendPositive": false}, {"name": "leg_R6", "order": 2, "bones": ["leg_R3", "leg_R4"], "target": "leg_R6", "bendPositive": false}, {"name": "leg_R8", "bones": ["leg_R", "leg_R2"], "target": "leg_R8", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"biyan": {"biyan": {"x": 2.85, "y": 18.32, "rotation": -14.81, "width": 33, "height": 23}}, "ear_L": {"ear_L": {"type": "mesh", "uvs": [0.68152, 0.0296, 0.75899, 0.30282, 0.96618, 0.66675, 0.9658, 0.86507, 0.80157, 0.97051, 0.48325, 0.97038, 0.16707, 0.85904, 0.03294, 0.77538, 0.03447, 0.55469, 0.28068, 0.26678, 0.55341, 0.02915, 0.50125, 0.66066, 0.56332, 0.47885, 0.62125, 0.19157], "triangles": [2, 3, 4, 4, 5, 11, 5, 6, 11, 2, 4, 11, 11, 6, 8, 6, 7, 8, 11, 12, 2, 11, 8, 12, 12, 1, 2, 8, 9, 12, 12, 13, 1, 12, 9, 13, 13, 0, 1, 9, 10, 13, 13, 10, 0], "vertices": [1, 43, 15.8, -1.16, 1, 2, 43, 7.32, -4.96, 0.96128, 42, 20.87, -5.14, 0.03872, 2, 43, -3.45, -12.99, 0.07298, 42, 9.92, -12.9, 0.92702, 2, 43, -9.9, -14.13, 0.00272, 42, 3.45, -13.89, 0.99728, 1, 42, -0.72, -9.71, 1, 1, 42, -2.12, -0.58, 1, 2, 43, -13.77, 8.71, 0.0538, 42, 0.12, 9.04, 0.9462, 2, 43, -11.74, 13.02, 0.10959, 42, 2.26, 13.3, 0.89041, 2, 43, -4.56, 14.26, 0.2738, 42, 9.46, 14.37, 0.7262, 2, 43, 6.05, 8.9, 0.88228, 42, 19.94, 8.75, 0.11772, 1, 43, 15.16, 2.49, 1, 2, 43, -5.62, 0.32, 0.00374, 42, 8.06, 0.46, 0.99626, 1, 43, 0.6, -0.4, 1, 1, 43, 10.23, -0.38, 1], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20], "width": 29, "height": 33}}, "ear_R": {"ear_R": {"type": "mesh", "uvs": [0.48547, 0.06118, 0.69885, 0.18512, 0.89714, 0.35771, 0.97048, 0.46563, 0.9703, 0.69306, 0.71738, 0.87693, 0.60571, 0.91878, 0.45829, 0.97403, 0.09343, 0.97438, 0.02996, 0.77177, 0.02998, 0.65415, 0.07752, 0.5111, 0.14513, 0.30766, 0.21244, 0.10511, 0.30167, 0.02586, 0.36422, 0.02553, 0.35213, 0.16774, 0.4094, 0.36171, 0.47813, 0.47113, 0.54686, 0.65018, 0.59268, 0.78447], "triangles": [8, 9, 18, 18, 9, 10, 19, 7, 8, 10, 11, 18, 7, 20, 6, 7, 19, 20, 19, 8, 18, 6, 20, 5, 5, 20, 4, 20, 19, 4, 4, 19, 3, 19, 2, 3, 19, 18, 2, 17, 11, 12, 17, 18, 11, 18, 1, 2, 18, 17, 1, 12, 16, 17, 1, 16, 0, 1, 17, 16, 12, 13, 16, 13, 14, 16, 16, 15, 0, 16, 14, 15], "vertices": [1, 41, 14.57, -4.9, 1, 2, 41, 8.02, -10.28, 0.93523, 40, 22.28, -10.39, 0.06477, 2, 41, -0.15, -14.64, 0.48053, 40, 14.05, -14.63, 0.51947, 2, 41, -4.78, -15.76, 0.28514, 40, 9.41, -15.7, 0.71486, 2, 41, -13.05, -13.25, 0.06542, 40, 1.17, -13.07, 0.93458, 1, 40, -2.95, -3, 1, 1, 40, -3.35, 0.99, 1, 1, 40, -3.88, 6.27, 1, 2, 41, -14.89, 17.54, 0.0008, 40, -0.24, 17.75, 0.9992, 2, 41, -6.91, 17.31, 0.05499, 40, 7.73, 17.41, 0.94501, 2, 41, -2.64, 16.02, 0.14759, 40, 11.99, 16.05, 0.85241, 2, 41, 2.11, 12.94, 0.40219, 40, 16.7, 12.91, 0.59781, 2, 41, 8.86, 8.56, 0.89279, 40, 23.39, 8.44, 0.10721, 2, 41, 15.59, 4.2, 0.99999, 40, 30.05, 3.99, 1e-05, 1, 41, 17.61, 0.51, 1, 1, 41, 17.03, -1.47, 1, 1, 41, 11.97, 0.48, 1, 2, 41, 4.37, 0.81, 0.99318, 40, 18.79, 0.75, 0.00682, 2, 41, -0.27, -0.15, 0.11739, 40, 14.14, -0.15, 0.88261, 2, 41, -7.44, -0.35, 0.00026, 40, 6.96, -0.25, 0.99974, 1, 40, 1.64, -0.14, 1], "hull": 16, "edges": [0, 30, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 14, 16, 16, 18, 18, 20, 26, 28, 28, 30, 24, 26, 20, 22, 22, 24, 10, 12, 12, 14], "width": 33, "height": 38}}, "hairiness": {"hairiness": {"type": "mesh", "uvs": [0.81209, 0.21024, 0.96984, 0.36262, 0.96934, 0.52477, 0.90531, 0.66964, 0.81199, 0.88078, 0.71831, 0.97035, 0.53871, 0.97031, 0.39245, 0.97028, 0.23808, 0.97024, 0.11963, 0.97021, 0.03001, 0.93946, 0.03092, 0.69608, 0.06746, 0.55457, 0.10169, 0.42202, 0.14459, 0.25587, 0.24472, 0.13446, 0.33074, 0.03016, 0.4708, 0.02779, 0.15277, 0.83526, 0.38839, 0.58314, 0.25433, 0.74071, 0.51027, 0.48465, 0.78246, 0.35465, 0.63621, 0.40586], "triangles": [0, 23, 17, 22, 0, 1, 2, 22, 1, 3, 22, 2, 4, 21, 3, 15, 16, 17, 21, 14, 15, 19, 14, 21, 13, 14, 19, 12, 13, 19, 20, 12, 19, 11, 12, 20, 18, 11, 20, 10, 11, 18, 9, 10, 18, 8, 18, 20, 9, 18, 8, 6, 7, 19, 20, 19, 7, 8, 20, 7, 4, 6, 19, 22, 23, 0, 21, 17, 23, 3, 23, 22, 3, 21, 23, 19, 21, 4, 5, 6, 4, 21, 15, 17], "vertices": [1, 6, -31.27, 9.13, 1, 1, 6, -27.67, 2.98, 1, 1, 6, -29.05, -2.19, 1, 1, 6, -32.26, -6.29, 1, 1, 6, -36.92, -12.26, 1, 2, 46, 14.87, 17.26, 0.77428, 47, 5.47, 16.59, 0.22572, 2, 46, 19.83, 14.36, 0.52348, 47, 9.72, 12.72, 0.47652, 2, 46, 23.87, 11.99, 0.24074, 47, 13.18, 9.57, 0.75926, 2, 46, 28.13, 9.5, 0.05215, 47, 16.84, 6.25, 0.94785, 2, 46, 31.4, 7.58, 0.00653, 47, 19.64, 3.7, 0.99347, 2, 46, 33.36, 5.26, 3e-05, 47, 21.08, 1.02, 0.99997, 1, 47, 15.66, -4.9, 1, 1, 47, 11.65, -7.57, 1, 1, 47, 7.9, -10.07, 1, 2, 46, 18.81, -12.36, 0.00214, 47, 3.19, -13.2, 0.99786, 2, 46, 14.02, -14.2, 0.059, 47, -1.87, -14.01, 0.941, 1, 6, -44.64, 18.81, 1, 1, 6, -40.28, 17.74, 1, 2, 46, 28.24, 4.27, 0.00488, 47, 15.86, 1.12, 0.99512, 2, 46, 17.53, 0.9, 0.00165, 47, 4.69, 0.03, 0.99835, 2, 46, 23.86, 3.22, 0.00929, 47, 11.36, 1, 0.99071, 1, 46, 12.52, 0.06, 1, 1, 46, 2.84, 0.76, 1, 2, 46, 7.73, -0.15, 0.99473, 47, -5.12, 1.04, 0.00527], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 8, 10, 18, 20, 20, 22, 32, 34, 22, 24, 14, 16, 16, 18, 4, 6, 6, 8, 28, 30, 30, 32, 24, 26, 26, 28, 10, 12, 12, 14], "width": 32, "height": 33}}, "hairiness_2": {"hairiness_2": {"type": "mesh", "uvs": [0.76395, 0.02888, 0.89504, 0.02914, 0.96808, 0.23769, 0.96822, 0.42703, 0.96844, 0.73843, 0.83932, 0.96912, 0.72135, 0.97633, 0.57617, 0.9104, 0.45253, 0.85426, 0.21556, 0.69267, 0.06425, 0.58949, 0.00754, 0.49247, 0.17157, 0.34302, 0.3349, 0.19421, 0.49683, 0.09893, 0.61638, 0.02858, 0.76936, 0.44645, 0.62032, 0.46101, 0.39678, 0.49013, 0.2371, 0.49498], "triangles": [5, 6, 4, 16, 3, 4, 16, 2, 3, 2, 0, 1, 8, 9, 18, 9, 19, 18, 9, 10, 19, 10, 11, 19, 11, 12, 19, 19, 12, 18, 17, 18, 13, 18, 12, 13, 2, 16, 0, 4, 6, 7, 7, 17, 4, 17, 16, 4, 7, 8, 17, 8, 18, 17, 13, 14, 17, 17, 14, 16, 16, 15, 0, 15, 16, 14], "vertices": [2, 44, 4.15, -14.18, 0.97748, 45, -11.14, -13.98, 0.02252, 1, 6, -32.09, 38.26, 1, 1, 6, -31.71, 30.82, 1, 1, 6, -33.35, 24.6, 1, 1, 6, -36.05, 14.36, 1, 1, 6, -41.93, 7.8, 1, 2, 44, 8.14, 17.81, 0.89515, 45, -6.56, 17.93, 0.10485, 2, 44, 12.44, 15.2, 0.77845, 45, -2.31, 15.25, 0.22155, 2, 44, 16.1, 12.98, 0.586, 45, 1.31, 12.96, 0.414, 2, 44, 22.96, 6.9, 0.06428, 45, 8.06, 6.75, 0.93572, 1, 45, 12.37, 2.79, 1, 1, 45, 13.79, -0.67, 1, 2, 44, 23.33, -5.06, 0.01481, 45, 8.21, -5.21, 0.98519, 2, 44, 17.87, -9.68, 0.38309, 45, 2.66, -9.73, 0.61691, 2, 44, 12.6, -12.5, 0.77683, 45, -2.66, -12.45, 0.22317, 2, 44, 8.7, -14.57, 0.9137, 45, -6.59, -14.45, 0.0863, 1, 44, 5.16, -0.02, 1, 2, 44, 9.8, 0.09, 0.99982, 45, -5.22, 0.19, 0.00018, 2, 44, 16.79, 0.5, 0.02109, 45, 1.77, 0.47, 0.97891, 2, 44, 21.74, 0.25, 8e-05, 45, 6.71, 0.13, 0.99992], "hull": 16, "edges": [2, 4, 8, 10, 10, 12, 20, 22, 4, 6, 6, 8, 16, 18, 18, 20, 22, 24, 24, 26, 26, 28, 28, 30, 12, 14, 14, 16, 2, 0, 0, 30], "width": 31, "height": 34}}, "hairiness_3": {"hairiness_3": {"type": "mesh", "uvs": [0.1073, 0.0328, 0.25823, 0.03295, 0.30902, 0.033, 0.43013, 0.19965, 0.48123, 0.22094, 0.74284, 0.32994, 0.87103, 0.38335, 0.98971, 0.5312, 0.98208, 0.7253, 0.77952, 0.96366, 0.67092, 0.69373, 0.53469, 0.81971, 0.33974, 1, 0.32131, 1, 0.26562, 0.80372, 0.20056, 0.57444, 0.16722, 0.45696, 0.01677, 0.31764, 0.0177, 0.11574, 0.39106, 0.76145, 0.42096, 0.59405, 0.43092, 0.47005, 0.72653, 0.51345, 0.86603, 0.51345, 0.80956, 0.73665, 0.20174, 0.31505], "triangles": [23, 5, 6, 22, 4, 5, 22, 5, 23, 23, 6, 7, 10, 21, 22, 8, 23, 7, 24, 22, 23, 24, 23, 8, 10, 22, 24, 9, 10, 24, 9, 24, 8, 19, 14, 20, 20, 15, 25, 14, 15, 20, 11, 20, 10, 19, 20, 11, 12, 13, 14, 19, 12, 14, 12, 19, 11, 25, 0, 1, 0, 17, 18, 0, 25, 17, 16, 17, 25, 21, 3, 4, 21, 4, 22, 15, 16, 25, 1, 3, 21, 3, 1, 2, 20, 21, 10, 1, 21, 20, 1, 20, 25], "vertices": [2, 48, -0.89, -20.96, 0.97876, 49, -9.05, -21.37, 0.02124, 2, 48, -3.2, -12.83, 0.99208, 49, -11.72, -13.35, 0.00792, 2, 48, -3.98, -10.1, 0.9989, 49, -12.62, -10.66, 0.0011, 1, 48, -1.04, -2.2, 1, 2, 48, -1.21, 0.72, 0.98953, 50, 1.14, -21.4, 0.01047, 2, 48, -2.09, 15.71, 0.22701, 50, 0.01, -6.44, 0.77299, 1, 50, -0.54, 0.9, 1, 1, 50, 1.78, 8.55, 1, 1, 50, 7.47, 9.83, 1, 1, 50, 17.61, 1.05, 1, 3, 48, 9.51, 14.83, 0.24196, 49, -0.22, 14.84, 0.08719, 50, 11.63, -7.12, 0.67085, 3, 48, 15.24, 8.54, 0.23144, 49, 5.78, 8.8, 0.64803, 50, 17.46, -13.32, 0.12053, 1, 49, 14.36, 0.15, 1, 1, 49, 14.69, -0.83, 1, 2, 48, 18.92, -6.08, 0.0316, 49, 10.09, -5.65, 0.9684, 2, 48, 13.3, -11.48, 0.45159, 49, 4.71, -11.28, 0.54841, 2, 48, 10.43, -14.24, 0.73422, 49, 1.96, -14.17, 0.26578, 2, 48, 8.72, -23.49, 0.95398, 49, 0.66, -23.48, 0.04602, 2, 48, 2.88, -25.1, 0.97393, 49, -5.1, -25.35, 0.02607, 3, 48, 15.77, 0.32, 0.0018, 49, 6.66, 0.61, 0.99693, 50, 18.12, -21.52, 0.00127, 2, 49, 1.37, 0.61, 0.99673, 50, 12.83, -21.38, 0.00327, 2, 48, 6.75, 0.07, 0.99973, 50, 9.11, -21.93, 0.00027, 3, 48, 3.45, 16.34, 0.19175, 49, -6.34, 16.08, 0.0133, 50, 5.55, -5.71, 0.79494, 1, 50, 3.28, 1.77, 1, 1, 50, 10.6, 0.68, 1, 2, 48, 5.8, -13.55, 0.85955, 49, -2.69, -13.68, 0.14045], "hull": 19, "edges": [0, 36, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20, 24, 26, 32, 34, 34, 36, 30, 32, 26, 28, 28, 30, 20, 22, 22, 24, 6, 8, 8, 10, 10, 12, 0, 2, 2, 4], "width": 56, "height": 30}}, "hairiness_4": {"hairiness_4": {"type": "mesh", "uvs": [0.19669, 0.09942, 0.09146, 0.30582, 0.03054, 0.45462, 0.00839, 0.60342, 0, 0.72342, 0.03608, 0.86742, 0.01392, 1, 0.26315, 0.96342, 0.45146, 0.88662, 0.60654, 0.80982, 0.75608, 0.76662, 0.92223, 0.74742, 1, 0.57942, 0.98869, 0.35382, 0.97761, 0.21462, 0.89454, 0.07542, 0.76161, 0.04182, 0.55669, 0, 0.34069, 0.04662], "triangles": [8, 4, 3, 4, 7, 5, 3, 9, 8, 8, 7, 4, 6, 5, 7, 12, 10, 13, 12, 11, 10, 10, 9, 13, 13, 9, 1, 2, 1, 9, 3, 2, 9, 13, 16, 14, 14, 16, 15, 17, 13, 18, 16, 13, 17, 18, 13, 1, 1, 0, 18], "vertices": [3, 51, 7.74, -10.23, 0.87338, 52, -7.05, -11.56, 0.12662, 53, -18.01, -18.06, 0, 3, 51, 17.49, -13.08, 0.32295, 52, 3.04, -12.76, 0.64104, 53, -8.06, -16.06, 0.03601, 3, 51, 24.44, -14.56, 0.0596, 52, 10.14, -13.07, 0.71533, 53, -1.22, -14.15, 0.22507, 3, 51, 31.19, -14.54, 0.00202, 52, 16.79, -11.93, 0.39155, 53, 4.76, -11, 0.60644, 2, 52, 22.05, -10.66, 0.11436, 53, 9.36, -8.16, 0.88564, 2, 52, 27.83, -7.42, 0.00274, 53, 13.85, -3.27, 0.99726, 1, 53, 19.22, -0.54, 1, 2, 52, 29.37, 2.31, 0, 53, 12.28, 6.46, 1, 3, 51, 41.56, 4.26, 0.00154, 52, 23.91, 8.32, 0.13408, 53, 5.22, 10.47, 0.86437, 3, 51, 37.34, 9.81, 0.03842, 52, 18.84, 13.09, 0.53425, 53, -1.09, 13.42, 0.42733, 3, 51, 34.65, 15.33, 0.12698, 52, 15.27, 18.1, 0.72005, 53, -6.04, 17.07, 0.15297, 3, 51, 32.94, 21.64, 0.20162, 52, 12.54, 24.04, 0.73641, 53, -10.47, 21.87, 0.06197, 3, 51, 25.05, 23.66, 0.31792, 52, 4.43, 24.73, 0.66192, 53, -18.4, 19.99, 0.02016, 3, 51, 15.04, 21.89, 0.60422, 52, -5.15, 21.33, 0.39564, 53, -26.45, 13.79, 0.00014, 2, 51, 8.89, 20.64, 0.78013, 52, -11.01, 19.08, 0.21987, 2, 51, 3.1, 16.6, 0.90089, 52, -16.05, 14.15, 0.09911, 2, 51, 2.28, 11.27, 0.95637, 52, -15.98, 8.75, 0.04363, 2, 51, 1.47, 3.1, 0.99999, 52, -15.44, 0.56, 1e-05, 2, 51, 4.65, -4.98, 0.9861, 52, -10.96, -6.88, 0.0139], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 39, "height": 45}}, "hairiness_5": {"hairiness_5": {"type": "mesh", "uvs": [0, 0.02208, 0.29676, 0.0193, 0.48509, 0.08414, 0.63653, 0.18006, 0.78332, 0.27302, 0.87209, 0.42373, 0.98108, 0.60874, 0.98159, 0.78132, 0.91788, 0.92702, 0.81682, 1, 0.74782, 0.84371, 0.67312, 0.6745, 0.54323, 0.53535, 0.42925, 0.44421, 0.32013, 0.35697, 0.19049, 0.25332, 0.04184, 0.13447, 0, 0.05218], "triangles": [11, 12, 5, 11, 5, 6, 10, 11, 6, 7, 10, 6, 8, 10, 7, 9, 10, 8, 13, 2, 3, 12, 13, 3, 12, 3, 4, 12, 4, 5, 16, 17, 0, 1, 16, 0, 15, 16, 1, 14, 1, 2, 15, 1, 14, 13, 14, 2], "vertices": [1, 34, 27.53, -0.33, 1, 2, 34, 13.7, -7.18, 0.97653, 33, 28.29, -4.45, 0.02347, 2, 34, 3.59, -8.77, 0.44167, 33, 18.62, -7.83, 0.55833, 2, 33, 9.66, -8.92, 0.99473, 32, 30.15, -6.56, 0.00527, 2, 33, 0.98, -9.97, 0.69561, 32, 21.91, -9.51, 0.30439, 2, 33, -6.84, -7.1, 0.08692, 32, 13.65, -8.44, 0.91308, 1, 32, 3.51, -7.13, 1, 1, 32, -2.49, -1.94, 1, 1, 32, -5.37, 4.96, 1, 1, 32, -4.46, 11.13, 1, 1, 32, 3.32, 9.12, 1, 3, 34, -17.06, 11.4, 3e-05, 33, -5.31, 8.32, 0.04962, 32, 11.74, 6.94, 0.95035, 3, 34, -8.19, 8.59, 0.10303, 33, 3.93, 7.14, 0.72499, 32, 21.01, 7.83, 0.17198, 3, 34, -1.03, 7.4, 0.72573, 33, 11.18, 7.25, 0.27415, 32, 28.06, 9.55, 0.00012, 1, 34, 5.83, 6.27, 1, 1, 34, 13.98, 4.92, 1, 1, 34, 23.32, 3.37, 1, 1, 34, 26.93, 0.92, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 22, 24, 32, 34, 30, 32, 28, 30, 4, 6, 6, 8, 24, 26, 26, 28, 8, 10, 10, 12, 18, 20, 20, 22], "width": 52, "height": 46}}, "hairiness_6": {"hairiness_6": {"type": "mesh", "uvs": [0.66998, 0.04608, 0.78835, 0.10629, 0.9234, 0.31979, 0.98962, 0.58325, 0.98995, 0.74846, 0.97656, 0.84446, 0.95908, 0.96982, 0.91186, 0.98239, 0.81962, 0.88457, 0.66724, 0.72296, 0.57327, 0.6233, 0.50743, 0.57633, 0.43642, 0.52568, 0.35804, 0.54018, 0.28572, 0.55357, 0.20076, 0.56929, 0.12111, 0.58403, 0.00965, 0.57526, 0.01024, 0.52496, 0.08523, 0.38245, 0.14976, 0.29103, 0.20287, 0.21577, 0.28051, 0.16279, 0.36282, 0.10662, 0.46105, 0.0642, 0.52818, 0.0352, 0.5933, 0.00708, 0.13016, 0.44299, 0.24969, 0.36366, 0.3622, 0.33026, 0.45712, 0.28016, 0.54853, 0.33444, 0.6417, 0.41376, 0.73487, 0.48056, 0.82101, 0.60999, 0.89132, 0.72689], "triangles": [27, 19, 20, 20, 28, 27, 28, 20, 21, 15, 28, 14, 15, 27, 28, 16, 19, 27, 16, 27, 15, 18, 19, 16, 17, 18, 16, 30, 23, 24, 29, 22, 23, 29, 23, 30, 30, 24, 25, 28, 21, 22, 28, 22, 29, 12, 29, 30, 11, 12, 30, 29, 14, 28, 13, 29, 12, 13, 14, 29, 31, 25, 26, 31, 30, 25, 32, 31, 0, 31, 11, 30, 10, 31, 32, 11, 31, 10, 10, 32, 9, 34, 33, 2, 35, 34, 2, 9, 32, 33, 5, 35, 4, 34, 9, 33, 8, 34, 35, 8, 9, 34, 5, 7, 35, 7, 8, 35, 5, 6, 7, 0, 31, 26, 33, 32, 0, 1, 33, 0, 33, 1, 2, 3, 35, 2, 35, 3, 4], "vertices": [1, 34, 9.13, -0.55, 1, 1, 33, 12.11, -3.45, 1, 2, 32, 16.46, -4.75, 0.93448, 33, -3.29, -4.13, 0.06552, 1, 32, 4.38, -2.58, 1, 3, 32, -0.62, 1.73, 0.56269, 33, -18.52, 5.97, 0, 28, -2.5, -3.83, 0.43731, 2, 32, -2.68, 5.21, 0.04673, 28, -3.32, 0.13, 0.95327, 1, 28, -4.4, 5.31, 1, 1, 28, -0.77, 7.99, 1, 1, 28, 8.78, 8.99, 1, 3, 33, 6.83, 23.25, 0, 28, 24.55, 10.65, 0.69047, 29, -1.08, 11.4, 0.30953, 4, 33, 16.39, 25.31, 0, 28, 34.27, 11.67, 0.08698, 29, 8.55, 9.67, 0.90668, 30, -8.69, 13.97, 0.00634, 4, 33, 22.54, 27.49, 0, 28, 40.62, 13.18, 0.00166, 29, 15.07, 9.35, 0.86327, 30, -2.77, 11.22, 0.13507, 3, 33, 29.19, 29.83, 0, 29, 22.1, 9, 0.29902, 30, 3.61, 8.24, 0.70098, 4, 33, 34.85, 34.7, 0, 29, 29.19, 11.35, 0.00377, 30, 11.07, 7.75, 0.99481, 31, -8.46, 8.4, 0.00142, 3, 33, 40.08, 39.19, 0, 30, 17.94, 7.3, 0.74048, 31, -1.64, 7.44, 0.25952, 3, 33, 46.22, 44.46, 0, 30, 26.02, 6.76, 0.02925, 31, 6.38, 6.31, 0.97075, 2, 33, 51.98, 49.41, 0, 31, 13.9, 5.26, 1, 2, 33, 60.73, 55.38, 0, 31, 24.16, 2.64, 1, 2, 33, 61.88, 53.72, 0, 31, 23.67, 0.68, 1, 1, 31, 15.49, -3.35, 1, 2, 30, 29.22, -4.95, 0.00457, 31, 8.71, -5.6, 0.99543, 2, 30, 23.8, -7.2, 0.22242, 31, 3.14, -7.45, 0.77758, 2, 30, 16.19, -8.24, 0.88611, 31, -4.52, -7.93, 0.11389, 3, 34, 34.33, 14.36, 0.002, 29, 32.91, -5.6, 0.00093, 30, 8.13, -9.35, 0.99707, 3, 34, 26.68, 8.76, 0.09428, 29, 24.26, -9.48, 0.32898, 30, -1.35, -9.69, 0.57674, 3, 34, 21.44, 4.93, 0.35025, 29, 18.34, -12.14, 0.51675, 30, -7.82, -9.92, 0.133, 3, 34, 16.37, 1.22, 0.73555, 29, 12.61, -14.71, 0.25213, 30, -14.11, -10.15, 0.01232, 1, 31, 11.84, -0.06, 1, 2, 30, 20.24, -0.71, 0.43583, 31, 0.07, -0.72, 0.56417, 1, 30, 9.47, -0.5, 1, 3, 34, 23.24, 16.7, 0.00489, 29, 22.55, -1.01, 0.17136, 30, 0.26, -1.19, 0.82375, 2, 34, 14.48, 14.86, 0.01674, 29, 13.6, -0.98, 0.98326, 1, 29, 4.24, -0.02, 1, 4, 32, 23.37, 12.98, 3e-05, 33, 7.37, 11.64, 0.02207, 34, -4, 12.4, 0.01353, 28, 23.84, -0.96, 0.96437, 3, 32, 14.09, 10.2, 0.01583, 33, -2.29, 10.98, 0.00429, 28, 14.17, -0.58, 0.97988, 1, 28, 6.04, 0.12, 1], "hull": 27, "edges": [2, 4, 4, 6, 6, 8, 12, 14, 32, 34, 34, 36, 36, 38, 18, 20, 14, 16, 16, 18, 46, 48, 8, 10, 10, 12, 2, 0, 0, 52, 20, 22, 22, 24, 48, 50, 50, 52, 24, 26, 26, 28, 42, 44, 44, 46, 28, 30, 30, 32, 38, 40, 40, 42], "width": 95, "height": 40}}, "hairiness_7": {"hairiness_7": {"type": "mesh", "uvs": [0.79319, 0.04662, 0.88324, 0.08308, 0.92843, 0.15378, 0.9914, 0.28334, 0.99091, 0.41074, 0.93859, 0.55162, 0.91125, 0.58537, 0.84524, 0.66685, 0.76527, 0.76556, 0.71491, 0.82773, 0.50427, 0.9524, 0.41175, 0.98169, 0.34142, 0.98159, 0.22805, 0.98143, 0.14945, 0.98132, 0.08244, 0.95543, 0.05364, 0.93858, 0.02166, 0.80691, 0.00903, 0.75491, 0.009, 0.69643, 0.08706, 0.49943, 0.15094, 0.36836, 0.20492, 0.25759, 0.24511, 0.20711, 0.29092, 0.14956, 0.34561, 0.08085, 0.43281, 0.05806, 0.50726, 0.03859, 0.58352, 0.01865, 0.65091, 0.01863, 0.72402, 0.01861, 0.45021, 0.13939, 0.5452, 0.14569, 0.66164, 0.19295, 0.74591, 0.24967, 0.83783, 0.38831, 0.90218, 0.52695, 0.63406, 0.62148, 0.55592, 0.56161, 0.44102, 0.5238, 0.33837, 0.54271, 0.20507, 0.57737, 0.13766, 0.62778, 0.07331, 0.71601, 0.27861, 0.56791, 0.69228, 0.6908, 0.37207, 0.13308], "triangles": [43, 19, 20, 18, 19, 43, 17, 18, 43, 16, 17, 43, 14, 15, 43, 16, 43, 15, 13, 14, 42, 14, 43, 42, 13, 41, 44, 13, 42, 41, 13, 44, 12, 10, 39, 38, 11, 12, 40, 39, 11, 40, 44, 40, 12, 10, 11, 39, 23, 24, 40, 44, 22, 23, 22, 41, 21, 23, 40, 44, 44, 41, 22, 42, 20, 21, 41, 42, 21, 42, 43, 20, 40, 46, 39, 39, 32, 38, 37, 33, 34, 45, 37, 34, 37, 38, 33, 7, 45, 35, 8, 45, 7, 9, 45, 8, 10, 38, 37, 9, 10, 37, 9, 37, 45, 46, 40, 24, 46, 24, 25, 46, 25, 26, 31, 26, 27, 46, 26, 31, 31, 27, 32, 39, 46, 31, 39, 31, 32, 32, 27, 28, 33, 29, 30, 33, 30, 34, 33, 38, 32, 32, 28, 33, 33, 28, 29, 34, 30, 0, 1, 34, 0, 35, 1, 2, 35, 2, 3, 35, 34, 1, 4, 35, 3, 36, 35, 4, 5, 36, 4, 6, 36, 5, 7, 35, 36, 7, 36, 6, 35, 45, 34], "vertices": [2, 28, 26.17, -6.26, 0.95529, 29, -4.24, -5.29, 0.04471, 1, 28, 16.7, -9.51, 1, 1, 28, 10.56, -8.73, 1, 1, 28, 1.18, -6.23, 1, 1, 28, -2.15, -0.36, 1, 2, 28, -0.96, 8.96, 0.84836, 35, -21.14, -9.89, 0.15164, 2, 28, 0.72, 12, 0.73949, 35, -19.09, -7.09, 0.26051, 3, 28, 4.79, 19.35, 0.35769, 35, -14.13, -0.32, 0.64212, 38, -36.98, -17.39, 0.00018, 3, 28, 9.71, 28.24, 0.03317, 35, -8.12, 7.88, 0.92793, 38, -29.83, -10.16, 0.0389, 3, 28, 12.81, 33.84, 0.00019, 35, -4.33, 13.04, 0.87099, 38, -25.33, -5.61, 0.12882, 2, 35, 14.28, 28.02, 0.03803, 38, -4.73, 6.5, 0.96197, 2, 38, 4.65, 10.51, 0.97631, 39, -9.81, 16.87, 0.02369, 2, 38, 12.08, 12.41, 0.78872, 39, -2.34, 15.16, 0.21128, 2, 38, 24.05, 15.47, 0.1352, 39, 9.71, 12.4, 0.8648, 2, 38, 32.35, 17.59, 0.00385, 39, 18.06, 10.48, 0.99615, 1, 39, 24.87, 7.52, 1, 2, 37, 37.17, 17.56, 0.00286, 39, 27.73, 5.95, 0.99714, 2, 37, 37.97, 9.8, 0.10729, 39, 29.58, -1.63, 0.89271, 2, 37, 38.29, 6.73, 0.17583, 39, 30.31, -4.63, 0.82417, 2, 37, 37.2, 3.83, 0.23871, 39, 29.62, -7.65, 0.76129, 2, 37, 25.56, -2.94, 0.9321, 39, 19, -15.93, 0.0679, 2, 31, 26.23, 9.87, 0.01206, 37, 16.59, -6.99, 0.98794, 3, 31, 19.22, 5.41, 0.12637, 36, 30.08, -7.24, 0.0007, 37, 9.02, -10.41, 0.87294, 3, 31, 14.37, 3.74, 0.29408, 36, 25.55, -9.66, 0.03993, 37, 3.97, -11.37, 0.66599, 3, 31, 8.84, 1.84, 0.62651, 36, 20.4, -12.42, 0.10311, 37, -1.77, -12.46, 0.27038, 1, 31, 2.23, -0.44, 1, 1, 30, 12.8, -0.11, 1, 4, 29, 26.12, 1.77, 0.20088, 30, 4.62, 0.03, 0.78842, 35, 32.73, -16.76, 0.00875, 36, -3.48, -16.95, 0.00195, 1, 29, 18.31, -1.25, 1, 1, 29, 11.17, -3.01, 1, 2, 28, 33.44, -3.77, 0.08368, 29, 3.44, -4.92, 0.91632, 5, 29, 30.88, 8.45, 0.00695, 30, 11.54, 4.43, 0.81103, 31, -8.24, 5.05, 0.00172, 35, 36.4, -9.42, 0.02612, 36, 3.03, -11.97, 0.15419, 4, 29, 20.75, 6.29, 0.54026, 30, 1.34, 6.24, 0.24094, 35, 26.72, -13.12, 0.19587, 36, -7.29, -11.05, 0.02293, 3, 28, 34.69, 7.64, 0.05112, 29, 7.83, 5.68, 0.74763, 35, 14.05, -15.73, 0.20125, 3, 28, 25.24, 5.64, 0.67333, 29, -1.81, 6.39, 0.15899, 35, 4.42, -16.52, 0.16769, 2, 28, 12.88, 6.97, 0.79504, 35, -7.67, -13.62, 0.20496, 2, 28, 3.13, 9.82, 0.77824, 35, -16.98, -9.56, 0.22176, 2, 35, 8.03, 6.37, 0.88095, 38, -14.08, -14.01, 0.11905, 3, 35, 17.11, 6.75, 0.79122, 36, -7.2, 11.03, 0.01997, 38, -5.04, -14.96, 0.18881, 4, 35, 29.43, 9.75, 0.19076, 36, 5.19, 8.31, 0.50501, 37, -10.09, 11.87, 1e-05, 38, 7.59, -13.79, 0.30422, 5, 35, 39.36, 15.01, 0.00216, 36, 16.42, 8.68, 0.36147, 37, 0.73, 8.86, 0.20381, 38, 18.18, -10.04, 0.39781, 39, -7.2, -7.59, 0.03475, 3, 37, 14.97, 5.46, 0.65137, 38, 31.79, -4.66, 0.00413, 39, 7.38, -9.04, 0.3445, 2, 37, 22.79, 5.37, 0.5718, 39, 15.14, -8.07, 0.4282, 2, 37, 31, 7.27, 0.23683, 39, 23.02, -5.08, 0.76317, 4, 36, 23, 9.64, 0.04967, 37, 7.3, 7.82, 0.46545, 38, 24.15, -7.13, 0.22008, 39, -0.55, -7.74, 0.2648, 2, 35, 0.75, 7.3, 0.91163, 38, -21.14, -12.02, 0.08837, 4, 30, 19.92, 2.88, 0.46135, 31, 0.01, 2.89, 0.4345, 36, 11.51, -12.79, 0.10295, 37, -10.36, -10.16, 0.0012], "hull": 31, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 18, 20, 20, 22, 28, 30, 30, 32, 36, 38, 38, 40, 48, 50, 10, 12, 16, 18, 32, 34, 34, 36, 26, 28, 22, 24, 24, 26, 12, 14, 14, 16, 2, 0, 0, 60, 56, 58, 58, 60, 54, 56, 50, 52, 52, 54, 44, 46, 46, 48, 40, 42, 42, 44], "width": 109, "height": 53}}, "head": {"head": {"x": 11.84, "y": 3.88, "rotation": -14.81, "width": 130, "height": 80}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.6197, 0.0248, 0.89673, 0.12764, 0.96592, 0.27981, 0.96687, 0.43089, 0.9675, 0.52922, 0.93849, 0.70662, 0.89462, 0.97499, 0.64182, 0.975, 0.25668, 0.97501, 0.14001, 0.85437, 0.07376, 0.657, 0.03333, 0.53656, 0.03378, 0.40438, 0.03403, 0.3283, 0.078, 0.17157, 0.20511, 0.07692, 0.41372, 0.02536, 0.38665, 0.13871, 0.3152, 0.26394, 0.3152, 0.41953, 0.42237, 0.57892, 0.49382, 0.73071, 0.58058, 0.84456], "triangles": [5, 21, 4, 22, 21, 5, 21, 9, 10, 6, 22, 5, 7, 22, 6, 8, 9, 21, 8, 21, 22, 8, 22, 7, 18, 15, 17, 14, 15, 18, 13, 14, 18, 2, 19, 18, 13, 18, 19, 12, 13, 19, 11, 12, 19, 3, 19, 2, 20, 3, 4, 3, 20, 19, 11, 19, 20, 10, 11, 20, 4, 21, 20, 21, 10, 20, 17, 15, 16, 17, 16, 0, 1, 17, 0, 18, 17, 2, 2, 17, 1], "vertices": [3, 25, 14.67, 5.03, 0.99008, 26, -4.75, 9.49, 0.00981, 27, -3.67, 28.44, 0.00011, 3, 25, 15.53, 13.97, 0.80431, 26, 0.65, 16.66, 0.17862, 27, 5.18, 26.9, 0.01707, 3, 25, 20.34, 17.98, 0.59311, 26, 6.85, 17.55, 0.34284, 27, 8.81, 21.8, 0.06404, 3, 25, 25.83, 20.12, 0.33746, 26, 12.65, 16.51, 0.47401, 27, 10.53, 16.16, 0.18854, 3, 25, 29.4, 21.52, 0.19701, 26, 16.43, 15.83, 0.46315, 27, 11.64, 12.49, 0.33984, 3, 25, 36.16, 23.22, 0.04382, 26, 23.08, 13.75, 0.20243, 27, 12.83, 5.62, 0.75374, 1, 27, 14.62, -4.77, 1, 1, 27, 7.59, -6.87, 1, 1, 27, -3.1, -10.08, 1, 2, 26, 24.54, -10.07, 0.00284, 27, -7.7, -6.55, 0.99716, 2, 26, 16.62, -10.56, 0.36479, 27, -11.75, 0.27, 0.63521, 2, 26, 11.79, -10.86, 0.69587, 27, -14.22, 4.43, 0.30413, 2, 26, 6.72, -9.91, 0.90397, 27, -15.69, 9.37, 0.09603, 2, 26, 3.8, -9.37, 0.96021, 27, -16.54, 12.22, 0.03979, 2, 26, -1.98, -7, 0.9985, 27, -17.07, 18.44, 0.0015, 2, 25, 20.89, -5.45, 0.22462, 26, -4.94, -2.71, 0.77538, 2, 25, 16.84, -0.53, 0.99823, 26, -5.81, 3.61, 0.00177, 1, 25, 21.25, 0.33, 1, 2, 26, 2.82, -0.89, 0.99945, 27, -9.45, 16.97, 0.00055, 2, 26, 8.78, -1.99, 0.98082, 27, -7.7, 11.15, 0.01918, 2, 26, 15.46, -0.07, 0.99966, 27, -2.94, 6.09, 0.00034, 3, 25, 41.67, 11.53, 0.00025, 26, 21.66, 0.89, 0.65517, 27, 0.75, 1.02, 0.34458, 1, 27, 4.43, -2.51, 1], "hull": 17, "edges": [0, 32, 0, 2, 2, 4, 16, 18, 26, 28, 28, 30, 30, 32, 8, 10, 10, 12, 18, 20, 20, 22, 12, 14, 14, 16, 4, 6, 6, 8, 22, 24, 24, 26], "width": 29, "height": 39}}, "leg_L2": {"leg_L2": {"type": "mesh", "uvs": [0.79302, 0.03844, 0.90632, 0.12685, 0.94334, 0.24351, 0.95335, 0.40765, 0.95959, 0.53659, 0.96606, 0.65332, 0.97228, 0.76557, 0.97942, 0.89418, 0.95339, 0.95485, 0.76836, 0.97845, 0.63533, 0.97834, 0.40924, 0.97815, 0.2684, 0.93107, 0.21536, 0.84735, 0.16699, 0.77099, 0.12527, 0.6723, 0.09532, 0.60145, 0.06245, 0.52367, 0.02773, 0.44153, 0.02813, 0.3085, 0.07143, 0.21077, 0.20024, 0.11042, 0.42891, 0.02192, 0.74354, 0.02194, 0.37488, 0.09406, 0.36276, 0.20006, 0.41122, 0.31312, 0.39305, 0.44739, 0.39608, 0.56752, 0.47179, 0.72534, 0.57476, 0.82663, 0.42636, 0.64054], "triangles": [29, 31, 4, 5, 29, 4, 30, 29, 5, 6, 30, 5, 9, 30, 6, 30, 9, 10, 30, 11, 29, 9, 6, 7, 11, 30, 10, 8, 9, 7, 19, 27, 18, 17, 18, 27, 26, 27, 19, 3, 28, 27, 17, 27, 28, 26, 0, 1, 26, 1, 2, 16, 17, 28, 27, 26, 3, 3, 26, 2, 4, 28, 3, 4, 31, 28, 15, 16, 28, 15, 28, 31, 14, 15, 31, 14, 31, 29, 13, 14, 29, 12, 13, 29, 11, 12, 29, 24, 21, 22, 25, 21, 24, 24, 23, 25, 23, 26, 25, 20, 21, 25, 26, 20, 25, 26, 19, 20, 22, 23, 24, 26, 23, 0], "vertices": [3, 12, 10.58, 13.91, 0.6276, 13, -8.19, 18.22, 0.3036, 14, 5.4, 32.77, 0.0688, 3, 12, 13.83, 18.49, 0.48627, 13, -3.49, 21.3, 0.39559, 14, 10.09, 29.68, 0.11813, 3, 12, 18.78, 20.66, 0.33507, 13, 1.92, 21.5, 0.46703, 14, 12.42, 24.8, 0.19789, 3, 12, 26, 22.27, 0.12691, 13, 9.22, 20.34, 0.45744, 14, 14.26, 17.64, 0.41564, 3, 12, 31.68, 23.47, 0.03481, 13, 14.95, 19.38, 0.29379, 14, 15.65, 12, 0.67141, 3, 12, 36.81, 24.59, 0.00497, 13, 20.14, 18.54, 0.10776, 14, 16.94, 6.9, 0.88727, 3, 12, 41.75, 25.67, 3e-05, 13, 25.13, 17.72, 0.01123, 14, 18.18, 2, 0.98874, 1, 14, 19.59, -3.62, 1, 2, 13, 33.33, 15.35, 0.00046, 14, 19.25, -6.48, 0.99954, 2, 13, 33.06, 8.79, 0.05582, 14, 13.13, -8.83, 0.94418, 2, 13, 32.11, 4.23, 0.23446, 14, 8.57, -9.77, 0.76554, 2, 13, 30.49, -3.51, 0.78092, 14, 0.82, -11.36, 0.21908, 2, 13, 27.42, -7.91, 0.97085, 14, -4.44, -10.29, 0.02915, 2, 12, 49.9, 0.19, 0.00019, 13, 23.35, -8.96, 0.99981, 2, 12, 46.8, -2.06, 0.0055, 13, 19.64, -9.92, 0.9945, 2, 12, 42.67, -4.26, 0.04028, 13, 15, -10.45, 0.95972, 2, 12, 39.71, -5.84, 0.10827, 13, 11.66, -10.83, 0.89173, 2, 12, 36.46, -7.57, 0.24908, 13, 8, -11.25, 0.75092, 2, 12, 33.02, -9.39, 0.45215, 13, 4.14, -11.69, 0.54785, 2, 12, 27.12, -10.4, 0.79692, 13, -1.72, -10.46, 0.20308, 2, 12, 22.53, -9.66, 0.9603, 13, -5.72, -8.08, 0.0397, 1, 12, 17.31, -5.99, 1, 3, 12, 12.02, 1.22, 0.98461, 13, -11.5, 5.89, 0.01363, 14, -7.23, 30.91, 0.00176, 3, 12, 10.14, 12.07, 0.6658, 13, -9.27, 16.68, 0.27586, 14, 3.56, 33.15, 0.05833, 1, 12, 15.54, -0.09, 1, 3, 12, 20.32, 0.31, 0.99125, 13, -4.12, 2, 0.00845, 14, -7.87, 22.6, 0.0003, 3, 12, 25.04, 2.85, 0.16243, 13, 1.2, 2.63, 0.82806, 14, -5.18, 17.96, 0.00951, 2, 13, 6.99, 0.78, 0.99565, 14, -4.58, 11.91, 0.00435, 2, 12, 36.41, 4.28, 0.00037, 13, 12.3, -0.21, 0.99963, 1, 14, 0.66, 0.22, 1, 2, 13, 24.99, 3.54, 0.21442, 14, 5.11, -3.51, 0.78558, 2, 13, 15.74, 0.16, 0.99555, 14, -1.67, 3.64, 0.00445], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 14, 16, 16, 18, 22, 24, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 18, 20, 20, 22, 12, 14, 10, 12, 4, 6, 6, 8, 8, 10, 32, 34, 34, 36, 24, 26, 26, 28, 28, 30, 30, 32], "width": 35, "height": 45}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.65228, 0.11069, 0.7846, 0.18827, 0.92229, 0.33117, 0.98091, 0.46365, 0.98096, 0.56993, 0.98108, 0.78812, 0.84115, 0.98406, 0.47164, 0.98481, 0.33623, 0.92904, 0.19575, 0.87118, 0.07794, 0.77414, 0.0415, 0.63894, 0.01916, 0.5561, 0.01889, 0.30345, 0.0408, 0.1997, 0.06317, 0.09381, 0.15366, 0.01601, 0.33641, 0.0158, 0.49012, 0.01562, 0.41686, 0.80335, 0.50157, 0.66051, 0.56933, 0.54903, 0.56933, 0.42709, 0.47616, 0.2529, 0.41263, 0.124], "triangles": [8, 19, 7, 19, 10, 11, 9, 10, 19, 8, 9, 19, 3, 21, 22, 21, 3, 4, 11, 12, 20, 21, 20, 12, 20, 19, 11, 21, 5, 20, 5, 21, 4, 6, 20, 5, 19, 20, 6, 7, 19, 6, 24, 17, 18, 24, 18, 0, 23, 24, 0, 1, 22, 23, 1, 23, 0, 22, 1, 2, 3, 22, 2, 23, 12, 13, 16, 17, 24, 15, 16, 24, 14, 15, 24, 13, 14, 23, 23, 14, 24, 22, 12, 23, 21, 12, 22], "vertices": [1, 20, 12.05, 12.62, 1, 2, 20, 19.18, 16.84, 0.99164, 21, -20.92, 2.04, 0.00836, 3, 20, 30.12, 19.68, 0.79422, 21, -15.65, 12.04, 0.2054, 22, -25.87, 37.08, 0.00039, 3, 20, 38.84, 19.09, 0.48814, 21, -9.35, 18.09, 0.50519, 22, -17.16, 37.81, 0.00667, 3, 20, 44.87, 16.42, 0.25175, 21, -3.32, 20.75, 0.72618, 22, -10.8, 36.08, 0.02207, 3, 20, 57.24, 10.95, 0.01566, 21, 9.05, 26.22, 0.88878, 22, 2.26, 32.54, 0.09556, 2, 21, 23.05, 24.6, 0.7915, 22, 12.11, 22.47, 0.2085, 3, 20, 57.88, -17.74, 0, 21, 30.7, 7.38, 0.31158, 22, 7.21, 4.27, 0.68842, 1, 22, 2.06, -1.49, 1, 3, 20, 45.74, -27.76, 0.02494, 21, 29.94, -8.34, 0.20442, 22, -3.28, -7.46, 0.77065, 3, 20, 37.81, -30.81, 0.10705, 21, 26.86, -16.26, 0.10141, 22, -10.66, -11.68, 0.79155, 3, 20, 29.39, -29.12, 0.27871, 21, 19.95, -21.35, 0.14961, 22, -19.23, -11.28, 0.57168, 3, 20, 24.23, -28.08, 0.42558, 21, 15.71, -24.47, 0.14258, 22, -24.49, -11.03, 0.43184, 3, 20, 9.9, -21.76, 0.84438, 21, 1.38, -30.8, 0.02962, 22, -39.61, -6.93, 0.126, 3, 20, 4.47, -18.13, 0.93415, 21, -4.95, -32.38, 0.00552, 22, -45.52, -4.17, 0.06033, 3, 20, -1.07, -14.43, 0.97551, 21, -11.42, -33.99, 3e-05, 22, -51.56, -1.35, 0.02446, 2, 20, -3.61, -8.26, 0.99046, 22, -55, 4.37, 0.00954, 2, 20, 0.15, 0.27, 1, 22, -52.57, 13.37, 0, 1, 20, 3.31, 7.44, 1, 1, 21, 21.54, 0.28, 1, 1, 21, 11.69, 0.66, 1, 1, 21, 3.97, 1.03, 1, 1, 20, 28.27, 0.81, 1, 1, 20, 16.47, 0.84, 1, 1, 20, 7.85, 1.11, 1], "hull": 19, "edges": [2, 4, 4, 6, 10, 12, 12, 14, 18, 20, 24, 26, 30, 32, 14, 16, 16, 18, 32, 34, 34, 36, 2, 0, 0, 36, 26, 28, 28, 30, 6, 8, 8, 10, 20, 22, 22, 24], "width": 51, "height": 62}}, "leg_R2": {"leg_R2": {"type": "mesh", "uvs": [0.51925, 0.01922, 0.58942, 0.04279, 0.76901, 0.10312, 0.97771, 0.24659, 0.97775, 0.37336, 0.92083, 0.47126, 0.86525, 0.56687, 0.86397, 0.6426, 0.882, 0.7698, 0.9051, 0.93278, 0.79601, 0.98077, 0.48167, 0.98074, 0.33131, 0.94003, 0.24327, 0.82064, 0.15883, 0.70612, 0.01515, 0.45942, 0.05339, 0.28244, 0.08336, 0.14375, 0.181, 0.05804, 0.29526, 0.01929, 0.49173, 0.18447, 0.39845, 0.32423, 0.34936, 0.45976, 0.42791, 0.63764, 0.49173, 0.75199, 0.60954, 0.86635], "triangles": [25, 24, 7, 8, 25, 7, 10, 25, 8, 25, 11, 24, 9, 10, 8, 11, 25, 10, 23, 22, 6, 7, 23, 6, 14, 22, 23, 24, 23, 7, 13, 14, 23, 13, 23, 24, 12, 13, 24, 11, 12, 24, 20, 19, 0, 20, 0, 1, 20, 18, 19, 21, 18, 20, 17, 18, 21, 16, 17, 21, 22, 16, 21, 15, 16, 22, 4, 5, 2, 4, 2, 3, 5, 20, 2, 21, 6, 22, 20, 1, 2, 5, 21, 20, 5, 6, 21, 14, 15, 22], "vertices": [2, 21, 7.44, -2.02, 0.99418, 22, -16.78, 11.61, 0.00582, 2, 21, 7.29, 1.29, 0.99981, 23, -9.17, 39.06, 0.00019, 3, 21, 6.92, 9.76, 0.97274, 22, -9.77, 21.1, 0.01616, 23, -0.7, 38.71, 0.0111, 3, 21, 9.9, 21.11, 0.85218, 22, -0.3, 28.04, 0.09802, 23, 10.36, 34.75, 0.0498, 3, 21, 15.82, 23.73, 0.75977, 22, 5.94, 26.34, 0.1577, 23, 12.45, 28.63, 0.08253, 3, 21, 21.4, 23.45, 0.60333, 22, 10.1, 22.62, 0.24756, 23, 11.69, 23.1, 0.14911, 3, 21, 26.84, 23.18, 0.35292, 22, 14.17, 18.98, 0.33808, 23, 10.95, 17.69, 0.309, 3, 21, 30.4, 24.69, 0.18164, 22, 17.88, 17.91, 0.30785, 23, 12.15, 14.02, 0.51051, 3, 21, 36.01, 28.04, 0.04148, 22, 24.35, 16.98, 0.1185, 23, 14.99, 8.14, 0.84002, 3, 21, 43.21, 32.32, 0.00079, 22, 32.63, 15.78, 0.00164, 23, 18.64, 0.6, 0.99757, 1, 23, 14.89, -3.27, 1, 2, 22, 30.11, -2.84, 0.17218, 23, 1.8, -7.74, 0.82782, 3, 21, 53.74, 9.38, 0.0128, 22, 26.37, -8.68, 0.6945, 23, -5.13, -7.91, 0.2927, 3, 21, 49.74, 3.37, 0.11082, 22, 19.48, -10.82, 0.87009, 23, -10.77, -3.4, 0.01909, 2, 21, 45.89, -2.38, 0.4067, 22, 12.87, -12.88, 0.5933, 2, 21, 36.94, -13.25, 0.99555, 22, -0.93, -15.68, 0.00445, 2, 21, 28, -15.35, 0.70151, 22, -9.2, -11.69, 0.29849, 2, 21, 21, -17, 0.61912, 22, -15.68, -8.56, 0.38088, 2, 21, 15.26, -14.84, 0.69809, 22, -18.77, -3.27, 0.30191, 2, 21, 11.42, -11.04, 0.81664, 22, -19.36, 2.1, 0.18336, 2, 21, 15.64, 0.27, 0.99995, 23, -10.9, 30.84, 5e-05, 2, 21, 23.82, -0.6, 0.95052, 22, -3.16, 2.4, 0.04948, 2, 21, 31.01, 0.21, 0.55514, 22, 2.94, -1.49, 0.44486, 2, 21, 37.92, 7.04, 0.00709, 22, 12.6, -0.54, 0.99291, 2, 22, 18.97, 0.64, 0.96237, 23, -1.55, 3.45, 0.03763, 1, 23, 5.24, -0.4, 1], "hull": 20, "edges": [0, 38, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 34, 36, 36, 38, 0, 2, 2, 4, 24, 26, 26, 28, 14, 16, 16, 18, 28, 30, 30, 32, 32, 34, 8, 10, 10, 12], "width": 44, "height": 51}}, "leg_R3": {"leg_R3": {"type": "mesh", "uvs": [0.7747, 0.01697, 0.88764, 0.09609, 0.93754, 0.27021, 0.95647, 0.49453, 0.92562, 0.5465, 0.94719, 0.65152, 0.97596, 0.7916, 0.97525, 0.92128, 0.8753, 0.95621, 0.64369, 0.98357, 0.40327, 0.98258, 0.17035, 0.94323, 0.10393, 0.90033, 0.08352, 0.7899, 0.04132, 0.62606, 0.01961, 0.54176, 0.08452, 0.33625, 0.14568, 0.25054, 0.19891, 0.17595, 0.32526, 0.087, 0.54943, 0.01702, 0.22683, 0.25564, 0.35633, 0.33613, 0.49322, 0.45038, 0.59682, 0.53866, 0.55242, 0.6529, 0.50802, 0.79052, 0.46363, 0.88918], "triangles": [7, 8, 6, 10, 27, 9, 10, 11, 27, 27, 11, 13, 11, 12, 13, 8, 9, 26, 9, 27, 26, 8, 26, 6, 6, 26, 25, 5, 24, 4, 6, 25, 5, 24, 5, 25, 27, 13, 26, 26, 13, 25, 14, 25, 13, 25, 23, 24, 25, 14, 23, 15, 22, 23, 15, 16, 22, 16, 21, 22, 16, 17, 21, 23, 14, 15, 4, 24, 3, 3, 24, 2, 24, 23, 2, 1, 2, 20, 20, 2, 22, 20, 0, 1, 22, 2, 23, 21, 19, 22, 22, 19, 20, 17, 18, 21, 21, 18, 19], "vertices": [1, 7, 19.95, 25.12, 1, 1, 7, 26.32, 25.44, 1, 2, 7, 35.02, 20.26, 0.98972, 8, -18.85, 7.77, 0.01028, 2, 7, 45.01, 12.24, 0.64152, 8, -7.02, 12.69, 0.35848, 2, 7, 46.38, 9.34, 0.43696, 8, -3.82, 12.5, 0.56304, 2, 7, 51.39, 5.96, 0.09892, 8, 1.55, 15.28, 0.90108, 2, 7, 58.09, 1.46, 0.00154, 8, 8.71, 18.99, 0.99846, 2, 8, 15.7, 21.4, 0.264, 9, 7.67, 21.66, 0.736, 1, 9, 8.57, 17.28, 1, 1, 9, 7.71, 7.93, 1, 2, 8, 26.53, 0.94, 0.00055, 9, 5.2, -1.36, 0.99945, 3, 7, 42.87, -28.23, 0.01528, 8, 27.48, -8.59, 0.00088, 9, 0.65, -9.79, 0.98385, 3, 7, 39.27, -28.56, 0.03091, 8, 26.04, -11.91, 0.01993, 9, -2.4, -11.73, 0.94916, 3, 7, 34.06, -24.94, 0.1183, 8, 20.36, -14.75, 0.15963, 9, -8.69, -10.91, 0.72206, 3, 7, 26, -19.93, 0.44577, 8, 12.1, -19.41, 0.24709, 9, -18.15, -10.16, 0.30714, 3, 7, 21.86, -17.35, 0.6261, 8, 7.85, -21.81, 0.18721, 9, -23.02, -9.77, 0.18669, 3, 7, 14.91, -7.56, 0.95539, 8, -4.07, -23.22, 0.02221, 9, -33.68, -4.27, 0.02239, 3, 7, 12.93, -2.47, 0.9968, 8, -9.49, -22.51, 0.00126, 9, -37.78, -0.65, 0.00194, 1, 7, 11.21, 1.96, 1, 1, 7, 10.84, 9.11, 1, 1, 7, 13.9, 18.44, 1, 3, 7, 15.33, -0.26, 0.99982, 8, -10.28, -19.35, 7e-05, 9, -36.67, 2.41, 0.00011, 1, 7, 22.2, 0.51, 1, 1, 7, 30.71, 0.2, 1, 3, 7, 37.22, -0.11, 0.46746, 8, 0.09, -0.07, 0.53253, 9, -17.29, 12.6, 0, 1, 8, 6.82, 0.4, 1, 1, 8, 14.81, 1.3, 1, 1, 8, 20.71, 1.47, 1], "hull": 21, "edges": [0, 40, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 36, 38, 38, 40, 32, 34, 34, 36, 26, 28, 28, 30, 8, 10, 10, 12], "width": 40, "height": 57}}, "leg_R4": {"leg_R4": {"type": "mesh", "uvs": [0.18645, 0.02247, 0.42227, 0.0224, 0.60261, 0.02234, 0.83469, 0.15965, 0.91985, 0.31073, 0.9461, 0.52108, 0.96143, 0.64401, 0.98009, 0.79353, 0.99244, 0.89255, 0.92664, 0.95511, 0.82552, 0.97795, 0.58925, 0.97787, 0.29599, 0.97777, 0.22914, 0.86303, 0.16576, 0.75423, 0.10428, 0.6183, 0.02545, 0.44404, 0.02476, 0.20542, 0.08903, 0.09024, 0.38054, 0.16044, 0.31982, 0.31516, 0.37674, 0.4598, 0.41469, 0.61116, 0.46782, 0.72553, 0.52474, 0.82644], "triangles": [23, 5, 6, 24, 23, 6, 7, 24, 6, 7, 10, 24, 9, 7, 8, 10, 11, 24, 7, 9, 10, 17, 18, 20, 16, 17, 20, 21, 20, 19, 16, 20, 21, 2, 22, 21, 15, 16, 21, 15, 21, 22, 22, 5, 23, 5, 22, 4, 14, 15, 22, 14, 22, 23, 13, 14, 23, 13, 23, 24, 12, 13, 24, 12, 24, 11, 19, 0, 1, 19, 18, 0, 20, 18, 19, 19, 2, 21, 1, 2, 19, 3, 22, 2, 4, 22, 3], "vertices": [2, 8, 12.06, -9.24, 0.64203, 9, -12.5, -1.7, 0.35797, 2, 8, 9.03, -0.56, 0.99825, 9, -10.15, 7.19, 0.00175, 2, 8, 6.71, 6.08, 0.96517, 10, 1.69, 32.62, 0.03483, 3, 8, 9.44, 16.62, 0.80164, 9, -0.2, 21.2, 0.0067, 10, 11.45, 27.8, 0.19166, 3, 8, 14.63, 21.94, 0.60674, 9, 7.07, 22.71, 0.03393, 10, 15.61, 21.64, 0.35933, 3, 8, 23.03, 25.95, 0.26727, 9, 16.28, 21.33, 0.03896, 10, 17.82, 12.6, 0.69377, 3, 8, 27.94, 28.29, 0.10102, 9, 21.67, 20.53, 0.01016, 10, 19.12, 7.32, 0.88882, 2, 8, 33.91, 31.14, 0.00716, 10, 20.7, 0.89, 0.99284, 1, 10, 21.74, -3.37, 1, 2, 9, 34.55, 15.72, 0.00207, 10, 19.55, -6.43, 0.99793, 2, 9, 34.52, 11.65, 0.02464, 10, 15.78, -7.94, 0.97536, 2, 9, 32.16, 2.74, 0.42626, 10, 6.64, -9.14, 0.57374, 1, 9, 29.23, -8.32, 1, 1, 9, 23.68, -9.55, 1, 1, 9, 18.42, -10.71, 1, 1, 9, 12.03, -11.5, 1, 1, 9, 3.83, -12.52, 1, 2, 8, 21.73, -12.55, 0.14438, 9, -6.33, -9.86, 0.85562, 2, 8, 16.12, -11.85, 0.40416, 9, -10.59, -6.14, 0.59584, 2, 8, 15.3, -0.1, 0.99835, 9, -4.69, 4.06, 0.00165, 2, 8, 22.51, -0.1, 0.2138, 9, 1.28, 0.03, 0.7862, 3, 8, 27.79, 4.09, 0.01532, 9, 8, 0.55, 0.98136, 10, -4.54, 12.39, 0.00332, 3, 8, 33.59, 7.68, 0.00106, 9, 14.82, 0.28, 0.99188, 10, -2.21, 5.98, 0.00706, 3, 8, 37.66, 11.29, 0.00054, 9, 20.21, 1, 0.64417, 10, 0.5, 1.26, 0.35528, 2, 9, 25.07, 2.01, 0.44572, 10, 3.28, -2.86, 0.55428], "hull": 19, "edges": [0, 36, 4, 6, 6, 8, 16, 18, 18, 20, 32, 34, 34, 36, 0, 2, 2, 4, 20, 22, 22, 24, 14, 16, 12, 14, 24, 26, 26, 28, 28, 30, 30, 32, 8, 10, 10, 12], "width": 39, "height": 44}}, "tails": {"tails": {"type": "mesh", "uvs": [0.27459, 0.43314, 0.39059, 0.26745, 0.53059, 0.14021, 0.68659, 0.03665, 0.86259, 0, 1, 0.0189, 1, 0.17572, 0.93459, 0.31183, 0.78259, 0.45386, 0.58659, 0.56334, 0.50259, 0.62843, 0.45459, 0.78229, 0.37859, 0.89473, 0.22659, 1, 0.12259, 1, 0.05459, 0.87994, 0.00659, 0.78821, 0.08259, 0.62547, 0.14259, 0.54558, 0.22259, 0.5012], "triangles": [12, 13, 15, 13, 14, 15, 11, 12, 16, 12, 15, 16, 16, 17, 11, 11, 17, 10, 10, 17, 18, 10, 18, 19, 19, 0, 10, 10, 0, 9, 8, 9, 1, 9, 0, 1, 1, 2, 8, 8, 2, 7, 2, 3, 7, 7, 3, 6, 6, 3, 4, 4, 5, 6], "vertices": [2, 17, 12.94, -8.71, 0.77041, 18, -3.91, -8.1, 0.22959, 2, 16, 19.6, -10.53, 0.49725, 17, -0.54, -10.62, 0.50275, 2, 16, 7.71, -11.98, 0.99717, 17, -12.5, -9.84, 0.00283, 1, 16, -3.6, -11.58, 1, 1, 16, -12.3, -6.91, 1, 1, 16, -16.69, -0.76, 1, 1, 16, -8.74, 7.48, 1, 2, 16, 0.7, 12.18, 0.99729, 17, -14.94, 15.2, 0.00271, 2, 16, 13.8, 13.95, 0.71213, 17, -1.73, 14.52, 0.28787, 3, 16, 26.97, 12.36, 0.04921, 17, 10.92, 10.53, 0.76999, 18, -2.12, 11.16, 0.18081, 4, 16, 33.53, 12.64, 0.00023, 17, 17.42, 9.59, 0.27186, 18, 4.07, 8.97, 0.72558, 19, -11.68, 11.12, 0.00233, 3, 17, 28.07, 13.99, 3e-05, 18, 15.38, 11.19, 0.64047, 19, -0.15, 11.38, 0.3595, 2, 18, 24.55, 10.8, 0.10369, 19, 8.81, 9.43, 0.89631, 1, 19, 18.29, 3.38, 1, 1, 19, 19.68, -2.06, 1, 2, 18, 30.71, -5.61, 0.00743, 19, 12.09, -7.79, 0.99257, 2, 18, 25.66, -10.71, 0.14357, 19, 6.24, -11.95, 0.85643, 2, 18, 13.14, -11.82, 0.85525, 19, -6.28, -10.91, 0.14475, 3, 17, 23.76, -9.76, 0.01402, 18, 6.49, -11.25, 0.97749, 19, -12.74, -9.21, 0.00849, 2, 17, 18.62, -8.12, 0.22145, 18, 1.77, -8.63, 0.77855], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 54, "height": 73}}, "trunk": {"trunk": {"type": "mesh", "uvs": [0.88503, 0.03525, 0.93657, 0.10594, 0.95405, 0.16652, 0.99361, 0.36468, 0.99423, 0.5343, 0.9551, 0.69335, 0.78807, 0.9222, 0.69692, 0.9913, 0.60432, 0.99132, 0.45849, 0.99137, 0.35774, 0.9914, 0.25582, 0.94775, 0.11533, 0.85091, 0.06519, 0.78256, 0.02539, 0.69345, 0.00618, 0.62631, 0.00593, 0.49612, 0.03804, 0.38761, 0.10857, 0.28779, 0.17155, 0.25325, 0.27166, 0.19075, 0.39858, 0.11151, 0.51701, 0.06042, 0.62447, 0.03516, 0.7404, 0.00792, 0.82678, 0.00844, 0.22735, 0.51465, 0.14514, 0.538, 0.06137, 0.58258, 0.29871, 0.50828, 0.41661, 0.51253, 0.48487, 0.50404, 0.57329, 0.49555, 0.67567, 0.47856, 0.7641, 0.43611, 0.8851, 0.41063, 0.94405, 0.38728], "triangles": [27, 17, 18, 27, 16, 17, 18, 26, 27, 28, 16, 27, 15, 16, 28, 14, 15, 28, 13, 28, 27, 14, 28, 13, 12, 13, 27, 12, 27, 26, 11, 12, 26, 26, 19, 29, 18, 19, 26, 31, 21, 22, 32, 31, 22, 30, 20, 21, 30, 29, 20, 19, 20, 29, 21, 31, 30, 11, 26, 29, 11, 29, 30, 9, 30, 31, 10, 11, 30, 9, 31, 8, 9, 10, 30, 33, 32, 23, 33, 34, 6, 7, 33, 6, 8, 32, 33, 7, 8, 33, 31, 32, 8, 0, 1, 25, 36, 2, 3, 2, 25, 1, 36, 35, 2, 34, 24, 25, 2, 34, 25, 35, 34, 2, 23, 24, 34, 33, 23, 34, 32, 22, 23, 36, 3, 4, 4, 35, 36, 5, 35, 4, 5, 6, 34, 5, 34, 35], "vertices": [3, 5, 45.79, 37.4, 0.98751, 3, 97.27, 55.03, 0.00565, 2, 103.03, 54.87, 0.00684, 3, 5, 49.97, 26.82, 0.9961, 3, 105.23, 46.89, 0.00089, 2, 111.07, 46.81, 0.00302, 3, 5, 49.74, 19.4, 0.99847, 3, 107.88, 39.95, 4e-05, 2, 113.8, 39.91, 0.00149, 2, 5, 46.45, -3.79, 0.99594, 4, 77.7, 10.62, 0.00406, 2, 5, 38.87, -21.58, 0.89678, 4, 75.92, -8.64, 0.10322, 3, 5, 26.08, -35.81, 0.64653, 4, 68.08, -26.09, 0.35314, 3, 107.4, -20.1, 0.00032, 4, 5, -8.18, -49.43, 0.0838, 4, 39.62, -49.53, 0.82921, 3, 81.07, -45.91, 0.08679, 15, -74.43, 68.25, 0.0002, 4, 5, -24.36, -51.02, 0.01489, 4, 24.7, -55.99, 0.80403, 3, 66.77, -53.64, 0.17661, 15, -58.61, 71.98, 0.00447, 4, 5, -37.63, -45.3, 0.00055, 4, 10.32, -54.59, 0.68357, 3, 52.32, -53.49, 0.29683, 15, -44.7, 68.07, 0.01904, 3, 4, -12.32, -52.38, 0.35985, 3, 29.58, -53.25, 0.54546, 15, -22.81, 61.91, 0.09469, 3, 4, -27.96, -50.86, 0.18354, 3, 13.86, -53.09, 0.62192, 15, -7.68, 57.65, 0.19454, 3, 4, -43.31, -44.36, 0.07226, 3, -1.98, -47.94, 0.57341, 15, 6.28, 48.55, 0.35434, 3, 4, -64.05, -31.25, 0.0055, 3, -23.78, -36.67, 0.29546, 15, 24.38, 31.99, 0.69905, 3, 4, -71.08, -22.73, 0.00038, 3, -31.52, -28.8, 0.1651, 15, 29.8, 22.37, 0.83452, 2, 3, -37.62, -18.57, 0.04889, 15, 33.02, 10.91, 0.95111, 2, 3, -40.54, -10.89, 0.00796, 15, 33.84, 2.73, 0.99204, 2, 2, -34.11, 2.33, 0.03359, 15, 29.85, -11.57, 0.96641, 3, 5, -91.44, 52.91, 0, 2, -29.1, 14.7, 0.16401, 15, 21.68, -22.12, 0.83599, 4, 5, -76.83, 58.99, 0.00195, 3, -24.15, 27.53, 0.01837, 2, -18.09, 26.08, 0.40027, 15, 8, -30.09, 0.5794, 4, 5, -66.25, 58.71, 0.00957, 3, -14.29, 31.36, 0.10093, 2, -8.27, 30.02, 0.52352, 15, -2.52, -31.22, 0.36598, 5, 5, -49.09, 59.06, 0.05461, 4, -32.47, 41.29, 0.00828, 3, 1.4, 38.32, 0.34555, 2, 7.35, 37.15, 0.49824, 15, -19.49, -33.84, 0.09332, 5, 5, -27.33, 59.51, 0.21727, 4, -11.88, 48.36, 0.06252, 3, 21.3, 47.15, 0.4303, 2, 27.15, 46.18, 0.2866, 15, -40.99, -37.17, 0.00331, 4, 5, -8.06, 57.53, 0.47355, 4, 7.07, 52.36, 0.09243, 3, 39.83, 52.77, 0.28607, 2, 45.62, 52, 0.14795, 4, 5, 8.47, 53.53, 0.73396, 4, 24.03, 53.6, 0.0542, 3, 56.63, 55.47, 0.14016, 2, 62.39, 54.88, 0.07168, 4, 5, 26.31, 49.21, 0.92097, 4, 42.34, 54.93, 0.00757, 3, 74.75, 58.39, 0.04439, 2, 80.47, 57.99, 0.02707, 4, 5, 38.66, 43.81, 0.97322, 4, 55.74, 53.56, 3e-05, 3, 88.22, 58.18, 0.01463, 2, 93.95, 57.93, 0.01212, 3, 5, -70.07, 27.9, 4e-05, 3, -5.9, 1.47, 0.03153, 2, 0.44, 0.22, 0.96843, 2, 2, -12.39, -2.44, 0.0073, 15, 10.24, -1.09, 0.9927, 2, 3, -31.88, -5.99, 0.00019, 15, 24.2, 0.26, 0.99981, 3, 5, -59.56, 24.16, 0.0008, 3, 5.24, 2.08, 0.9545, 2, 11.57, 0.95, 0.0447, 3, 5, -42.87, 16.42, 0.00174, 3, 23.62, 1.4, 0.99376, 2, 29.96, 0.46, 0.0045, 4, 5, -32.71, 13.09, 0.00644, 4, -2.83, 2.51, 0.05393, 3, 34.28, 2.26, 0.9354, 2, 40.61, 1.43, 0.00423, 4, 5, -19.66, 8.51, 0.01691, 4, 10.99, 2.14, 0.97334, 3, 48.09, 3.08, 0.00798, 2, 54.4, 2.4, 0.00177, 4, 5, -4.23, 3.95, 0.2432, 4, 27.08, 2.51, 0.75486, 3, 64.08, 4.84, 0.00108, 2, 70.37, 4.33, 0.00085, 3, 5, 10.35, 2.93, 0.99903, 3, 77.92, 9.54, 0.0004, 2, 84.17, 9.17, 0.00057, 3, 5, 28.83, -1.89, 0.9916, 4, 60.34, 7.05, 0.00839, 2, 103.04, 12.08, 1e-05, 2, 5, 38.33, -3.09, 0.9934, 4, 69.75, 8.8, 0.0066], "hull": 26, "edges": [0, 50, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 42, 44, 48, 50, 18, 20, 14, 16, 16, 18, 38, 40, 40, 42, 44, 46, 46, 48], "width": 156, "height": 114}}, "yinying": {"yinying": {"x": 100.87, "y": 0.05, "rotation": 0.17, "width": 201, "height": 94}}}}], "animations": {"attack": {"bones": {"ear_L2": {"rotate": [{"angle": -4.55}]}, "hairiness_9": {"rotate": [{"angle": 11.03, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 14.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 5.6, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.03}]}, "hairiness_19": {"rotate": [{"angle": 1.14}]}, "hairiness_13": {"rotate": [{"angle": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 4.86, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.05, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.5}]}, "ear_R": {"rotate": [{"angle": 0.41}]}, "trunk3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -22.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -24.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -9.34, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"x": 0.26, "y": 0.26, "curve": "stepped"}, {"time": 0.1667, "x": 0.26, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 2.36, "y": 5.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.26, "y": 0.26}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.101, "y": 1.101, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "hairiness_6": {"rotate": [{"angle": 1.11, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.24, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.11}]}, "trunk4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -15.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 10.65, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 13.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"x": 0.03, "y": 0.32, "curve": "stepped"}, {"time": 0.3333, "x": 0.03, "y": 0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 3.58, "y": 16.17, "curve": "stepped"}, {"time": 0.6667, "x": 3.58, "y": 16.17, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.03, "y": 0.32}]}, "head": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.77, "curve": "stepped"}, {"time": 0.3333, "angle": -7.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -0.21, "curve": "stepped"}, {"time": 0.6667, "angle": -0.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"x": 0.07, "y": 0.17}]}, "leg_R3": {"rotate": [{"angle": 1.61}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 6.93, "y": -5.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -0.05, "y": -40.84, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 16.47, "y": 6.06, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.198, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "leg_R4": {"rotate": [{"angle": -5.02}]}, "leg_R6": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -8.64, "y": 6.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -18.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "leg_L2": {"rotate": [{"angle": 0.46}]}, "leg_L3": {"rotate": [{"angle": -0.89}]}, "leg_L4": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -5.22, "y": 8.89, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8.26, "curve": "stepped"}, {"time": 0.3333, "x": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "trunk2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -18.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.95, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"x": 0.06, "y": 0.09, "curve": "stepped"}, {"time": 0.1667, "x": 0.06, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -9.3, "y": -0.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 2.21, "y": 0.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.06, "y": 0.09}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 1.094, "y": 1.094, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "tails3": {"rotate": [{"angle": -8.29, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -36.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 17.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -46.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 17.64, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -8.29}]}, "trunk5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 10.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 20.54, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.63, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"y": 0.05}]}, "hairiness_10": {"rotate": [{"angle": 4.76, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -9.56, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -8.54, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.76}]}, "tails": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -20.22, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.5, "angle": -22.44, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.6667, "angle": 6.52, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -2.65, "y": -7.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 9.96, "y": -11.27, "curve": "stepped"}, {"time": 0.6667, "x": 9.96, "y": -11.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "tails4": {"rotate": [{"angle": -24.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -42.01, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 18.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -75.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 10.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -24.1}]}, "leg_R": {"rotate": [{"angle": 2.52}]}, "leg_R2": {"rotate": [{"angle": -5.3}]}, "leg_R8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -20.49, "curve": "stepped"}, {"time": 0.6667, "angle": -20.49, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -11.83, "y": 6.35, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -21.13, "y": -0.81, "curve": "stepped"}, {"time": 0.3333, "x": -21.13, "y": -0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -7.4, "y": -0.81, "curve": "stepped"}, {"time": 0.6667, "x": -7.4, "y": -0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "leg_R9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 23.29, "curve": "stepped"}, {"time": 0.6667, "angle": 23.29, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "leg_L": {"rotate": [{"angle": 1.54}]}, "leg_L6": {"rotate": [{"angle": -4.17}]}, "leg_L7": {"translate": [{"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -4.09, "y": 2.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -6.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "trunk": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.12, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 13.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -19.5, "y": -5.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -23.95, "y": -5.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 10, "y": -1.67, "curve": "stepped"}, {"time": 0.6667, "x": 10, "y": -1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "hairiness_14": {"rotate": [{"angle": 3.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 11.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 11.09, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -2.79, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.1}]}, "tails2": {"rotate": [{"angle": -1.46, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -21.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 8.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -33.39, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5667, "angle": -49.14, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.7667, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.46}]}, "hairiness_7": {"rotate": [{"angle": 3.62, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.57, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.62}]}, "hairiness_8": {"rotate": [{"angle": 6.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -4.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.65}]}, "hairiness_2": {"rotate": [{"angle": 5.53}]}, "hairiness_11": {"rotate": [{"angle": 11.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 18, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -4.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.94}]}, "hairiness_15": {"rotate": [{"angle": 6.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 20.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -6.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 20.2, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -3.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.97}]}, "hairiness_20": {"rotate": [{"angle": -1.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -7.3, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.07, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.29}]}, "hairiness_16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "hairiness_12": {"rotate": [{"angle": 4.11}]}, "hairiness_17": {"rotate": [{"angle": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 20.72, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -12.28, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 20.72, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -7.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.48}]}, "ear_L": {"rotate": [{"angle": -0.94}]}, "ear_R2": {"rotate": [{"angle": 4.6}]}, "hairiness_5": {"rotate": [{"angle": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -16.64, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 2.37}]}, "hairiness_21": {"rotate": [{"angle": -4.37, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -15.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 4.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.65, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 7.35, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.37}]}, "hairiness_3": {"rotate": [{"angle": 9.81}]}, "hairiness_4": {"rotate": [{"angle": 1.14}]}, "hairiness2": {"rotate": [{"angle": 7.09}]}, "hairiness": {"rotate": [{"angle": 3.22}]}, "hairiness_22": {"rotate": [{"angle": -21.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -36.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -37.3, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -21.81}]}, "yinying": {"translate": [{}, {"time": 0.1667, "x": -7.59, "curve": "stepped"}, {"time": 0.3333, "x": -7.59}, {"time": 0.4, "x": -3.8, "curve": "stepped"}, {"time": 0.6667, "x": -3.8}, {"time": 0.8333}], "scale": [{}, {"time": 0.1667, "x": 1.01}, {"time": 0.3333, "x": 0.933}, {"time": 0.4, "x": 1.076, "curve": "stepped"}, {"time": 0.6667, "x": 1.076}, {"time": 0.8333}]}}, "deform": {"default": {"trunk": {"trunk": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "offset": 26, "vertices": [5.19576, 1.6815, 3.70683, 4.01045, 4.0115, 3.7056, 27.12396, 0.59661, 23.47647, 13.68316, 24.46479, 11.80995, -27.02673, 1.09607, 17.07121, -2.67155, 16.2994, 5.98685, 16.70417, 4.69127, -16.70607, 3.74647, 1.35712, -2.78015, 2.60214, -1.7712, 2.44531, -1.96821, -1.07184, 2.79805, -1.12933, -2.63764, -1.34002, -2.54085, 2.49899, 1.5314, -14.09501, 0.48995, -14.02465, 1.56946, 11.91695, -7.82176, -31.10983, 3.96924, -30.71, 6.35471, 24.33705, -19.70621, -14.60821, 4.16287, -14.22331, 5.31292, 9.59241, -11.58905], "curve": 0.25, "c3": 0.75}, {"time": 0.5, "offset": 32, "vertices": [15.84856, 4.5477, 11.77106, 11.54549, 12.61844, 10.61266, -16.03776, -3.8268, 8.77509, 7.03642, 4.36594, 10.36589, 5.14486, 10.00208, -9.08404, -6.63268, 3.69612, 4.32393, 1.19131, 5.5622, 1.61263, 5.45494, -3.8877, -4.15247, -0.58978, 3.70258, -0.30529, 3.73676, -1.40294, -3.47687, -12.28383, 4.74787, -11.88536, 5.67211, 8.07943, -10.39992, -31.7944, 3.84444, -31.40797, 6.26134, 25.26455, -19.68179, -12.19747, 2.1135, -12.00044, 3.03886, 9.36321, -8.0979], "curve": "stepped"}, {"time": 0.6667, "offset": 32, "vertices": [15.84856, 4.5477, 11.77106, 11.54549, 12.61844, 10.61266, -16.03776, -3.8268, 8.77509, 7.03642, 4.36594, 10.36589, 5.14486, 10.00208, -9.08404, -6.63268, 3.69612, 4.32393, 1.19131, 5.5622, 1.61263, 5.45494, -3.8877, -4.15247, -0.58978, 3.70258, -0.30529, 3.73676, -1.40294, -3.47687, -12.28383, 4.74787, -11.88536, 5.67211, 8.07943, -10.39992, -31.7944, 3.84444, -31.40797, 6.26134, 25.26455, -19.68179, -12.19747, 2.1135, -12.00044, 3.03886, 9.36321, -8.0979], "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8333}]}}}}, "idle": {"slots": {"biyan": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "biyan"}]}}, "bones": {"trunk": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -1.9, "y": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hairiness_6": {"rotate": [{"angle": 1.11, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 1.11}]}, "trunk2": {"translate": [{"x": 0.06, "y": 0.09, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.99, "y": 1.49, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "x": 0.06, "y": 0.09}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.01, "y": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "trunk3": {"translate": [{"x": 0.26, "y": 0.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.01, "y": 1.97, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": 0.26, "y": 0.26}]}, "trunk4": {"translate": [{"x": 0.03, "y": 0.32, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 0.14, "y": 1.48, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "x": 0.03, "y": 0.32}]}, "head": {"translate": [{"x": 0.07, "y": 0.17, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.2, "y": 0.46, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "x": 0.07, "y": 0.17}]}, "leg_R3": {"rotate": [{"angle": 1.61}]}, "leg_R4": {"rotate": [{"angle": -5.02}]}, "leg_L2": {"rotate": [{"angle": 0.46}]}, "leg_L3": {"rotate": [{"angle": -0.89}]}, "trunk5": {"translate": [{"y": 0.05, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "y": 1.99, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "y": 0.05}]}, "tails": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "tails2": {"rotate": [{"angle": -1.46, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.24, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -1.46}]}, "tails3": {"rotate": [{"angle": -8.29, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -22.54, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -8.29}]}, "tails4": {"rotate": [{"angle": -24.1, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -38.13, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -24.1}]}, "leg_R": {"rotate": [{"angle": 2.52}]}, "leg_R2": {"rotate": [{"angle": -5.3}]}, "leg_L": {"rotate": [{"angle": 1.54}]}, "leg_L6": {"rotate": [{"angle": -4.17}]}, "hairiness_7": {"rotate": [{"angle": 3.62, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3333, "angle": 0.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.77, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 3.62}]}, "hairiness_8": {"rotate": [{"angle": 6.65, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3333, "angle": 2.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 7.64, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 6.65}]}, "hairiness_9": {"rotate": [{"angle": 11.03, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 6.98, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.03}]}, "hairiness_5": {"rotate": [{"angle": 2.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 3.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 2.37}]}, "hairiness_10": {"rotate": [{"angle": 4.76, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": 0.71, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 5.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 4.76}]}, "hairiness_11": {"rotate": [{"angle": 11.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": 4.39, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.94}]}, "hairiness_13": {"rotate": [{"angle": 0.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.82, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.5}]}, "hairiness_14": {"rotate": [{"angle": 3.1, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": 1.11, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 8.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.1}]}, "hairiness_15": {"rotate": [{"angle": 6.97, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.1667, "angle": 4.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 6.97}]}, "hairiness_16": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hairiness_17": {"rotate": [{"angle": 1.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 11.37, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.48}]}, "ear_R": {"rotate": [{"angle": 0.41, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.16, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.41}]}, "ear_R2": {"rotate": [{"angle": 4.6, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": 1.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 12.63, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 4.6}]}, "ear_L": {"rotate": [{"angle": -0.94, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.19, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -0.94}]}, "ear_L2": {"rotate": [{"angle": -4.55, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": -1.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -12.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -4.55}]}, "hairiness_2": {"rotate": [{"angle": 5.53, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 8.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 5.53}]}, "hairiness_3": {"rotate": [{"angle": 9.81, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.5, "angle": 1.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 11.3, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 9.81}]}, "hairiness": {"rotate": [{"angle": 3.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 8.74, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.22}]}, "hairiness2": {"rotate": [{"angle": 7.09, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3333, "angle": 1.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.3, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 7.09}]}, "hairiness_4": {"rotate": [{"angle": 1.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.14}]}, "hairiness_12": {"rotate": [{"angle": 4.11, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.1667, "angle": 1.47, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 11.3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 4.11}]}, "hairiness_19": {"rotate": [{"angle": 1.14, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.74, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.14}]}, "hairiness_20": {"rotate": [{"angle": -1.29, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -1.29}]}, "hairiness_21": {"rotate": [{"angle": -4.37, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3333, "angle": -0.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -4.37}]}, "hairiness_22": {"rotate": [{"angle": -21.81, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3333, "angle": -9.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -25.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": -21.81}]}}}, "run": {"bones": {"leg_R6": {"rotate": [{"angle": -20.4}, {"time": 0.1, "angle": 22.35}, {"time": 0.2333, "angle": 51.76}, {"time": 0.3667, "angle": 15.04}, {"time": 0.5667, "angle": 16.29}, {"time": 0.6667, "angle": -20.4}], "translate": [{"x": 26.62, "y": 40.69}, {"time": 0.1, "x": 36.32, "y": 38.98}, {"time": 0.2333, "x": 46.86, "y": 26.74}, {"time": 0.3667, "x": 26.99, "y": -1.84}, {"time": 0.4, "x": 8.42, "y": -3.75}, {"time": 0.5667, "x": -11.77, "y": -2.09}, {"time": 0.6667, "x": 26.62, "y": 40.69}]}, "trunk": {"rotate": [{"angle": 13.62}, {"time": 0.2333, "angle": -9.06}, {"time": 0.3667, "angle": -22.41}, {"time": 0.4, "angle": -9.93}, {"time": 0.5667, "angle": -16.63}, {"time": 0.6667, "angle": 13.62}], "translate": [{"x": 10.11, "y": -0.77}, {"time": 0.2333, "x": 9.34, "y": 28.92}, {"time": 0.3667, "x": 12.09, "y": 14.11}, {"time": 0.4, "x": 6.25, "y": 0.51}, {"time": 0.5667, "x": 5.52, "y": -4.57}, {"time": 0.6667, "x": 10.11, "y": -0.77}]}, "trunk2": {"rotate": [{"angle": 4.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.49}]}, "trunk3": {"rotate": [{"angle": 3.29}]}, "trunk4": {"rotate": [{"angle": 4.04}]}, "head": {"rotate": [{"angle": -6.88}], "translate": [{"x": -4.26, "y": -0.56}]}, "leg_R3": {"rotate": [{"angle": 1.61}], "translate": [{"time": 0.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5667, "x": -21.24, "y": 6.09, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667}]}, "leg_R4": {"rotate": [{"angle": -5.02}]}, "leg_R7": {"rotate": [{"angle": -16.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -19.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -16.76}]}, "leg_L2": {"rotate": [{"angle": 0.46}]}, "leg_L3": {"rotate": [{"angle": -0.89}]}, "leg_L4": {"rotate": [{"angle": -7.71}, {"time": 0.1, "angle": 15.17}, {"time": 0.2333, "angle": 65.94}, {"time": 0.3667, "angle": 15.3}, {"time": 0.5667, "angle": 10.68}, {"time": 0.6667, "angle": -7.71}], "translate": [{"x": 14.09, "y": 41.49}, {"time": 0.1, "x": 23.6, "y": 39.54}, {"time": 0.2333, "x": 34.08, "y": 29.07}, {"time": 0.3667, "x": 19.7, "y": -4.37}, {"time": 0.4, "x": 0.28, "y": -3.23}, {"time": 0.5667, "x": -21.92, "y": -5.07}, {"time": 0.6667, "x": 14.09, "y": 41.49}]}, "leg_L5": {"rotate": [{"angle": -11.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -14.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.46}]}, "trunk5": {"rotate": [{"angle": -13.45}, {"time": 0.2667, "angle": -27.15}, {"time": 0.3667, "angle": -60.59}, {"time": 0.5667, "angle": 5.98}, {"time": 0.6667, "angle": -13.45}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 2.45, "y": -2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "tails": {"rotate": [{"angle": 12.89, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 23.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 2.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 12.89}]}, "tails2": {"rotate": [{"angle": -18.41, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.1667, "angle": 10.9, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": 22.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -24.54, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -18.41}]}, "tails3": {"rotate": [{"angle": -51.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -16.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3667, "angle": 18.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -51.18}]}, "tails4": {"rotate": [{"angle": -13.98, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1667, "angle": -28.08, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.5, "angle": 13.42, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -13.98}]}, "leg_R": {"rotate": [{"angle": 2.52}], "translate": [{"curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1, "x": -7.14, "y": -12.4, "curve": 0.303, "c2": 0.24, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "x": 5.43, "y": 3.41, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.3667, "x": 12.57, "y": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "x": 0.06, "y": -2.16, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.5667, "x": -2.78, "y": -18.89, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667}], "scale": [{"time": 0.3667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.4333, "x": 1.18, "curve": 0.321, "c2": 0.3, "c3": 0.679, "c4": 0.7}, {"time": 0.5667, "x": 1.092, "y": 0.996, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667}]}, "leg_R2": {"rotate": [{"angle": -5.3}], "scale": [{"time": 0.3667, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5667, "x": 0.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.6667}]}, "leg_R8": {"rotate": [{}, {"time": 0.1, "angle": -48.05}, {"time": 0.2333, "angle": -70.25}, {"time": 0.3333, "angle": -124.87}, {"time": 0.4667, "angle": -5.56}, {"time": 0.5667, "angle": 1.8}, {"time": 0.6667}], "translate": [{"x": -3.69}, {"time": 0.1, "x": -16.69, "y": 8.19}, {"time": 0.2333, "x": -16.32, "y": 39.41}, {"time": 0.3333, "x": -22.06, "y": 59.36}, {"time": 0.4667, "x": -28.26, "y": 2.67}, {"time": 0.5667, "x": 10.51}, {"time": 0.6667, "x": -3.69}]}, "leg_L": {"rotate": [{"angle": 1.54}]}, "leg_L6": {"rotate": [{"angle": -4.17}]}, "leg_L7": {"rotate": [{}, {"time": 0.1, "angle": -40.82}, {"time": 0.2333, "angle": -39.55}, {"time": 0.3333, "angle": -142.5}, {"time": 0.4667, "angle": -3.62}, {"time": 0.5667, "angle": 1.8}, {"time": 0.6667}], "translate": [{"x": -3.69}, {"time": 0.1, "x": -17.05, "y": 8}, {"time": 0.2333, "x": -18.36, "y": 38.34}, {"time": 0.3333, "x": -18.27, "y": 28.28}, {"time": 0.4667, "x": -23.08, "y": 0.08}, {"time": 0.5667, "x": 10.51}, {"time": 0.6667, "x": -3.69}]}, "hairiness_6": {"rotate": [{"angle": 2.9, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": 3.03, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "angle": 1.11, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "angle": 2.9}]}, "hairiness_7": {"rotate": [{"angle": 4.37, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 5.77, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 3.62, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3667, "angle": 0.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 4.37}]}, "hairiness_8": {"rotate": [{"angle": 3.82, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 7.64, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "angle": 6.65, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3667, "angle": 2.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 3.82}]}, "hairiness_9": {"rotate": [{"angle": 2.67, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 11.03, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": 6.98, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 2.67}]}, "hairiness_5": {"rotate": [{"angle": 2.84, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 3.74, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 2.37, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.84}]}, "hairiness_10": {"rotate": [{"angle": 2.74, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 5.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "angle": 4.76, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.4333, "angle": 0.71, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 2.74}]}, "hairiness_11": {"rotate": [{"angle": 2.89, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 11.94, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.4333, "angle": 4.39, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 2.89}]}, "hairiness_13": {"rotate": [{"angle": 3.65, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 0.2333, "angle": 0.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.65}]}, "hairiness_14": {"rotate": [{"angle": 8.16, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": 8.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "angle": 3.1, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.3, "angle": 1.11, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "angle": 8.16}]}, "hairiness_15": {"rotate": [{"angle": 8.37, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 11.04, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": 6.97, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.3, "angle": 4.06, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 8.37}]}, "hairiness_16": {"rotate": [{"angle": 4.27, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.64, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 4.27}]}, "hairiness_17": {"rotate": [{"angle": 10.87, "curve": 0.278, "c2": 0.15, "c3": 0.689, "c4": 0.74}, {"time": 0.2333, "angle": 1.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 10.87}]}, "hairiness_20": {"rotate": [{"angle": -3.35, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.0333, "angle": -3.5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "angle": -1.29, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3667, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "angle": -3.35}]}, "hairiness_21": {"rotate": [{"angle": -5.28, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -6.97, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "angle": -4.37, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3667, "angle": -0.91, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -5.28}]}, "hairiness_22": {"rotate": [{"angle": -12.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -25.06, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.2333, "angle": -21.81, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.3667, "angle": -9.22, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -12.53}]}, "zong": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 8.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "yinying": {"translate": [{"x": 12.95}, {"time": 0.1667, "x": -3.98}, {"time": 0.3333, "x": -25.9}, {"time": 0.5, "x": 4.98}, {"time": 0.6667, "x": 12.95}], "scale": [{"x": 0.974, "y": 1.014}, {"time": 0.1667, "x": 1.09}, {"time": 0.3333, "x": 1.211}, {"time": 0.5, "x": 0.994}, {"time": 0.6667, "x": 0.974, "y": 1.014}]}}}}}