{"skeleton": {"hash": "riDmNtubEOABGFra6S1ta2+pRDQ", "spine": "3.8.99", "x": -49.42, "y": -17.41, "width": 85.94, "height": 133.78, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/荆棘谷/劈颅巨魔盾"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 26.83}, {"name": "body", "parent": "bone", "length": 12.09, "x": 1.16, "y": 32.94, "color": "000000ff"}, {"name": "body3", "parent": "body", "length": 8.38, "rotation": 92.12, "x": -0.93, "y": -3.56, "color": "000000ff"}, {"name": "body4", "parent": "body3", "length": 15.09, "rotation": 2.59, "x": 8.38, "color": "000000ff"}, {"name": "body2", "parent": "body4", "length": 17, "rotation": 7.93, "x": 15.43, "y": 0.44, "color": "000000ff"}, {"name": "head", "parent": "body2", "length": 12.61, "rotation": -33.53, "x": -11.27, "y": -2.08, "color": "000000ff"}, {"name": "ear", "parent": "head", "length": 4.62, "rotation": 71.33, "x": 14.27, "y": 19.55}, {"name": "ear2", "parent": "ear", "length": 11.4, "rotation": -4.34, "x": 4.62}, {"name": "ear3", "parent": "ear2", "length": 9.08, "rotation": -9.43, "x": 11.4}, {"name": "ear4", "parent": "ear3", "length": 8.17, "rotation": -3.29, "x": 9.08}, {"name": "ear5", "parent": "ear4", "length": 10.67, "rotation": 21.07, "x": 8.17}, {"name": "ear6", "parent": "head", "length": 13.32, "rotation": -8.36, "x": 23.64, "y": -10.22}, {"name": "ear7", "parent": "ear6", "length": 10.71, "rotation": 15.01, "x": 13.19, "y": -0.08}, {"name": "ear8", "parent": "ear7", "length": 7.79, "rotation": 8.53, "x": 10.86, "y": 0.04}, {"name": "ear9", "parent": "ear8", "length": 9.85, "rotation": -7.01, "x": 7.79}, {"name": "tooth", "parent": "head", "length": 7.45, "rotation": 93.96, "x": 5.91, "y": -5.05}, {"name": "hair4", "parent": "head", "length": 12.99, "rotation": 56.66, "x": 16.58, "y": 11.3, "color": "ff0000ff"}, {"name": "hair5", "parent": "hair4", "length": 11.91, "rotation": 2.88, "x": 12.99, "color": "ff0000ff"}, {"name": "hair6", "parent": "hair5", "length": 11.09, "rotation": -5.64, "x": 11.91, "color": "ff0000ff"}, {"name": "hair7", "parent": "hair6", "length": 9.31, "rotation": -14.59, "x": 11.09, "color": "ff0000ff"}, {"name": "hair3", "parent": "head", "length": 5.28, "rotation": 61.12, "x": 25.46, "y": 1.92, "color": "900000ff"}, {"name": "hair8", "parent": "hair3", "length": 13.98, "rotation": -4.26, "x": 5.75, "y": -0.4, "color": "900000ff"}, {"name": "hair9", "parent": "hair8", "length": 10.85, "rotation": -5.02, "x": 13.98, "color": "900000ff"}, {"name": "hair10", "parent": "hair9", "length": 8.81, "rotation": 3.29, "x": 10.85, "color": "900000ff"}, {"name": "hair11", "parent": "hair10", "length": 8.46, "rotation": 29.66, "x": 8.81, "color": "900000ff"}, {"name": "hair", "parent": "head", "length": 9.02, "rotation": 25.81, "x": 22.64, "y": -1.98, "color": "ffb800ff"}, {"name": "hair2", "parent": "hair", "length": 13.99, "rotation": 4.64, "x": 9.02, "color": "ffb800ff"}, {"name": "hair12", "parent": "hair2", "length": 13.32, "rotation": 2.53, "x": 13.99, "color": "ffb800ff"}, {"name": "hair13", "parent": "hair12", "length": 9.31, "rotation": 6.34, "x": 13.32, "color": "ffb800ff"}, {"name": "hair14", "parent": "hair13", "length": 8.45, "rotation": 24.33, "x": 9.31, "color": "ffb800ff"}, {"name": "hair15", "parent": "head", "length": 7.85, "rotation": 11.8, "x": 19.85, "y": -5.53, "color": "00ff8fff"}, {"name": "hair16", "parent": "hair15", "length": 11.25, "rotation": 1.97, "x": 7.85, "color": "00ff8fff"}, {"name": "hair17", "parent": "hair16", "length": 12.63, "rotation": 0.78, "x": 11.25, "color": "00ff8fff"}, {"name": "hair18", "parent": "hair17", "length": 11.51, "rotation": 10.98, "x": 12.63, "color": "00ff8fff"}, {"name": "hand_L3", "parent": "body2", "length": 29.03, "rotation": -169.67, "x": 9.09, "y": -12.06}, {"name": "hand_L2", "parent": "hand_L3", "length": 21.42, "rotation": -12.49, "x": 29.12, "y": -0.23}, {"name": "hand_R2", "parent": "body2", "length": 28.5, "rotation": 145.09, "x": 14.26, "y": 10.18}, {"name": "hand_R", "parent": "hand_R2", "length": 27.26, "rotation": 21.89, "x": 27.61, "y": -0.17}, {"name": "weapons", "parent": "hand_R", "length": 15.86, "rotation": -168.69, "x": 17.18, "y": -2.37}, {"name": "body6", "parent": "body", "length": 19.41, "rotation": -91.29, "x": -13.94, "y": 3.77, "color": "603cffff"}, {"name": "leg_R3", "parent": "body6", "length": 20.46, "rotation": -40.47, "x": 19.3, "y": -0.11, "color": "603cffff"}, {"name": "leg_R", "parent": "leg_R3", "length": 18.56, "rotation": -3.51, "x": 19.93, "y": 0.26, "transform": "noRotationOrReflection", "color": "603cffff"}, {"name": "leg_L3", "parent": "body", "length": 20.02, "rotation": -68.6, "x": 2.73, "y": 3.23, "color": "603cffff"}, {"name": "leg_L5", "parent": "leg_L3", "length": 17.69, "rotation": -55.09, "x": 19.8, "y": -0.26, "color": "603cffff"}, {"name": "leg_L2", "parent": "leg_L5", "length": 21.15, "rotation": -0.11, "x": 17.51, "y": 0.01, "transform": "noRotationOrReflection", "color": "603cffff"}, {"name": "yinying", "parent": "bone", "x": -0.9, "y": 3.48}, {"name": "leg_R2", "parent": "root", "x": -26.92, "y": 2.2, "color": "ff3f00ff"}, {"name": "leg_L", "parent": "root", "x": 0.99, "y": 2.93, "color": "ff3f00ff"}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "hand_L3", "bone": "hand_L3", "attachment": "hand_L3"}, {"name": "hand_L2", "bone": "hand_L2", "attachment": "hand_L2"}, {"name": "hand_L", "bone": "hand_L2", "attachment": "hand_L"}, {"name": "leg_L3", "bone": "leg_L5", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L5", "attachment": "leg_L"}, {"name": "body2", "bone": "body2", "attachment": "body2"}, {"name": "body", "bone": "leg_L3", "attachment": "body"}, {"name": "leg_R3", "bone": "leg_R3", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R3", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "hand_R2", "bone": "hand_R2", "attachment": "hand_R2"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}, {"name": "hair7", "bone": "head", "attachment": "hair7"}, {"name": "hair6", "bone": "hand_L3", "attachment": "hair6"}, {"name": "hair5", "bone": "head", "attachment": "hair5"}, {"name": "ear2", "bone": "ear6", "attachment": "ear2"}, {"name": "hair4", "bone": "hair4", "attachment": "hair4"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye_close2", "bone": "head", "color": "ffffff00", "attachment": "eye_close2"}, {"name": "eye_close", "bone": "head", "color": "ffffff00", "attachment": "eye_close"}, {"name": "tooth", "bone": "tooth", "attachment": "tooth"}, {"name": "hair3", "bone": "hair3", "attachment": "hair3"}, {"name": "ear", "bone": "ear", "attachment": "ear"}, {"name": "hair2", "bone": "hair15", "attachment": "hair2"}, {"name": "hair", "bone": "hair", "attachment": "hair"}, {"name": "weapons", "bone": "weapons", "attachment": "weapons"}], "ik": [{"name": "leg_L", "order": 1, "bones": ["leg_L3", "leg_L5"], "target": "leg_L", "bendPositive": false}, {"name": "leg_R", "bones": ["body6", "leg_R3"], "target": "leg_R2", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.57062, 0.14476, 0.35265, 0.11442, 0.13255, 0, 0.06907, 0.17509, 0.01828, 0.41017, 0.00346, 0.65663, 0.08388, 0.83105, 0.24895, 0.95617, 0.46269, 0.98271, 0.63411, 0.97134, 0.7886, 0.85, 0.91981, 0.74384, 1, 0.6263, 0.95155, 0.44051, 0.90499, 0.2623, 0.83939, 0.11822, 0.72511, 0.1258, 0.18945, 0.29382, 0.35251, 0.38799, 0.60228, 0.42874, 0.77368, 0.40913, 0.55391, 0.67917, 0.7952, 0.71617, 0.25496, 0.21773, 0.7418, 0.29929, 0.26803, 0.5286, 0.23441, 0.73348], "triangles": [25, 4, 17, 25, 5, 4, 26, 5, 25, 6, 5, 26, 8, 7, 26, 6, 26, 7, 21, 26, 25, 21, 8, 26, 22, 20, 13, 12, 11, 13, 22, 21, 19, 11, 22, 13, 10, 22, 11, 22, 9, 21, 10, 9, 22, 8, 21, 9, 21, 25, 18, 23, 17, 2, 4, 3, 17, 17, 3, 2, 23, 2, 1, 18, 1, 0, 24, 16, 15, 24, 15, 14, 20, 24, 14, 24, 19, 0, 24, 0, 16, 18, 0, 19, 19, 24, 20, 20, 14, 13, 21, 18, 19, 22, 19, 20, 18, 23, 1, 17, 23, 18, 25, 17, 18], "vertices": [1, 2, -0.03, 4.96, 1, 1, 2, -9.41, 5.7, 1, 1, 2, -18.86, 8.47, 1, 2, 2, -21.6, 4.27, 0.75, 40, -0.36, -7.67, 0.25, 2, 2, -23.8, -1.36, 0.33333, 40, 5.32, -9.76, 0.66667, 3, 2, -24.45, -7.28, 0.00011, 43, -0.07, -29.14, 0.00025, 40, 11.24, -10.3, 0.99964, 3, 2, -21, -11.47, 0.01186, 43, 5.09, -27.45, 0.04163, 40, 15.37, -6.77, 0.94651, 3, 2, -13.91, -14.49, 0.04441, 43, 10.47, -21.94, 0.18623, 40, 18.26, 0.38, 0.76936, 3, 2, -4.72, -15.15, 0.12551, 43, 14.42, -13.61, 0.4291, 40, 18.74, 9.58, 0.44539, 3, 2, 2.65, -14.89, 0.09826, 43, 16.85, -6.65, 0.65582, 40, 18.35, 16.94, 0.24592, 3, 2, 9.3, -11.99, 0.17562, 43, 16.56, 0.6, 0.73463, 40, 15.32, 23.54, 0.08975, 3, 2, 14.95, -9.45, 0.24393, 43, 16.25, 6.78, 0.74539, 40, 12.68, 29.14, 0.01068, 3, 2, 18.4, -6.64, 0.056, 43, 14.88, 11.02, 0.944, 40, 9.81, 32.54, 0, 3, 2, 16.33, -2.18, 0.30399, 43, 9.97, 10.71, 0.696, 40, 5.38, 30.38, 1e-05, 3, 2, 14.34, 2.1, 0.59919, 43, 5.26, 10.4, 0.4, 40, 1.14, 28.31, 0.00081, 2, 2, 11.52, 5.57, 0.936, 43, 1.01, 9.04, 0.064, 1, 2, 6.61, 5.4, 1, 1, 2, -16.43, 1.41, 1, 1, 2, -9.43, -0.86, 1, 3, 2, 1.31, -1.86, 0.78285, 43, 4.23, -3.17, 0.15337, 40, 5.35, 15.36, 0.06378, 3, 2, 8.68, -1.41, 0.93775, 43, 6.48, 3.86, 0.06063, 40, 4.76, 22.72, 0.00162, 3, 2, -0.78, -7.87, 0.47193, 43, 9.06, -7.3, 0.28847, 40, 11.39, 13.38, 0.2396, 3, 2, 9.59, -8.78, 0.08276, 43, 13.68, 2.04, 0.91723, 40, 12.11, 23.77, 1e-05, 2, 2, -13.61, 3.23, 0.00768, 40, 0.53, 0.34, 0.99232, 1, 2, 7.32, 1.23, 1, 3, 2, -13.07, -4.23, 0.01995, 43, 1.22, -17.43, 0.00151, 40, 7.98, 1.03, 0.97854, 1, 40, 12.92, -0.34, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32, 6, 34, 34, 36, 36, 38, 38, 40, 40, 28, 16, 42, 42, 38], "width": 43, "height": 24}}, "body2": {"body2": {"type": "mesh", "uvs": [0.54251, 0.0105, 0.30122, 0.02302, 0.07289, 0.05641, 0, 0.21919, 0.021, 0.37572, 0.06251, 0.48841, 0.05473, 0.63032, 0.04176, 0.78893, 0.17408, 0.91415, 0.36868, 0.98511, 0.56846, 0.99345, 0.75527, 0.98302, 0.8824, 0.89537, 0.94467, 0.75554, 0.95246, 0.58858, 0.93689, 0.43624, 1, 0.2985, 1, 0.12737, 0.86684, 0.01885, 0.44651, 0.21085, 0.48543, 0.40284, 0.53992, 0.59902, 0.55289, 0.75554, 0.55289, 0.87867, 0.79678, 0.85571, 0.79938, 0.73258, 0.79159, 0.57815, 0.76824, 0.34858, 0.70078, 0.14198, 0.18187, 0.22545, 0.26489, 0.43206, 0.28305, 0.60528, 0.33495, 0.7785, 0.36349, 0.90997], "triangles": [28, 0, 18, 19, 1, 0, 19, 0, 28, 29, 2, 1, 29, 1, 19, 3, 2, 29, 28, 18, 17, 16, 28, 17, 19, 28, 27, 4, 3, 29, 20, 19, 27, 30, 29, 19, 30, 19, 20, 4, 29, 30, 5, 4, 30, 31, 30, 20, 5, 30, 31, 6, 5, 31, 27, 28, 16, 15, 27, 16, 26, 27, 15, 20, 27, 26, 26, 15, 14, 21, 20, 26, 31, 20, 21, 25, 26, 14, 21, 26, 25, 22, 21, 25, 13, 25, 14, 32, 31, 21, 32, 21, 22, 24, 22, 25, 24, 25, 13, 23, 32, 22, 23, 22, 24, 12, 24, 13, 33, 32, 23, 6, 32, 7, 32, 6, 31, 8, 32, 33, 8, 7, 32, 11, 23, 24, 11, 24, 12, 9, 33, 23, 8, 33, 9, 10, 23, 11, 9, 23, 10], "vertices": [2, 4, 34.04, -2.54, 0.15458, 5, 18.02, -5.52, 0.84542, 3, 3, 42.25, 7.94, 4e-05, 4, 34.2, 6.4, 0.053, 5, 19.41, 3.32, 0.94696, 3, 3, 41.03, 16.44, 0.00192, 4, 33.37, 14.95, 0.00893, 5, 19.76, 11.9, 0.98914, 3, 3, 33.65, 19.41, 0.01228, 4, 26.12, 18.25, 0.01886, 5, 13.05, 16.17, 0.96885, 3, 3, 26.42, 18.9, 0.05173, 4, 18.89, 18.07, 0.06447, 5, 5.85, 16.98, 0.88381, 3, 3, 21.19, 17.56, 0.14379, 4, 13.59, 16.96, 0.13226, 5, 0.46, 16.62, 0.72395, 3, 3, 14.67, 18.09, 0.30114, 4, 7.11, 17.79, 0.18874, 5, -5.85, 18.33, 0.51012, 3, 3, 7.4, 18.84, 0.50686, 4, -0.12, 18.86, 0.18846, 5, -12.86, 20.4, 0.30467, 3, 3, 1.46, 14.16, 0.71295, 4, -6.26, 14.46, 0.13609, 5, -19.56, 16.88, 0.15097, 3, 3, -2.06, 7.09, 0.8704, 4, -10.11, 7.55, 0.06889, 5, -24.32, 10.57, 0.06072, 3, 3, -2.72, -0.29, 0.93253, 4, -11.1, 0.22, 0.04955, 5, -26.31, 3.44, 0.01792, 3, 3, -2.5, -7.21, 0.8812, 4, -11.19, -6.71, 0.1162, 5, -27.35, -3.41, 0.0026, 3, 3, 1.36, -12.06, 0.71298, 4, -7.56, -11.73, 0.2867, 5, -24.45, -8.88, 0.00031, 3, 3, 7.7, -14.6, 0.47551, 4, -1.34, -14.56, 0.5124, 5, -18.68, -12.54, 0.01209, 3, 3, 15.36, -15.17, 0.24422, 4, 6.29, -15.48, 0.6969, 5, -11.25, -14.5, 0.05888, 3, 3, 22.39, -14.86, 0.08906, 4, 13.33, -15.48, 0.74973, 5, -4.28, -15.47, 0.16122, 3, 3, 28.63, -17.43, 0.01919, 4, 19.45, -18.33, 0.66938, 5, 1.39, -19.14, 0.31143, 3, 3, 36.5, -17.72, 0.00178, 4, 27.29, -18.97, 0.50183, 5, 9.07, -20.86, 0.4964, 3, 3, 41.67, -12.98, 0, 4, 32.67, -14.47, 0.31955, 5, 15.02, -17.15, 0.68045, 2, 4, 25.15, 1.75, 0.00024, 5, 9.8, -0.04, 0.99976, 2, 3, 24.54, 1.78, 6e-05, 5, 0.87, 0.49, 0.99994, 1, 4, 7.07, -0.22, 1, 2, 3, 8.24, -0.12, 0.62461, 4, -0.15, -0.11, 0.37539, 2, 3, 2.58, 0.09, 0.99992, 5, -21.03, 2.85, 8e-05, 2, 3, 3.3, -8.96, 0.80106, 4, -5.48, -8.73, 0.19894, 2, 3, 8.95, -9.27, 0.44106, 4, 0.16, -9.29, 0.55894, 3, 3, 16.06, -9.24, 0.0885, 4, 7.26, -9.58, 0.91036, 5, -9.48, -8.8, 0.00114, 3, 3, 26.65, -8.77, 5e-05, 4, 17.86, -9.59, 0.74674, 5, 1.02, -10.27, 0.2532, 2, 4, 27.53, -7.88, 0.27412, 5, 10.84, -9.91, 0.72588, 2, 3, 33.11, 12.7, 0.00023, 5, 11.29, 9.66, 0.99977, 3, 3, 23.5, 9.98, 0.02264, 4, 15.56, 9.29, 0.07526, 5, 1.35, 8.75, 0.9021, 3, 3, 15.51, 9.6, 0.15173, 4, 7.56, 9.27, 0.4347, 5, -6.58, 9.83, 0.41356, 3, 3, 7.48, 7.98, 0.60661, 4, -0.53, 8.01, 0.29141, 5, -14.77, 9.71, 0.10198, 3, 3, 1.4, 7.15, 0.91974, 4, -6.65, 7.46, 0.04526, 5, -20.9, 10, 0.035], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 37, "height": 46}}, "ear": {"ear": {"type": "mesh", "uvs": [0, 0.06239, 0.07359, 0.1489, 0.12128, 0.26713, 0.17215, 0.39113, 0.23892, 0.52667, 0.31205, 0.65355, 0.3979, 0.77467, 0.51554, 0.90155, 0.63318, 0.97076, 0.76672, 1, 0.82077, 0.92462, 0.81123, 0.81216, 0.93841, 0.75736, 1, 0.61318, 0.91933, 0.51513, 0.81441, 0.40555, 0.68087, 0.27578, 0.53779, 0.18639, 0.42015, 0.1143, 0.26754, 0.05086, 0.134, 0.0076, 0, 0, 0.14672, 0.12583, 0.25164, 0.20658, 0.34703, 0.35941, 0.48692, 0.5209, 0.59185, 0.63048, 0.70631, 0.70257, 0.77625, 0.7689], "triangles": [0, 21, 20, 22, 20, 19, 0, 20, 22, 1, 0, 22, 23, 22, 19, 2, 1, 22, 2, 22, 23, 23, 19, 18, 24, 23, 18, 24, 18, 17, 3, 2, 23, 3, 23, 24, 16, 25, 24, 16, 24, 17, 25, 16, 15, 4, 3, 24, 4, 24, 25, 5, 4, 25, 26, 25, 15, 27, 26, 15, 14, 27, 15, 26, 6, 5, 26, 5, 25, 7, 6, 26, 7, 26, 27, 27, 14, 13, 12, 27, 13, 28, 27, 12, 11, 28, 12, 7, 27, 28, 8, 7, 28, 8, 28, 11, 8, 11, 10, 9, 8, 10], "vertices": [2, 10, 18.16, 4.49, 0.00138, 11, 10.94, 0.6, 0.99862, 3, 9, 22.77, 3.36, 0.00116, 10, 13.48, 4.14, 0.14965, 11, 6.44, 1.96, 0.84919, 4, 8, 29.55, 1.96, 1e-05, 9, 17.59, 4.91, 0.08768, 10, 8.21, 5.39, 0.38231, 11, 1.97, 5.01, 0.53001, 4, 8, 24.43, 4.43, 0.03031, 9, 12.12, 6.5, 0.35476, 10, 2.66, 6.67, 0.41687, 11, -2.74, 8.2, 0.19806, 5, 7, 23.6, 5.4, 0.00041, 8, 18.51, 6.82, 0.21324, 9, 5.89, 7.89, 0.50356, 10, -3.64, 7.7, 0.26863, 11, -8.25, 11.43, 0.01415, 4, 7, 17.92, 7.79, 0.03011, 8, 12.67, 8.78, 0.49378, 9, -0.19, 8.87, 0.44013, 10, -9.76, 8.32, 0.03598, 4, 7, 12.02, 9.68, 0.19993, 8, 6.65, 10.21, 0.62699, 9, -6.36, 9.29, 0.17305, 10, -15.95, 8.39, 3e-05, 3, 7, 5.01, 10.96, 0.4835, 8, -0.44, 10.96, 0.49342, 9, -13.48, 8.87, 0.02308, 2, 7, -0.42, 10.33, 0.78343, 8, -5.81, 9.92, 0.21657, 2, 7, -5.24, 7.98, 0.94695, 8, -10.44, 7.22, 0.05305, 2, 7, -4.8, 4.14, 0.9963, 8, -9.71, 3.42, 0.0037, 2, 7, -1.43, 0.65, 0.98929, 8, -6.09, 0.19, 0.01071, 3, 7, -3.75, -4.32, 0.91519, 8, -8.03, -4.95, 0.08299, 9, -18.36, -8.06, 0.00182, 3, 7, -1.66, -10.63, 0.74945, 8, -5.46, -11.08, 0.23231, 9, -14.82, -13.69, 0.01825, 4, 7, 3.45, -11.88, 0.47382, 8, -0.27, -11.93, 0.42375, 9, -9.56, -13.68, 0.1012, 10, -17.82, -14.73, 0.00123, 4, 7, 9.61, -12.91, 0.21675, 8, 5.95, -12.49, 0.44507, 9, -3.33, -13.22, 0.30189, 10, -11.64, -13.91, 0.03629, 5, 7, 17.18, -13.89, 0.04917, 8, 13.57, -12.9, 0.31065, 9, 4.25, -12.37, 0.44737, 10, -4.11, -12.63, 0.1887, 11, -16, -7.37, 0.00411, 5, 7, 23.93, -13.3, 0.00217, 8, 20.25, -11.8, 0.10892, 9, 10.67, -10.19, 0.4119, 10, 2.17, -10.09, 0.40573, 11, -9.23, -7.25, 0.07129, 4, 8, 25.71, -10.86, 0.01532, 9, 15.89, -8.37, 0.21034, 10, 7.28, -7.96, 0.40633, 11, -3.69, -7.11, 0.36801, 4, 8, 31.89, -8.69, 0.00041, 9, 21.64, -5.22, 0.04844, 10, 12.83, -4.49, 0.25392, 11, 2.74, -5.87, 0.69723, 3, 9, 26.24, -2.15, 0.00096, 10, 17.25, -1.17, 0.03566, 11, 8.06, -4.36, 0.96338, 1, 11, 12.5, -1.58, 1, 1, 11, 4.7, -0.51, 1, 3, 9, 16.64, -0.72, 0.0008, 10, 7.58, -0.29, 0.96923, 11, -0.65, -0.06, 0.02997, 2, 9, 9.14, 0.22, 0.45052, 10, 0.05, 0.22, 0.54948, 2, 8, 11.71, -0.06, 0.19145, 9, 0.32, -0.01, 0.80855, 1, 8, 5.5, 0.5, 1, 3, 7, 4.73, -0.37, 0.37962, 8, 0.13, -0.37, 0.62032, 9, -11.06, -2.21, 6e-05, 1, 7, 0.81, 0.09, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 39, "height": 43}}, "ear2": {"ear2": {"type": "mesh", "uvs": [0.96334, 0, 0.69629, 0.06904, 0.49755, 0.21967, 0.30503, 0.3678, 0.14355, 0.53601, 0.03176, 0.71427, 0.09387, 0.88499, 0.40439, 1, 0.69629, 0.90005, 0.89502, 0.77452, 0.96955, 0.60631, 0.98818, 0.45065, 1, 0.30001, 0.98818, 0.16193, 0.78324, 0.15691, 0.74597, 0.26738, 0.7025, 0.42052, 0.62176, 0.55359, 0.52861, 0.64648, 0.41061, 0.78457, 0.28019, 0.8875], "triangles": [15, 3, 2, 15, 13, 12, 15, 14, 13, 15, 2, 14, 2, 1, 14, 13, 1, 0, 13, 14, 1, 17, 3, 16, 11, 16, 12, 16, 3, 15, 16, 15, 12, 10, 18, 17, 10, 17, 11, 4, 3, 17, 17, 16, 11, 6, 20, 7, 7, 20, 8, 20, 19, 8, 8, 19, 9, 20, 6, 19, 6, 5, 19, 19, 18, 9, 19, 5, 18, 9, 18, 10, 5, 4, 18, 18, 4, 17], "vertices": [2, 14, 20.76, -3, 0.00087, 15, 13.24, -1.4, 0.99913, 3, 12, 38.59, 11.16, 5e-05, 14, 17.03, 1.72, 0.07992, 15, 8.96, 2.84, 0.92003, 4, 12, 30.57, 10.99, 0.02501, 13, 19.66, 6.19, 0.02591, 14, 9.61, 4.78, 0.35406, 15, 1.22, 4.96, 0.59503, 4, 12, 22.71, 10.78, 0.23659, 13, 12.01, 8.02, 0.08517, 14, 2.32, 7.72, 0.41655, 15, -6.38, 7, 0.26169, 4, 12, 14.31, 9.59, 0.56778, 13, 3.59, 9.05, 0.08517, 14, -5.86, 9.99, 0.33872, 15, -14.76, 8.25, 0.00833, 3, 12, 5.97, 7.35, 0.87616, 13, -5.05, 9.05, 0.05926, 14, -14.4, 11.27, 0.06458, 3, 12, -0.46, 2.4, 0.99701, 13, -12.54, 5.93, 0.00089, 14, -22.27, 9.3, 0.0021, 2, 12, -2.29, -5.38, 0.9502, 13, -16.33, -1.12, 0.0498, 2, 12, 4.52, -7.93, 0.72356, 13, -10.41, -5.34, 0.27644, 2, 12, 11.51, -8.34, 0.39112, 13, -3.76, -7.55, 0.60888, 3, 12, 19.1, -5.71, 0.10669, 13, 4.25, -6.98, 0.81206, 14, -7.58, -5.96, 0.08125, 3, 13, 11.43, -5.52, 0.59248, 14, -0.27, -5.58, 0.30221, 15, -7.31, -6.52, 0.10531, 3, 13, 18.34, -4, 0.25915, 14, 6.8, -5.1, 0.30309, 15, -0.36, -5.18, 0.43777, 3, 13, 24.58, -2.18, 0.00706, 14, 13.24, -4.23, 0.22184, 15, 5.92, -3.53, 0.7711, 1, 15, 5.29, 0.32, 1, 2, 14, 7.85, -0.14, 0.42215, 15, 0.07, -0.14, 0.57785, 1, 14, 0.6, -0.04, 1, 3, 12, 18.03, 1.26, 0.0006, 13, 5.03, 0.04, 0.99913, 14, -5.77, 0.87, 0.00027, 2, 12, 13.36, 0.67, 0.99829, 14, -10.29, 2.19, 0.00171, 2, 12, 6.6, -0.54, 0.99163, 13, -6.48, 1.26, 0.00837, 2, 12, 1.17, -0.74, 0.99931, 13, -11.78, 2.47, 0.00069], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 19, "height": 47}}, "eye_close": {"eye_close": {"x": 18.99, "y": -12.42, "rotation": -69.11, "width": 7, "height": 9}}, "eye_close2": {"eye_close2": {"x": 13.86, "y": 3.82, "rotation": -69.11, "width": 11, "height": 11}}, "hair": {"hair": {"type": "mesh", "uvs": [0.01343, 0, 0.05215, 0.09313, 0.06185, 0.19555, 0.02585, 0.31358, 0.04985, 0.45293, 0.12585, 0.59555, 0.21785, 0.72506, 0.41785, 0.84309, 0.63785, 0.9267, 0.86185, 1, 0.96585, 0.90211, 1, 0.79719, 1, 0.67096, 1, 0.53981, 0.98185, 0.4103, 0.88585, 0.27752, 0.68985, 0.16276, 0.44985, 0.06276, 0.25785, 0.00375, 0.21785, 0.08735, 0.33385, 0.14473, 0.43385, 0.28407, 0.48985, 0.39555, 0.54185, 0.49719, 0.60185, 0.61358, 0.65785, 0.73653, 0.66185, 0.8267, 0.80163, 0.92163], "triangles": [20, 17, 16, 21, 20, 16, 21, 16, 15, 2, 20, 21, 3, 2, 21, 19, 0, 18, 19, 18, 17, 1, 0, 19, 20, 19, 17, 2, 1, 19, 2, 19, 20, 22, 21, 15, 22, 15, 14, 22, 4, 3, 22, 3, 21, 23, 22, 14, 4, 22, 23, 23, 14, 13, 5, 4, 23, 24, 23, 13, 5, 23, 24, 24, 13, 12, 6, 5, 24, 25, 24, 12, 6, 24, 25, 25, 12, 11, 26, 25, 11, 7, 6, 25, 7, 25, 26, 10, 26, 11, 27, 26, 10, 8, 7, 26, 8, 26, 27, 9, 27, 10, 8, 27, 9], "vertices": [3, 28, 32.76, 6.9, 4e-05, 30, 11.76, -0.15, 0.98621, 29, 20.08, 4.71, 0.01375, 3, 28, 27, 7.14, 0.02573, 30, 6.93, 3, 0.76165, 29, 14.39, 5.59, 0.21261, 4, 27, 34.45, 9.13, 0.00083, 28, 20.84, 8.21, 0.20315, 30, 2.18, 7.06, 0.43164, 29, 8.38, 7.33, 0.36438, 4, 27, 27.5, 11.21, 0.05116, 28, 13.99, 10.6, 0.46663, 30, -2.5, 12.61, 0.11211, 29, 1.84, 10.46, 0.3701, 5, 26, 27, 13.53, 0.0026, 27, 19.01, 12.03, 0.28054, 28, 5.55, 11.8, 0.54229, 30, -9.15, 17.95, 0.00332, 29, -6.41, 12.58, 0.17125, 4, 26, 18.17, 12.38, 0.07932, 27, 10.12, 11.6, 0.52786, 28, -3.35, 11.76, 0.37334, 29, -15.27, 13.53, 0.01948, 3, 26, 10.1, 10.77, 0.38098, 27, 1.95, 10.65, 0.50921, 28, -11.56, 11.17, 0.10982, 3, 26, 2.5, 6.41, 0.71171, 27, -5.98, 6.91, 0.27983, 28, -19.65, 7.79, 0.00846, 2, 26, -3.05, 1.37, 0.96832, 27, -11.93, 2.34, 0.03168, 2, 26, -7.99, -3.83, 0.99971, 27, -17.27, -2.44, 0.00029, 2, 26, -2.26, -6.93, 0.953, 27, -11.81, -6, 0.047, 3, 26, 4.04, -8.33, 0.68887, 27, -5.64, -7.9, 0.30929, 28, -19.96, -7.03, 0.00184, 3, 26, 11.71, -8.99, 0.35607, 27, 1.95, -9.18, 0.56341, 28, -12.43, -8.64, 0.08053, 4, 26, 19.68, -9.68, 0.06944, 27, 9.84, -10.51, 0.58596, 28, -4.61, -10.32, 0.34013, 29, -18.95, -8.28, 0.00447, 4, 26, 27.59, -9.91, 0.00024, 27, 17.71, -11.38, 0.32501, 28, 3.21, -11.53, 0.57988, 29, -11.32, -10.34, 0.09487, 4, 27, 26.09, -10.36, 0.0706, 28, 11.63, -10.88, 0.53824, 30, -15.48, -4.66, 0.0063, 29, -2.87, -10.63, 0.38486, 4, 27, 33.81, -6.69, 0.00134, 28, 19.51, -7.56, 0.27864, 30, -7.02, -5.82, 0.26784, 29, 5.32, -8.19, 0.45218, 3, 28, 26.73, -2.97, 0.03705, 30, 1.54, -5.56, 0.60117, 29, 13, -4.43, 0.36178, 2, 30, 7.44, -4.48, 0.92821, 29, 17.93, -1.01, 0.07179, 1, 30, 4.37, -0.28, 1, 2, 30, -0.17, -0.03, 0.38015, 29, 9.18, -0.1, 0.61985, 2, 28, 13.61, 0.25, 0.26304, 29, 0.32, 0.22, 0.73696, 3, 27, 20.64, 0.6, 0.00019, 28, 6.67, 0.31, 0.99967, 29, -6.57, 1.04, 0.00014, 2, 27, 14.31, 0.35, 0.16621, 28, 0.33, 0.34, 0.83379, 2, 27, 7.06, 0.05, 0.99995, 28, -6.92, 0.36, 5e-05, 2, 26, 8.46, -0.13, 0.95354, 27, -0.57, -0.08, 0.04646, 2, 26, 2.97, 0.24, 0.99967, 27, -6.01, 0.73, 0.00033, 1, 26, -3.1, -2.74, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 25, "height": 61}}, "hair2": {"hair2": {"type": "mesh", "uvs": [0.37113, 0.01009, 0.37113, 0.09957, 0.22758, 0.20047, 0.13353, 0.30138, 0.05433, 0.43274, 0.01473, 0.58695, 0.04938, 0.72593, 0.16818, 0.86682, 0.41073, 0.98295, 0.68297, 0.88966, 0.83642, 0.76782, 0.95522, 0.6098, 0.99977, 0.46701, 0.97997, 0.3147, 0.88592, 0.18334, 0.72257, 0.07672, 0.53943, 0, 0.52953, 0.19286, 0.55923, 0.32993, 0.54932, 0.44416, 0.50477, 0.57362, 0.48002, 0.67262, 0.43547, 0.76591, 0.37607, 0.86872], "triangles": [3, 2, 18, 2, 17, 18, 18, 17, 14, 2, 1, 17, 17, 15, 14, 17, 1, 15, 1, 16, 15, 1, 0, 16, 12, 20, 19, 20, 4, 19, 12, 19, 13, 19, 18, 13, 4, 3, 19, 19, 3, 18, 18, 14, 13, 9, 22, 10, 7, 6, 22, 22, 21, 10, 10, 21, 11, 22, 6, 21, 6, 5, 21, 21, 20, 11, 21, 5, 20, 11, 20, 12, 5, 4, 20, 7, 23, 8, 8, 23, 9, 23, 22, 9, 23, 7, 22], "vertices": [1, 34, 16.52, 2.65, 1, 2, 33, 23.72, 5.23, 0.01475, 34, 11.88, 3.03, 0.98525, 3, 32, 29.33, 7.76, 0.00025, 33, 18.19, 7.51, 0.14372, 34, 6.89, 6.31, 0.85603, 3, 32, 23.89, 8.97, 0.03721, 33, 12.77, 8.8, 0.3982, 34, 1.81, 8.61, 0.56459, 4, 31, 24.43, 10.27, 0.00164, 32, 16.92, 9.7, 0.26312, 33, 5.8, 9.62, 0.48852, 34, -4.87, 10.74, 0.24673, 4, 31, 16.38, 9.79, 0.08972, 32, 8.86, 9.49, 0.50304, 33, -2.25, 9.52, 0.36463, 34, -12.8, 12.18, 0.0426, 4, 31, 9.36, 7.96, 0.41526, 32, 1.78, 7.9, 0.47388, 33, -9.36, 8.03, 0.11015, 34, -20.06, 12.07, 0.00072, 3, 31, 2.5, 4.46, 0.74695, 32, -5.2, 4.64, 0.24797, 33, -16.38, 4.86, 0.00507, 2, 31, -2.7, -1.29, 0.98495, 32, -10.59, -0.92, 0.01505, 3, 31, 2.95, -5.9, 0.80197, 32, -5.1, -5.73, 0.19167, 33, -16.42, -5.5, 0.00635, 3, 31, 9.69, -7.93, 0.47615, 32, 1.57, -7.99, 0.35666, 33, -9.79, -7.85, 0.16719, 4, 31, 18.18, -8.97, 0.15007, 32, 10.02, -9.32, 0.36528, 33, -1.36, -9.31, 0.48272, 34, -15.5, -6.47, 0.00193, 4, 31, 25.65, -8.68, 0.00751, 32, 17.5, -9.29, 0.18086, 33, 6.12, -9.37, 0.66854, 34, -8.18, -7.96, 0.14309, 3, 32, 25.31, -7.91, 0.01587, 33, 13.95, -8.1, 0.51831, 34, -0.25, -8.21, 0.46583, 2, 33, 20.53, -5.48, 0.20278, 34, 6.71, -6.88, 0.79722, 2, 33, 25.68, -1.62, 0.0106, 34, 12.5, -4.08, 0.9894, 1, 34, 16.77, -0.75, 1, 2, 33, 19.25, 1.55, 0, 34, 6.79, 0.26, 1, 2, 33, 12.23, 0.17, 0.87022, 34, -0.36, 0.25, 0.12978, 1, 33, 6.31, -0.29, 1, 3, 31, 18.61, 0.22, 2e-05, 32, 10.77, -0.15, 0.98546, 33, -0.48, -0.14, 0.01453, 3, 31, 13.45, -0.11, 0.00188, 32, 5.6, -0.3, 0.99781, 33, -5.65, -0.22, 0.00031, 1, 32, 0.67, -0.02, 1, 1, 31, 3.06, 0.34, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 20, "height": 52}}, "hair3": {"hair3": {"type": "mesh", "uvs": [0.0194, 0.02879, 0.02946, 0.16688, 0.06297, 0.31625, 0.11659, 0.47124, 0.18362, 0.61497, 0.28751, 0.76715, 0.44838, 0.89961, 0.60254, 0.97288, 0.78016, 1, 0.874, 0.94188, 0.99464, 0.8517, 0.998, 0.69952, 0.95778, 0.52479, 0.90416, 0.37825, 0.77346, 0.24579, 0.626, 0.13306, 0.44167, 0.05979, 0.28081, 0.00906, 0.15011, 0, 0.16016, 0.09925, 0.30092, 0.22607, 0.36794, 0.29934, 0.43162, 0.3867, 0.49865, 0.5107, 0.6193, 0.64315, 0.72319, 0.76997, 0.82038, 0.87425], "triangles": [19, 18, 17, 0, 18, 19, 1, 0, 19, 19, 17, 20, 20, 1, 19, 20, 17, 16, 21, 20, 16, 21, 16, 15, 20, 2, 1, 21, 2, 20, 22, 21, 15, 22, 15, 14, 21, 3, 2, 3, 21, 22, 23, 22, 14, 4, 3, 22, 4, 22, 23, 5, 4, 23, 23, 14, 13, 24, 23, 13, 12, 24, 13, 25, 24, 12, 5, 23, 24, 11, 25, 12, 6, 5, 24, 6, 24, 25, 11, 26, 25, 10, 26, 11, 9, 26, 10, 7, 6, 25, 7, 25, 26, 8, 7, 26, 8, 26, 9], "vertices": [3, 23, 27.39, 4.63, 0.00034, 24, 16.79, 3.68, 0.0517, 25, 8.75, -0.75, 0.94796, 4, 22, 36.54, 5.49, 0.00028, 23, 21.99, 7.44, 0.04727, 24, 11.56, 6.79, 0.23407, 25, 5.74, 4.54, 0.71838, 5, 21, 36.78, 5.66, 0.00151, 22, 30.5, 8.35, 0.02303, 23, 15.72, 9.76, 0.23856, 24, 5.43, 9.46, 0.33503, 25, 1.74, 9.9, 0.40188, 5, 21, 30.3, 8.55, 0.01793, 22, 23.81, 10.75, 0.13324, 23, 8.85, 11.57, 0.42473, 24, -1.33, 11.66, 0.30374, 25, -3.04, 15.15, 0.12036, 5, 21, 23.87, 10.74, 0.09455, 22, 17.24, 12.46, 0.32764, 23, 2.15, 12.69, 0.4395, 24, -7.95, 13.17, 0.12064, 25, -8.05, 19.74, 0.01768, 5, 21, 16.27, 12.13, 0.31398, 22, 9.56, 13.28, 0.41359, 23, -5.57, 12.84, 0.2519, 24, -15.65, 13.76, 0.01968, 25, -14.45, 24.06, 0.00085, 4, 21, 7.98, 11.35, 0.6189, 22, 1.35, 11.89, 0.31537, 23, -13.63, 10.74, 0.06538, 24, -23.82, 12.12, 0.00034, 3, 21, 1.83, 9.08, 0.87562, 22, -4.61, 9.17, 0.12069, 23, -19.33, 7.5, 0.00368, 2, 21, -3.32, 4.84, 0.98738, 22, -9.44, 4.55, 0.01262, 2, 21, -3.61, 0.53, 0.93589, 22, -9.41, 0.24, 0.06411, 3, 21, -3.47, -5.44, 0.70746, 22, -8.82, -5.71, 0.29235, 23, -22.22, -7.68, 0.00019, 4, 21, 1.56, -9.86, 0.37907, 22, -3.48, -9.74, 0.60085, 23, -16.54, -11.23, 0.01864, 24, -27.98, -9.64, 0.00144, 4, 21, 8.39, -13.69, 0.10922, 22, 3.62, -13.05, 0.7861, 23, -9.18, -13.91, 0.08962, 24, -20.79, -12.74, 0.01506, 4, 21, 14.6, -16.34, 0.00432, 22, 10, -15.24, 0.68307, 23, -2.63, -15.53, 0.23304, 24, -14.35, -14.73, 0.07958, 3, 22, 17.56, -14.75, 0.40526, 23, 4.85, -14.38, 0.3377, 24, -6.81, -14.01, 0.25704, 4, 22, 24.78, -13.25, 0.15832, 23, 11.91, -12.25, 0.29207, 24, 0.36, -12.29, 0.53758, 25, -13.43, -6.5, 0.01202, 4, 22, 31.39, -9.62, 0.03312, 23, 18.19, -8.06, 0.14847, 24, 6.87, -8.47, 0.6025, 25, -5.88, -6.4, 0.21592, 4, 22, 36.7, -6.12, 0.00179, 23, 23.16, -4.11, 0.02535, 24, 12.06, -4.81, 0.42468, 25, 0.44, -5.78, 0.54818, 3, 23, 25.99, -0.17, 0, 24, 15.11, -1.03, 0.13051, 25, 4.96, -4.02, 0.86949, 1, 25, 2.71, -0.26, 1, 2, 22, 28.53, -1.11, 0, 24, 3.75, -0.05, 1, 3, 22, 24.47, -1.22, 0.00026, 23, 10.55, -0.3, 0.81829, 24, -0.31, -0.28, 0.18146, 3, 22, 19.97, -0.87, 0.00123, 23, 6.04, -0.34, 0.9965, 24, -4.81, -0.07, 0.00227, 3, 21, 19.84, -1.12, 0.00052, 22, 14.1, 0.33, 0.39165, 23, 0.09, 0.34, 0.60783, 2, 21, 12.51, -0.76, 0.00162, 22, 6.76, 0.14, 0.99838, 2, 21, 5.76, -0.09, 0.38174, 22, -0.01, 0.31, 0.61826, 1, 21, -0.06, 0.13, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 37, "height": 44}}, "hair4": {"hair4": {"type": "mesh", "uvs": [0.05748, 0.01464, 0.03748, 0.1593, 0.04148, 0.33054, 0.08548, 0.51359, 0.22548, 0.68187, 0.39748, 0.80587, 0.60548, 0.91216, 0.85348, 1, 1, 0.8974, 0.99348, 0.72616, 0.97748, 0.57559, 0.93748, 0.44273, 0.82148, 0.32464, 0.66148, 0.25083, 0.47748, 0.17702, 0.29748, 0.10321, 0.19348, 0.04416, 0.12148, 0, 0.16548, 0.13273, 0.20548, 0.23607, 0.30148, 0.3335, 0.39348, 0.4634, 0.51348, 0.56378, 0.63748, 0.69368, 0.80548, 0.82654], "triangles": [19, 1, 18, 19, 2, 1, 19, 15, 14, 19, 18, 15, 1, 0, 18, 18, 0, 16, 18, 16, 15, 16, 0, 17, 4, 3, 21, 3, 20, 21, 3, 2, 20, 13, 20, 14, 13, 21, 20, 2, 19, 20, 20, 19, 14, 5, 22, 23, 5, 4, 22, 4, 21, 22, 23, 22, 11, 22, 12, 11, 22, 13, 12, 22, 21, 13, 6, 24, 7, 7, 24, 8, 6, 23, 24, 6, 5, 23, 24, 9, 8, 24, 23, 9, 23, 10, 9, 10, 23, 11], "vertices": [2, 19, 21.61, -1.33, 0.00431, 20, 10.51, 1.36, 0.99569, 3, 18, 28.93, 0.83, 0.00083, 19, 16.85, 2.5, 0.25839, 20, 4.94, 3.87, 0.74077, 4, 17, 35.93, 6.39, 0.00029, 18, 23.23, 5.23, 0.07818, 19, 10.75, 6.31, 0.51402, 20, -1.92, 6.02, 0.40751, 4, 17, 28.9, 9.78, 0.03149, 18, 16.38, 8.96, 0.32988, 19, 3.56, 9.36, 0.56013, 20, -9.64, 7.16, 0.07849, 4, 17, 20.62, 10.39, 0.21209, 18, 8.15, 9.99, 0.48149, 19, -4.73, 9.57, 0.30635, 20, -17.72, 5.28, 7e-05, 3, 17, 13.28, 9.11, 0.54202, 18, 0.75, 9.08, 0.40726, 19, -12, 7.94, 0.05072, 3, 17, 5.89, 6.48, 0.84415, 18, -6.77, 6.83, 0.15555, 19, -19.26, 4.97, 0.00029, 2, 17, -1.6, 2.4, 0.99689, 18, -14.45, 3.14, 0.00311, 2, 17, -0.76, -3.8, 0.97466, 18, -13.92, -3.1, 0.02534, 3, 17, 5.2, -7.84, 0.82101, 18, -8.18, -7.44, 0.17748, 19, -19.26, -9.38, 0.0015, 3, 17, 10.62, -11.14, 0.55898, 18, -2.93, -11, 0.42326, 19, -13.69, -12.41, 0.01776, 3, 17, 15.87, -13.39, 0.26724, 18, 2.2, -13.52, 0.65311, 19, -8.33, -14.41, 0.07965, 4, 17, 22, -13.37, 0.08842, 18, 8.32, -13.81, 0.67833, 19, -2.22, -14.1, 0.22845, 20, -9.33, -16.99, 0.0048, 4, 17, 27.41, -11.16, 0.01712, 18, 13.84, -11.87, 0.48043, 19, 3.09, -11.63, 0.42759, 20, -4.82, -13.27, 0.07486, 4, 17, 33.26, -8.35, 0.00087, 18, 19.82, -9.36, 0.22711, 19, 8.79, -8.53, 0.41111, 20, -0.07, -8.84, 0.36092, 3, 18, 25.73, -6.94, 0.04974, 19, 14.43, -5.54, 0.26114, 20, 4.63, -4.52, 0.68911, 3, 18, 29.68, -5.97, 0.00187, 19, 18.27, -4.19, 0.04575, 20, 8, -2.25, 0.95239, 2, 19, 21.04, -3.33, 0.00034, 20, 10.47, -0.72, 0.99966, 3, 18, 27.32, -2.97, 1e-05, 19, 15.62, -1.44, 0.00129, 20, 4.75, -0.25, 0.99871, 3, 18, 23.15, -1.22, 6e-05, 19, 11.31, -0.11, 0.28127, 20, 0.24, -0.05, 0.71867, 3, 18, 18.1, -0.99, 0.00264, 19, 6.26, -0.38, 0.99591, 20, -4.58, -1.58, 0.00145, 3, 17, 25.02, 0.8, 2e-05, 18, 12.06, 0.19, 0.36221, 19, 0.13, 0.21, 0.63777, 2, 18, 6.44, -0.08, 0.99991, 19, -5.43, -0.62, 9e-05, 2, 17, 12.76, 0.32, 0.849, 18, -0.22, 0.33, 0.151, 1, 17, 5.18, -0.65, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 31, "height": 42}}, "hair5": {"hair5": {"x": -0.13, "y": -5.8, "rotation": -69.11, "width": 15, "height": 14}}, "hair6": {"hair6": {"x": 24.2, "y": 3.96, "rotation": 67.03, "width": 15, "height": 15}}, "hair7": {"hair7": {"x": -1.05, "y": 12.04, "rotation": -69.11, "width": 19, "height": 21}}, "hand_L": {"hand_L": {"x": 18.83, "y": 1.03, "rotation": 79.53, "width": 24, "height": 18}}, "hand_L2": {"hand_L2": {"x": 6.8, "y": -0.81, "rotation": 79.53, "width": 32, "height": 35}}, "hand_L3": {"hand_L3": {"x": 17.64, "y": 1.31, "rotation": 67.03, "width": 29, "height": 39}}, "hand_R": {"hand_R": {"x": 10.43, "y": -0.89, "rotation": 90.37, "width": 30, "height": 39}}, "hand_R2": {"hand_R2": {"x": 16.4, "y": -2.02, "rotation": 112.26, "width": 33, "height": 38}}, "head": {"head": {"x": 17.09, "y": 2.37, "rotation": -69.11, "width": 44, "height": 44}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.56455, 0.14005, 0.37281, 0.18742, 0.12238, 0.18268, 0.09499, 0.45268, 0.04803, 0.70847, 0.02064, 0.92636, 0.18108, 1, 0.33368, 0.83163, 0.55281, 0.71321, 0.77977, 0.6611, 0.99499, 0.63742, 0.98716, 0.44321, 0.99499, 0.2111, 0.97933, 0, 0.74455, 0.069, 0.49292, 0.31754, 0.37624, 0.51754, 0.22067, 0.7939, 0.80222, 0.5093, 0.82584, 0.2623], "triangles": [6, 5, 17, 18, 11, 10, 18, 9, 8, 9, 18, 10, 8, 7, 16, 6, 17, 7, 18, 8, 15, 18, 19, 11, 18, 15, 19, 19, 14, 13, 12, 19, 13, 15, 1, 0, 11, 19, 12, 19, 0, 14, 19, 15, 0, 16, 1, 15, 8, 16, 15, 3, 1, 16, 1, 3, 2, 7, 17, 16, 17, 3, 16, 4, 3, 17, 5, 4, 17], "vertices": [1, 44, 0.11, -0.29, 1, 1, 44, 3.31, -3.46, 1, 1, 44, 6.43, -8.3, 1, 1, 44, 11.05, -5.98, 1, 2, 44, 15.69, -4.18, 0.66667, 45, -2.48, 3.84, 0.33333, 2, 44, 19.48, -2.41, 0.33333, 45, -3.11, -0.3, 0.66667, 1, 45, 0.58, -1.7, 1, 1, 45, 4.09, 1.5, 1, 1, 45, 9.13, 3.75, 1, 1, 45, 14.35, 4.74, 1, 1, 45, 19.3, 5.19, 1, 2, 44, -0.49, 11, 0.35733, 45, 19.12, 8.88, 0.64267, 2, 44, -4.26, 8.7, 0.65867, 45, 19.3, 13.29, 0.34133, 2, 44, -7.39, 6.18, 0.728, 45, 18.94, 17.3, 0.272, 2, 44, -3.31, 2.41, 0.88, 45, 13.54, 15.99, 0.12, 2, 44, 3.83, 0.21, 0.88, 45, 7.75, 11.27, 0.12, 2, 44, 8.48, 0.09, 0.99999, 45, 5.07, 7.47, 1e-05, 2, 44, 14.84, 0.02, 0.99898, 45, 1.49, 2.22, 0.00102, 2, 44, 2.92, 8.15, 0.08, 45, 14.87, 7.63, 0.92, 2, 44, -1.29, 6, 0.496, 45, 15.41, 12.32, 0.504], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 23, "height": 19}}, "leg_L2": {"leg_L2": {"x": 12.42, "y": 1.8, "width": 29, "height": 14}}, "leg_L3": {"leg_L3": {"type": "mesh", "uvs": [0.35572, 0.17905, 0.1839, 0.29905, 0, 0.51333, 0.06935, 0.72333, 0.12663, 0.92905, 0.42117, 0.98905, 0.74435, 0.9419, 0.93662, 0.74476, 0.99799, 0.44048, 0.95299, 0.26905, 0.91617, 0.08905, 0.75253, 0, 0.57662, 0.06334, 0.45779, 0.38913, 0.55644, 0.65037, 0.45044, 0.84772, 0.78164, 0.75706, 0.7975, 0.47846, 0.70289, 0.1915, 0.2288, 0.4453, 0.26603, 0.67237, 0.27041, 0.83093], "triangles": [6, 16, 7, 6, 15, 16, 7, 16, 17, 16, 14, 17, 7, 17, 8, 20, 19, 13, 14, 13, 17, 2, 1, 19, 13, 18, 17, 17, 9, 8, 17, 18, 9, 19, 1, 13, 1, 0, 13, 13, 12, 18, 13, 0, 12, 18, 10, 9, 18, 11, 10, 18, 12, 11, 4, 21, 5, 5, 15, 6, 5, 21, 15, 16, 15, 14, 4, 3, 21, 14, 15, 20, 3, 20, 21, 15, 21, 20, 3, 2, 20, 2, 19, 20, 20, 13, 14], "vertices": [2, 44, -5.82, -9.24, 0.06502, 43, 8.89, -0.78, 0.93498, 2, 44, -1.63, -10.99, 0.24695, 43, 9.86, -5.22, 0.75305, 2, 44, 4.36, -11.86, 0.50916, 43, 12.57, -10.63, 0.49084, 2, 44, 7.19, -8.14, 0.7684, 43, 17.24, -10.82, 0.2316, 2, 44, 10.08, -4.7, 0.91468, 43, 21.72, -11.22, 0.08532, 2, 44, 7.54, 1.39, 0.71648, 43, 25.26, -5.64, 0.28352, 2, 44, 2.77, 6.76, 0.39391, 43, 26.93, 1.34, 0.60609, 2, 44, -3.02, 7.98, 0.06571, 43, 24.61, 6.79, 0.93429, 1, 43, 19.16, 10.37, 1, 1, 43, 15.44, 10.76, 1, 1, 43, 11.63, 11.39, 1, 1, 43, 8.57, 8.72, 1, 2, 44, -10.54, -6.54, 0.00169, 43, 8.4, 4.63, 0.99831, 2, 44, -3.39, -4.92, 0.00537, 43, 13.82, -0.3, 0.99463, 1, 44, -0.03, -0.07, 1, 2, 44, 4.71, 0.28, 0.99215, 43, 22.73, -3.96, 0.00785, 2, 44, -0.92, 5.29, 0.0152, 43, 23.61, 3.52, 0.9848, 1, 43, 18.29, 5.97, 1, 1, 43, 11.92, 6.23, 1, 2, 44, 0.38, -8.46, 0.32422, 43, 13.08, -5.42, 0.67578, 2, 44, 3.9, -5.13, 0.78363, 43, 17.82, -6.4, 0.21637, 2, 44, 6.61, -3.21, 0.96797, 43, 20.95, -7.52, 0.03203], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 22, "height": 21}}, "leg_R": {"leg_R": {"x": 12.47, "y": 0.72, "rotation": 3.37, "width": 29, "height": 13}}, "leg_R2": {"leg_R2": {"type": "mesh", "uvs": [0.60764, 0.16791, 0.42564, 0.09587, 0.2224, 0, 0.14354, 0.221, 0.06164, 0.47504, 0.01917, 0.72529, 0.07681, 0.94899, 0.31037, 0.98691, 0.5773, 0.97933, 0.80784, 0.92245, 0.9595, 0.75941, 0.90187, 0.55845, 0.9595, 0.38783, 1, 0.13758, 0.80784, 0.1755, 0.50715, 0.30439, 0.39523, 0.46398, 0.27434, 0.68807, 0.28377, 0.85487, 0.60235, 0.8956, 0.69895, 0.66022, 0.80139, 0.30728], "triangles": [6, 18, 7, 7, 18, 8, 8, 19, 9, 8, 18, 19, 6, 5, 18, 19, 20, 9, 9, 20, 10, 16, 20, 19, 17, 19, 18, 20, 11, 10, 20, 21, 11, 5, 17, 18, 16, 19, 17, 5, 4, 17, 17, 4, 16, 16, 15, 20, 20, 15, 21, 21, 15, 0, 11, 21, 12, 4, 3, 16, 16, 3, 1, 16, 1, 15, 1, 3, 2, 21, 14, 12, 12, 14, 13, 21, 0, 14, 15, 1, 0], "vertices": [2, 41, -1.44, 0.24, 0.94106, 42, 13.26, 16.76, 0.05894, 2, 41, 0.9, -4.98, 0.996, 42, 7.71, 18.17, 0.004, 1, 41, 3.25, -11.06, 1, 1, 41, 8.78, -9.29, 1, 1, 41, 14.96, -7.07, 1, 2, 41, 20.29, -4.02, 0.91504, 42, -3.58, 2.37, 0.08496, 2, 41, 23.15, 0.85, 0.5817, 42, -1.54, -2.88, 0.4183, 2, 41, 19.16, 6.68, 0.24837, 42, 5.51, -3.38, 0.75163, 2, 41, 13.69, 12.53, 0.00161, 42, 13.5, -2.73, 0.99839, 2, 41, 8.07, 16.78, 0.04285, 42, 20.32, -0.96, 0.95715, 2, 41, 2.12, 17.57, 0.18024, 42, 24.63, 3.21, 0.81976, 2, 41, -0.33, 13.07, 0.41106, 42, 22.62, 7.93, 0.58894, 2, 41, -4.54, 11.63, 0.63677, 42, 24.11, 12.12, 0.36323, 2, 41, -9.83, 8.54, 0.77779, 42, 24.97, 18.18, 0.22221, 2, 41, -5.31, 4.84, 0.87468, 42, 19.27, 16.94, 0.12532, 2, 41, 3.01, 0.18, 0.99843, 42, 10.44, 13.32, 0.00157, 2, 41, 8.1, 0.22, 0.99512, 42, 7.32, 9.3, 0.00488, 2, 41, 14.53, 1.1, 0.85486, 42, 4.01, 3.72, 0.14514, 1, 42, 4.53, -0.26, 1, 1, 42, 14.13, -0.68, 1, 2, 41, 5.55, 10.16, 0.27567, 42, 16.69, 5.13, 0.72433, 2, 41, -2.82, 6.81, 0.78459, 42, 19.26, 13.77, 0.21541], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 30, "height": 24}}, "leg_R3": {"leg_R3": {"type": "mesh", "uvs": [0.63741, 0.09862, 0.41691, 0.0149, 0.11941, 0, 0.07041, 0.20418, 0.03891, 0.40802, 0, 0.61186, 0.10891, 0.81206, 0.30141, 0.9249, 0.52541, 0.9795, 0.80891, 0.90306, 0.91741, 0.73562, 1, 0.49538, 1, 0.3425, 0.84741, 0.2151, 0.56692, 0.29137, 0.48705, 0.55729, 0.36157, 0.77602, 0.2726, 0.15745, 0.22438, 0.48517, 0.80528, 0.44364, 0.73226, 0.64767], "triangles": [7, 16, 8, 7, 6, 16, 16, 6, 18, 6, 5, 18, 16, 18, 15, 5, 4, 18, 4, 3, 18, 8, 16, 20, 8, 20, 9, 20, 16, 15, 9, 20, 10, 20, 19, 10, 10, 19, 11, 20, 15, 19, 15, 14, 19, 15, 18, 14, 19, 12, 11, 18, 17, 14, 18, 3, 17, 19, 13, 12, 19, 14, 13, 17, 1, 14, 14, 0, 13, 14, 1, 0, 3, 2, 17, 17, 2, 1], "vertices": [2, 40, 6.75, 4.17, 0.98819, 41, -12.3, -4.97, 0.01181, 2, 40, 4.75, -1.6, 0.95022, 41, -10.04, -10.64, 0.04978, 2, 40, 4.5, -9.34, 0.84697, 41, -5.17, -16.65, 0.15303, 2, 40, 9.63, -10.53, 0.66506, 41, -0.51, -14.2, 0.33494, 2, 40, 14.74, -11.26, 0.426, 41, 3.84, -11.42, 0.574, 2, 40, 19.85, -12.19, 0.21369, 41, 8.31, -8.78, 0.78631, 2, 40, 24.81, -9.28, 0.1385, 41, 10.16, -3.34, 0.8615, 2, 40, 27.55, -4.23, 0.25462, 41, 8.93, 2.28, 0.74538, 2, 40, 28.82, 1.62, 0.51583, 41, 6.07, 7.53, 0.48417, 2, 40, 26.79, 8.96, 0.78049, 41, -0.26, 11.75, 0.21951, 2, 40, 22.55, 11.71, 0.94141, 41, -5.27, 11.07, 0.05859, 2, 40, 16.51, 13.76, 0.99645, 41, -11.18, 8.67, 0.00355, 1, 40, 12.69, 13.7, 1, 2, 40, 9.57, 9.68, 0.99931, 41, -13.76, 1.05, 0.00069, 1, 40, 11.6, 2.42, 1, 1, 40, 18.28, 0.45, 1, 2, 40, 23.8, -2.73, 0.0433, 41, 5.11, 0.96, 0.9567, 2, 40, 8.37, -5.29, 0.90971, 41, -4.88, -11.06, 0.09029, 2, 40, 16.59, -6.41, 0.33801, 41, 2.06, -6.54, 0.66199, 1, 40, 15.3, 8.67, 1, 2, 40, 20.43, 6.86, 0.99966, 41, -3.7, 6.02, 0.00034], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 26, "height": 25}}, "tooth": {"tooth": {"x": 5.78, "y": -2, "rotation": -163.07, "width": 16, "height": 17}}, "weapons": {"weapons": {"x": 5.54, "y": -1.6, "rotation": -100.93, "width": 44, "height": 63}}, "yinying": {"yinying": {"x": -0.08, "y": -2.89, "width": 75, "height": 36}}}}], "animations": {"attack": {"bones": {"yinying": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 4.42, "curve": "stepped"}, {"time": 0.7, "x": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.157, "curve": "stepped"}, {"time": 0.7, "x": 1.157, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "leg_L5": {"rotate": [{"angle": 0.01}]}, "leg_L3": {"rotate": [{"angle": -0.12}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -3.8, "y": -1.53, "curve": "stepped"}, {"time": 0.7, "x": -3.8, "y": -1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "weapons": {"rotate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -21.74, "curve": "stepped"}, {"time": 0.7, "angle": -21.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "translate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -0.23, "y": -2.75, "curve": "stepped"}, {"time": 0.7, "x": -0.23, "y": -2.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "ear3": {"rotate": [{"angle": -4.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -22.71, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 2.17, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.87, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.58}]}, "ear6": {"rotate": [{"angle": 5.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -10.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 11.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 1.85, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 5.15}]}, "body6": {"rotate": [{"angle": 0.21}]}, "leg_R3": {"rotate": [{"angle": -0.36}]}, "hand_R2": {"rotate": [{"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 173.16, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.4667, "angle": 60.09, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5, "angle": -42.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -43.96, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "translate": [{"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -4.42, "y": -0.6, "curve": "stepped"}, {"time": 0.7, "x": -4.42, "y": -0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "hair18": {"rotate": [{"angle": -0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -18.39, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 6.49, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.26}]}, "hair2": {"rotate": [{"angle": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 6.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -15.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 7.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.02, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.28}]}, "hair9": {"rotate": [{"angle": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 2.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -22.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 2.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.48, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.19}]}, "ear7": {"rotate": [{"angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -15.16, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 9.72, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.97}]}, "ear8": {"rotate": [{"angle": 2.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 8.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -15.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.04, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -1.01, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.29}]}, "hair13": {"rotate": [{"angle": 6.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 13.17, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -22.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 13.62, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.57, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 6.87}]}, "ear9": {"rotate": [{"angle": 1.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 7.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -16.73, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 8.15, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 1.41}]}, "hair14": {"rotate": [{"angle": 5.56, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 11.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -24.49, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 12.31, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 2.27, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 5.56}]}, "hair11": {"rotate": [{"angle": -2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 3.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -20.46, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -5.63, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.33}]}, "hair16": {"rotate": [{"angle": 2.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -12.85, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 9.37, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.68, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.62}]}, "hair10": {"rotate": [{"angle": -3.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -21.45, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.43, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -6.62, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.32}]}, "hair12": {"rotate": [{"angle": 2.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 8.69, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -20.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 9.14, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -0.91, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.39}]}, "hair7": {"rotate": [{"angle": -7.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -1.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -25.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -7.75}]}, "hair6": {"rotate": [{"angle": -8.19, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -26.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -1.44, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.49, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -8.19}]}, "hair8": {"rotate": [{"angle": -1.64, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -17.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 5.11, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -4.93, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -1.64}]}, "hand_L3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -30.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 6.48, "curve": "stepped"}, {"time": 0.7, "angle": 6.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "hair5": {"rotate": [{"angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -22.03, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 2.85, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -7.2, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.9}]}, "hair4": {"rotate": [{"angle": -0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 5.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -16.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 5.88, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -4.17, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.87}]}, "ear5": {"rotate": [{"angle": -3.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 3.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -21.31, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 3.57, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -6.47, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.18}]}, "hair17": {"rotate": [{"angle": -0.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -18.89, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 5.99, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.76}]}, "hand_R": {"rotate": [{"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.46, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "ear4": {"rotate": [{"angle": -3.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 2.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -22.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.78, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -7.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.97}]}, "body3": {"rotate": [{"angle": -0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.08, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.04, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.58}], "translate": [{"x": 0.01, "y": -0.17}]}, "ear2": {"rotate": [{"angle": -4.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -20.31, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 1.91, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -8.13, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -4.84}]}, "head": {"rotate": [{"angle": 0.72, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "angle": -8.69, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 3.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 4.56, "curve": "stepped"}, {"time": 0.7, "angle": 4.56, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.8667, "angle": 0.17, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 1.0667, "angle": 2.36, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.72}], "translate": [{"x": 0.09, "y": -0.06}]}, "body2": {"rotate": [{"angle": -0.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -3.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.23, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 0.36, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.18}], "translate": [{"x": -0.28, "y": -0.01}]}, "hand_L2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -56.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 46.22, "curve": "stepped"}, {"time": 0.7, "angle": 46.22, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -0.27, "y": 2.24, "curve": "stepped"}, {"time": 0.7, "x": -0.27, "y": 2.24, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "body": {"rotate": [{"angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.57, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -1.71}], "translate": [{"y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -5.51, "y": 2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 6.79, "y": -5.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 6.79, "y": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -1.44, "y": 0.12, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "y": -1.32}]}, "leg_L": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 10.94, "curve": "stepped"}, {"time": 0.8667, "x": 10.94, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "x": 5.47, "y": 2.88, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.1333}]}, "body4": {"rotate": [{"angle": -0.44, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": -6.86, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": 4.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -9.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -9.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -11.69, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.44}], "translate": [{"x": -0.44, "y": -0.01}]}}, "deform": {"default": {"body": {"body": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "vertices": [-0.18213, 0.10221, -0.19574, -0.09426, -0.25162, -0.293, -0.16735, -0.34899, 0.3671, -0.12262, -0.05944, -0.40119, 0.40546, -0.00905, 0.05522, -0.42999, 0.27585, -0.33443, 0.41978, 0.1083, 0.16143, -0.35623, 0.32643, -0.21542, 0.33337, 0.20451, 0.2576, -0.17591, 0.31172, -0.01159, 0.14248, 0.27749, -5.61935, 2.26715, -5.96327, -1.07584, -1.55039, -5.85778, 2.87607, 3.14513, 0.75769, 4.19406, -3.47878, 2.46235, 0.14174, 1.14171, -0.48845, 1.04166, -1.15049, -0.00144, -0.01157, 0.43867, -0.24355, 0.36504, -0.43382, -0.06607, -0.13547, 0.46918, -0.36464, 0.32483, -0.44868, -0.1928, -0.14689, 0.39802, -0.33639, 0.25853, -0.37664, -0.19528, -0.17188, 0.35808, -0.33626, 0.21142, -0.33391, -0.2151, -0.20174, 0.33487, -0.34915, 0.17587, -0.19282, 0.24128, -0.10433, -0.24052, -0.05821, -0.09324, -0.03302, 0.1295, -0.09695, 0.09199, -0.12439, -0.04888, -0.05434, 0.28335, -0.19697, 0.21082, -0.27439, -0.08918, -1.86423, 3.05878, -3.20746, 1.59498, -2.80307, -2.23038, -1.29301, -0.09226, -1.045, -0.76704, 0.25241, -1.27147, -0.14239, -0.18304, 0.19934, -0.1185, -0.10572, 0.25705, 0.01458, -0.18608, 0.11149, -0.14968, 0.18281, 0.03763, 0.21083, 0.14121], "curve": "stepped"}, {"time": 0.7, "vertices": [-0.18213, 0.10221, -0.19574, -0.09426, -0.25162, -0.293, -0.16735, -0.34899, 0.3671, -0.12262, -0.05944, -0.40119, 0.40546, -0.00905, 0.05522, -0.42999, 0.27585, -0.33443, 0.41978, 0.1083, 0.16143, -0.35623, 0.32643, -0.21542, 0.33337, 0.20451, 0.2576, -0.17591, 0.31172, -0.01159, 0.14248, 0.27749, -5.61935, 2.26715, -5.96327, -1.07584, -1.55039, -5.85778, 2.87607, 3.14513, 0.75769, 4.19406, -3.47878, 2.46235, 0.14174, 1.14171, -0.48845, 1.04166, -1.15049, -0.00144, -0.01157, 0.43867, -0.24355, 0.36504, -0.43382, -0.06607, -0.13547, 0.46918, -0.36464, 0.32483, -0.44868, -0.1928, -0.14689, 0.39802, -0.33639, 0.25853, -0.37664, -0.19528, -0.17188, 0.35808, -0.33626, 0.21142, -0.33391, -0.2151, -0.20174, 0.33487, -0.34915, 0.17587, -0.19282, 0.24128, -0.10433, -0.24052, -0.05821, -0.09324, -0.03302, 0.1295, -0.09695, 0.09199, -0.12439, -0.04888, -0.05434, 0.28335, -0.19697, 0.21082, -0.27439, -0.08918, -1.86423, 3.05878, -3.20746, 1.59498, -2.80307, -2.23038, -1.29301, -0.09226, -1.045, -0.76704, 0.25241, -1.27147, -0.14239, -0.18304, 0.19934, -0.1185, -0.10572, 0.25705, 0.01458, -0.18608, 0.11149, -0.14968, 0.18281, 0.03763, 0.21083, 0.14121], "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}}}}, "idle": {"bones": {"leg_L5": {"rotate": [{"angle": 0.01}]}, "leg_L3": {"rotate": [{"angle": -0.12}]}, "leg_R3": {"rotate": [{"angle": -0.36}]}, "body6": {"rotate": [{"angle": 0.21}]}, "hand_R": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hand_R2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.85, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hand_L2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hand_L3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.08, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hair18": {"rotate": [{"angle": -0.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "angle": -2.77, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.8333, "angle": -0.48, "curve": 0.35, "c2": 0.39, "c3": 0.704, "c4": 0.79}, {"time": 1.1333, "angle": 1.57, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.2667, "angle": 2.01, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": -0.26}]}, "hair17": {"rotate": [{"angle": -0.76, "curve": 0.38, "c2": 0.53, "c3": 0.746}, {"time": 0.3667, "angle": -2.77, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 0.8333, "angle": 0.03, "curve": 0.365, "c2": 0.45, "c3": 0.719, "c4": 0.86}, {"time": 1.1333, "angle": 1.88, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.2, "angle": 2.01, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 1.6667, "angle": -0.76}]}, "hair16": {"rotate": [{"angle": 2.62, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 0.8333, "angle": 5.64, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 1.1333, "angle": 8.26, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": 2.62}]}, "hair14": {"rotate": [{"angle": 5.56, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.8333, "angle": 6.23, "curve": 0.344, "c2": 0.37, "c3": 0.687, "c4": 0.73}, {"time": 1.0333, "angle": 9.71, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.2333, "angle": 11.75, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": 5.56}]}, "hair13": {"rotate": [{"angle": 6.87, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.8333, "angle": 11.9, "curve": 0.354, "c2": 0.41, "c3": 0.697, "c4": 0.77}, {"time": 1.0333, "angle": 16.97, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.1667, "angle": 18.67, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 6.87}]}, "hair12": {"rotate": [{"angle": 2.39, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 0.8333, "angle": 6.61, "curve": 0.365, "c2": 0.47, "c3": 0.708, "c4": 0.84}, {"time": 1.0333, "angle": 8.69, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.1, "angle": 8.94, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": 2.39}]}, "hair2": {"rotate": [{"angle": 0.28, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.8333, "angle": 1.33, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.0333, "angle": 1.61, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 0.28}]}, "hair11": {"rotate": [{"angle": -2.33, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": -5.48, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 0.8333, "angle": 1.33, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.9333, "angle": 2.74, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.1333, "angle": 4.46, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -2.33}]}, "hair10": {"rotate": [{"angle": -3.32, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -5.48, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 0.8333, "angle": 2.32, "curve": 0.349, "c2": 0.39, "c3": 0.685, "c4": 0.74}, {"time": 0.9333, "angle": 3.56, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.0667, "angle": 4.46, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -3.32}]}, "hair9": {"rotate": [{"angle": -4.19, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -5.48, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.8333, "angle": 3.22, "curve": 0.355, "c2": 0.45, "c3": 0.691, "c4": 0.79}, {"time": 0.9333, "angle": 4.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1, "angle": 4.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -4.19}]}, "hair8": {"rotate": [{"angle": -1.64, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -1.91, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 0.8333, "angle": 2.44, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.9333, "angle": 2.72, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -1.64}]}, "hair7": {"rotate": [{"angle": -7.75, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -8.32, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.7333, "angle": -0.25, "curve": 0.352, "c2": 0.42, "c3": 0.688, "c4": 0.76}, {"time": 0.8333, "angle": 0.9, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.9333, "angle": 1.44, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -7.75}]}, "hair6": {"rotate": [{"angle": -8.19, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 0.0333, "angle": -8.32, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 0.7333, "angle": 0.55, "curve": 0.359, "c2": 0.5, "c3": 0.695, "c4": 0.86}, {"time": 0.8333, "angle": 1.37, "curve": 0.345, "c2": 0.66, "c3": 0.679}, {"time": 0.8667, "angle": 1.44, "curve": 0.248, "c3": 0.733, "c4": 0.93}, {"time": 1.6667, "angle": -8.19}]}, "hair5": {"rotate": [{"angle": -3.9, "curve": 0.262, "c2": 0.07, "c3": 0.721, "c4": 0.87}, {"time": 0.7333, "angle": 1.29, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.8, "angle": 1.44, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 0.8333, "angle": 1.37, "curve": 0.267, "c2": 0.07, "c3": 0.752}, {"time": 1.6333, "angle": -3.97, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 1.6667, "angle": -3.9}]}, "hair4": {"rotate": [{"angle": -0.87, "curve": 0.296, "c2": 0.19, "c3": 0.756}, {"time": 0.7333, "angle": 1.44, "curve": 0.3, "c3": 0.636, "c4": 0.36}, {"time": 0.8333, "angle": 1.3, "curve": 0.296, "c2": 0.19, "c3": 0.756}, {"time": 1.5667, "angle": -1.02, "curve": 0.3, "c3": 0.636, "c4": 0.36}, {"time": 1.6667, "angle": -0.87}]}, "ear9": {"rotate": [{"angle": 1.41, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 3.26, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.8333, "angle": -5.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.0333, "angle": -7.44, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 1.41}]}, "ear8": {"rotate": [{"angle": 2.29, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": 3.26, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 0.8333, "angle": -6.46, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.9667, "angle": -7.44, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 2.29}]}, "ear7": {"rotate": [{"angle": 2.97, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": 3.26, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 0.8333, "angle": -7.15, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.9, "angle": -7.44, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": 2.97}]}, "ear6": {"rotate": [{"angle": 5.15, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 5.15}]}, "ear5": {"rotate": [{"angle": -3.18, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -4.84, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.8333, "angle": 3.07, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.0333, "angle": 4.73, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -3.18}]}, "ear4": {"rotate": [{"angle": -3.97, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -4.84, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 0.8333, "angle": 3.86, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.9667, "angle": 4.73, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -3.97}]}, "ear3": {"rotate": [{"angle": -4.58, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": -4.84, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 0.8333, "angle": 4.47, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.9, "angle": 4.73, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": -4.58}]}, "ear2": {"rotate": [{"angle": -4.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.87, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.84}]}, "head": {"rotate": [{"angle": 0.72, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 0.8333, "angle": 1.55, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 1.1333, "angle": 2.27, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": 0.72}], "translate": [{"x": 0.09, "y": -0.06, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "x": -0.17, "y": 0.03, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 0.8333, "x": 0.39, "y": -0.15, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 1.1333, "x": 0.65, "y": -0.24, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "x": 0.09, "y": -0.06}]}, "body2": {"rotate": [{"angle": -0.18, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -0.58, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.8333, "angle": 1.34, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.0333, "angle": 1.74, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -0.18}], "translate": [{"x": -0.28, "y": -0.01, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "x": -0.52, "y": 0.02, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 0.8333, "x": 0.61, "y": -0.12, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.0333, "x": 0.85, "y": -0.15, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "x": -0.28, "y": -0.01}]}, "body3": {"rotate": [{"angle": -0.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.74, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.58}], "translate": [{"x": 0.01, "y": -0.17, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.02, "y": 0.69, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 0.01, "y": -0.17}]}, "body": {"rotate": [{"angle": -1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.06, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.71}], "translate": [{"y": -1.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -1.32}]}, "body4": {"rotate": [{"angle": -0.44, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -0.58, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 0.8333, "angle": 1.6, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.9333, "angle": 1.74, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -0.44}], "translate": [{"x": -0.44, "y": -0.01, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "x": -0.52, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 0.8333, "x": 0.78, "y": -0.08, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.9333, "x": 0.86, "y": -0.09, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "x": -0.44, "y": -0.01}]}}}, "run": {"bones": {"leg_L": {"translate": [{"x": -13.02, "y": 21.05}, {"time": 0.0667, "x": -4.63, "y": 13.39}, {"time": 0.1667, "x": 18.36, "y": 13.45}, {"time": 0.2667, "x": 1.06, "y": -0.13}, {"time": 0.3333, "x": -4.68}, {"time": 0.5, "x": -31, "y": 33.67}, {"time": 0.6667, "x": -13.02, "y": 21.05}]}, "leg_R2": {"translate": [{"x": 5.26}, {"time": 0.1, "x": -9.36, "y": 10.98}, {"time": 0.1667, "x": -15.94, "y": 35.36}, {"time": 0.3333, "x": 7.81, "y": 13.16}, {"time": 0.5, "x": 38.13, "y": 11.37}, {"time": 0.5667, "x": 23.15, "y": 2.74}, {"time": 0.6667, "x": 5.26}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.782, "y": 0.782, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.782, "y": 0.782, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "leg_L2": {"rotate": [{"angle": -63.71}, {"time": 0.1667, "angle": 38.89}, {"time": 0.2667, "angle": 12.91}, {"time": 0.3333}, {"time": 0.5, "angle": -78.83}, {"time": 0.6667, "angle": -63.71}]}, "leg_L5": {"rotate": [{"angle": 0.01}]}, "leg_L3": {"rotate": [{"angle": -0.12}], "translate": [{}, {"time": 0.0667, "x": -7.61, "y": -7.44}, {"time": 0.1667, "x": -7.72, "y": -3.74}, {"time": 0.3333}]}, "leg_R": {"rotate": [{}, {"time": 0.1, "angle": -17.29}, {"time": 0.1667, "angle": -84.06}, {"time": 0.3333, "angle": -23.05}, {"time": 0.5, "angle": 37.45}, {"time": 0.6667}]}, "leg_R3": {"rotate": [{"angle": -0.36}]}, "body6": {"rotate": [{"angle": 0.21}], "translate": [{}, {"time": 0.1667, "x": 2.49, "y": -0.5}, {"time": 0.3333}, {"time": 0.5, "y": -4.46}, {"time": 0.6667}]}, "weapons": {"rotate": [{"angle": -5.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -10.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -5.49}]}, "hand_R": {"rotate": [{"angle": 1.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 9.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.07}]}, "hand_R2": {"rotate": [{"angle": 1.07, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 9.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.58, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.07}]}, "hand_L2": {"rotate": [{"angle": 0.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -18.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 19.38, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 0.68}]}, "hand_L3": {"rotate": [{"angle": -5.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -18.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 7.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -5.41}]}, "hair18": {"rotate": [{"angle": -4.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -7.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2667, "angle": 5.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 13.27, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -4.79}]}, "hair17": {"rotate": [{"angle": -7.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": 10.57, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.49}]}, "hair16": {"rotate": [{"angle": -4.79, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -4.79}]}, "hair14": {"rotate": [{"angle": -4.79, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -7.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2, "angle": 0.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4, "angle": 13.27, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -4.79}]}, "hair13": {"rotate": [{"angle": -7.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "angle": 5.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.49}]}, "hair12": {"rotate": [{"angle": -4.76, "curve": 0.306, "c2": 0.24, "c3": 0.694, "c4": 0.76}, {"time": 0.2, "angle": 10.57, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -4.76}]}, "hair2": {"rotate": [{"angle": 0.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 0.15}]}, "hair11": {"rotate": [{"angle": -7.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": 0.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.49}]}, "hair10": {"rotate": [{"angle": -4.8, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.1333, "angle": 5.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -4.8}]}, "hair9": {"rotate": [{"angle": 0.24, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.1333, "angle": 10.57, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 0.24}]}, "hair8": {"rotate": [{"angle": 5.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 5.63}]}, "hair7": {"rotate": [{"angle": -4.83, "curve": 0.311, "c2": 0.26, "c3": 0.651, "c4": 0.61}, {"time": 0.0667, "angle": 0.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2667, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -4.83}]}, "hair6": {"rotate": [{"angle": 0.17, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.0667, "angle": 5.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 0.17}]}, "hair5": {"rotate": [{"angle": 5.72, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.0667, "angle": 10.57, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 5.72}]}, "ear9": {"rotate": [{"angle": 0.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 0.15}]}, "ear8": {"rotate": [{"angle": 5.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 5.63}]}, "ear7": {"rotate": [{"angle": 10.57, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -7.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 10.57}]}, "ear6": {"rotate": [{"angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 13.27}]}, "ear5": {"rotate": [{"angle": 0.15, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -7.49, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 0.15}]}, "ear4": {"rotate": [{"angle": 5.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.49, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 5.63}]}, "ear3": {"rotate": [{"angle": 10.57, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -7.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 10.57}]}, "ear2": {"rotate": [{"angle": 13.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 13.27}]}, "head": {"rotate": [{"angle": -0.28, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -0.28}], "translate": [{"x": -0.11, "y": 0.01, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "x": -0.83, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -0.83, "y": 0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "x": -0.11, "y": 0.01}]}, "body2": {"rotate": [{"angle": -0.78, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -0.78}], "translate": [{"x": -0.23, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -0.63, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.63, "y": 0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": -0.23}]}, "body4": {"rotate": [{"angle": -1.34, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -1.34}], "translate": [{"x": -0.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": -0.4}]}, "body3": {"rotate": [{"angle": -1.84, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -1.84}], "translate": [{"y": -0.36, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "y": -0.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": -0.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "y": -0.36}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 12.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 12.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}}}}}