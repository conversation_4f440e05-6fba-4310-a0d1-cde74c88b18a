{"skeleton": {"hash": "stKTST2R069rafDW7AiHjKqydK4", "spine": "3.8.99", "x": -38.67, "y": -8.41, "width": 68.49, "height": 90.69, "images": "./images/", "audio": "D:/spine动效/怪物/赤脊山/幼龙"}, "bones": [{"name": "root", "scaleX": 0.4, "scaleY": 0.4}, {"name": "zong", "parent": "root"}, {"name": "body", "parent": "zong", "x": -24.49, "y": 43.35}, {"name": "body2", "parent": "body", "length": 23.87, "rotation": 33.96, "x": 8.08, "y": 3.23}, {"name": "body3", "parent": "body2", "length": 27.76, "rotation": 34.7, "x": 23.87}, {"name": "body4", "parent": "body3", "length": 22.19, "rotation": 31.83, "x": 27.76}, {"name": "head", "parent": "body4", "length": 57.37, "rotation": -88.71, "x": 23.23, "y": -1.02}, {"name": "hand_R2", "parent": "body3", "length": 25.24, "rotation": -178.54, "x": 26.28, "y": 16.06}, {"name": "hand_R", "parent": "hand_R2", "length": 18.55, "rotation": 92.83, "x": 25.14, "y": 0.27}, {"name": "hand_L2", "parent": "body3", "length": 21.65, "rotation": -124.97, "x": 27.83, "y": -9.13}, {"name": "hand_L", "parent": "hand_L2", "length": 14.35, "rotation": 32.81, "x": 21.5, "y": -0.24}, {"name": "hand_L3", "parent": "hand_L", "length": 12.38, "rotation": -26.19, "x": 14.35}, {"name": "hand_R3", "parent": "hand_R", "length": 14.21, "rotation": -32.86, "x": 18.46, "y": 0.27}, {"name": "body_2", "parent": "body", "length": 18.8, "rotation": -166.81, "x": -4.55, "y": -3.74}, {"name": "body_3", "parent": "body_2", "length": 15.79, "rotation": -18.39, "x": 18.8}, {"name": "body_4", "parent": "body_3", "length": 16.13, "rotation": -31.88, "x": 15.79}, {"name": "wings_R2", "parent": "body3", "length": 19.48, "rotation": 71.1, "x": 14.16, "y": 29.52}, {"name": "wings_R3", "parent": "wings_R2", "length": 18.72, "rotation": -52.24, "x": 19.29, "y": 0.3}, {"name": "wings_R4", "parent": "wings_R3", "length": 15.18, "rotation": -21.95, "x": 18.72}, {"name": "wings_R5", "parent": "wings_R4", "length": 11.11, "rotation": -8.65, "x": 15.18}, {"name": "wings_R6", "parent": "wings_R5", "length": 19.04, "rotation": 69.15, "x": 11.2, "y": 0.39}, {"name": "wings_R7", "parent": "wings_R6", "length": 20.08, "rotation": -2.99, "x": 19.04}, {"name": "wings_R8", "parent": "wings_R7", "length": 21.22, "rotation": -1.61, "x": 20.08}, {"name": "wings_R9", "parent": "wings_R8", "length": 20.75, "rotation": 24.09, "x": 21.24, "y": 0.06}, {"name": "wings_R10", "parent": "wings_R4", "length": 25.01, "rotation": 97.33, "x": 13.7, "y": 7.32}, {"name": "wings_R11", "parent": "wings_R10", "length": 16.71, "rotation": 9.62, "x": 25.01}, {"name": "wings_R12", "parent": "wings_R11", "length": 19.19, "rotation": 8.09, "x": 16.71}, {"name": "wings_R13", "parent": "wings_R3", "length": 16.1, "rotation": 115.79, "x": 21.29, "y": 6.86}, {"name": "wings_R14", "parent": "wings_R13", "length": 16.78, "rotation": 15, "x": 15.26, "y": -0.29}, {"name": "wings_R15", "parent": "wings_R14", "length": 18.1, "rotation": 7.3, "x": 16.78}, {"name": "wings_R16", "parent": "wings_R2", "length": 13.02, "rotation": 97.03, "x": 2.45, "y": 7.97}, {"name": "wings_R17", "parent": "wings_R16", "length": 8.89, "rotation": 21.65, "x": 13.02}, {"name": "wings_L2", "parent": "body3", "length": 31.51, "rotation": 24.49, "x": 27.7, "y": -18.78}, {"name": "wings_L3", "parent": "wings_L2", "length": 21.91, "rotation": -11.13, "x": 31.5, "y": -0.22}, {"name": "wings_L4", "parent": "wings_L3", "length": 16.2, "rotation": -12.41, "x": 21.91}, {"name": "wings_L5", "parent": "wings_L4", "length": 19.81, "rotation": -4.22, "x": 16.2}, {"name": "wings_L6", "parent": "wings_L5", "length": 19.69, "rotation": -7.34, "x": 19.81}, {"name": "wings_L7", "parent": "wings_L3", "length": 11.5, "rotation": -100.82, "x": 22.11, "y": -4.57}, {"name": "wings_L8", "parent": "wings_L7", "length": 8.31, "rotation": -21.22, "x": 11.5}, {"name": "wings_L9", "parent": "wings_L3", "length": 9.21, "rotation": -146.74, "x": 9.25, "y": -6.59}, {"name": "wings_L10", "parent": "wings_L9", "length": 11.12, "rotation": -15.7, "x": 9.21}, {"name": "wings_L11", "parent": "wings_L10", "length": 7.07, "rotation": 0.86, "x": 11.12}, {"name": "leg_R", "parent": "body", "length": 15.83, "rotation": -63.43, "x": 3.78, "y": -0.03}, {"name": "leg_R2", "parent": "leg_R", "length": 11.91, "rotation": -68.55, "x": 15.83}, {"name": "leg_R3", "parent": "leg_R2", "length": 13.26, "rotation": 62.84, "x": 11.91}, {"name": "leg_L", "parent": "body", "length": 21.3, "rotation": -59.17, "x": 34.46, "y": 5.57}, {"name": "leg_L2", "parent": "leg_L", "length": 12.35, "rotation": -70.98, "x": 19.98, "y": 0.1}, {"name": "leg_L3", "parent": "leg_L2", "length": 11.4, "rotation": 50.59, "x": 12.16, "y": 0.23}, {"name": "yinying", "parent": "root", "x": -9.93, "y": 7.49}, {"name": "head3", "parent": "head", "length": 72.78, "rotation": -6.96, "x": -13.85, "y": 12.31}, {"name": "head4", "parent": "head", "length": 56.69, "rotation": -36.02, "x": -13.56, "y": 12.5}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "leg_L", "bone": "leg_L", "attachment": "leg_L"}, {"name": "body_2", "bone": "body_2", "attachment": "body_2"}, {"name": "wings_L2", "bone": "wings_L2", "attachment": "wings_L2"}, {"name": "wings_L", "bone": "zong"}, {"name": "hand_L2", "bone": "hand_L2", "attachment": "hand_L2"}, {"name": "hand_L", "bone": "hand_L", "attachment": "hand_L"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "hand_R2", "bone": "hand_R2", "attachment": "hand_R2"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}, {"name": "head3", "bone": "head4", "attachment": "head_2"}, {"name": "head2", "bone": "head3", "attachment": "head"}, {"name": "eye", "bone": "head"}, {"name": "wings_R2", "bone": "wings_R2", "attachment": "wings_R2"}, {"name": "wings_R", "bone": "zong"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.6172, 0, 0.68827, 0.00902, 0.76116, 0.02031, 0.8762, 0.06408, 0.96662, 0.17009, 0.95104, 0.25095, 0.93857, 0.31569, 0.98712, 0.47793, 0.98702, 0.58051, 0.98694, 0.66392, 0.89817, 0.80468, 0.82562, 0.86029, 0.72563, 0.90956, 0.60187, 0.94714, 0.50013, 0.97804, 0.41599, 0.98601, 0.29313, 0.99765, 0.21405, 0.95298, 0.05526, 0.78424, 0.01322, 0.68623, 0.01249, 0.54607, 0.09041, 0.41139, 0.14026, 0.32523, 0.24349, 0.19918, 0.34748, 0.12034, 0.42401, 0.06231, 0.16003, 0.73324, 0.25219, 0.69614, 0.35011, 0.64378, 0.4365, 0.58924, 0.52002, 0.53905, 0.59202, 0.46924, 0.63522, 0.35796, 0.66978, 0.2816, 0.6669, 0.1856, 0.67266, 0.11578], "triangles": [26, 19, 27, 18, 19, 26, 17, 26, 27, 18, 26, 17, 16, 17, 15, 35, 0, 1, 35, 1, 2, 25, 0, 35, 34, 25, 35, 3, 34, 35, 5, 33, 34, 35, 2, 3, 3, 4, 34, 34, 4, 5, 24, 34, 33, 34, 24, 25, 6, 33, 5, 32, 24, 33, 23, 24, 32, 32, 22, 23, 31, 22, 32, 30, 22, 31, 7, 33, 6, 33, 7, 8, 33, 8, 32, 9, 10, 8, 8, 31, 32, 8, 30, 31, 29, 21, 22, 30, 29, 22, 28, 20, 21, 29, 28, 21, 28, 27, 20, 10, 30, 8, 11, 30, 10, 29, 30, 11, 12, 29, 11, 13, 28, 29, 12, 13, 29, 14, 27, 28, 27, 15, 17, 28, 13, 14, 14, 15, 27, 27, 19, 20], "vertices": [4, 2, 32.01, 69.42, 0.00032, 3, 56.82, 41.53, 0.0004, 4, 50.73, 15.38, 0.00085, 5, 27.63, 0.95, 0.99843, 1, 5, 25.78, -4.13, 1, 1, 5, 23.69, -9.3, 1, 2, 4, 51.89, -5.02, 0.00404, 5, 17.86, -16.99, 0.99596, 2, 4, 44.59, -15.16, 0.12473, 5, 6.3, -21.75, 0.87527, 2, 4, 36.7, -16.98, 0.35621, 5, -1.35, -19.14, 0.64379, 3, 3, 59.35, 2.14, 0.00029, 4, 30.39, -18.44, 0.67435, 5, -7.49, -17.06, 0.32536, 3, 3, 53.4, -13.21, 0.07485, 4, 16.76, -27.68, 0.91841, 5, -23.94, -17.71, 0.00674, 3, 3, 47.72, -21.63, 0.21924, 4, 7.3, -31.36, 0.78044, 13, -66.18, -0.6, 0.00032, 3, 3, 43.1, -28.48, 0.35877, 4, -0.4, -34.36, 0.63797, 13, -64.29, 7.44, 0.00327, 3, 3, 29.8, -36.32, 0.61863, 4, -15.8, -33.23, 0.35524, 13, -54.63, 19.49, 0.02614, 3, 3, 22.21, -37.84, 0.71012, 4, -22.91, -30.17, 0.24021, 13, -48.07, 23.61, 0.04967, 3, 3, 13.26, -37.7, 0.77522, 4, -30.18, -24.96, 0.12897, 13, -39.66, 26.64, 0.09582, 3, 3, 3.49, -35.6, 0.76852, 4, -37.02, -17.66, 0.04436, 13, -29.77, 28.15, 0.18713, 3, 3, -4.55, -33.88, 0.69523, 4, -42.65, -11.67, 0.01145, 13, -21.64, 29.39, 0.29333, 4, 2, 16.92, -28.2, 3e-05, 3, -10.23, -31.01, 0.6087, 4, -45.68, -6.08, 0.00218, 13, -15.32, 28.71, 0.38909, 2, 3, -18.51, -26.81, 0.47693, 13, -6.09, 27.73, 0.52307, 3, 2, 1.77, -24.93, 0.00293, 3, -20.96, -19.83, 0.38447, 13, -1.32, 22.08, 0.61261, 4, 2, -10.14, -8.22, 0.00012, 3, -21.51, 0.68, 0.00722, 4, -36.92, 26.39, 0, 13, 6.46, 3.09, 0.99266, 5, 2, -13.29, 1.48, 0.29736, 3, -18.7, 10.48, 0.01166, 4, -29.03, 32.86, 0.00199, 5, -30.92, 57.86, 0.00029, 13, 7.32, -7.07, 0.68871, 5, 2, -13.34, 15.36, 0.59693, 3, -11, 22.02, 0.21652, 4, -16.12, 37.96, 0.03317, 5, -17.27, 55.39, 0.00955, 13, 4.21, -20.6, 0.14383, 5, 2, -7.5, 28.69, 0.40366, 3, 1.3, 29.82, 0.38375, 4, -1.58, 37.36, 0.14261, 5, -5.22, 47.22, 0.05849, 13, -4.53, -32.24, 0.01148, 5, 2, -3.76, 37.22, 0.2699, 3, 9.16, 34.81, 0.35444, 4, 7.73, 36.98, 0.23964, 5, 2.49, 41.99, 0.13583, 13, -10.11, -39.7, 0.00018, 4, 2, 3.98, 49.7, 0.12472, 3, 22.56, 40.83, 0.20264, 4, 22.17, 34.31, 0.30194, 5, 13.35, 32.1, 0.37069, 4, 2, 11.78, 57.5, 0.0556, 3, 33.39, 42.95, 0.09207, 4, 32.28, 29.89, 0.2122, 5, 19.6, 23.01, 0.64012, 4, 2, 17.52, 63.25, 0.02464, 3, 41.36, 44.51, 0.03915, 4, 39.72, 26.63, 0.10808, 5, 24.21, 16.32, 0.82814, 2, 2, -2.28, -3.17, 0.26657, 13, -2.34, -0.03, 0.73343, 5, 2, 4.63, 0.5, 0.46342, 3, -4.39, -0.34, 0.52012, 4, -23.42, 15.8, 2e-05, 5, -35.15, 40.42, 0, 13, -9.91, -2.03, 0.01643, 2, 3, 4.6, -0.14, 0.99988, 13, -18.24, -5.4, 0.00012, 4, 2, 18.46, 11.08, 0.00232, 3, 12.99, 0.72, 0.99384, 4, -8.53, 6.78, 0.0036, 5, -27.26, 24.9, 0.00024, 4, 2, 24.72, 16.05, 0.00169, 3, 20.96, 1.34, 0.90368, 4, -1.63, 2.75, 0.09421, 5, -23.51, 17.84, 0.00042, 4, 2, 30.12, 22.96, 0.00031, 3, 29.3, 4.05, 0.00466, 4, 6.78, 0.24, 0.9948, 5, -17.7, 11.27, 0.00023, 4, 2, 33.36, 33.98, 0.001, 3, 38.14, 11.38, 0.00312, 4, 18.22, 1.23, 0.9891, 5, -7.46, 6.08, 0.00678, 4, 2, 35.95, 41.54, 0.0007, 3, 44.52, 16.2, 0.00137, 4, 26.2, 1.56, 0.82279, 5, -0.5, 2.15, 0.17514, 4, 2, 35.74, 51.04, 0.00023, 3, 49.65, 24.21, 0.00039, 4, 34.98, 5.22, 0.00494, 5, 8.89, 0.63, 0.99444, 1, 5, 15.6, -1.05, 1], "hull": 26, "edges": [0, 50, 4, 6, 6, 8, 12, 14, 18, 20, 20, 22, 22, 24, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 2, 4, 0, 2, 24, 26, 26, 28, 40, 42, 42, 44, 14, 16, 16, 18, 8, 10, 10, 12, 46, 48, 48, 50, 28, 30, 30, 32], "width": 75, "height": 99}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.74776, 0.00983, 0.87185, 0.10886, 0.95586, 0.29125, 1, 0.57708, 1, 0.63515, 0.91702, 0.8999, 0.82358, 0.98102, 0.68662, 0.98119, 0.52337, 0.98139, 0.37216, 0.9034, 0.25436, 0.84265, 0.16307, 0.74444, 0.01179, 0.49953, 0.01151, 0.32578, 0.04319, 0.25118, 0.19911, 0.34465, 0.33124, 0.34559, 0.42848, 0.23965, 0.52381, 0.13579, 0.62575, 0.02472, 0.85839, 0.28654, 0.75169, 0.39454, 0.6528, 0.49008, 0.53569, 0.54824, 0.4368, 0.55654, 0.3249, 0.56901, 0.21039, 0.53577, 0.12451, 0.47347, 0.06205, 0.39039], "triangles": [28, 14, 15, 13, 14, 28, 27, 28, 15, 12, 13, 28, 12, 28, 27, 26, 15, 16, 27, 15, 26, 11, 27, 26, 12, 27, 11, 25, 26, 16, 24, 25, 16, 25, 11, 26, 10, 11, 25, 9, 25, 24, 10, 25, 9, 23, 17, 18, 22, 23, 18, 24, 16, 17, 23, 24, 17, 6, 22, 5, 7, 22, 6, 23, 22, 7, 8, 24, 23, 8, 23, 7, 9, 24, 8, 22, 18, 21, 20, 0, 1, 20, 1, 2, 21, 0, 20, 19, 0, 21, 18, 19, 21, 3, 20, 2, 20, 3, 21, 4, 5, 3, 5, 21, 3, 22, 21, 5], "vertices": [3, 3, 8.53, 16.77, 0.66257, 2, 5.78, 21.91, 0.32694, 13, -15.91, -22.61, 0.0105, 2, 3, 14.19, 6.75, 0.93967, 2, 16.08, 16.76, 0.06033, 2, 3, 14.68, -5.01, 0.99518, 13, -29.39, -4.42, 0.00482, 2, 3, 9.41, -19.39, 0.85474, 13, -29.56, 10.89, 0.14526, 3, 3, 7.73, -21.89, 0.81992, 2, 26.72, -10.61, 2e-05, 13, -28.88, 13.83, 0.18006, 3, 3, -5.68, -29.46, 0.53273, 2, 19.83, -24.38, 0.0001, 13, -19.03, 25.66, 0.46717, 2, 3, -14.46, -28.63, 0.38793, 13, -10.52, 28, 0.61207, 3, 3, -23.9, -22.29, 0.17414, 13, 0.56, 25.41, 0.824, 14, -25.33, 18.36, 0.00187, 3, 3, -35.14, -14.73, 0.02247, 13, 13.75, 22.33, 0.87498, 14, -11.84, 19.6, 0.10255, 2, 13, 25.04, 15.52, 0.41811, 14, 1.03, 16.69, 0.58189, 3, 13, 33.84, 10.21, 0.0504, 14, 11.05, 14.43, 0.94071, 15, -11.65, 9.75, 0.00889, 2, 14, 19.06, 10.03, 0.66275, 15, -2.52, 10.25, 0.33725, 1, 15, 15.17, 7.65, 1, 1, 15, 20.64, 0.46, 1, 1, 15, 20.88, -4.22, 1, 4, 2, -39.75, 4.5, 0.00041, 13, 32.4, -16.05, 0.00042, 14, 17.97, -10.94, 0.19685, 15, 7.62, -8.14, 0.80231, 4, 2, -28.79, 4.45, 0.0432, 13, 21.73, -13.5, 0.21706, 14, 7.04, -11.88, 0.64185, 15, -1.16, -14.72, 0.0979, 5, 3, -20.13, 21.66, 0.01802, 2, -20.72, 9.96, 0.24413, 13, 12.62, -17.02, 0.5615, 14, -0.5, -18.1, 0.17528, 15, -4.28, -23.98, 0.00107, 4, 3, -10.55, 21.72, 0.14141, 2, -12.8, 15.36, 0.4934, 13, 3.68, -20.47, 0.3432, 14, -7.89, -24.2, 0.02199, 4, 3, -0.31, 21.79, 0.39252, 2, -4.34, 21.13, 0.51068, 13, -5.88, -24.17, 0.09663, 14, -15.79, -30.71, 0.00017, 2, 3, 8.1, -0.29, 0.99981, 13, -21.57, -6.5, 0.00019, 3, 3, -2.38, 0, 0.75546, 2, 6.11, 1.9, 0.24055, 13, -11.66, -3.06, 0.00399, 2, 2, -2.1, -3.07, 0.33975, 13, -2.54, -0.09, 0.66025, 2, 3, -21.71, 3.38, 6e-05, 13, 7.61, 0.63, 0.99994, 3, 2, -20.03, -6.52, 0.00196, 13, 15.7, -0.82, 0.98939, 14, -2.68, -1.75, 0.00864, 4, 2, -29.31, -7.17, 0.00018, 13, 24.89, -2.31, 0.00025, 14, 6.51, -0.27, 0.99866, 15, -7.74, -5.13, 0.00091, 3, 2, -38.82, -5.44, 1e-05, 14, 16.13, -1.13, 0.14314, 15, 0.88, -0.78, 0.85685, 1, 15, 8.52, 0.93, 1, 1, 15, 15.26, 0.61, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 12, 14, 14, 16, 16, 18, 18, 20], "width": 83, "height": 52}}, "eye": {"eye": {"x": 15.47, "y": 13.09, "rotation": -11.78, "width": 29, "height": 26}}, "hand_L": {"hand_L": {"type": "mesh", "uvs": [0.36656, 0.02895, 0.48328, 0.09283, 0.6285, 0.17232, 0.75961, 0.31886, 0.93232, 0.51191, 0.97423, 0.63866, 0.97435, 0.88297, 0.76062, 0.88263, 0.66761, 0.99221, 0.38473, 0.8329, 0.26876, 0.72902, 0.18023, 0.64973, 0.05103, 0.46922, 0.01238, 0.38083, 0.03449, 0.28429, 0.1328, 0.17441, 0.26424, 0.0275, 0.20751, 0.29115, 0.3324, 0.37091, 0.54651, 0.50385, 0.70114, 0.64344, 0.4454, 0.45068], "triangles": [20, 19, 3, 20, 3, 4, 20, 4, 5, 9, 19, 20, 7, 20, 5, 8, 9, 20, 7, 5, 6, 7, 8, 20, 18, 0, 1, 21, 18, 1, 21, 1, 2, 19, 21, 2, 12, 13, 17, 3, 19, 2, 18, 11, 12, 18, 12, 17, 11, 18, 21, 10, 11, 21, 9, 10, 21, 9, 21, 19, 17, 15, 16, 14, 15, 17, 0, 17, 16, 18, 17, 0, 17, 13, 14], "vertices": [2, 9, 16.5, 10.81, 0.80649, 10, 1.78, 11.99, 0.19351, 2, 9, 20.76, 13.29, 0.49249, 10, 6.72, 11.77, 0.50751, 3, 9, 26.07, 16.39, 0.18123, 10, 12.85, 11.49, 0.79222, 11, -6.41, 9.65, 0.02654, 3, 9, 32.98, 17.77, 0.02935, 10, 19.41, 8.91, 0.56535, 11, 0.61, 10.23, 0.4053, 2, 10, 28.05, 5.51, 0.06425, 11, 9.86, 10.99, 0.93575, 2, 10, 31.23, 2.19, 0.01037, 11, 14.18, 9.41, 0.98963, 1, 11, 20.52, 4.04, 1, 2, 10, 27.09, -8.65, 0.00141, 11, 15.25, -2.14, 0.99859, 2, 10, 25.33, -13.48, 0.0197, 11, 15.81, -7.25, 0.9803, 2, 10, 13.32, -12.8, 0.49232, 11, 4.72, -11.94, 0.50768, 2, 10, 7.87, -11.32, 0.83138, 11, -0.82, -13.02, 0.16862, 2, 10, 3.71, -10.19, 0.96171, 11, -5.05, -13.84, 0.03829, 2, 9, 22.3, -7.47, 0.03012, 10, -3.24, -6.52, 0.96988, 2, 9, 18.98, -7.03, 0.19018, 10, -5.79, -4.34, 0.80982, 2, 9, 16.72, -4.51, 0.51349, 10, -6.33, -1, 0.48651, 1, 9, 15.68, 0.67, 1, 2, 9, 14.3, 7.6, 0.94059, 10, -1.8, 10.49, 0.05941, 1, 9, 20.56, 0.83, 1, 2, 9, 25.45, 3.28, 0.06043, 10, 5.23, 0.81, 0.93957, 2, 10, 14.49, -0.09, 0.32309, 11, 0.17, -0.01, 0.67691, 2, 10, 21.77, -2.1, 0.00225, 11, 7.59, 1.4, 0.99775, 2, 9, 30.09, 5.35, 0.00013, 10, 10.25, 0.04, 0.99987], "hull": 17, "edges": [0, 32, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 26, 28, 4, 6, 6, 8, 0, 2, 2, 4, 18, 20, 20, 22, 28, 30, 30, 32], "width": 38, "height": 34}}, "hand_L2": {"hand_L2": {"type": "mesh", "uvs": [0.64941, 0.21109, 0.94139, 0.518, 0.94272, 0.65211, 0.94432, 0.81346, 0.83728, 0.88142, 0.70413, 0.96595, 0.42624, 0.96373, 0.25174, 0.96234, 0.1521, 0.77166, 0.05788, 0.59135, 0.05801, 0.27556, 0.0581, 0.06147, 0.45398, 0.00566, 0.20095, 0.18267, 0.29412, 0.37822, 0.41836, 0.57378, 0.50377, 0.68133, 0.69788, 0.79378], "triangles": [6, 17, 5, 5, 17, 4, 17, 6, 16, 6, 7, 8, 4, 17, 3, 17, 2, 3, 16, 6, 8, 17, 16, 2, 8, 15, 16, 8, 9, 15, 2, 16, 1, 16, 15, 1, 9, 14, 15, 9, 10, 14, 1, 15, 0, 15, 14, 0, 10, 13, 14, 14, 13, 0, 10, 11, 13, 13, 12, 0, 13, 11, 12], "vertices": [1, 9, 8.83, 8.84, 1, 2, 9, 18.48, 8.37, 0.98156, 10, 2.13, 8.87, 0.01844, 2, 9, 21.51, 6.38, 0.79599, 10, 3.6, 5.56, 0.20401, 2, 9, 25.15, 3.99, 0.13177, 10, 5.36, 1.58, 0.86823, 1, 10, 4.42, -0.83, 1, 1, 10, 3.25, -3.83, 1, 2, 9, 23.64, -5.59, 0.02423, 10, -1.1, -5.66, 0.97577, 2, 9, 21.96, -8.04, 0.1007, 10, -3.84, -6.81, 0.8993, 2, 9, 16.74, -6.59, 0.60352, 10, -7.44, -2.76, 0.39648, 2, 9, 11.8, -5.22, 0.95686, 10, -10.85, 1.07, 0.04314, 1, 9, 4.7, -0.49, 1, 1, 9, -0.11, 2.72, 1, 1, 9, 2.37, 9.15, 1, 1, 9, 3.96, 2.92, 1, 1, 9, 9.24, 1.31, 1, 1, 9, 14.8, 0.14, 1, 2, 9, 18.02, -0.26, 0.98697, 10, -2.93, 1.86, 0.01303, 2, 9, 22.38, 0.8, 0.22236, 10, 1.3, 0.39, 0.77764], "hull": 13, "edges": [22, 24, 2, 0, 0, 24, 18, 20, 20, 22, 14, 16, 16, 18, 2, 4, 4, 6, 10, 12, 12, 14, 6, 8, 8, 10], "width": 17, "height": 27}}, "hand_R": {"hand_R": {"type": "mesh", "uvs": [0.76562, 0.06284, 0.88187, 0.2156, 0.97843, 0.34249, 0.9791, 0.57112, 0.86898, 0.80196, 0.73909, 0.97282, 0.64972, 0.97383, 0.52916, 0.88939, 0.39958, 0.79865, 0.19196, 0.65324, 0.10013, 0.53273, 0.02029, 0.42795, 0.02187, 0.27799, 0.18244, 0.11976, 0.39024, 0.02829, 0.49787, 0.02818, 0.69199, 0.028, 0.21784, 0.27472, 0.44384, 0.30701, 0.59123, 0.37158, 0.71406, 0.57821, 0.78776, 0.70089], "triangles": [19, 15, 16, 0, 19, 16, 1, 19, 0, 20, 1, 2, 20, 2, 3, 1, 20, 19, 21, 20, 3, 8, 19, 20, 4, 21, 3, 7, 8, 20, 7, 20, 21, 21, 6, 7, 5, 21, 4, 5, 6, 21, 18, 14, 15, 17, 14, 18, 18, 15, 19, 9, 10, 17, 9, 17, 18, 8, 9, 18, 8, 18, 19, 17, 13, 14, 12, 13, 17, 17, 11, 12, 10, 11, 17], "vertices": [3, 7, 10.08, 21.14, 0.03623, 8, 21.59, 14.01, 0.00781, 12, -4.83, 13.24, 0.95596, 2, 7, 13.29, 27.99, 0.00459, 12, 2.71, 13.89, 0.99541, 2, 7, 15.96, 33.68, 1e-05, 12, 8.97, 14.42, 0.99999, 1, 12, 15.11, 9.29, 1, 2, 8, 33.72, -9.33, 0.00082, 12, 18.02, 0.21, 0.99918, 2, 8, 29.76, -16.8, 0.0721, 12, 18.75, -8.21, 0.9279, 2, 8, 25.84, -18.04, 0.14031, 12, 16.13, -11.38, 0.85969, 2, 8, 19.67, -16.83, 0.38722, 12, 10.3, -13.72, 0.61278, 3, 7, 40.02, 14.06, 0.00219, 8, 13.04, -15.54, 0.7519, 12, 4.03, -16.23, 0.24591, 3, 7, 38.48, 3.35, 0.18542, 8, 2.42, -13.48, 0.80773, 12, -6.02, -20.26, 0.00685, 2, 7, 35.95, -2.06, 0.54653, 8, -2.86, -10.68, 0.45347, 2, 7, 33.75, -6.76, 0.85742, 8, -7.44, -8.25, 0.14258, 2, 7, 28.79, -8.47, 0.98668, 8, -8.91, -3.21, 0.01332, 2, 7, 21.07, -3.41, 1, 12, -20.58, -8.56, 0, 3, 7, 14.81, 4.49, 0.59512, 8, 4.73, 10.11, 0.27883, 12, -16.87, 0.81, 0.12605, 3, 7, 13.13, 9.15, 0.29921, 8, 9.46, 11.56, 0.31748, 12, -13.69, 4.6, 0.3833, 3, 7, 10.09, 17.54, 0.06455, 8, 18, 14.19, 0.04797, 12, -7.94, 11.43, 0.88748, 2, 7, 25.62, -0.03, 0.75079, 8, -0.33, -0.46, 0.24921, 3, 7, 23.15, 10.13, 0.02981, 8, 9.94, 1.5, 0.92922, 12, -7.82, -3.59, 0.04096, 3, 7, 22.97, 17.27, 0.00341, 8, 17.09, 1.33, 0.40252, 12, -1.73, 0.14, 0.59407, 2, 8, 24.61, -3.93, 0.00313, 12, 7.44, -0.19, 0.99687, 2, 8, 29.11, -7.04, 0.0014, 12, 12.91, -0.37, 0.9986], "hull": 17, "edges": [0, 32, 4, 6, 6, 8, 8, 10, 10, 12, 22, 24, 24, 26, 26, 28, 16, 18, 0, 2, 2, 4, 12, 14, 14, 16, 28, 30, 30, 32, 18, 20, 20, 22], "width": 46, "height": 35}}, "hand_R2": {"hand_R2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27.92, 24.17, 39.47, -7.81, 0.91, -21.75, -10.64, 10.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 34, "height": 41}}, "head2": {"head": {"x": 26.69, "y": 8.89, "rotation": -4.83, "width": 109, "height": 68}}, "head3": {"head_2": {"x": 28.29, "y": 3.24, "rotation": 24.24, "width": 88, "height": 41}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.74751, 0.01912, 0.97365, 0.17406, 0.97369, 0.30901, 0.97374, 0.45872, 0.89108, 0.59041, 0.83247, 0.68379, 0.77683, 0.77245, 0.67071, 0.94152, 0.53468, 0.96516, 0.39917, 0.98872, 0.2086, 0.8539, 0.1211, 0.71061, 0.02781, 0.55784, 0.02701, 0.44901, 0.02588, 0.29552, 0.12482, 0.15488, 0.37628, 0.01945, 0.37801, 0.16156, 0.44068, 0.27952, 0.55034, 0.39379, 0.56079, 0.49332, 0.46679, 0.59285, 0.39368, 0.68501, 0.41457, 0.77716, 0.46157, 0.89144], "triangles": [6, 21, 5, 22, 21, 6, 23, 22, 6, 23, 10, 11, 23, 11, 22, 24, 23, 6, 10, 23, 24, 7, 24, 6, 8, 24, 7, 9, 10, 24, 9, 24, 8, 2, 19, 1, 19, 2, 3, 20, 19, 3, 12, 13, 21, 4, 20, 3, 21, 19, 20, 19, 21, 13, 5, 20, 4, 21, 20, 5, 22, 12, 21, 11, 12, 22, 17, 16, 0, 15, 16, 17, 18, 17, 0, 18, 0, 1, 19, 18, 1, 17, 13, 14, 15, 17, 14, 18, 13, 17, 19, 13, 18], "vertices": [2, 45, 3.59, 15.52, 0.62011, 46, -19.93, -10.47, 0.37989, 2, 45, 14.55, 18.47, 0.19185, 46, -19.14, 0.85, 0.80815, 3, 45, 20.46, 14.94, 0.0215, 46, -13.88, 5.29, 0.97843, 47, -12.62, 23.33, 6e-05, 3, 45, 27.02, 11.03, 0.11666, 46, -8.04, 10.21, 0.85212, 47, -5.11, 21.95, 0.03122, 3, 45, 31.26, 5.03, 0.48451, 46, -0.99, 12.27, 0.29735, 47, 0.96, 17.81, 0.21814, 3, 45, 34.27, 0.78, 0.45368, 46, 4.01, 13.73, 0.05514, 47, 5.26, 14.87, 0.49118, 3, 45, 37.13, -3.26, 0.26333, 46, 8.76, 15.11, 0.0006, 47, 9.34, 12.08, 0.73607, 2, 45, 42.57, -10.96, 0.04842, 47, 17.13, 6.76, 0.95158, 2, 45, 41.1, -15.78, 0.01134, 47, 17.43, 1.73, 0.98866, 2, 46, 25.96, 11.83, 0.00448, 47, 17.73, -3.29, 0.99552, 2, 46, 25.12, 2.16, 0.30736, 47, 9.72, -8.79, 0.69264, 3, 45, 22.32, -21.91, 0.02894, 46, 21.57, -4.96, 0.86919, 47, 1.96, -10.56, 0.10187, 2, 45, 13.91, -20.8, 0.2054, 46, 17.78, -12.55, 0.7946, 2, 45, 9.13, -17.98, 0.41258, 46, 13.56, -16.16, 0.58742, 2, 45, 2.38, -14.01, 0.74338, 46, 7.6, -21.23, 0.25662, 2, 45, -1.95, -7.27, 0.94953, 46, -0.18, -23.14, 0.05047, 2, 45, -3.24, 4.04, 0.98026, 46, -11.29, -20.67, 0.01974, 2, 45, 3.01, 0.38, 0.99876, 46, -5.8, -15.95, 0.00124, 1, 45, 9.34, -0.77, 1, 1, 45, 16.36, -0.36, 1, 3, 45, 20.92, -2.64, 0.00231, 46, 2.89, -0.01, 0.99766, 47, -6.07, 7.01, 4e-05, 3, 45, 23.54, -8.15, 0.00734, 46, 8.96, 0.68, 0.85182, 47, -1.69, 2.76, 0.14084, 2, 46, 14.24, 1.69, 0.16384, 47, 2.46, -0.68, 0.83616, 2, 46, 17.35, 5.3, 0.02178, 47, 7.22, -0.79, 0.97822, 3, 45, 36.52, -16.11, 0.00157, 46, 20.72, 10.35, 0.00165, 47, 13.25, -0.18, 0.99678], "hull": 17, "edges": [0, 32, 0, 2, 18, 20, 28, 30, 30, 32, 14, 16, 16, 18, 12, 14, 6, 8, 20, 22, 22, 24, 8, 10, 10, 12, 24, 26, 26, 28, 2, 4, 4, 6], "width": 36, "height": 51}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.80421, 0.05752, 0.97807, 0.19982, 0.97803, 0.4341, 0.90362, 0.56344, 0.68923, 0.79022, 0.68944, 0.89093, 0.65177, 0.95259, 0.51385, 0.92047, 0.4298, 0.99817, 0.25979, 0.91974, 0.07445, 0.71879, 0.11078, 0.60016, 0.02087, 0.45342, 0.0219, 0.30174, 0.05842, 0.22427, 0.19913, 0.10918, 0.42195, 0.01797, 0.67109, 0.01739, 0.39479, 0.15664, 0.44492, 0.30362, 0.50341, 0.43693, 0.49088, 0.56682, 0.44074, 0.64202, 0.42821, 0.72747, 0.46581, 0.83002], "triangles": [23, 11, 22, 10, 11, 23, 23, 22, 4, 24, 23, 4, 24, 4, 5, 9, 10, 23, 9, 23, 24, 7, 24, 5, 8, 9, 24, 6, 7, 5, 7, 8, 24, 21, 20, 3, 21, 11, 20, 22, 11, 21, 4, 21, 3, 22, 21, 4, 18, 15, 16, 17, 18, 16, 19, 18, 17, 0, 1, 19, 19, 1, 20, 0, 19, 17, 2, 20, 1, 3, 20, 2, 19, 20, 12, 13, 14, 19, 18, 14, 15, 19, 14, 18, 19, 12, 13, 20, 11, 12], "vertices": [2, 42, 0.7, 19.92, 0.99967, 43, -24.08, -6.8, 0.00033, 2, 42, 11.2, 23.42, 0.95938, 43, -23.49, 4.25, 0.04062, 3, 42, 22.72, 17.66, 0.71076, 43, -13.91, 12.87, 0.27423, 44, -0.34, 28.85, 0.015, 3, 42, 27.59, 11.48, 0.40869, 43, -6.39, 15.14, 0.50284, 44, 5.12, 23.19, 0.08847, 3, 42, 34.43, -2.73, 0.00145, 43, 9.34, 16.31, 0.24053, 44, 13.34, 9.73, 0.75802, 2, 43, 13.45, 20.02, 0.04887, 44, 18.52, 7.77, 0.95113, 2, 43, 17.1, 21.03, 0.01934, 44, 21.08, 4.98, 0.98066, 2, 43, 19.94, 15.24, 0.00165, 44, 17.22, -0.19, 0.99835, 1, 44, 19.87, -5.25, 1, 1, 44, 13.12, -10.86, 1, 3, 42, 18.54, -25.71, 0.02469, 43, 24.92, -6.88, 0.10794, 44, -0.18, -14.72, 0.86737, 3, 42, 13.44, -21.33, 0.15578, 43, 18.98, -10.03, 0.34065, 44, -5.7, -10.87, 0.50357, 3, 42, 4.41, -21.34, 0.48077, 43, 15.69, -18.44, 0.41381, 44, -14.68, -11.78, 0.10542, 3, 42, -3.03, -17.57, 0.70804, 43, 9.46, -23.98, 0.27521, 44, -22.46, -8.77, 0.01675, 3, 42, -6.11, -14.2, 0.79787, 43, 5.19, -25.61, 0.1987, 44, -25.85, -5.71, 0.00343, 2, 42, -8.94, -5.7, 0.94718, 43, -3.75, -25.14, 0.05282, 1, 42, -8.94, 5.51, 1, 1, 42, -3.96, 15.55, 1, 2, 42, -2.67, 1.01, 0.99875, 43, -7.7, -16.85, 0.00125, 2, 42, 5.57, -0.59, 0.99346, 43, -3.2, -9.76, 0.00654, 2, 42, 13.31, -1.52, 0.83078, 43, 0.49, -2.9, 0.16922, 2, 43, 6.18, 1.46, 0.94049, 44, -1.32, 5.76, 0.05951, 2, 43, 10.76, 2.55, 0.39802, 44, 1.74, 2.18, 0.60198, 1, 44, 5.93, -0.02, 1, 1, 44, 11.81, -0.44, 1], "hull": 18, "edges": [0, 34, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34], "width": 45, "height": 55}}, "wings_L": {"wings_L": {"type": "mesh", "uvs": [0.20156, 0.0531, 0.31501, 0.07631, 0.42104, 0.0549, 0.45267, 0.1752, 0.51699, 0.29835, 0.60377, 0.37869, 0.67182, 0.41029, 0.6634, 0.50767, 0.68996, 0.61479, 0.74649, 0.7113, 0.8228, 0.81503, 0.9116, 0.90064, 0.95686, 0.95151, 1, 1, 0.88733, 0.98565, 0.78616, 0.97276, 0.62596, 0.92911, 0.50964, 0.87313, 0.38574, 0.79738, 0.25576, 0.70582, 0.15577, 0.63539, 0.14768, 0.5384, 0.13078, 0.41214, 0.09922, 0.30543, 0.03814, 0.19706, 0, 0.15451, 0, 0, 0.10591, 0, 0.08993, 0.10578, 0.15291, 0.20011, 0.20196, 0.29617, 0.24608, 0.39836, 0.28383, 0.49029, 0.29627, 0.59226, 0.38899, 0.66911, 0.47776, 0.72299, 0.57116, 0.77629, 0.65689, 0.82226, 0.74211, 0.86691, 0.83371, 0.92182, 0.92357, 0.9659, 0.37272, 0.56348, 0.44897, 0.52781, 0.51694, 0.49593, 0.57251, 0.46973, 0.62294, 0.44278, 0.32185, 0.46902, 0.34983, 0.40897, 0.37085, 0.34978, 0.37584, 0.29948, 0.40504, 0.24894, 0.40163, 0.18312, 0.39096, 0.12371, 0.50281, 0.60281, 0.57523, 0.66157, 0.64086, 0.72117, 0.7047, 0.76684, 0.78058, 0.81607, 0.55222, 0.5785, 0.61406, 0.55871, 0.43908, 0.4601, 0.50515, 0.40036, 0.50059, 0.3514], "triangles": [52, 1, 2, 52, 2, 3, 51, 52, 3, 50, 51, 3, 1, 51, 29, 52, 51, 1, 50, 3, 4, 49, 30, 50, 49, 50, 4, 62, 49, 4, 48, 49, 62, 62, 4, 5, 43, 60, 61, 61, 62, 5, 48, 61, 47, 61, 48, 62, 60, 47, 61, 45, 5, 6, 61, 5, 45, 44, 61, 45, 7, 45, 6, 44, 45, 7, 54, 59, 8, 44, 43, 61, 42, 60, 43, 43, 44, 59, 42, 46, 60, 58, 43, 59, 42, 43, 58, 53, 42, 58, 59, 7, 8, 54, 58, 59, 59, 44, 7, 38, 37, 57, 39, 10, 11, 38, 57, 10, 39, 38, 10, 16, 37, 38, 40, 11, 12, 39, 11, 40, 15, 38, 39, 16, 38, 15, 14, 39, 40, 15, 39, 14, 40, 12, 13, 14, 40, 13, 55, 54, 8, 9, 55, 8, 36, 35, 54, 56, 55, 9, 55, 36, 54, 18, 34, 35, 57, 56, 9, 10, 57, 9, 56, 36, 55, 37, 56, 57, 37, 36, 56, 36, 18, 35, 17, 36, 37, 17, 18, 36, 16, 17, 37, 20, 21, 33, 33, 32, 41, 34, 41, 53, 33, 41, 34, 19, 20, 33, 19, 33, 34, 35, 34, 53, 18, 19, 34, 35, 53, 54, 41, 42, 53, 53, 58, 54, 31, 30, 48, 47, 31, 48, 46, 31, 47, 46, 47, 60, 32, 31, 46, 32, 21, 22, 32, 22, 31, 42, 41, 46, 32, 46, 41, 21, 32, 33, 28, 26, 27, 28, 27, 0, 25, 26, 28, 24, 25, 28, 29, 28, 0, 29, 0, 1, 24, 28, 29, 29, 51, 30, 30, 51, 50, 23, 24, 29, 23, 29, 30, 48, 30, 49, 22, 23, 30, 22, 30, 31], "vertices": [2, 32, -0.95, -3.37, 0.95592, 41, 19.26, -12.36, 0.04408, 2, 32, 2.55, -9.68, 0.4648, 41, 16.6, -5.66, 0.5352, 2, 32, 1.33, -16.29, 0.17034, 41, 18.64, 0.75, 0.82966, 1, 41, 5.92, 2.23, 1, 3, 38, 12.72, -16.13, 0.03237, 39, 14.43, 4.29, 0.10876, 40, 3.87, 5.55, 0.85887, 4, 37, 17.6, -9.72, 0.00381, 38, 9.2, -6.85, 0.53519, 39, 7.36, 11.25, 0.39256, 40, -4.83, 10.33, 0.06844, 3, 38, 9.05, -1.6, 0.90379, 39, 5.03, 15.96, 0.09088, 40, -8.34, 14.23, 0.00533, 4, 34, 5.31, -17.95, 0.02994, 35, -9.54, -18.7, 0.01287, 37, 13.63, 3.76, 0.44417, 38, 0.63, 4.27, 0.51302, 4, 34, 15.66, -13.19, 0.25231, 35, 0.43, -13.2, 0.35605, 37, 9.17, 14.24, 0.39085, 38, -7.33, 12.43, 0.00079, 4, 34, 26.04, -10.55, 0.01859, 35, 10.59, -9.8, 0.93905, 36, -7.89, -10.89, 0.0019, 37, 6.81, 24.68, 0.04047, 2, 35, 22.06, -6.89, 0.27834, 36, 3.12, -6.54, 0.72166, 1, 36, 13.28, -4.06, 1, 1, 36, 19, -2.26, 1, 1, 36, 24.46, -0.54, 1, 1, 36, 18.68, 3.23, 1, 1, 36, 13.49, 6.62, 1, 2, 35, 24.6, 9.74, 0.23442, 36, 3.51, 10.27, 0.76558, 3, 34, 32.74, 10.57, 0.00228, 35, 15.71, 11.76, 0.84854, 36, -5.56, 11.14, 0.14918, 2, 34, 22.03, 12.5, 0.22686, 35, 4.89, 12.89, 0.77314, 3, 33, 34.37, 11.42, 0.07157, 34, 9.72, 13.83, 0.82803, 35, -7.49, 13.32, 0.10039, 4, 32, 59.16, 9.08, 0.00019, 33, 25.34, 14.46, 0.34833, 34, 0.25, 14.86, 0.65091, 35, -17.01, 13.64, 0.00057, 3, 32, 49, 7.93, 0.0388, 33, 15.6, 11.37, 0.76822, 34, -8.61, 9.75, 0.19298, 2, 32, 35.71, 6.82, 0.78673, 33, 2.77, 7.71, 0.21327, 1, 32, 24.32, 6.89, 1, 1, 32, 12.47, 8.69, 1, 1, 32, 7.68, 10.22, 1, 1, 32, -8.38, 7.64, 1, 1, 32, -7.37, 1.39, 1, 1, 32, 3.47, 4.1, 1, 1, 32, 13.88, 1.96, 1, 1, 32, 24.33, 0.67, 1, 2, 32, 35.37, -0.22, 0.09531, 33, 3.8, 0.74, 0.90469, 3, 32, 45.29, -0.91, 0.01342, 33, 13.66, 1.98, 0.97854, 34, -8.48, 0.16, 0.00804, 3, 32, 56.01, 0.06, 0.00027, 33, 23.99, 5.01, 0.33339, 34, 0.96, 5.34, 0.66635, 3, 33, 33.51, 2.61, 0.00962, 34, 10.76, 5.04, 0.93643, 35, -5.8, 4.63, 0.05395, 2, 34, 18.4, 3.64, 0.253, 35, 1.93, 3.79, 0.747, 2, 34, 26.14, 1.97, 0.0016, 35, 9.77, 2.69, 0.9984, 2, 35, 16.72, 1.5, 0.99057, 36, -3.26, 1.09, 0.00943, 1, 36, 3.66, 0.73, 1, 1, 36, 11.63, 0.84, 1, 1, 36, 18.7, 0.23, 1, 4, 33, 22.73, -0.33, 0.11793, 34, 0.87, -0.15, 0.86813, 37, -4.28, -0.19, 0.01392, 39, -14.71, 2.16, 2e-05, 3, 33, 20.79, -5.91, 0.05501, 37, 1.56, -1.05, 0.92824, 39, -10.03, 5.76, 0.01675, 4, 33, 19.05, -10.88, 0.00959, 37, 6.77, -1.82, 0.89113, 38, -3.75, -3.41, 0.02644, 39, -5.84, 8.96, 0.07284, 3, 37, 11.04, -2.47, 0.42692, 38, 0.47, -2.47, 0.48768, 39, -2.41, 11.58, 0.0854, 4, 37, 15.09, -3.35, 0.01439, 38, 4.56, -1.82, 0.92661, 39, 1.04, 13.88, 0.05812, 40, -11.62, 11.15, 0.00088, 3, 33, 12.35, -0.93, 0.93186, 37, -1.75, -10.27, 0.01155, 39, -5.7, -3.04, 0.05659, 2, 33, 7, -4.69, 0.37013, 39, 0.84, -2.83, 0.62987, 4, 32, 31.51, -8.4, 0.00454, 33, 1.59, -8.02, 0.2293, 39, 7.19, -3.01, 0.60561, 40, -1.13, -3.44, 0.16055, 5, 32, 26.33, -9.53, 0.07774, 33, -3.28, -10.14, 0.09091, 39, 12.42, -3.91, 0.03649, 40, 4.15, -2.89, 0.79344, 41, -7.01, -2.79, 0.00141, 3, 32, 21.35, -12.1, 0.02401, 33, -7.67, -13.62, 0.00218, 40, 9.39, -0.89, 0.97382, 2, 32, 14.47, -13, 0.02367, 41, 5.19, -0.85, 0.97633, 2, 32, 8.2, -13.37, 0.13555, 41, 11.46, -1.28, 0.86445, 3, 34, 8.56, -4.46, 0.65385, 35, -7.29, -5.01, 0.02097, 37, 0.24, 7.38, 0.32518, 3, 34, 16.11, -4.76, 0.39559, 35, 0.25, -4.75, 0.48593, 37, 0.75, 14.91, 0.11848, 3, 34, 23.51, -4.67, 0.0111, 35, 7.63, -4.12, 0.96387, 37, 0.87, 22.31, 0.02504, 3, 35, 13.77, -4.29, 0.97416, 36, -5.45, -5.02, 0.02203, 37, 1.66, 28.4, 0.00382, 2, 35, 20.64, -4.8, 0.38995, 36, 1.43, -4.66, 0.61005, 3, 34, 8, -8.33, 0.32297, 35, -7.57, -8.91, 0.04915, 37, 4.09, 6.71, 0.62787, 4, 34, 8.24, -12.56, 0.17058, 35, -7.01, -13.11, 0.06709, 37, 8.33, 6.84, 0.7324, 38, -5.43, 5.23, 0.02992, 4, 33, 13.9, -7.83, 0.12073, 37, 4.74, -7.46, 0.31752, 38, -3.6, -9.4, 0.04073, 39, -3.21, 3.58, 0.52103, 4, 37, 11.37, -10.81, 0.05906, 38, 3.79, -10.12, 0.24295, 39, 3.81, 6.02, 0.675, 40, -6.83, 4.33, 0.02298, 4, 37, 13.8, -15.36, 0.00155, 38, 7.7, -13.49, 0.12159, 39, 8.77, 4.59, 0.58548, 40, -1.66, 4.3, 0.29138], "hull": 28, "edges": [52, 54, 54, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 48, 50, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 26, 28, 28, 30, 26, 24, 24, 22], "width": 55, "height": 73}}, "wings_L2": {"wings_L2": {"type": "mesh", "uvs": [0.90615, 0.0086, 0.97499, 0.00828, 0.97511, 0.03498, 0.90362, 0.09882, 0.84604, 0.15025, 0.8143, 0.22831, 0.7914, 0.28462, 0.76955, 0.33836, 0.77968, 0.41489, 0.79053, 0.49686, 0.80002, 0.56855, 0.73967, 0.60119, 0.64209, 0.65396, 0.64203, 0.68855, 0.64193, 0.74741, 0.64183, 0.80948, 0.64176, 0.8555, 0.6417, 0.89184, 0.41809, 0.93729, 0.26572, 1, 0.24194, 1, 0.10946, 0.94449, 0.05413, 0.84834, 0.01293, 0.77677, 0.02129, 0.70871, 0.02947, 0.64207, 0.03712, 0.5798, 0.04406, 0.52332, 0.09214, 0.42159, 0.19139, 0.34908, 0.26721, 0.29369, 0.35145, 0.23215, 0.46884, 0.14639, 0.61561, 0.09623, 0.76906, 0.04378, 0.30411, 0.53226, 0.34478, 0.56971, 0.42611, 0.60717, 0.47929, 0.66389, 0.53872, 0.71419, 0.56062, 0.76769, 0.57939, 0.81264, 0.57939, 0.85973, 0.37293, 0.49075, 0.50431, 0.50359, 0.62006, 0.51537, 0.73893, 0.53998, 0.2774, 0.46425, 0.33683, 0.39361, 0.39627, 0.33261, 0.44632, 0.27697, 0.53704, 0.2256, 0.60899, 0.17958, 0.72786, 0.12714, 0.80606, 0.08326, 0.24844, 0.92822, 0.24482, 0.86267, 0.21228, 0.79712, 0.2159, 0.73404, 0.21951, 0.66354, 0.20505, 0.60541, 0.24121, 0.44709, 0.19059, 0.52873], "triangles": [18, 42, 17, 17, 42, 16, 42, 56, 41, 41, 57, 40, 42, 41, 16, 16, 41, 15, 41, 40, 15, 15, 40, 14, 40, 58, 39, 58, 38, 39, 40, 39, 14, 14, 39, 13, 39, 38, 13, 13, 38, 12, 59, 37, 38, 38, 37, 12, 12, 37, 11, 45, 11, 37, 60, 36, 37, 37, 36, 44, 11, 45, 46, 11, 46, 10, 46, 9, 10, 46, 45, 9, 37, 44, 45, 44, 35, 43, 44, 36, 35, 43, 35, 47, 9, 45, 8, 8, 45, 44, 8, 44, 48, 43, 48, 44, 5, 52, 4, 4, 52, 53, 52, 33, 53, 52, 32, 33, 4, 53, 3, 3, 53, 54, 53, 33, 54, 3, 54, 2, 33, 34, 54, 2, 34, 0, 2, 54, 34, 0, 1, 2, 7, 8, 49, 29, 30, 49, 49, 50, 7, 7, 50, 6, 49, 30, 50, 30, 31, 50, 50, 51, 6, 6, 51, 5, 50, 31, 51, 31, 32, 51, 51, 52, 5, 51, 32, 52, 47, 62, 61, 27, 28, 61, 49, 8, 48, 47, 48, 43, 47, 61, 48, 61, 28, 48, 28, 29, 48, 48, 29, 49, 58, 59, 38, 25, 60, 59, 59, 60, 37, 25, 26, 60, 26, 62, 60, 36, 62, 35, 36, 60, 62, 26, 27, 62, 35, 62, 47, 62, 27, 61, 18, 19, 55, 19, 20, 55, 20, 21, 55, 21, 56, 55, 21, 22, 56, 42, 18, 56, 18, 55, 56, 22, 57, 56, 41, 56, 57, 58, 40, 57, 22, 23, 57, 57, 23, 58, 23, 24, 58, 58, 24, 59, 24, 25, 59], "vertices": [1, 36, 23.36, 1.36, 1, 1, 36, 24.81, -0.9, 1, 1, 36, 22.23, -2.51, 1, 1, 36, 14.58, -4, 1, 2, 35, 27.49, -6.23, 0.01305, 36, 8.42, -5.19, 0.98695, 5, 35, 18.89, -8.8, 0.64669, 36, 0.21, -8.85, 0.35022, 37, 6.67, 33.04, 0.00309, 39, -30.96, 33.14, 0, 40, -47.64, 21.03, 0, 7, 34, 28.06, -11.57, 0.00571, 35, 12.68, -10.67, 0.92105, 36, -5.71, -11.49, 0.0354, 37, 7.89, 26.67, 0.0378, 38, -13.02, 23.56, 4e-05, 39, -25.54, 29.59, 0, 40, -41.46, 19.08, 0, 6, 34, 22.02, -12.9, 0.07865, 35, 6.75, -12.44, 0.75392, 37, 9.06, 20.6, 0.16136, 38, -9.74, 18.32, 0.00608, 39, -20.36, 26.2, 0, 40, -35.56, 17.22, 0, 6, 34, 13.98, -16.31, 0.15578, 35, -1.02, -16.43, 0.26253, 37, 12.24, 12.47, 0.48726, 38, -3.82, 11.89, 0.09443, 39, -12.31, 22.83, 0, 40, -26.89, 16.16, 0, 6, 34, 5.37, -19.96, 0.01622, 35, -9.33, -20.71, 0.01998, 37, 15.65, 3.76, 0.2147, 38, 2.51, 5, 0.7491, 39, -3.68, 19.22, 0, 40, -17.6, 15.02, 0, 5, 34, -2.16, -23.16, 0, 35, -16.61, -24.44, 0, 38, 8.05, -1.02, 0.94815, 39, 3.87, 16.07, 0.05184, 40, -9.48, 14.02, 1e-05, 6, 34, -6.47, -22.24, 0, 35, -20.98, -23.85, 0, 37, 17.6, -8.14, 0.00573, 38, 8.63, -5.38, 0.65327, 39, 6.23, 12.35, 0.32678, 40, -6.21, 11.08, 0.01422, 5, 35, -28.03, -22.9, 0, 37, 15.94, -15.06, 0.00027, 38, 9.59, -12.44, 0.1399, 39, 10.04, 6.34, 0.5179, 40, -0.91, 6.33, 0.34193, 4, 35, -31.62, -24.54, 0, 38, 12.12, -15.46, 0.02454, 39, 13.61, 4.65, 0.13355, 40, 2.98, 5.67, 0.84191, 3, 35, -37.72, -27.33, 0, 40, 9.59, 4.55, 0.81294, 41, -1.46, 4.57, 0.18706, 2, 40, 16.57, 3.36, 0.00058, 41, 5.5, 3.28, 0.99942, 3, 32, 9.45, -16.85, 0.04163, 40, 21.74, 2.49, 0, 41, 10.66, 2.33, 0.95837, 2, 32, 5.31, -16.62, 0.1364, 41, 14.73, 1.57, 0.8636, 3, 32, 0.62, -7.62, 0.75858, 34, -46.76, -23.83, 0, 41, 18.25, -7.94, 0.24142, 3, 32, -6.19, -1.3, 0.99989, 34, -55.53, -20.75, 0, 41, 24.2, -15.08, 0.00011, 2, 32, -6.14, -0.37, 1, 34, -55.85, -19.88, 0, 2, 32, 0.46, 4.44, 1, 40, 28.29, -19.67, 0, 1, 32, 11.53, 5.99, 1, 1, 32, 19.76, 7.15, 1, 2, 32, 27.49, 6.39, 1, 40, 1.21, -18.59, 0, 3, 32, 35.06, 5.66, 0.74265, 33, 2.36, 6.45, 0.25735, 40, -6.23, -17.01, 0, 2, 32, 42.13, 4.97, 0.10099, 33, 9.43, 7.14, 0.89901, 3, 32, 48.54, 4.34, 0.00296, 33, 15.84, 7.76, 0.94767, 34, -7.6, 6.28, 0.04938, 2, 33, 27.59, 7.51, 0.14557, 34, 3.93, 8.56, 0.85443, 2, 34, 13.03, 7.81, 0.86128, 35, -3.74, 7.55, 0.13872, 3, 34, 19.98, 7.23, 0.17592, 35, 3.23, 7.49, 0.82408, 40, -43.84, -1.25, 0, 2, 35, 10.97, 7.43, 0.99989, 36, -9.71, 6.24, 0.00011, 2, 35, 21.77, 7.33, 0.35598, 36, 1.01, 7.52, 0.64402, 2, 35, 29.35, 4.51, 5e-05, 36, 8.89, 5.69, 0.99995, 2, 36, 17.13, 3.78, 1, 40, -68.67, 22.8, 0, 4, 33, 16.24, -2.42, 0.73459, 37, -1.01, -6.17, 0.18408, 39, -8.13, 0.35, 0.08133, 40, -16.79, -4.36, 0, 5, 33, 12.23, -4.58, 0.41406, 37, 1.87, -9.7, 0.11268, 38, -5.47, -12.53, 0.00286, 39, -3.59, -0.04, 0.4704, 40, -12.31, -3.51, 0, 4, 35, -26.69, -13.02, 0, 37, 6.24, -12.73, 0.01692, 38, -0.29, -13.77, 0.01378, 39, 1.62, 1, 0.96929, 4, 35, -31.7, -17.6, 0, 38, 5.45, -17.38, 0.00074, 39, 8.35, 0.11, 0.91298, 40, -0.85, -0.12, 0.08628, 3, 35, -35.95, -22.09, 0, 38, 10.91, -20.29, 0.00041, 40, 5.19, 1.21, 0.99959, 2, 40, 11.35, 1.03, 0.43068, 41, 0.24, 1.03, 0.56932, 1, 41, 5.41, 0.82, 1, 4, 32, 9.1, -14.39, 0.08664, 34, -36.28, -26.65, 0, 40, 21.81, 0.01, 0, 41, 10.69, -0.15, 0.91336, 3, 33, 21.3, -4.42, 0.08389, 37, 0.01, -0.83, 0.90904, 39, -11.26, 4.79, 0.00708, 4, 33, 20.56, -9.7, 0.00375, 37, 5.33, -0.56, 0.98035, 38, -5.55, -2.76, 0.00195, 39, -7.75, 8.8, 0.01395, 3, 37, 10.04, -0.38, 0.99208, 38, -1.23, -0.89, 0.00125, 39, -4.61, 12.31, 0.00667, 3, 37, 15.33, -1.55, 0.00014, 38, 4.13, -0.06, 0.99919, 39, -0.09, 15.3, 0.00067, 1, 34, 1.89, 0.09, 1, 1, 34, 10.24, 0.72, 1, 2, 34, 17.57, 0.97, 0.08206, 35, 1.29, 1.07, 0.91794, 2, 35, 7.87, 1.93, 1, 40, -44.56, 5.96, 0, 1, 35, 14.67, 1.16, 1, 2, 35, 20.6, 0.79, 0.19559, 36, 0.69, 0.88, 0.80441, 1, 36, 8.21, 0.11, 1, 1, 36, 14.07, 0.17, 1, 3, 32, 2.02, -1.07, 0.98554, 34, -48.09, -17.27, 0, 41, 16.03, -14.26, 0.01446, 3, 32, 9.49, -1.34, 0.96092, 34, -41.13, -14.53, 0, 41, 8.65, -13.05, 0.03908, 5, 32, 17.02, -0.49, 0.98757, 33, -14.16, -3.06, 4e-05, 34, -34.57, -10.74, 0, 40, 12.39, -12.92, 0.00366, 41, 1.08, -12.94, 0.00873, 6, 32, 24.19, -1.03, 0.93952, 33, -7.02, -2.2, 0.02449, 34, -27.78, -8.37, 0, 39, 11.2, -12.59, 0.00136, 40, 5.32, -11.58, 0.03316, 41, -5.97, -11.5, 0.00147, 4, 33, 0.96, -1.23, 0.94418, 34, -20.2, -5.7, 0, 39, 3.99, -9.03, 0.04432, 40, -2.58, -10.11, 0.0115, 2, 32, 38.85, -1.41, 0.00545, 33, 7.45, 0.25, 0.99455, 2, 33, 25.51, 1.35, 0.07423, 34, 3.23, 2.1, 0.92577, 2, 33, 16.02, 2.02, 0.99456, 34, -6.18, 0.71, 0.00544], "hull": 35, "edges": [0, 68, 0, 2, 2, 4, 34, 36, 36, 38, 38, 40, 40, 42, 54, 56, 64, 66, 66, 68, 4, 6, 6, 8, 8, 10, 62, 64, 10, 12, 12, 14, 60, 62, 56, 58, 58, 60, 14, 16, 52, 54, 50, 52, 46, 48, 48, 50, 42, 44, 44, 46, 32, 34, 30, 32, 28, 30, 24, 26, 26, 28, 20, 22, 22, 24, 16, 18, 18, 20], "width": 39, "height": 114}}, "wings_R": {"wings_R": {"type": "mesh", "uvs": [0.95476, 0.00806, 0.98891, 0.05396, 0.98906, 0.13759, 0.86637, 0.2379, 0.83164, 0.32014, 0.91382, 0.45535, 0.83758, 0.54597, 0.7168, 0.64446, 0.63315, 0.71267, 0.53331, 0.79409, 0.44964, 0.86232, 0.30892, 0.93322, 0.19755, 0.98934, 0.08981, 0.99289, 0.15801, 0.86817, 0.15742, 0.78252, 0.15696, 0.71528, 0.09145, 0.6454, 0.00115, 0.54907, 0.12235, 0.45041, 0.17808, 0.35137, 0.21154, 0.20604, 0.19389, 0.14668, 0.38112, 0.14662, 0.55054, 0.11898, 0.74354, 0.06713, 0.92137, 0.00904, 0.27109, 0.18146, 0.36278, 0.2343, 0.45177, 0.29155, 0.54075, 0.32678, 0.61896, 0.36201, 0.72412, 0.39063, 0.11469, 0.53595, 0.30075, 0.52935, 0.48682, 0.50733, 0.63783, 0.49192, 0.75918, 0.4721, 0.24952, 0.87504, 0.36547, 0.8354, 0.48412, 0.77375, 0.55424, 0.7099, 0.63514, 0.64384, 0.71873, 0.57999, 0.77266, 0.52715, 0.78345, 0.42366, 0.72952, 0.33339, 0.7457, 0.27394, 0.79154, 0.20568, 0.84278, 0.13522, 0.91019, 0.08898, 0.81311, 0.48751, 0.26839, 0.37962, 0.37626, 0.39944, 0.46794, 0.41705, 0.55424, 0.41705, 0.59738, 0.55797, 0.51648, 0.61082, 0.41671, 0.63724, 0.33851, 0.66806, 0.24952, 0.68788, 0.49761, 0.19027, 0.61356, 0.22329, 0.68907, 0.19907], "triangles": [27, 22, 23, 61, 23, 24, 21, 22, 27, 28, 27, 23, 28, 23, 61, 28, 52, 21, 28, 21, 27, 53, 52, 28, 20, 21, 52, 29, 28, 61, 29, 53, 28, 54, 29, 30, 53, 29, 54, 30, 61, 62, 29, 61, 30, 31, 30, 62, 46, 31, 62, 32, 31, 46, 55, 30, 31, 54, 30, 55, 36, 31, 32, 19, 20, 52, 19, 52, 34, 33, 18, 19, 17, 18, 33, 34, 33, 19, 60, 33, 34, 17, 33, 60, 16, 17, 60, 34, 52, 53, 53, 35, 34, 58, 34, 35, 59, 34, 58, 60, 34, 59, 36, 32, 37, 55, 31, 36, 35, 54, 55, 35, 55, 36, 36, 37, 44, 56, 35, 36, 43, 36, 44, 56, 36, 43, 57, 35, 56, 35, 53, 54, 58, 35, 57, 15, 16, 60, 60, 59, 39, 15, 60, 39, 10, 39, 40, 38, 14, 15, 39, 38, 15, 11, 38, 39, 11, 39, 10, 12, 14, 38, 12, 38, 11, 13, 14, 12, 58, 57, 41, 40, 58, 41, 59, 58, 40, 9, 40, 41, 9, 41, 8, 39, 59, 40, 10, 40, 9, 42, 56, 43, 57, 56, 42, 7, 42, 43, 41, 57, 42, 8, 41, 42, 8, 42, 7, 44, 51, 6, 43, 44, 6, 7, 43, 6, 51, 45, 5, 44, 37, 51, 6, 51, 5, 45, 32, 4, 45, 4, 5, 37, 32, 45, 37, 45, 51, 50, 25, 26, 26, 0, 1, 50, 26, 1, 49, 25, 50, 50, 1, 2, 63, 24, 25, 48, 63, 25, 62, 61, 24, 49, 48, 25, 63, 62, 24, 2, 3, 49, 2, 49, 50, 48, 49, 3, 47, 63, 48, 3, 47, 48, 4, 47, 3, 47, 62, 63, 46, 47, 4, 46, 62, 47, 4, 32, 46], "vertices": [2, 17, -36.56, -17.45, 1, 23, -103.27, 86.45, 0, 2, 17, -29.84, -20.7, 1, 23, -102.47, 79.03, 0, 2, 17, -17.27, -21.11, 1, 23, -96.17, 68.15, 0, 3, 17, -1.55, -10.67, 0.99726, 18, -14.81, -17.47, 0.00274, 29, -25.37, 36.49, 0, 5, 17, 11, -7.97, 0.74414, 18, -4.18, -10.28, 0.24931, 19, -17.6, -13.07, 0.0064, 20, -22.83, 22.12, 0.00014, 29, -32.9, 26.1, 0, 4, 17, 30.89, -15.92, 0.00574, 18, 17.23, -10.21, 0.27367, 19, 3.56, -9.79, 0.71149, 20, -12.23, 3.51, 0.0091, 1, 20, 2.7, -0.26, 1, 2, 20, 21.18, -1.85, 0.03555, 21, 2.24, -1.74, 0.96445, 2, 21, 15.08, -2.17, 1, 29, -67.04, -25.77, 0, 1, 22, 10.39, -2.39, 1, 2, 22, 23.24, -2.46, 0.32059, 23, 0.8, -3.13, 0.67941, 1, 23, 17.17, -6.36, 1, 1, 23, 30.12, -8.92, 1, 1, 23, 38.82, -4.79, 1, 6, 22, 39.72, 17.67, 0.00727, 23, 24.06, 8.52, 0.95148, 25, 28.31, -54.21, 0.01571, 26, 3.85, -55.3, 0.02554, 28, -31.93, -80.81, 0, 29, -58.58, -73.96, 0, 6, 22, 29.3, 25.23, 0.06195, 23, 17.64, 19.68, 0.686, 25, 27.65, -41.34, 0.09404, 26, 5, -42.47, 0.15799, 28, -23.18, -71.36, 0, 29, -48.7, -65.7, 2e-05, 6, 22, 21.12, 31.17, 0.08498, 23, 12.59, 28.45, 0.38506, 25, 27.12, -31.25, 0.15317, 26, 5.91, -32.4, 0.37673, 28, -16.3, -63.94, 1e-05, 29, -40.94, -59.22, 5e-05, 6, 22, 16.13, 41.95, 0.03472, 23, 12.44, 40.32, 0.12182, 25, 32.36, -20.59, 0.0711, 26, 12.6, -22.59, 0.77214, 28, -5.01, -60.27, 5e-05, 29, -29.27, -57.02, 0.00017, 5, 22, 9.26, 56.81, 0.00078, 23, 12.23, 56.69, 0.0108, 26, 21.82, -9.07, 0.98768, 28, 10.56, -55.21, 0.00016, 29, -13.18, -53.98, 0.00059, 5, 23, -4.71, 64.36, 0, 25, 27.97, 8.61, 0.00158, 26, 12.36, 6.94, 0.95704, 28, 12.86, -36.76, 0.0109, 29, -8.56, -35.97, 0.03048, 5, 23, -16.55, 74.87, 0, 25, 22.17, 23.34, 0.10562, 26, 8.7, 22.34, 0.42518, 28, 19.38, -22.34, 0.10699, 29, -0.26, -22.49, 0.36221, 4, 23, -30.15, 92.34, 0, 25, 17.97, 45.08, 0.0045, 26, 7.59, 44.45, 0.01679, 29, 14.6, -6.07, 0.97871, 1, 29, 22.41, -1.55, 1, 4, 17, -12.69, 32.9, 0.04298, 27, 38.23, 19.27, 0.06656, 28, 27.25, 12.94, 0.13035, 29, 12.03, 11.51, 0.76011, 4, 17, -17.74, 17.97, 0.44117, 27, 26.98, 30.31, 0.29831, 28, 19.24, 26.53, 0.15404, 29, 5.82, 26, 0.10648, 5, 17, -26.56, 1.05, 0.94998, 23, -82.28, 87.76, 0, 27, 15.59, 45.61, 0.04333, 28, 12.19, 44.25, 0.00503, 29, 1.08, 44.48, 0.00167, 2, 17, -36.24, -14.49, 1, 23, -100.58, 87.75, 0, 3, 17, -6.87, 42.52, 5e-05, 27, 44.36, 9.84, 9e-05, 29, 14.12, 0.46, 0.99986, 4, 17, 0.59, 34.12, 0.00337, 27, 33.55, 6.78, 0.00645, 28, 19.49, 2.1, 0.07428, 29, 2.96, 1.73, 0.9159, 4, 17, 8.72, 25.94, 0.005, 27, 22.65, 3.02, 0.03824, 28, 7.99, 1.28, 0.95667, 29, -8.55, 2.39, 9e-05, 2, 17, 13.54, 17.86, 0.01968, 27, 13.27, 2.19, 0.98032, 3, 17, 18.42, 10.74, 0.02062, 27, 4.74, 0.89, 0.97938, 29, -25.93, 7.22, 0, 5, 17, 22.17, 1.26, 0.05429, 18, 2.73, 2.45, 0.70585, 23, -56.32, 46.52, 0, 24, -3.43, 11.5, 0.06416, 27, -5.43, 1.65, 0.1757, 6, 22, 1.52, 49.92, 0.00359, 23, 2.35, 53.56, 0.01343, 25, 29.37, -4.22, 0.00179, 26, 11.94, -5.96, 0.98109, 28, 4.64, -46.71, 2e-05, 29, -17.97, -44.79, 8e-05, 5, 21, 11.79, 37.58, 0.00063, 22, -9.35, 37.33, 0.01348, 23, -12.71, 46.5, 0.00873, 25, 12.75, -3.68, 0.95037, 26, -4.44, -3.09, 0.02679, 5, 21, -1.27, 26.7, 0.0095, 22, -22.09, 26.09, 0.00335, 23, -28.93, 41.45, 2e-05, 24, 21.21, -1.5, 0.98714, 29, -35.31, -16.07, 0, 6, 19, -2.85, 14.73, 1e-05, 20, 8.4, 18.23, 0.01748, 21, -11.57, 17.65, 0.01433, 23, -41.91, 37.03, 0, 24, 7.5, -1.85, 0.96817, 29, -41.91, -4.05, 0, 5, 18, 15.22, 3.71, 0.36522, 19, -0.52, 3.68, 0.16189, 20, -1.1, 12.12, 0.04645, 24, -3.77, -1.05, 0.42643, 29, -46.36, 6.33, 0, 6, 22, 35.61, 10.58, 0.00602, 23, 17.42, 3.74, 0.97421, 25, 20.22, -55.46, 0.00853, 26, -4.33, -55.4, 0.01123, 28, -38.47, -75.88, 0, 29, -64.44, -68.25, 0, 6, 22, 24.51, 5.86, 0.08195, 23, 5.35, 3.95, 0.89393, 25, 9.57, -49.8, 0.01601, 26, -14.09, -48.3, 0.00811, 28, -41.84, -64.29, 0, 29, -66.31, -56.32, 0, 7, 22, 10.57, 2.88, 0.96846, 23, -8.59, 6.92, 0.00806, 24, 30.34, -40.51, 0.0037, 25, -1.52, -40.83, 0.01799, 26, -23.8, -37.86, 0.00179, 28, -43.15, -50.1, 0, 29, -65.8, -42.08, 0, 6, 21, 19.17, 3.55, 0.44927, 22, -1.01, 3.52, 0.499, 23, -18.9, 12.24, 5e-05, 24, 22.08, -32.37, 0.02933, 25, -8.3, -31.42, 0.02226, 26, -29.19, -27.59, 8e-05, 7, 20, 25.98, 3.62, 0.0067, 21, 6.74, 3.97, 0.89179, 22, -13.45, 3.6, 0.00232, 23, -30.22, 17.39, 0, 24, 12.81, -24.08, 0.09568, 25, -16.06, -21.7, 0.00351, 29, -59.24, -18.96, 0, 6, 19, 12.05, 14.7, 0.00631, 20, 13.68, 4.3, 0.74431, 21, -5.58, 4.01, 0.07275, 23, -41.59, 22.13, 0, 24, 3.37, -16.17, 0.17662, 29, -56.53, -6.95, 0, 6, 18, 23.37, 5.43, 0.0007, 19, 7.28, 6.6, 0.23107, 20, 4.41, 5.87, 0.54199, 23, -49.8, 26.71, 0, 24, -3.11, -9.35, 0.22624, 29, -53.44, 1.93, 0, 4, 17, 26.82, -4.17, 0.00921, 18, 9.07, -0.85, 0.97624, 19, -5.92, -1.76, 0.01456, 29, -42.14, 12.71, 0, 4, 17, 13.53, 1.05, 0.94061, 23, -61.06, 53.74, 0, 27, -1.86, 9.52, 0.05939, 29, -28.76, 17.7, 0, 2, 17, 4.51, -0.11, 0.99982, 18, -13.14, -5.42, 0.00018, 4, 17, -5.99, -3.87, 0.9972, 27, 2.21, 29.23, 0.00268, 28, -4.97, 31.89, 0.00011, 29, -17.52, 34.4, 1e-05, 4, 17, -16.85, -8.09, 0.99748, 23, -84.9, 74.69, 0, 27, 3.13, 40.85, 0.00251, 28, -1.07, 42.88, 1e-05, 2, 17, -24.16, -13.87, 1, 23, -93.67, 77.83, 0, 5, 19, 3.7, 0.52, 0.97393, 20, -2.54, 7.05, 0.01649, 23, -55.96, 30.14, 0, 24, -7.97, -4.24, 0.00958, 29, -51.12, 8.59, 0, 6, 23, -21.49, 67.35, 0, 24, 36.03, 21.01, 3e-05, 25, 14.37, 18.88, 0.25931, 26, 0.34, 19.02, 0.2855, 28, 10.73, -19.86, 0.22441, 29, -8.52, -18.93, 0.23076, 6, 23, -28.43, 60.18, 0, 24, 27.26, 16.24, 0.05655, 25, 4.93, 15.63, 0.39947, 26, -9.46, 17.14, 0.03391, 28, 1.83, -15.35, 0.47242, 29, -16.77, -13.33, 0.03766, 6, 23, -34.27, 53.99, 0, 24, 19.84, 12.07, 0.35701, 25, -3.08, 12.76, 0.15336, 26, -17.8, 15.42, 7e-05, 27, 12.65, -13.01, 0.05628, 28, -5.82, -11.61, 0.43328, 4, 24, 12.3, 10.57, 0.44716, 25, -10.77, 12.55, 0.00366, 27, 5.94, -9.26, 0.41908, 28, -11.33, -6.25, 0.1301, 6, 20, 18.38, 14.48, 0.05492, 21, -1.41, 14.42, 0.2631, 22, -21.89, 13.82, 0.00855, 24, 13.24, -10.84, 0.66933, 25, -13.42, -8.72, 0.0041, 29, -47.27, -13.27, 0, 7, 20, 29.26, 14.69, 0.00028, 21, 9.44, 15.2, 0.32548, 22, -11.07, 14.9, 0.14243, 23, -23.43, 26.73, 0.00273, 24, 22.07, -17.18, 0.39444, 25, -5.77, -16.45, 0.13453, 26, -24.58, -13.12, 0.00011, 8, 21, 18.19, 19.7, 0.09183, 22, -2.45, 19.64, 0.29447, 23, -13.63, 27.54, 0.05189, 24, 31.67, -19.33, 0.12559, 25, 3.33, -20.17, 0.41269, 26, -16.08, -18.09, 0.02354, 28, -24.95, -39.17, 0, 29, -46.36, -33.55, 0, 8, 21, 26.25, 22.31, 0.01712, 22, 5.54, 22.47, 0.27578, 23, -5.18, 26.86, 0.17671, 24, 39.53, -22.49, 0.02122, 25, 10.56, -24.61, 0.39217, 26, -9.56, -23.49, 0.11699, 28, -23.09, -47.44, 0, 29, -45.57, -41.99, 1e-05, 8, 21, 33.61, 26.66, 0.00057, 22, 12.77, 27.03, 0.1491, 23, 3.28, 28.07, 0.30708, 24, 47.97, -23.86, 0.00047, 25, 18.65, -27.36, 0.25642, 26, -1.94, -27.36, 0.28634, 28, -19.43, -55.16, 1e-05, 29, -42.92, -50.11, 2e-05, 4, 17, -6.75, 22.34, 0.20629, 27, 26.14, 18.51, 0.30363, 28, 15.37, 15.35, 0.31005, 29, 0.55, 15.4, 0.18003, 4, 17, -2.4, 11.88, 0.50439, 27, 14.82, 19.15, 0.42701, 28, 4.6, 18.89, 0.05155, 29, -9.67, 20.28, 0.01705, 4, 17, -6.44, 5.28, 0.82143, 27, 10.64, 25.66, 0.15929, 28, 2.24, 26.26, 0.01368, 29, -11.08, 27.89, 0.0056], "hull": 27, "edges": [0, 52, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 24, 26, 26, 28, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 20, 22, 22, 24, 18, 20, 16, 18, 12, 14, 14, 16, 32, 34, 34, 36, 28, 30, 30, 32], "width": 89, "height": 109}, "wings_R2": {"type": "mesh", "path": "wings_R", "uvs": [0.72952, 0.33339, 0.91382, 0.45535, 0.83758, 0.54597, 0.7168, 0.64446, 0.63315, 0.71267, 0.53331, 0.79409, 0.44964, 0.86232, 0.30892, 0.93322, 0.19755, 0.98934, 0.08981, 0.99289, 0.15801, 0.86817, 0.15742, 0.78252, 0.15696, 0.71528, 0.09145, 0.6454, 0.00115, 0.54907, 0.12235, 0.45041, 0.17808, 0.35137, 0.21154, 0.20604, 0.19389, 0.14668, 0.27109, 0.18146, 0.36278, 0.2343, 0.45177, 0.29155, 0.54075, 0.32678, 0.61896, 0.36201, 0.72412, 0.39063, 0.11469, 0.53595, 0.30075, 0.52935, 0.48682, 0.50733, 0.63783, 0.49192, 0.75918, 0.4721, 0.24952, 0.87504, 0.36547, 0.8354, 0.48412, 0.77375, 0.55424, 0.7099, 0.63514, 0.64384, 0.71873, 0.57999, 0.77266, 0.52715, 0.78345, 0.42366, 0.81311, 0.48751, 0.26839, 0.37962, 0.37626, 0.39944, 0.46794, 0.41705, 0.55424, 0.41705, 0.59738, 0.55797, 0.51648, 0.61082, 0.41671, 0.63724, 0.33851, 0.66806, 0.24952, 0.68788], "triangles": [0, 19, 18, 17, 18, 19, 0, 20, 19, 20, 39, 17, 20, 17, 19, 40, 39, 20, 16, 17, 39, 20, 0, 21, 21, 40, 20, 41, 21, 22, 40, 21, 41, 0, 22, 21, 23, 22, 0, 24, 23, 0, 42, 22, 23, 41, 22, 42, 28, 23, 24, 15, 16, 39, 15, 39, 26, 25, 14, 15, 13, 14, 25, 26, 25, 15, 47, 25, 26, 13, 25, 47, 12, 13, 47, 26, 39, 40, 40, 27, 26, 45, 26, 27, 46, 26, 45, 47, 26, 46, 28, 24, 29, 42, 23, 28, 27, 41, 42, 27, 42, 28, 28, 29, 36, 43, 27, 28, 35, 28, 36, 43, 28, 35, 44, 27, 43, 27, 40, 41, 45, 27, 44, 11, 12, 47, 47, 46, 31, 11, 47, 31, 6, 31, 32, 30, 10, 11, 31, 30, 11, 7, 30, 31, 7, 31, 6, 8, 10, 30, 8, 30, 7, 9, 10, 8, 45, 44, 33, 32, 45, 33, 46, 45, 32, 5, 32, 33, 5, 33, 4, 31, 46, 32, 6, 32, 5, 34, 43, 35, 44, 43, 34, 3, 34, 35, 33, 44, 34, 4, 33, 34, 4, 34, 3, 36, 38, 2, 35, 36, 2, 3, 35, 2, 38, 37, 1, 36, 29, 38, 2, 38, 1, 1, 37, 0, 24, 0, 37, 29, 24, 37, 29, 37, 38], "vertices": [4, 17, 13.53, 1.05, 0.94061, 23, -61.06, 53.74, 0, 27, -1.86, 9.52, 0.05939, 29, -28.76, 17.7, 0, 4, 17, 30.89, -15.92, 0.00574, 18, 17.23, -10.21, 0.27367, 19, 3.56, -9.79, 0.71149, 20, -12.23, 3.51, 0.0091, 1, 20, 2.7, -0.26, 1, 2, 20, 21.18, -1.85, 0.03555, 21, 2.24, -1.74, 0.96445, 2, 21, 15.08, -2.17, 1, 29, -67.04, -25.77, 0, 1, 22, 10.39, -2.39, 1, 2, 22, 23.24, -2.46, 0.32059, 23, 0.8, -3.13, 0.67941, 1, 23, 17.17, -6.36, 1, 1, 23, 30.12, -8.92, 1, 1, 23, 38.82, -4.79, 1, 6, 22, 39.72, 17.67, 0.00727, 23, 24.06, 8.52, 0.95148, 25, 28.31, -54.21, 0.01571, 26, 3.85, -55.3, 0.02554, 28, -31.93, -80.81, 0, 29, -58.58, -73.96, 0, 6, 22, 29.3, 25.23, 0.06195, 23, 17.64, 19.68, 0.686, 25, 27.65, -41.34, 0.09404, 26, 5, -42.47, 0.15799, 28, -23.18, -71.36, 0, 29, -48.7, -65.7, 2e-05, 6, 22, 21.12, 31.17, 0.08498, 23, 12.59, 28.45, 0.38506, 25, 27.12, -31.25, 0.15317, 26, 5.91, -32.4, 0.37673, 28, -16.3, -63.94, 1e-05, 29, -40.94, -59.22, 5e-05, 6, 22, 16.13, 41.95, 0.03472, 23, 12.44, 40.32, 0.12182, 25, 32.36, -20.59, 0.0711, 26, 12.6, -22.59, 0.77214, 28, -5.01, -60.27, 5e-05, 29, -29.27, -57.02, 0.00017, 5, 22, 9.26, 56.81, 0.00078, 23, 12.23, 56.69, 0.0108, 26, 21.82, -9.07, 0.98768, 28, 10.56, -55.21, 0.00016, 29, -13.18, -53.98, 0.00059, 5, 23, -4.71, 64.36, 0, 25, 27.97, 8.61, 0.00158, 26, 12.36, 6.94, 0.95704, 28, 12.86, -36.76, 0.0109, 29, -8.56, -35.97, 0.03048, 5, 23, -16.55, 74.87, 0, 25, 22.17, 23.34, 0.10562, 26, 8.7, 22.34, 0.42518, 28, 19.38, -22.34, 0.10699, 29, -0.26, -22.49, 0.36221, 4, 23, -30.15, 92.34, 0, 25, 17.97, 45.08, 0.0045, 26, 7.59, 44.45, 0.01679, 29, 14.6, -6.07, 0.97871, 1, 29, 22.41, -1.55, 1, 3, 17, -6.87, 42.52, 5e-05, 27, 44.36, 9.84, 9e-05, 29, 14.12, 0.46, 0.99986, 4, 17, 0.59, 34.12, 0.00337, 27, 33.55, 6.78, 0.00645, 28, 19.49, 2.1, 0.07428, 29, 2.96, 1.73, 0.9159, 4, 17, 8.72, 25.94, 0.005, 27, 22.65, 3.02, 0.03824, 28, 7.99, 1.28, 0.95667, 29, -8.55, 2.39, 9e-05, 2, 17, 13.54, 17.86, 0.01968, 27, 13.27, 2.19, 0.98032, 3, 17, 18.42, 10.74, 0.02062, 27, 4.74, 0.89, 0.97938, 29, -25.93, 7.22, 0, 5, 17, 22.17, 1.26, 0.05429, 18, 2.73, 2.45, 0.70585, 23, -56.32, 46.52, 0, 24, -3.43, 11.5, 0.06416, 27, -5.43, 1.65, 0.1757, 6, 22, 1.52, 49.92, 0.00359, 23, 2.35, 53.56, 0.01343, 25, 29.37, -4.22, 0.00179, 26, 11.94, -5.96, 0.98109, 28, 4.64, -46.71, 2e-05, 29, -17.97, -44.79, 8e-05, 5, 21, 11.79, 37.58, 0.00063, 22, -9.35, 37.33, 0.01348, 23, -12.71, 46.5, 0.00873, 25, 12.75, -3.68, 0.95037, 26, -4.44, -3.09, 0.02679, 5, 21, -1.27, 26.7, 0.0095, 22, -22.09, 26.09, 0.00335, 23, -28.93, 41.45, 2e-05, 24, 21.21, -1.5, 0.98714, 29, -35.31, -16.07, 0, 6, 19, -2.85, 14.73, 1e-05, 20, 8.4, 18.23, 0.01748, 21, -11.57, 17.65, 0.01433, 23, -41.91, 37.03, 0, 24, 7.5, -1.85, 0.96817, 29, -41.91, -4.05, 0, 5, 18, 15.22, 3.71, 0.36522, 19, -0.52, 3.68, 0.16189, 20, -1.1, 12.12, 0.04645, 24, -3.77, -1.05, 0.42643, 29, -46.36, 6.33, 0, 6, 22, 35.61, 10.58, 0.00602, 23, 17.42, 3.74, 0.97421, 25, 20.22, -55.46, 0.00853, 26, -4.33, -55.4, 0.01123, 28, -38.47, -75.88, 0, 29, -64.44, -68.25, 0, 6, 22, 24.51, 5.86, 0.08195, 23, 5.35, 3.95, 0.89393, 25, 9.57, -49.8, 0.01601, 26, -14.09, -48.3, 0.00811, 28, -41.84, -64.29, 0, 29, -66.31, -56.32, 0, 7, 22, 10.57, 2.88, 0.96846, 23, -8.59, 6.92, 0.00806, 24, 30.34, -40.51, 0.0037, 25, -1.52, -40.83, 0.01799, 26, -23.8, -37.86, 0.00179, 28, -43.15, -50.1, 0, 29, -65.8, -42.08, 0, 6, 21, 19.17, 3.55, 0.44927, 22, -1.01, 3.52, 0.499, 23, -18.9, 12.24, 5e-05, 24, 22.08, -32.37, 0.02933, 25, -8.3, -31.42, 0.02226, 26, -29.19, -27.59, 8e-05, 7, 20, 25.98, 3.62, 0.0067, 21, 6.74, 3.97, 0.89179, 22, -13.45, 3.6, 0.00232, 23, -30.22, 17.39, 0, 24, 12.81, -24.08, 0.09568, 25, -16.06, -21.7, 0.00351, 29, -59.24, -18.96, 0, 6, 19, 12.05, 14.7, 0.00631, 20, 13.68, 4.3, 0.74431, 21, -5.58, 4.01, 0.07275, 23, -41.59, 22.13, 0, 24, 3.37, -16.17, 0.17662, 29, -56.53, -6.95, 0, 6, 18, 23.37, 5.43, 0.0007, 19, 7.28, 6.6, 0.23107, 20, 4.41, 5.87, 0.54199, 23, -49.8, 26.71, 0, 24, -3.11, -9.35, 0.22624, 29, -53.44, 1.93, 0, 4, 17, 26.82, -4.17, 0.00921, 18, 9.07, -0.85, 0.97624, 19, -5.92, -1.76, 0.01456, 29, -42.14, 12.71, 0, 5, 19, 3.7, 0.52, 0.97393, 20, -2.54, 7.05, 0.01649, 23, -55.96, 30.14, 0, 24, -7.97, -4.24, 0.00958, 29, -51.12, 8.59, 0, 6, 23, -21.49, 67.35, 0, 24, 36.03, 21.01, 3e-05, 25, 14.37, 18.88, 0.25931, 26, 0.34, 19.02, 0.2855, 28, 10.73, -19.86, 0.22441, 29, -8.52, -18.93, 0.23076, 6, 23, -28.43, 60.18, 0, 24, 27.26, 16.24, 0.05655, 25, 4.93, 15.63, 0.39947, 26, -9.46, 17.14, 0.03391, 28, 1.83, -15.35, 0.47242, 29, -16.77, -13.33, 0.03766, 6, 23, -34.27, 53.99, 0, 24, 19.84, 12.07, 0.35701, 25, -3.08, 12.76, 0.15336, 26, -17.8, 15.42, 7e-05, 27, 12.65, -13.01, 0.05628, 28, -5.82, -11.61, 0.43328, 4, 24, 12.3, 10.57, 0.44716, 25, -10.77, 12.55, 0.00366, 27, 5.94, -9.26, 0.41908, 28, -11.33, -6.25, 0.1301, 6, 20, 18.38, 14.48, 0.05492, 21, -1.41, 14.42, 0.2631, 22, -21.89, 13.82, 0.00855, 24, 13.24, -10.84, 0.66933, 25, -13.42, -8.72, 0.0041, 29, -47.27, -13.27, 0, 7, 20, 29.26, 14.69, 0.00028, 21, 9.44, 15.2, 0.32548, 22, -11.07, 14.9, 0.14243, 23, -23.43, 26.73, 0.00273, 24, 22.07, -17.18, 0.39444, 25, -5.77, -16.45, 0.13453, 26, -24.58, -13.12, 0.00011, 8, 21, 18.19, 19.7, 0.09183, 22, -2.45, 19.64, 0.29447, 23, -13.63, 27.54, 0.05189, 24, 31.67, -19.33, 0.12559, 25, 3.33, -20.17, 0.41269, 26, -16.08, -18.09, 0.02354, 28, -24.95, -39.17, 0, 29, -46.36, -33.55, 0, 8, 21, 26.25, 22.31, 0.01712, 22, 5.54, 22.47, 0.27578, 23, -5.18, 26.86, 0.17671, 24, 39.53, -22.49, 0.02122, 25, 10.56, -24.61, 0.39217, 26, -9.56, -23.49, 0.11699, 28, -23.09, -47.44, 0, 29, -45.57, -41.99, 1e-05, 8, 21, 33.61, 26.66, 0.00057, 22, 12.77, 27.03, 0.1491, 23, 3.28, 28.07, 0.30708, 24, 47.97, -23.86, 0.00047, 25, 18.65, -27.36, 0.25642, 26, -1.94, -27.36, 0.28634, 28, -19.43, -55.16, 1e-05, 29, -42.92, -50.11, 2e-05], "hull": 19, "edges": [2, 4, 16, 18, 18, 20, 28, 30, 30, 32, 32, 34, 34, 36, 12, 14, 14, 16, 10, 12, 8, 10, 4, 6, 6, 8, 24, 26, 26, 28, 20, 22, 22, 24, 36, 0, 0, 2], "width": 89, "height": 109}}, "wings_R2": {"wings_R2": {"type": "mesh", "uvs": [0.1842, 0.00759, 0.29406, 0.04283, 0.39165, 0.07413, 0.45368, 0.10891, 0.52514, 0.14899, 0.63317, 0.20956, 0.70396, 0.24925, 0.77822, 0.307, 0.86242, 0.37247, 0.82865, 0.52525, 0.86933, 0.62233, 0.99022, 0.69065, 0.98998, 0.74833, 0.93945, 0.82683, 0.9116, 0.84866, 0.83793, 0.90642, 0.8104, 0.94936, 0.78153, 0.9944, 0.69698, 0.95905, 0.59684, 0.94059, 0.50725, 0.92407, 0.34196, 0.93172, 0.31411, 0.85365, 0.28831, 0.7813, 0.22181, 0.69184, 0.19144, 0.65099, 0.15385, 0.60951, 0.0496, 0.56018, 0.10039, 0.4804, 0.13111, 0.36305, 0.13105, 0.2914, 0.13099, 0.22095, 0.08581, 0.15031, 0.00865, 0.02968, 0.00992, 0.00766, 0.16301, 0.15621, 0.2441, 0.18431, 0.33871, 0.22996, 0.42881, 0.27386, 0.52116, 0.32127, 0.61577, 0.36692, 0.66983, 0.40379, 0.65406, 0.44593, 0.53918, 0.4512, 0.46485, 0.45823, 0.38151, 0.46701, 0.29366, 0.48456, 0.21482, 0.50388, 0.14725, 0.52144, 0.67659, 0.56534, 0.5955, 0.62152, 0.53918, 0.66893, 0.50314, 0.70932, 0.4626, 0.76902, 0.43782, 0.81467, 0.39277, 0.87789, 0.88832, 0.762, 0.83877, 0.72161, 0.78246, 0.6584, 0.76444, 0.59343, 0.74191, 0.52495, 0.74867, 0.45471, 0.75768, 0.37219, 0.78471, 0.76902, 0.74642, 0.81994, 0.71263, 0.87262, 0.70137, 0.91652, 0.28465, 0.64611, 0.39953, 0.58816, 0.46035, 0.55656, 0.52567, 0.53022, 0.59324, 0.50739, 0.6901, 0.49159, 0.1968, 0.36692, 0.29366, 0.37043, 0.377, 0.38272, 0.45584, 0.38623, 0.53918, 0.40379, 0.59775, 0.41257, 0.70362, 0.65313, 0.67434, 0.70932, 0.64956, 0.74795, 0.63379, 0.79887, 0.61352, 0.83574, 0.5955, 0.89545], "triangles": [17, 18, 16, 18, 66, 16, 18, 19, 66, 16, 66, 15, 19, 84, 66, 66, 84, 65, 66, 65, 15, 65, 64, 15, 15, 64, 14, 63, 14, 64, 83, 82, 65, 65, 82, 64, 82, 81, 64, 64, 81, 63, 20, 84, 19, 21, 55, 20, 21, 22, 55, 84, 20, 54, 20, 55, 54, 84, 83, 65, 84, 54, 83, 55, 22, 54, 22, 23, 54, 54, 53, 83, 83, 53, 82, 54, 23, 53, 53, 52, 82, 24, 67, 23, 23, 67, 53, 82, 52, 81, 53, 67, 52, 52, 67, 68, 52, 51, 81, 81, 51, 80, 52, 68, 51, 68, 69, 51, 51, 69, 50, 51, 50, 80, 80, 50, 79, 50, 69, 70, 50, 49, 79, 70, 71, 50, 50, 71, 49, 49, 71, 72, 24, 25, 67, 25, 26, 67, 26, 47, 67, 67, 47, 46, 27, 48, 26, 26, 48, 47, 27, 28, 48, 48, 28, 47, 73, 28, 29, 28, 73, 47, 47, 73, 46, 30, 73, 29, 67, 46, 68, 46, 45, 68, 68, 45, 69, 45, 74, 75, 45, 46, 74, 46, 73, 74, 45, 75, 76, 75, 74, 38, 74, 73, 37, 69, 44, 70, 69, 45, 44, 70, 43, 71, 70, 44, 43, 71, 42, 72, 42, 43, 78, 42, 71, 43, 45, 76, 44, 44, 77, 43, 44, 76, 77, 42, 41, 61, 43, 77, 78, 42, 78, 41, 78, 40, 41, 78, 77, 40, 41, 40, 62, 76, 39, 77, 77, 39, 40, 76, 75, 38, 31, 35, 36, 31, 32, 35, 35, 0, 1, 35, 32, 0, 32, 33, 0, 0, 33, 34, 37, 73, 30, 37, 30, 36, 30, 31, 36, 4, 37, 3, 37, 2, 3, 37, 36, 2, 36, 1, 2, 36, 35, 1, 39, 76, 38, 74, 37, 38, 40, 39, 5, 39, 38, 5, 38, 4, 5, 4, 38, 37, 62, 40, 7, 40, 6, 7, 40, 5, 6, 8, 61, 62, 61, 41, 62, 62, 7, 8, 59, 60, 9, 49, 72, 60, 60, 61, 9, 9, 61, 8, 60, 72, 61, 72, 42, 61, 80, 58, 63, 80, 79, 58, 57, 58, 10, 79, 59, 58, 58, 59, 10, 79, 49, 59, 59, 9, 10, 49, 60, 59, 14, 63, 56, 14, 56, 13, 13, 56, 12, 63, 81, 80, 63, 57, 56, 63, 58, 57, 12, 56, 11, 11, 56, 57, 57, 10, 11], "vertices": [1, 23, 9.99, -6.48, 1, 2, 22, 21.64, -4.81, 0.61744, 23, -1.62, -4.62, 0.38256, 1, 22, 11.55, -7.51, 1, 2, 21, 23.85, -7.83, 0.16384, 22, 3.99, -7.72, 0.83616, 2, 21, 15.13, -7.83, 0.90984, 22, -4.73, -7.97, 0.09016, 2, 20, 20.59, -7.92, 0.32245, 21, 1.96, -7.83, 0.67755, 2, 20, 11.97, -7.47, 0.96919, 21, -6.67, -7.83, 0.03081, 2, 19, 16.78, 0.21, 0.22621, 20, 1.82, -5.28, 0.77379, 2, 18, 23.97, -11.11, 0.0298, 19, 10.36, -9.66, 0.9702, 3, 17, 19.5, -9.87, 0.27673, 18, 4.41, -8.87, 0.70061, 19, -9.32, -10.39, 0.02266, 3, 16, 20.05, -11.7, 0.27291, 17, 6.53, -9.73, 0.69961, 18, -7.67, -13.58, 0.02748, 2, 16, 5.31, -12.81, 0.97092, 17, -5.53, -18.28, 0.02908, 2, 16, 0.6, -7.2, 0.9995, 17, -12.46, -15.91, 0.0005, 2, 16, -2.02, 3.64, 0.55695, 30, -3.75, 4.97, 0.44305, 2, 16, -1.71, 7.54, 0.1097, 30, 0.08, 4.18, 0.8903, 2, 30, 10.21, 2.09, 0.98693, 31, -1.83, 2.98, 0.01307, 1, 31, 4.06, 1.4, 1, 3, 17, -35.44, 13.66, 0, 30, 22.62, 3.54, 0, 31, 10.24, -0.25, 1, 7, 16, 5.46, 31.96, 0.0073, 17, -28.5, 20.15, 0.00162, 27, 33.63, 39.05, 0.00718, 28, 27.92, 33.25, 0.0051, 29, 15.28, 31.56, 0.13223, 30, 23.45, -5.92, 0.07221, 31, 7.51, -9.35, 0.77437, 7, 16, 14.54, 36.58, 0.01608, 17, -23.1, 28.79, 0.00577, 27, 39.05, 30.43, 0.0162, 28, 30.93, 23.52, 0.01298, 29, 17.03, 21.53, 0.46635, 30, 26.92, -15.5, 0.10719, 31, 7.2, -19.53, 0.37542, 7, 16, 22.66, 40.71, 0.00654, 17, -18.27, 36.51, 0.00219, 27, 43.91, 22.72, 0.00733, 28, 33.62, 14.81, 0.00321, 29, 18.59, 12.55, 0.8033, 30, 30.02, -24.07, 0.04324, 31, 6.93, -28.64, 0.13419, 1, 29, 25.93, -2.11, 1, 4, 25, 21.94, 46.24, 0.00207, 26, 11.68, 45.04, 0.01087, 28, 35.62, -6.2, 0.0025, 29, 17.91, -8.55, 0.98456, 4, 25, 22.24, 36.7, 0.02817, 26, 10.64, 35.56, 0.09006, 28, 29, -13.07, 0.11862, 29, 10.47, -14.52, 0.76316, 4, 25, 25.95, 24.1, 0.0918, 26, 12.54, 22.56, 0.36919, 28, 22.55, -24.51, 0.25829, 29, 2.62, -25.04, 0.28072, 4, 25, 27.64, 18.35, 0.08439, 26, 13.4, 16.63, 0.57296, 28, 19.61, -29.73, 0.19426, 29, -0.97, -29.85, 0.14838, 4, 25, 30.01, 12.35, 0.02994, 26, 14.9, 10.36, 0.8374, 28, 16.96, -35.61, 0.08123, 29, -4.34, -35.35, 0.05143, 1, 26, 22.16, 0.71, 1, 5, 21, 24.69, 50.89, 0.00059, 22, 3.18, 51, 0.0105, 23, 4.31, 53.87, 0.00351, 25, 31.26, -4.83, 0.00952, 26, 13.72, -6.83, 0.97589, 5, 21, 30.93, 37.02, 0.02732, 22, 9.81, 37.31, 0.16271, 23, 4.77, 38.67, 0.06816, 25, 24.77, -18.59, 0.28241, 26, 5.37, -19.54, 0.45939, 6, 21, 36.25, 29.63, 0.03463, 22, 15.33, 30.08, 0.30088, 23, 6.86, 29.81, 0.17907, 24, 51.9, -23.26, 2e-05, 25, 22.62, -27.43, 0.26444, 26, 1.99, -27.99, 0.22095, 5, 21, 41.48, 22.37, 0.01365, 22, 20.76, 22.97, 0.3331, 23, 8.91, 21.1, 0.42053, 25, 20.51, -36.13, 0.143, 26, -1.33, -36.3, 0.08972, 5, 21, 50.34, 17.7, 5e-05, 22, 29.76, 18.54, 0.12446, 23, 15.32, 13.39, 0.81796, 25, 22.73, -45.9, 0.03662, 26, -0.5, -46.29, 0.02092, 2, 22, 45.12, 11, 0, 23, 26.26, 0.23, 1, 2, 22, 46.72, 8.7, 0, 23, 26.78, -2.52, 1, 5, 21, 43.7, 13.85, 0.00126, 22, 23.22, 14.51, 0.2538, 23, 7.71, 12.37, 0.65728, 25, 15.48, -43.36, 0.05653, 26, -7.32, -42.76, 0.03113, 6, 21, 35.1, 12.06, 0.01724, 22, 14.68, 12.47, 0.56899, 23, -0.92, 14.01, 0.28356, 24, 39.77, -36.03, 0.00206, 25, 8.53, -37.99, 0.08835, 26, -13.45, -36.46, 0.0398, 6, 21, 24.11, 11.3, 0.20531, 22, 3.71, 11.4, 0.55818, 23, -11.37, 17.51, 0.04977, 24, 30.84, -29.58, 0.02902, 25, 0.8, -30.14, 0.12847, 26, -19.99, -27.6, 0.02925, 7, 20, 33.19, 9.89, 0.00145, 21, 13.62, 10.61, 0.62633, 22, -6.76, 10.43, 0.12256, 23, -21.33, 20.89, 0.00551, 24, 22.34, -23.39, 0.1232, 25, -6.54, -22.61, 0.11392, 26, -26.21, -19.11, 0.00704, 6, 20, 22.25, 10.01, 0.14858, 21, 2.68, 10.16, 0.49586, 22, -17.68, 9.67, 0.00445, 23, -31.61, 24.66, 1e-05, 24, 13.65, -16.73, 0.31829, 25, -14, -14.6, 0.03281, 4, 19, 6.02, 14.38, 0.00978, 20, 11.23, 9.82, 0.45793, 21, -8.31, 9.4, 0.04416, 24, 4.72, -10.28, 0.48812, 5, 18, 18.92, 7.7, 0.00138, 19, 2.54, 8.18, 0.14489, 20, 4.2, 10.87, 0.24979, 21, -15.39, 10.08, 0.00077, 24, -0.28, -5.23, 0.60317, 3, 20, 2.62, 16.21, 0.00159, 24, 1.65, 0, 0.99534, 27, -9.02, -10.4, 0.00307, 3, 20, 11.82, 22.92, 0.00035, 21, -8.41, 22.52, 0.00029, 24, 13.05, -0.14, 0.99936, 4, 24, 20.45, 0.22, 0.99697, 25, -4.46, 0.98, 0.00034, 27, 5.43, -22.42, 0.0003, 28, -15.22, -18.83, 0.00239, 4, 24, 28.76, 0.75, 9e-05, 25, 3.82, 0.11, 0.99849, 28, -10.07, -25.38, 0.00141, 29, -29.86, -21.76, 1e-05, 3, 25, 12.8, 0.22, 0.99867, 28, -3.74, -31.74, 0.00121, 29, -24.38, -28.87, 0.00012, 3, 26, 4.31, 0.14, 0.99946, 28, 2.34, -37.22, 0.00042, 29, -19.05, -35.08, 0.00012, 4, 21, 17.89, 52.41, 0, 22, -3.66, 52.33, 0.00017, 23, -1.4, 57.88, 5e-05, 26, 11.35, -0.27, 0.99977, 3, 17, 19.5, 6.02, 0.14587, 18, -1.53, 5.88, 0.0533, 27, 0.02, 1.98, 0.80082, 5, 16, 40.81, 5.73, 0.00028, 17, 15.32, 15.91, 0.02898, 27, 10.75, 1.44, 0.97003, 30, -6.91, -37.8, 0.00071, 31, -32.47, -27.78, 0, 6, 16, 41.18, 13.93, 0.00103, 17, 11.41, 23.13, 0.00817, 27, 18.94, 1.83, 0.14747, 28, 4.1, 1.09, 0.84182, 30, 1.18, -39.17, 0.0013, 31, -25.45, -32.04, 0.0002, 7, 16, 40.59, 20.15, 0.00067, 17, 7.69, 28.15, 0.002, 27, 25.08, 2.98, 0.00815, 28, 10.33, 0.62, 0.98688, 29, -6.31, 1.43, 0.00092, 30, 7.42, -39.35, 0.001, 31, -19.71, -34.51, 0.00037, 7, 16, 38.76, 28.53, 0.00054, 17, 1.8, 34.38, 0.0007, 27, 33.26, 5.58, 0.00152, 28, 18.9, 1.01, 0.00283, 29, 2.24, 0.73, 0.99197, 30, 15.96, -38.55, 0.00125, 31, -11.48, -36.92, 0.00118, 6, 16, 36.89, 34.54, 0.00028, 17, -2.91, 38.57, 0.00023, 27, 39.07, 7.99, 0.00046, 29, 8.53, 0.76, 0.99628, 30, 22.16, -37.43, 0.00101, 31, -5.31, -38.16, 0.00174, 3, 25, 15.1, 51.07, 1e-05, 26, 5.6, 50.79, 6e-05, 29, 17.67, -0.18, 0.99994, 5, 16, 7.16, 0.62, 0.97126, 27, 2.57, 34.48, 7e-05, 28, -3.26, 36.87, 4e-05, 29, -15.19, 39.12, 7e-05, 30, -7.86, -3.78, 0.02856, 2, 16, 14.22, -0.12, 0.99851, 17, -4.45, -2.82, 0.00149, 3, 16, 23.66, -2.65, 0.00204, 17, 4.95, -0.11, 0.99791, 18, -12.73, -5.26, 5e-05, 3, 16, 30.35, -7.8, 0.00049, 17, 13.33, -1.07, 0.97422, 18, -4.6, -3.01, 0.02529, 3, 17, 22.29, -1.75, 0.00714, 18, 3.96, -0.29, 0.99275, 19, -11.05, -1.97, 0.00011, 2, 17, 30.52, -5.25, 5e-05, 18, 12.9, -0.45, 0.99995, 3, 19, 8.26, 0.49, 0.91359, 20, -0.95, 2.79, 0.0803, 24, -9.24, -8.61, 0.00611, 7, 16, 14.41, 7.93, 0.57067, 17, -8.43, 4.18, 0.05562, 27, 10.52, 27.93, 0.03409, 28, 2.72, 28.48, 0.01445, 29, -10.33, 30.04, 0.01902, 30, -1.5, -11.87, 0.30162, 31, -17.87, -5.68, 0.00454, 7, 16, 13.13, 15.32, 0.21278, 17, -13.34, 9.85, 0.04628, 27, 17.75, 29.88, 0.04564, 28, 10.21, 28.5, 0.03057, 29, -2.89, 29.1, 0.0687, 30, 5.99, -11.5, 0.53474, 31, -10.77, -8.1, 0.06129, 7, 16, 11.36, 22.58, 0.06517, 17, -18.6, 15.16, 0.01961, 27, 24.83, 32.31, 0.02846, 28, 17.67, 29.01, 0.02516, 29, 4.58, 28.66, 0.14025, 30, 13.42, -10.64, 0.41524, 31, -3.55, -10.03, 0.3061, 7, 16, 8.61, 27.56, 0.02015, 17, -23.53, 18.01, 0.00582, 27, 29.53, 35.51, 0.01329, 28, 23.04, 30.88, 0.01119, 29, 10.14, 29.83, 0.1566, 30, 18.69, -8.51, 0.19062, 31, 2.14, -10.01, 0.60232, 5, 24, 39.94, 22.75, 0.00081, 25, 18.53, 19.94, 0.15481, 26, 4.6, 19.48, 0.34699, 28, 14.39, -22.1, 0.31541, 29, -5.17, -21.61, 0.18198, 5, 24, 28.08, 16.22, 0.0791, 25, 5.73, 15.48, 0.31027, 26, -8.69, 16.87, 0.07345, 28, 2.28, -16.03, 0.5142, 29, -16.42, -14.06, 0.02299, 6, 24, 21.78, 12.65, 0.26418, 25, -1.07, 13.01, 0.20984, 26, -15.77, 15.38, 0.01128, 27, 14.51, -13.82, 0.01444, 28, -4.24, -12.88, 0.49915, 29, -22.48, -10.11, 0.00111, 5, 24, 15.1, 9.77, 0.47774, 25, -8.14, 11.29, 0.04389, 26, -23.02, 14.68, 7e-05, 27, 7.55, -11.68, 0.16825, 28, -10.4, -9.01, 0.31004, 5, 18, 5.36, 14.53, 0.00833, 24, 8.22, 7.36, 0.55874, 25, -15.33, 10.06, 0.00064, 27, 0.75, -9.06, 0.36061, 28, -16.29, -4.71, 0.07169, 4, 18, 7.9, 5.07, 0.49281, 24, -1.49, 6.04, 0.33666, 27, -7.5, -3.77, 0.17031, 28, -22.89, 2.53, 0.00022, 6, 21, 25.36, 33.62, 0.03982, 22, 4.34, 33.75, 0.15859, 23, -1.68, 37.66, 0.05889, 24, 46.09, -13.23, 0.00272, 25, 18.57, -16.57, 0.35795, 26, -0.49, -16.67, 0.38203, 6, 21, 17.32, 28.38, 0.10112, 22, -3.56, 28.29, 0.12773, 23, -11.11, 35.89, 0.02881, 24, 36.56, -12.11, 0.04867, 25, 9.36, -13.87, 0.56544, 26, -9.23, -12.69, 0.12822, 7, 20, 30.03, 24.29, 0.00165, 21, 9.71, 24.83, 0.15282, 22, -11.07, 24.53, 0.05944, 23, -19.5, 35.52, 0.00715, 24, 28.44, -9.96, 0.24736, 25, 1.72, -10.39, 0.51686, 26, -16.31, -8.18, 0.01473, 7, 20, 23.22, 20.45, 0.02818, 21, 3.11, 20.64, 0.18017, 22, -17.54, 20.15, 0.01619, 23, -27.2, 34.17, 0.00069, 24, 20.69, -8.96, 0.62955, 25, -5.76, -8.11, 0.14512, 26, -23.39, -4.87, 0.00011, 5, 20, 15.08, 17.86, 0.09266, 21, -4.89, 17.63, 0.07941, 22, -25.46, 16.92, 0.00024, 24, 12.62, -6.15, 0.82525, 25, -13.25, -3.99, 0.00244, 4, 19, -0.02, 14.93, 0.00383, 20, 9.6, 15.66, 0.12977, 21, -10.25, 15.15, 0.02097, 24, 6.91, -4.62, 0.84542, 7, 16, 30.05, 1.88, 0.0079, 17, 8.08, 7.06, 0.65428, 27, 5.93, 11.8, 0.32461, 28, -5.89, 14.1, 0.00303, 29, -20.69, 16.86, 0.00101, 30, -9.42, -26.65, 0.00895, 31, -30.68, -16.49, 0.00023, 7, 16, 27.65, 9.2, 0.09957, 17, 2.26, 12.1, 0.39139, 27, 13, 14.86, 0.3386, 28, 1.73, 15.22, 0.07295, 29, -12.99, 17.01, 0.02486, 30, -1.86, -25.16, 0.06483, 31, -23.11, -17.9, 0.0078, 7, 16, 26.36, 14.53, 0.12232, 17, -1.6, 16, 0.21888, 27, 18.18, 16.64, 0.25729, 28, 7.2, 15.6, 0.1602, 29, -7.51, 16.69, 0.08935, 30, 3.59, -24.53, 0.1229, 31, -17.81, -19.32, 0.02905, 7, 16, 23.37, 20.48, 0.10481, 17, -7.23, 19.55, 0.09856, 27, 23.83, 20.16, 0.129, 28, 13.57, 17.53, 0.15279, 29, -0.96, 17.8, 0.23459, 30, 9.85, -22.29, 0.18845, 31, -11.16, -19.56, 0.0918, 7, 16, 21.88, 25.35, 0.06372, 17, -11.02, 22.95, 0.04717, 27, 28.54, 22.09, 0.06961, 28, 18.62, 18.18, 0.10118, 29, 4.14, 17.8, 0.37914, 30, 14.87, -21.41, 0.17481, 31, -6.17, -20.58, 0.16437, 7, 16, 18.34, 32.29, 0.02408, 17, -17.63, 27.07, 0.01182, 27, 35.13, 26.25, 0.02401, 28, 26.06, 20.49, 0.02689, 29, 11.81, 19.15, 0.49721, 30, 22.19, -18.75, 0.12149, 31, 1.61, -20.81, 0.2945], "hull": 35, "edges": [0, 68, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 34, 36, 40, 42, 50, 52, 52, 54, 54, 56, 56, 58, 66, 68, 62, 64, 64, 66, 12, 14, 14, 16, 10, 12, 8, 10, 4, 6, 6, 8, 0, 2, 2, 4, 58, 60, 60, 62, 26, 28, 28, 30, 30, 32, 32, 34, 36, 38, 38, 40, 42, 44, 44, 46, 46, 48, 48, 50], "width": 99, "height": 127}}, "yinying": {"yinying": {"x": -3.05, "y": 1.62, "scaleX": 1.5452, "scaleY": 1.5452, "width": 82, "height": 39}}}}], "animations": {"attack": {"slots": {"wings_L": {"attachment": [{"time": 0.2, "name": "wings_L"}, {"time": 0.4667, "name": null}]}, "wings_L2": {"attachment": [{"time": 0.2, "name": null}, {"time": 0.4667, "name": "wings_L2"}]}, "wings_R": {"attachment": [{"time": 0.2, "name": "wings_R2"}, {"time": 0.2333, "name": "wings_R"}, {"time": 0.4667, "name": null}]}, "wings_R2": {"attachment": [{"time": 0.2333, "name": null}, {"time": 0.4333, "name": "wings_R2"}]}}, "bones": {"body4": {"rotate": [{"angle": 2.81, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.1667, "angle": 5.09, "curve": 0.373, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "angle": 11.79, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.4, "angle": -5.61, "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 0.6667, "angle": -8.07, "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 1, "angle": 2.81}]}, "body": {"rotate": [{"angle": 12.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.72, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -15.43, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4, "angle": -22, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6667, "angle": -18.89, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8333, "angle": 15.72, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 12.53}], "translate": [{"x": -2.4, "y": -10.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -49.8, "y": 46.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -58.87, "y": 46.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 6.44, "y": 46.94, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6667, "x": 15.51, "y": 46.94, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.8333, "x": -19.91, "y": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.4, "y": -10.14}]}, "body2": {"rotate": [{"angle": -10.63, "curve": 0.34, "c2": 0.37, "c3": 0.673, "c4": 0.71}, {"time": 0.1667, "angle": -7.99, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.3333, "angle": -1.29, "curve": 0.338, "c2": 0.37, "c3": 0.671, "c4": 0.7}, {"time": 0.4, "angle": -18.68, "curve": 0.354, "c2": 0.65, "c3": 0.688}, {"time": 0.6667, "angle": -21.14, "curve": 0.354, "c2": 0.65, "c3": 0.688}, {"time": 1, "angle": -10.63}]}, "body3": {"rotate": [{"angle": -10.29, "curve": 0.342, "c2": 0.37, "c3": 0.675, "c4": 0.71}, {"time": 0.1667, "angle": -7.8, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3333, "angle": -1.1, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.4, "angle": -18.44, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.6667, "angle": -20.9, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 1, "angle": -10.29}]}, "wings_R3": {"rotate": [{"angle": -3.31, "curve": 0.275, "c2": 0.13, "c3": 0.71, "c4": 0.82}, {"time": 0.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": 33.45, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.2333, "angle": 23.78, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.3, "angle": 18.35, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 0.3333, "angle": 26.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": -3.31, "curve": 0.275, "c2": 0.13, "c3": 0.71, "c4": 0.82}, {"time": 0.7, "angle": 3.71, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.7333, "angle": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.31}], "translate": [{"time": 0.2, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 0.2333, "x": -16.95, "y": 3.68, "curve": 0.318, "c2": 0.17, "c3": 0.652, "c4": 0.51}, {"time": 0.3, "x": -24.51, "y": 7.8, "curve": "stepped"}, {"time": 0.4, "x": -24.51, "y": 7.8, "curve": 0.289, "c2": 0.15, "c3": 0.634, "c4": 0.53}, {"time": 0.4333, "x": -16.95, "y": 3.68, "curve": 0.318, "c2": 0.17, "c3": 0.652, "c4": 0.51}, {"time": 0.4667}], "scale": [{"time": 0.1333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.1667, "x": 0.925, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "x": 0.445, "curve": "stepped"}, {"time": 0.2333, "x": -0.518, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "x": -0.845, "curve": "stepped"}, {"time": 0.4, "x": -0.845, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "x": -0.518, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.4667, "x": 0.925, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.5}]}, "head": {"rotate": [{"angle": -6.53, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.1667, "angle": 2.71, "curve": 0.373, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "angle": 36.08, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.4667, "angle": 38.45, "curve": 0, "c2": 0.91, "c3": 0.705}, {"time": 0.6667, "angle": 24.97, "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 1, "angle": -6.53}]}, "hand_R2": {"rotate": [{"angle": -3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.55, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.24}]}, "hand_R": {"rotate": [{"angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -20.97, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -24.74, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.2}]}, "hand_L2": {"rotate": [{"angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.66, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -15.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.85, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.64}]}, "hand_L": {"rotate": [{"angle": -14.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -23.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -27.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -9.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -14.19}]}, "hand_L3": {"rotate": [{"angle": -27.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -18.22, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -41.02, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -23.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -27.94}]}, "hand_R3": {"rotate": [{"angle": -26.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -36.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -39.98, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -22.02, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -26.81}]}, "body_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 9.33, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "body_3": {"rotate": [{"angle": 6.1, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 20.73, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.1}]}, "leg_L2": {"rotate": [{"angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -27.04, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -21.55, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.47}]}, "wings_R2": {"rotate": [{"angle": 11.12, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3, "angle": 11.43, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 11.29, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.5, "angle": 11.12, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.7, "angle": 12.32, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9667, "angle": 10.94, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "angle": 11.12}]}, "wings_L2": {"rotate": [{"angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": -8.97, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.5, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.73}]}, "wings_L3": {"rotate": [{"angle": 6.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 8.97, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.3333, "angle": -13.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": 6.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": 8.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -13.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 6.03}], "scale": [{"time": 0.1333, "curve": 0.325, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.2, "x": 0.246, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 0.2333, "x": -0.575, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.3, "x": -1, "curve": "stepped"}, {"time": 0.4, "x": -1, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.4333, "x": -0.48, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.4667, "x": 0.469, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.5}]}, "wings_L4": {"rotate": [{"angle": 6.16, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 12.87, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -11.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": -14.83, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": 6.16, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5667, "angle": 12.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -14.83, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "angle": 6.16}]}, "wings_L5": {"rotate": [{"angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 18.54, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -4.25, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -17.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": 18.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -17.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 0.51}]}, "wings_L6": {"rotate": [{"angle": -4.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": 23.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 1.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": -20.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": -4.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667, "angle": 23.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -20.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": -4.27}]}, "wings_L7": {"rotate": [{"angle": 11.04, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 20.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -13.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": -18.17, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": 11.04, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5667, "angle": 20.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -18.17, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1, "angle": 11.04}]}, "wings_L8": {"rotate": [{"angle": 4.69, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.0667, "angle": 21.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": 27.12, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -1.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -18.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 4.69, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.5667, "angle": 21.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6333, "angle": 27.12, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -18.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 4.69}]}, "wings_L9": {"rotate": [{"angle": -5.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -14.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -2.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -5.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6333, "angle": -14.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -5.03}]}, "wings_L10": {"rotate": [{"angle": 1.46, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.1333, "angle": -6.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -7.77, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -3.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4333, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 1.46, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.6333, "angle": -6.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7, "angle": -7.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1, "angle": 1.46}]}, "wings_L11": {"rotate": [{"angle": 4.22, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.1333, "angle": 0.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -1.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -0.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4667, "angle": 4.46, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 4.22, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.6333, "angle": 0.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7333, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.22}]}, "leg_R": {"rotate": [{"angle": -4.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -18.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.13}]}, "leg_R2": {"rotate": [{"angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 10.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -15.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -19.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -0.25, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -5.04}]}, "leg_R3": {"rotate": [{"angle": -6.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -16.26, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -20.03, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -1.38, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.17}]}, "leg_L": {"rotate": [{"angle": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.38, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -23.59, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -18.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -3.91}]}, "wings_R7": {"rotate": [{"angle": -9.07, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1333, "angle": -12.14, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -13.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -0.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 12.24, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5, "angle": -9.07, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.5667, "angle": -14.34, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.7, "angle": -1.56, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.8333, "angle": 9.94, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1, "angle": -9.07}]}, "body_4": {"rotate": [{"angle": 8.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 17.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 29.96, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 8.86}]}, "wings_R12": {"rotate": [{"angle": 10.49, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.1333, "angle": -11.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -32.81, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.3333, "angle": -20.21, "curve": 0.348, "c2": 0.39, "c3": 0.685, "c4": 0.73}, {"time": 0.4667, "angle": 24.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.5, "angle": 10.49, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.7, "angle": -12.94, "curve": 0.272, "c2": 0.12, "c3": 0.649, "c4": 0.6}, {"time": 0.8333, "angle": 5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.9333, "angle": 17.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 1, "angle": 10.49}]}, "wings_R4": {"rotate": [{"angle": -6.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 8.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": 9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -6.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.7, "angle": 3.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7667, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.46}], "scale": [{"time": 0.1333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.1667, "x": 0.866, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "x": 0.879, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": 0.495, "c2": 0.37, "c4": 0.75}, {"time": 0.4333, "x": 0.866, "curve": 0, "c2": 0.28, "c3": 0.4, "c4": 0.62}, {"time": 0.4667}]}, "wings_R6": {"rotate": [{"angle": -8.99, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -5.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 4.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 9.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -8.99, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.5333, "angle": -10.76, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 0.7, "angle": 2.48, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.8, "angle": 8.63, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1, "angle": -8.99}]}, "wings_R8": {"rotate": [{"angle": -7.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -16.32, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -20.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -7.88, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4333, "angle": 14.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": -7.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "angle": -19.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7, "angle": -7.77, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.8667, "angle": 11.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -7.77}]}, "wings_R5": {"rotate": [{"angle": -7.49, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": 9.18, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.4, "angle": 12.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "angle": -7.49, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.5333, "angle": -7.88, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 0.7, "angle": 3.32, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.7667, "angle": 6.42, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1, "angle": -7.49}], "scale": [{"time": 0.1333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 0.482, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "x": 0.53, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 0.4333, "x": 0.482, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.67}, {"time": 0.4667}]}, "wings_R15": {"rotate": [{"angle": -2.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 5.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 5.75, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4333, "angle": 15.64, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": -2.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "angle": -13.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.7, "angle": -2.37, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.7667, "angle": 5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.8667, "angle": 17.23, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -2.4}]}, "wings_R9": {"rotate": [{"angle": -4.55, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1333, "angle": -17.95, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2333, "angle": -29.33, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -17.95, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.4667, "angle": 17.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.5, "angle": -4.55, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.6333, "angle": -26.25, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "angle": -17.3, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.9, "angle": 14.97, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1, "angle": -4.55}]}, "wings_R14": {"rotate": [{"angle": -5.3, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 1.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 7.85, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 12.42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -5.3, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": -7.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.7, "angle": 4.34, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.7667, "angle": 8.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8, "angle": 11.28, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -5.3}]}, "wings_R11": {"rotate": [{"angle": 2.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1333, "angle": -15.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -19.56, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.3333, "angle": -5.39, "curve": 0.345, "c2": 0.4, "c3": 0.679, "c4": 0.73}, {"time": 0.4333, "angle": 11.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.5, "angle": 2.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.6333, "angle": -7.78, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.7, "angle": -3.56, "curve": 0.322, "c2": 0.3, "c3": 0.697, "c4": 0.76}, {"time": 0.8333, "angle": 8.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.9, "angle": 11.28, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1, "angle": 2.26}]}, "leg_L3": {"rotate": [{"angle": -11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.96, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -31.16, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -25.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.69}]}, "wings_R13": {"rotate": [{"angle": -4.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 7.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": 9.12, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.5, "angle": -4.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.7, "angle": 3.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.7667, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.73}]}, "wings_R10": {"rotate": [{"angle": -2.26, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.1333, "angle": -14.18, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -4.5, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.4, "angle": 5.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": -2.26, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.5667, "angle": -4.73, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.7, "angle": -0.35, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.8333, "angle": 4.51, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1, "angle": -2.26}]}, "yinying": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -51.7, "curve": "stepped"}, {"time": 0.3333, "x": -51.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 32.21, "y": -2.54, "curve": "stepped"}, {"time": 0.6667, "x": 32.21, "y": -2.54, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.27, "curve": "stepped"}, {"time": 0.6667, "x": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "head4": {"rotate": [{"angle": -9.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -42.51, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.6333, "angle": -1.97}, {"time": 1, "angle": -9.81}]}, "head3": {"rotate": [{}, {"time": 0.4667, "angle": 6.05, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.6333}]}}}, "idle": {"slots": {"eye": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.1, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "eye"}]}}, "bones": {"body": {"rotate": [{"angle": 12.53}], "translate": [{"x": -2.4, "y": -10.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -2.4, "y": 21, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -2.4, "y": -10.14}]}, "body2": {"rotate": [{"angle": -10.63, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -10.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -8.42, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -10.63}]}, "body3": {"rotate": [{"angle": -10.29, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -10.77, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.09, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -10.29}]}, "body4": {"rotate": [{"angle": 2.81, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 1.78, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 7.71, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 2.81}]}, "head": {"rotate": [{"angle": -6.53, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -5.68, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -10.6, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -6.53}]}, "hand_R2": {"rotate": [{"angle": -3.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -3.24}]}, "hand_R": {"rotate": [{"angle": -11.2, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.4333, "angle": -1.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -14.84, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "angle": -11.2}]}, "hand_L2": {"rotate": [{"angle": -5.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -6.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": -5.64}]}, "hand_L": {"rotate": [{"angle": -14.19, "curve": 0.282, "c2": 0.15, "c3": 0.718, "c4": 0.85}, {"time": 0.6667, "angle": -0.63, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -14.84, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.6667, "angle": -14.19}]}, "hand_L3": {"rotate": [{"angle": -27.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -3.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -27.94}]}, "hand_R3": {"rotate": [{"angle": -26.81, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.4333, "angle": -10.28, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -27.94, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.6667, "angle": -26.81}]}, "body_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "body_3": {"rotate": [{"angle": 6.1, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 25.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "angle": 6.1}]}, "body_4": {"rotate": [{"angle": 8.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 17.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 8.86}]}, "wings_R2": {"rotate": [{"angle": 11.12, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "angle": 12.32, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1.5, "angle": 10.94, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 11.12}]}, "wings_L2": {"rotate": [{"angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.73}]}, "wings_L3": {"rotate": [{"angle": 6.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 8.97, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 6.03}]}, "wings_L4": {"rotate": [{"angle": 6.16, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": 12.87, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -14.83, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "angle": 6.16}]}, "wings_L5": {"rotate": [{"angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": 18.54, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -17.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 0.51}]}, "wings_L6": {"rotate": [{"angle": -4.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "angle": 23.75, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -20.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -4.27}]}, "wings_L7": {"rotate": [{"angle": 11.04, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": 20.38, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -18.17, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 1.6667, "angle": 11.04}]}, "wings_L8": {"rotate": [{"angle": 4.69, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.2667, "angle": 21.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": 27.12, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -18.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": 4.69}]}, "wings_L9": {"rotate": [{"angle": -5.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": -14.52, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -5.03}]}, "wings_L10": {"rotate": [{"angle": 1.46, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.4333, "angle": -6.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.6, "angle": -7.77, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "angle": 1.46}]}, "wings_L11": {"rotate": [{"angle": 4.22, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.4333, "angle": 0.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7667, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 4.46, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 1.6667, "angle": 4.22}]}, "leg_R": {"rotate": [{"angle": -4.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": -8.27, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -4.13}]}, "leg_R2": {"rotate": [{"angle": -5.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "angle": -13.7, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.2667, "angle": -0.58, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -5.04}]}, "leg_R3": {"rotate": [{"angle": -6.17, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.6, "angle": -25.47, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.2667, "angle": -3.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.4333, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "angle": -6.17}]}, "leg_L": {"rotate": [{"angle": -3.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -6.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -3.91}]}, "leg_L2": {"rotate": [{"angle": -7.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "angle": -14.94, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 1.1667, "angle": -0.64, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 1.2667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1.6667, "angle": -7.47}]}, "leg_L3": {"rotate": [{"angle": -11.69, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "angle": -31.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.1667, "angle": -4.13, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -11.69}]}, "wings_R9": {"rotate": [{"angle": -4.55, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "angle": -26.25, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.6667, "angle": -17.3, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 1.2667, "angle": 14.97, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": -4.55}]}, "wings_R8": {"rotate": [{"angle": -7.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -19.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.77, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 1.1667, "angle": 11.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -7.77}]}, "wings_R7": {"rotate": [{"angle": -9.07, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -14.34, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.6667, "angle": -1.56, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 1.0667, "angle": 9.94, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -9.07}]}, "wings_R6": {"rotate": [{"angle": -8.99, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -10.76, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 0.6667, "angle": 2.48, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.9667, "angle": 8.63, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -8.99}]}, "wings_R5": {"rotate": [{"angle": -7.49, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": -7.88, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 0.6667, "angle": 3.32, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.9, "angle": 6.42, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": -7.49}]}, "wings_R4": {"rotate": [{"angle": -6.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 3.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.46}]}, "wings_R3": {"rotate": [{"angle": -3.31, "curve": 0.275, "c2": 0.13, "c3": 0.71, "c4": 0.82}, {"time": 0.6667, "angle": 3.71, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.7667, "angle": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -3.54, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 1.6667, "angle": -3.31}]}, "wings_R15": {"rotate": [{"angle": -2.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -13.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -2.37, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.8333, "angle": 5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.1667, "angle": 17.23, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -2.4}]}, "wings_R14": {"rotate": [{"angle": -5.3, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -7.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.34, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.8333, "angle": 8.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1, "angle": 11.28, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -5.3}]}, "wings_R13": {"rotate": [{"angle": -4.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 3.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.73}]}, "wings_R12": {"rotate": [{"angle": 10.49, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.6, "angle": -13.83, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 0.6667, "angle": -12.94, "curve": 0.272, "c2": 0.12, "c3": 0.649, "c4": 0.6}, {"time": 1.1, "angle": 5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 1.4333, "angle": 17.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 1.6667, "angle": 10.49}]}, "wings_R11": {"rotate": [{"angle": 2.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "angle": -7.78, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.6667, "angle": -3.56, "curve": 0.322, "c2": 0.3, "c3": 0.697, "c4": 0.76}, {"time": 1.1, "angle": 8.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.2667, "angle": 11.28, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": 2.26}]}, "wings_R10": {"rotate": [{"angle": -2.26, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "angle": -4.73, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.6667, "angle": -0.35, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 1.1, "angle": 4.51, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": -2.26}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.944, "y": 0.944, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.944, "y": 0.944, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "head4": {"rotate": [{"angle": -9.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -18.84, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.81}]}}}, "run": {"slots": {"wings_L": {"attachment": [{"time": 0.2333, "name": "wings_L"}, {"time": 0.6, "name": null}]}, "wings_L2": {"attachment": [{"time": 0.2333, "name": null}, {"time": 0.6, "name": "wings_L2"}]}, "wings_R": {"attachment": [{"time": 0.2333, "name": "wings_R2"}, {"time": 0.2667, "name": "wings_R"}, {"time": 0.6, "name": null}]}, "wings_R2": {"attachment": [{"time": 0.2667, "name": null}, {"time": 0.5667, "name": "wings_R2"}]}}, "bones": {"wings_R2": {"rotate": [{"angle": 8.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.43, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.1}]}, "wings_R3": {"rotate": [{"angle": 0.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2333, "angle": 33.45, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.2667, "angle": 23.78, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.3333, "angle": 18.35, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": 26.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 0.78}], "translate": [{"time": 0.2333, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 0.2667, "x": -16.95, "y": 3.68, "curve": 0.318, "c2": 0.17, "c3": 0.652, "c4": 0.51}, {"time": 0.3333, "x": -24.51, "y": 7.8, "curve": "stepped"}, {"time": 0.5, "x": -24.51, "y": 7.8, "curve": 0.289, "c2": 0.15, "c3": 0.634, "c4": 0.53}, {"time": 0.5667, "x": -16.95, "y": 3.68, "curve": 0.318, "c2": 0.17, "c3": 0.652, "c4": 0.51}, {"time": 0.6}], "scale": [{"time": 0.1667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2, "x": 0.925, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2333, "x": 0.445, "curve": "stepped"}, {"time": 0.2667, "x": -0.518, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "x": -0.845, "curve": "stepped"}, {"time": 0.5, "x": -0.845, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "x": -0.518, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.6, "x": 0.445, "curve": "stepped"}, {"time": 0.6333, "x": 0.925, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.6667}]}, "wings_R4": {"rotate": [{"angle": 2.32, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.32}], "scale": [{"time": 0.1667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.2, "x": 0.866, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2333, "x": 0.879, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": 0.495, "c2": 0.37, "c4": 0.75}, {"time": 0.5333, "x": 0.879, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.67}, {"time": 0.5667, "x": 0.866, "curve": 0, "c2": 0.28, "c3": 0.4, "c4": 0.62}, {"time": 0.6}]}, "wings_R5": {"rotate": [{"angle": 4.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 12.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.46}], "scale": [{"time": 0.1667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.2, "x": 0.482, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2333, "x": 0.53, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 0.5333, "x": 0.53, "curve": 0.495, "c2": 0.37, "c4": 0.75}, {"time": 0.5667, "x": 0.482, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.67}, {"time": 0.6}]}, "wings_R6": {"rotate": [{"angle": 2.02, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -5.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 9.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 2.02}]}, "wings_R7": {"rotate": [{"angle": 2.74, "curve": 0.349, "c2": 0.39, "c3": 0.722, "c4": 0.85}, {"time": 0.1667, "angle": -12.14, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.2, "angle": -13.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 12.24, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 2.74}]}, "wings_R8": {"rotate": [{"angle": 5.85, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.1667, "angle": -16.32, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -20.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 14.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 5.85}]}, "wings_R9": {"rotate": [{"angle": 11.51, "curve": 0.303, "c2": 0.24, "c3": 0.674, "c4": 0.69}, {"time": 0.1667, "angle": -17.95, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2667, "angle": -29.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 17.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 11.51}]}, "wings_R10": {"rotate": [{"angle": -4.16, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "angle": 5.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -4.16}]}, "wings_R11": {"rotate": [{"angle": 4.14, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.1667, "angle": -15.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -19.56, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 0.5667, "angle": 11.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 4.14}]}, "wings_R12": {"rotate": [{"angle": 22.43, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": -11.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3, "angle": -32.81, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.6333, "angle": 24.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 22.43}]}, "wings_R13": {"rotate": [{"angle": 2.21, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 9.12, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 2.21}]}, "wings_R14": {"rotate": [{"angle": 6.15, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.1, "angle": 1.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 12.42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 6.15}]}, "wings_R15": {"rotate": [{"angle": 11.84, "curve": 0.32, "c2": 0.29, "c3": 0.667, "c4": 0.67}, {"time": 0.1, "angle": 5.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 15.64, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 11.84}]}, "wings_L2": {"rotate": [{"angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.73}]}, "wings_L11": {"rotate": [{"angle": 4.22, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": 0.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 4.46, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 4.22}]}, "wings_L10": {"rotate": [{"angle": 1.46, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.1667, "angle": -6.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -7.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 1.46}]}, "wings_L9": {"rotate": [{"angle": -5.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -5.03}]}, "wings_L8": {"rotate": [{"angle": 4.69, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.1, "angle": 21.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 27.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.69}]}, "wings_L7": {"rotate": [{"angle": 11.04, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 20.38, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -18.17, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 11.04}]}, "wings_L6": {"rotate": [{"angle": -4.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 23.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -20.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -4.27}]}, "wings_L5": {"rotate": [{"angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 18.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 0.51}]}, "wings_L4": {"rotate": [{"angle": 6.16, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 12.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.83, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 6.16}]}, "wings_L3": {"rotate": [{"angle": 6.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 8.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 6.03}], "scale": [{"time": 0.1667, "curve": 0.325, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.2333, "x": 0.246, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 0.2667, "x": -0.575, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.3333, "x": -1, "curve": "stepped"}, {"time": 0.5, "x": -1, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.5667, "x": -0.48, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.6, "x": 0.469, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.6667}]}, "body": {"rotate": [{"angle": -21.8, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -27.15, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -21.8}], "translate": [{"x": -4.45, "y": 26.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -4.45, "y": 57.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -4.45, "y": 26.69}]}, "leg_R": {"rotate": [{"angle": -4.13, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -8.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -4.13}]}, "leg_R2": {"rotate": [{"angle": -5.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": -13.7, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.5, "angle": -0.58, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -5.04}]}, "leg_R3": {"rotate": [{"angle": -6.17, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -25.47, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": -3.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -6.17}]}, "leg_L": {"rotate": [{"angle": -3.91, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -6.19, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -3.91}]}, "leg_L2": {"rotate": [{"angle": -7.47, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -14.94, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.4667, "angle": -0.64, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -7.47}]}, "leg_L3": {"rotate": [{"angle": -11.69, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": -31.77, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4667, "angle": -4.13, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -11.69}]}, "hand_R2": {"rotate": [{"angle": -3.24, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.48, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -3.24}]}, "hand_R": {"rotate": [{"angle": -11.2, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.1667, "angle": -1.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -14.84, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -11.2}]}, "hand_R3": {"rotate": [{"angle": -26.81, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.1667, "angle": -10.28, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -27.94, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -26.81}]}, "hand_L2": {"rotate": [{"angle": -5.64, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.48, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -5.64}]}, "hand_L": {"rotate": [{"angle": -14.19, "curve": 0.282, "c2": 0.15, "c3": 0.718, "c4": 0.85}, {"time": 0.2667, "angle": -0.63, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -14.84, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -14.19}]}, "hand_L3": {"rotate": [{"angle": -27.94, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.2667, "angle": -3.64, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -27.94}]}, "body2": {"rotate": [{"angle": -9.33}]}, "body3": {"rotate": [{"angle": -9.33}]}, "body4": {"rotate": [{"angle": 3.21}]}, "head": {"rotate": [{"angle": 12.55}]}, "body_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "body_3": {"rotate": [{"angle": 6.1, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 6.1}]}, "body_4": {"rotate": [{"angle": 8.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 17.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 8.86}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.765, "y": 0.765, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.765, "y": 0.765, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "head4": {"rotate": [{"angle": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.65}]}}}, "skill": {"slots": {"wings_L": {"attachment": [{"time": 0.2, "name": "wings_L"}]}, "wings_L2": {"attachment": [{"time": 0.2, "name": null}]}, "wings_R": {"attachment": [{"time": 0.2, "name": "wings_R2"}, {"time": 0.2333, "name": "wings_R"}]}, "wings_R2": {"attachment": [{"time": 0.2333, "name": null}]}}, "bones": {"body4": {"rotate": [{"angle": 2.81, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.1667, "angle": 5.09, "curve": 0.373, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "angle": 11.79}]}, "body": {"rotate": [{"angle": 12.53, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -18.72, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -15.43}], "translate": [{"x": -2.4, "y": -10.14, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -49.8, "y": 46.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -58.87, "y": 46.94}]}, "body2": {"rotate": [{"angle": -10.63, "curve": 0.34, "c2": 0.37, "c3": 0.673, "c4": 0.71}, {"time": 0.1667, "angle": -7.99, "curve": 0.358, "c2": 0.65, "c3": 0.693}, {"time": 0.3333, "angle": -1.29}]}, "body3": {"rotate": [{"angle": -10.29, "curve": 0.342, "c2": 0.37, "c3": 0.675, "c4": 0.71}, {"time": 0.1667, "angle": -7.8, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.3333, "angle": -1.1}]}, "wings_R3": {"rotate": [{"angle": -3.31, "curve": 0.275, "c2": 0.13, "c3": 0.71, "c4": 0.82}, {"time": 0.0333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.2, "angle": 33.45, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.2333, "angle": 23.78, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.3, "angle": 18.35, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 0.3333, "angle": 26.26}], "translate": [{"time": 0.2, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 0.2333, "x": -16.95, "y": 3.68, "curve": 0.318, "c2": 0.17, "c3": 0.652, "c4": 0.51}, {"time": 0.3, "x": -24.51, "y": 7.8}], "scale": [{"time": 0.1333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.1667, "x": 0.925, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "x": 0.445, "curve": "stepped"}, {"time": 0.2333, "x": -0.518, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.3, "x": -0.845}]}, "head": {"rotate": [{"angle": -6.53, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.1667, "angle": 2.71, "curve": 0.373, "c2": 0.62, "c3": 0.712}, {"time": 0.3333, "angle": 36.08}]}, "hand_R2": {"rotate": [{"angle": -3.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 12.05}]}, "hand_R": {"rotate": [{"angle": -11.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.09}]}, "hand_L2": {"rotate": [{"angle": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.66}]}, "hand_L": {"rotate": [{"angle": -14.19, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -4.47, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.1}]}, "hand_L3": {"rotate": [{"angle": -27.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -18.22, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": -13.67}]}, "hand_R3": {"rotate": [{"angle": -26.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -17.09, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": -12.54}]}, "body_2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.71}]}, "body_3": {"rotate": [{"angle": 6.1, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": 19.08}]}, "leg_L2": {"rotate": [{"angle": -7.47, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.26, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3333, "angle": 7.29}]}, "wings_R2": {"rotate": [{"angle": 11.12, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3, "angle": 11.43, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": 11.29}]}, "wings_L2": {"rotate": [{"angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -9.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.3333, "angle": -8.97}]}, "wings_L3": {"rotate": [{"angle": 6.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 8.97, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.3333, "angle": -13.64}], "scale": [{"time": 0.1333, "curve": 0.325, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.2, "x": 0.246, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 0.2333, "x": -0.575, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.3, "x": -1}]}, "wings_L4": {"rotate": [{"angle": 6.16, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 12.87, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -11.23}]}, "wings_L5": {"rotate": [{"angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": 18.54, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -4.25}]}, "wings_L6": {"rotate": [{"angle": -4.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "angle": 23.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 1.58}]}, "wings_L7": {"rotate": [{"angle": 11.04, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 20.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": -13.16}]}, "wings_L8": {"rotate": [{"angle": 4.69, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.0667, "angle": 21.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "angle": 27.12, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -1.51}]}, "wings_L9": {"rotate": [{"angle": -5.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "angle": -14.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": -2.52}]}, "wings_L10": {"rotate": [{"angle": 1.46, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.1333, "angle": -6.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -7.77, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -3.27}]}, "wings_L11": {"rotate": [{"angle": 4.22, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.1333, "angle": 0.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -1.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3333, "angle": -0.71}]}, "leg_R": {"rotate": [{"angle": -4.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 5.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.16}]}, "leg_R2": {"rotate": [{"angle": -5.04, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.68, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 0.3333, "angle": 9.72}]}, "leg_R3": {"rotate": [{"angle": -6.17, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.56, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 8.1}]}, "leg_L": {"rotate": [{"angle": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 5.81, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.38}]}, "wings_R7": {"rotate": [{"angle": -9.07, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1333, "angle": -12.14, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.1667, "angle": -13.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": -0.49}]}, "body_4": {"rotate": [{"angle": 8.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 8.86}]}, "wings_R12": {"rotate": [{"angle": 10.49, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.1333, "angle": -11.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -32.81, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.3333, "angle": -20.21}]}, "wings_R4": {"rotate": [{"angle": -6.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 8.34}], "scale": [{"time": 0.1333, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.1667, "x": 0.866, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "x": 0.879, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.2333}]}, "wings_R6": {"rotate": [{"angle": -8.99, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -5.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 4.03}]}, "wings_R8": {"rotate": [{"angle": -7.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -16.32, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -20.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": -7.88}]}, "wings_R5": {"rotate": [{"angle": -7.49, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": 9.18}], "scale": [{"time": 0.1333, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.1667, "x": 0.482, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "x": 0.53, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.2333}]}, "wings_R15": {"rotate": [{"angle": -2.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 5.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3333, "angle": 5.75}]}, "wings_R9": {"rotate": [{"angle": -4.55, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1333, "angle": -17.95, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2333, "angle": -29.33, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -17.95}]}, "wings_R14": {"rotate": [{"angle": -5.3, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 1.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3333, "angle": 7.85}]}, "wings_R11": {"rotate": [{"angle": 2.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.1333, "angle": -15.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -19.56, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.3333, "angle": -5.39}]}, "leg_L3": {"rotate": [{"angle": -11.69, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -1.96, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.3333, "angle": 2.58}]}, "wings_R13": {"rotate": [{"angle": -4.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.0667, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3333, "angle": 7.93}]}, "wings_R10": {"rotate": [{"angle": -2.26, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.1333, "angle": -14.18, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.3333, "angle": -4.5}]}, "yinying": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -51.7}]}, "head4": {"rotate": [{"angle": -9.81, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.3333, "angle": -35.17}]}, "head3": {"rotate": [{}, {"time": 0.3333, "angle": 4.32}]}}}, "skill1": {"slots": {"wings_L": {"attachment": [{"name": "wings_L"}]}, "wings_L2": {"attachment": [{"name": null}]}, "wings_R": {"attachment": [{"name": "wings_R"}, {"time": 0.1333, "name": null}, {"time": 0.5333, "name": "wings_R"}]}, "wings_R2": {"attachment": [{"name": null}, {"time": 0.1333, "name": "wings_R2"}, {"time": 0.5667, "name": null}]}}, "bones": {"body4": {"rotate": [{"angle": 11.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 11.79}]}, "body": {"rotate": [{"angle": -15.43}], "translate": [{"x": -58.87, "y": 46.94, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -58.87, "y": 38.76, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -58.87, "y": 46.94}]}, "body2": {"rotate": [{"angle": -1.29}]}, "body3": {"rotate": [{"angle": -1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.1}]}, "wings_R3": {"rotate": [{"angle": 26.26, "curve": 0, "c2": 0.3, "c3": 0.357, "c4": 0.64}, {"time": 0.0333, "angle": 18.35, "curve": 0.34, "c2": 0.34, "c3": 0.674, "c4": 0.67}, {"time": 0.1, "angle": 23.78, "curve": 0.495, "c2": 0.37, "c4": 0.75}, {"time": 0.1333, "angle": 33.45, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.67}, {"time": 0.3}, {"time": 0.3333, "angle": -3.31, "curve": 0.275, "c2": 0.13, "c3": 0.71, "c4": 0.82}, {"time": 0.3667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5333, "angle": 33.45, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.5667, "angle": 23.78, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.6333, "angle": 18.35, "curve": 0.351, "c2": 0.42, "c3": 0.686, "c4": 0.76}, {"time": 0.6667, "angle": 26.26}], "translate": [{"x": -24.51, "y": 7.8, "curve": "stepped"}, {"time": 0.0333, "x": -24.51, "y": 7.8, "curve": 0.421, "c2": 0.35, "c3": 0.763, "c4": 0.69}, {"time": 0.1, "x": -16.95, "y": 3.68, "curve": 0.387, "c2": 0.35, "c3": 0.742, "c4": 0.69}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.5333, "curve": 0.325, "c3": 0.658, "c4": 0.34}, {"time": 0.5667, "x": -16.95, "y": 3.68, "curve": 0.318, "c2": 0.17, "c3": 0.652, "c4": 0.51}, {"time": 0.6333, "x": -24.51, "y": 7.8}], "scale": [{"x": -0.845, "curve": "stepped"}, {"time": 0.0333, "x": -0.845, "curve": 0.258, "c2": 0.32, "c3": 0.595, "c4": 0.66}, {"time": 0.1, "x": -0.518, "curve": 0.172, "c2": 0.32, "c3": 0.512, "c4": 0.65}, {"time": 0.1333, "x": 0.445, "curve": 0, "c2": 0.32, "c3": 0.339, "c4": 0.66}, {"time": 0.1667, "x": 0.925, "curve": 0.583, "c2": 0.38, "c4": 0.73}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "x": 0.925, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.5333, "x": 0.445, "curve": "stepped"}, {"time": 0.5667, "x": -0.518, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.6333, "x": -0.845}]}, "head": {"rotate": [{"angle": 36.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 28.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 36.08}]}, "hand_R2": {"rotate": [{"angle": 12.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.05}]}, "hand_R": {"rotate": [{"angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.09}]}, "hand_L2": {"rotate": [{"angle": 9.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.66}]}, "hand_L": {"rotate": [{"angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -6.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.1}]}, "hand_L3": {"rotate": [{"angle": -13.67}]}, "hand_R3": {"rotate": [{"angle": -12.54}]}, "body_2": {"rotate": [{"angle": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 26.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.71}]}, "body_3": {"rotate": [{"angle": 19.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 36.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 19.08}]}, "leg_L2": {"rotate": [{"angle": 7.29}]}, "wings_R2": {"rotate": [{"angle": 11.29}, {"time": 0.0333, "angle": 11.43, "curve": 0.338, "c2": 0.33, "c3": 0.676, "c4": 0.67}, {"time": 0.3333, "angle": 11.12, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6333, "angle": 11.43, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": 11.29}]}, "wings_L2": {"rotate": [{"angle": -8.97, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.0333, "angle": -9.53, "curve": 0, "c2": 0.28, "c3": 0.385, "c4": 0.63}, {"time": 0.3333, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.53, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.6667, "angle": -8.97}]}, "wings_L3": {"rotate": [{"angle": -13.64}, {"time": 0.3, "angle": 8.97}, {"time": 0.3333, "angle": 6.03}, {"time": 0.3667, "angle": 8.97, "curve": 0.245, "c3": 0.711, "c4": 0.83}, {"time": 0.6667, "angle": -13.64}], "scale": [{"x": -1, "curve": "stepped"}, {"time": 0.0333, "x": -1, "curve": 0, "c2": 0.3, "c3": 0.357, "c4": 0.64}, {"time": 0.1, "x": -0.575, "curve": 0.317, "c2": 0.33, "c3": 0.653, "c4": 0.66}, {"time": 0.1333, "x": 0.246, "curve": 0, "c2": 0.26, "c3": 0.455, "c4": 0.62}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4667, "curve": 0.325, "c2": 0.3, "c3": 0.659, "c4": 0.64}, {"time": 0.5333, "x": 0.246, "curve": 0.328, "c2": 0.32, "c3": 0.662, "c4": 0.65}, {"time": 0.5667, "x": -0.575, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.6333, "x": -1}]}, "wings_L4": {"rotate": [{"angle": -11.23}, {"time": 0.2667, "angle": 12.87, "curve": 0, "c2": 0.24, "c3": 0.714, "c4": 0.68}, {"time": 0.3333, "angle": 6.16}, {"time": 0.4, "angle": 12.87, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -11.23}]}, "wings_L5": {"rotate": [{"angle": -4.25}, {"time": 0.2, "angle": 18.54, "curve": 0, "c2": 0.25, "c3": 0.556, "c4": 0.64}, {"time": 0.3333, "angle": 0.51}, {"time": 0.4667, "angle": 18.54, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -4.25}]}, "wings_L6": {"rotate": [{"angle": 1.58}, {"time": 0.1667, "angle": 23.75, "curve": 0.304, "c2": 0.32, "c3": 0.641, "c4": 0.66}, {"time": 0.3333, "angle": -4.27}, {"time": 0.5, "angle": 23.75, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 1.58}]}, "wings_L7": {"rotate": [{"angle": -13.16}, {"time": 0.2667, "angle": 20.38, "curve": 0.615, "c2": 0.37, "c4": 0.72}, {"time": 0.3333, "angle": 11.04}, {"time": 0.4, "angle": 20.38, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -13.16}]}, "wings_L8": {"rotate": [{"angle": -1.51}, {"time": 0.2, "angle": 27.12, "curve": 0, "c2": 0.25, "c3": 0.556, "c4": 0.64}, {"time": 0.2667, "angle": 21.23, "curve": 0, "c2": 0.25, "c3": 0.833, "c4": 0.71}, {"time": 0.3333, "angle": 4.69}, {"time": 0.4, "angle": 21.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4667, "angle": 27.12, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -1.51}]}, "wings_L9": {"rotate": [{"angle": -2.52}, {"time": 0.2, "angle": -14.52, "curve": 0.615, "c2": 0.37, "c4": 0.72}, {"time": 0.3333, "angle": -5.03}, {"time": 0.4667, "angle": -14.52, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -2.52}]}, "wings_L10": {"rotate": [{"angle": -3.27}, {"time": 0.1333, "angle": -7.77}, {"time": 0.2, "angle": -6.18, "curve": 0.545, "c2": 0.38, "c4": 0.74}, {"time": 0.3333, "angle": 1.46}, {"time": 0.4667, "angle": -6.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5333, "angle": -7.77, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -3.27}]}, "wings_L11": {"rotate": [{"angle": -0.71}, {"time": 0.0667, "angle": -1.49, "curve": 0.329, "c2": 0.33, "c3": 0.663, "c4": 0.67}, {"time": 0.2, "angle": 0.7, "curve": 0, "c2": 0.24, "c3": 0.714, "c4": 0.68}, {"time": 0.3333, "angle": 4.22}, {"time": 0.4667, "angle": 0.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "angle": -1.49, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -0.71}]}, "leg_R": {"rotate": [{"angle": 11.16}]}, "leg_R2": {"rotate": [{"angle": 9.72}]}, "leg_R3": {"rotate": [{"angle": 8.1}]}, "leg_L": {"rotate": [{"angle": 11.38}]}, "wings_R7": {"rotate": [{"angle": -0.49}, {"time": 0.1667, "angle": -13.22, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.2, "angle": -12.14, "curve": 0, "c2": 0.26, "c3": 0.455, "c4": 0.62}, {"time": 0.3333, "angle": -9.07, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.4667, "angle": -12.14, "curve": 0.36, "c2": 0.64, "c3": 0.695}, {"time": 0.5, "angle": -13.22, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -0.49}]}, "body_4": {"rotate": [{"angle": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 47.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.86}]}, "wings_R12": {"rotate": [{"angle": -20.21}, {"time": 0.0667, "angle": -32.81}, {"time": 0.2, "angle": -11.64, "curve": 0.643, "c2": 0.36, "c4": 0.7}, {"time": 0.3333, "angle": 10.49, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.4667, "angle": -11.64, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "angle": -32.81, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.6667, "angle": -20.21}]}, "wings_R4": {"rotate": [{"angle": 8.34}, {"time": 0.2667, "curve": 0, "c2": 0.25, "c3": 0.486, "c4": 0.62}, {"time": 0.3333, "angle": -6.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 8.34}], "scale": [{"time": 0.1, "curve": 0, "c2": 0.26, "c3": 0.467, "c4": 0.62}, {"time": 0.1333, "x": 0.879, "curve": 0.362, "c2": 0.34, "c3": 0.696, "c4": 0.67}, {"time": 0.1667, "x": 0.866, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.67}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4667, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.5, "x": 0.866, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.5333, "x": 0.879, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.5667}]}, "wings_R6": {"rotate": [{"angle": 4.03}, {"time": 0.2, "angle": -5.6, "curve": 0, "c2": 0.25, "c3": 0.476, "c4": 0.62}, {"time": 0.3333, "angle": -8.99, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.4667, "angle": -5.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 4.03}]}, "wings_R8": {"rotate": [{"angle": -7.88}, {"time": 0.1333, "angle": -20.93, "curve": 0.495, "c2": 0.37, "c4": 0.75}, {"time": 0.2, "angle": -16.32, "curve": 0, "c2": 0.28, "c3": 0.4, "c4": 0.62}, {"time": 0.3333, "angle": -7.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4667, "angle": -16.32, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5333, "angle": -20.93, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -7.88}]}, "wings_R5": {"rotate": [{"angle": 9.18}, {"time": 0.2333, "curve": 0, "c2": 0.25, "c3": 0.486, "c4": 0.62}, {"time": 0.3333, "angle": -7.49, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.4333, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 9.18}], "scale": [{"time": 0.1}, {"time": 0.1333, "x": 0.53, "curve": 0, "c2": 0.26, "c3": 0.455, "c4": 0.62}, {"time": 0.1667, "x": 0.482, "curve": 0, "c2": 0.25, "c3": 0.5, "c4": 0.63}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.5, "x": 0.482, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.5333, "x": 0.53, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.5667}]}, "wings_R15": {"rotate": [{"angle": 5.75}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "angle": 5.75, "curve": 0, "c2": 0.25, "c3": 0.5, "c4": 0.62}, {"time": 0.3333, "angle": -2.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": 5.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 5.75}]}, "wings_R9": {"rotate": [{"angle": -17.95}, {"time": 0.1, "angle": -29.33, "curve": 0, "c2": 0.26, "c3": 0.467, "c4": 0.62}, {"time": 0.2, "angle": -17.95, "curve": 0, "c2": 0.25, "c3": 0.556, "c4": 0.64}, {"time": 0.3333, "angle": -4.55, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "angle": -17.95, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.5667, "angle": -29.33, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -17.95}]}, "wings_R14": {"rotate": [{"angle": 7.85}, {"time": 0.2, "curve": 0, "c2": 0.25, "c3": 0.556, "c4": 0.64}, {"time": 0.2667, "angle": 1.62, "curve": 0, "c2": 0.24, "c3": 0.714, "c4": 0.68}, {"time": 0.3333, "angle": -5.3, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4, "angle": 1.62, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 7.85}]}, "wings_R11": {"rotate": [{"angle": -5.39}, {"time": 0.1333, "angle": -19.56, "curve": 0, "c2": 0.26, "c3": 0.455, "c4": 0.62}, {"time": 0.2, "angle": -15.48, "curve": 0.615, "c2": 0.37, "c4": 0.72}, {"time": 0.3333, "angle": 2.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "angle": -15.48, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5333, "angle": -19.56, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.6667, "angle": -5.39}]}, "leg_L3": {"rotate": [{"angle": 2.58}]}, "wings_R13": {"rotate": [{"angle": 7.93}, {"time": 0.2667, "curve": 0, "c2": 0.25, "c3": 0.486, "c4": 0.62}, {"time": 0.3333, "angle": -4.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 7.93}]}, "wings_R10": {"rotate": [{"angle": -4.5}, {"time": 0.2, "angle": -14.18, "curve": 0, "c2": 0.25, "c3": 0.476, "c4": 0.62}, {"time": 0.3333, "angle": -2.26, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.4667, "angle": -14.18, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -4.5}]}, "yinying": {"translate": [{"x": -51.7}]}, "head4": {"rotate": [{"angle": -35.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -28.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -35.17}]}, "head3": {"rotate": [{"angle": 4.32}]}}}, "skill2": {"slots": {"wings_L": {"attachment": [{"name": "wings_L"}, {"time": 0.1333, "name": null}]}, "wings_L2": {"attachment": [{"name": null}, {"time": 0.1333, "name": "wings_L2"}]}, "wings_R": {"attachment": [{"name": "wings_R"}, {"time": 0.1333, "name": null}]}, "wings_R2": {"attachment": [{"name": null}, {"time": 0.1, "name": "wings_R2"}]}}, "bones": {"body4": {"rotate": [{"angle": 11.79, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.0667, "angle": -5.61, "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 0.3333, "angle": -8.07, "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 0.6667, "angle": 2.81}]}, "body": {"rotate": [{"angle": -15.43, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.0667, "angle": -22, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "angle": -18.89, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5, "angle": 15.72, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 12.53}], "translate": [{"x": -58.87, "y": 46.94, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 6.44, "y": 46.94, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.3333, "x": 15.51, "y": 46.94, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.5, "x": -19.91, "y": -6.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -2.4, "y": -10.14}]}, "body2": {"rotate": [{"angle": -1.29, "curve": 0.338, "c2": 0.37, "c3": 0.671, "c4": 0.7}, {"time": 0.0667, "angle": -18.68, "curve": 0.354, "c2": 0.65, "c3": 0.688}, {"time": 0.3333, "angle": -21.14, "curve": 0.354, "c2": 0.65, "c3": 0.688}, {"time": 0.6667, "angle": -10.63}]}, "body3": {"rotate": [{"angle": -1.1, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.0667, "angle": -18.44, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.3333, "angle": -20.9, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.6667, "angle": -10.29}]}, "wings_R3": {"rotate": [{"angle": 26.26, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": -3.31, "curve": 0.275, "c2": 0.13, "c3": 0.71, "c4": 0.82}, {"time": 0.3667, "angle": 3.71, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4, "angle": 4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.31}], "translate": [{"x": -24.51, "y": 7.8, "curve": "stepped"}, {"time": 0.0667, "x": -24.51, "y": 7.8, "curve": 0.289, "c2": 0.15, "c3": 0.634, "c4": 0.53}, {"time": 0.1, "x": -16.95, "y": 3.68, "curve": 0.318, "c2": 0.17, "c3": 0.652, "c4": 0.51}, {"time": 0.1333}], "scale": [{"x": -0.845, "curve": "stepped"}, {"time": 0.0667, "x": -0.845, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "x": -0.518, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.1333, "x": 0.925, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.67}, {"time": 0.1667}]}, "head": {"rotate": [{"angle": 36.08, "curve": 0.34, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.1333, "angle": 95.6, "curve": 0, "c2": 0.91, "c3": 0.705}, {"time": 0.3333, "angle": 65.73, "curve": 0.368, "c2": 0.63, "c3": 0.705}, {"time": 0.6667, "angle": -6.53}]}, "hand_R2": {"rotate": [{"angle": 12.05, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -13.55, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -17.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 1.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.24}]}, "hand_R": {"rotate": [{"angle": 4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -20.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -24.74, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.2}]}, "hand_L2": {"rotate": [{"angle": 9.66, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -15.6, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.64}]}, "hand_L": {"rotate": [{"angle": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -27.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -9.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -14.19}]}, "hand_L3": {"rotate": [{"angle": -13.67, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -12.65, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -37.25, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -41.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -23.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -27.94}]}, "hand_R3": {"rotate": [{"angle": -12.54, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": -11.52, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -36.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -39.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -22.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -26.81}]}, "body_2": {"rotate": [{"angle": 8.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 9.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "body_3": {"rotate": [{"angle": 19.08, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 25.18, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.3333, "angle": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.73, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 6.1}]}, "leg_L2": {"rotate": [{"angle": 7.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": 7.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -27.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.55, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.47}]}, "wings_R2": {"rotate": [{"angle": 11.29, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1667, "angle": 11.12, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3667, "angle": 12.32, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.6333, "angle": 10.94, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": 11.12}]}, "wings_L2": {"rotate": [{"angle": -8.97, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 0.1667, "angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.73}]}, "wings_L3": {"rotate": [{"angle": -13.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.1667, "angle": 6.03, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": 8.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 6.03}], "scale": [{"x": -1, "curve": "stepped"}, {"time": 0.0667, "x": -1, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.1, "x": -0.48, "curve": 0.347, "c2": 0.38, "c3": 0.688, "c4": 0.74}, {"time": 0.1333, "x": 0.469, "curve": 0.377, "c2": 0.61, "c3": 0.719}, {"time": 0.1667}]}, "wings_L4": {"rotate": [{"angle": -11.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -14.83, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": 6.16, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2333, "angle": 12.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -14.83, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 6.16}]}, "wings_L5": {"rotate": [{"angle": -4.25, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -17.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 0.51, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": 18.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -17.52, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 0.51}]}, "wings_L6": {"rotate": [{"angle": 1.58, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": -20.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": -4.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": 23.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -20.58, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -4.27}]}, "wings_L7": {"rotate": [{"angle": -13.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -18.17, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": 11.04, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.2333, "angle": 20.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.17, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 11.04}]}, "wings_L8": {"rotate": [{"angle": -1.51, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -18.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 4.69, "curve": 0.347, "c2": 0.38, "c3": 0.694, "c4": 0.76}, {"time": 0.2333, "angle": 21.23, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": 27.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -18.17, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.69}]}, "wings_L9": {"rotate": [{"angle": -2.52, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -5.03, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3, "angle": -14.52, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.46, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -5.03}]}, "wings_L10": {"rotate": [{"angle": -3.27, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": 1.46, "curve": 0.326, "c2": 0.31, "c3": 0.697, "c4": 0.76}, {"time": 0.3, "angle": -6.18, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": -7.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 4.46, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 1.46}]}, "wings_L11": {"rotate": [{"angle": -0.71, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": 4.46, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1667, "angle": 4.22, "curve": 0.278, "c2": 0.15, "c3": 0.651, "c4": 0.61}, {"time": 0.3, "angle": 0.7, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.22}]}, "leg_R": {"rotate": [{"angle": 11.16, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -18.21, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.13}]}, "leg_R2": {"rotate": [{"angle": 9.72, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.0333, "angle": 10.25, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -15.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.02, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -0.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.04}]}, "leg_R3": {"rotate": [{"angle": 8.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 9.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -16.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -20.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.17}]}, "leg_L": {"rotate": [{"angle": 11.38, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -23.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 0.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -3.91}]}, "wings_R7": {"rotate": [{"angle": -0.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1, "angle": 12.24, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1667, "angle": -9.07, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -14.34, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 0.3667, "angle": -1.56, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.5, "angle": 9.94, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 0.6667, "angle": -9.07}]}, "body_4": {"rotate": [{"angle": 8.86, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 17.72, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "angle": 8.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 29.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.86}]}, "wings_R12": {"rotate": [{"angle": -20.21, "curve": 0.348, "c2": 0.39, "c3": 0.685, "c4": 0.73}, {"time": 0.1333, "angle": 24.76, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.1667, "angle": 10.49, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.3667, "angle": -12.94, "curve": 0.272, "c2": 0.12, "c3": 0.649, "c4": 0.6}, {"time": 0.5, "angle": 5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.6, "angle": 17.23, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.6667, "angle": 10.49}]}, "wings_R4": {"rotate": [{"angle": 8.34, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 9.58, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": -6.46, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 3.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": 4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.46}], "scale": [{"time": 0.0667, "curve": 0.495, "c2": 0.37, "c4": 0.75}, {"time": 0.1, "x": 0.866, "curve": 0, "c2": 0.28, "c3": 0.4, "c4": 0.62}, {"time": 0.1333}]}, "wings_R6": {"rotate": [{"angle": 4.03, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 9.64, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -8.99, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "angle": -10.76, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 0.3667, "angle": 2.48, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667, "angle": 8.63, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 0.6667, "angle": -8.99}]}, "wings_R8": {"rotate": [{"angle": -7.88, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 14.54, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -7.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -19.27, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": -7.77, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "angle": 11.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -7.77}]}, "wings_R5": {"rotate": [{"angle": 9.18, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.0667, "angle": 12.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.1667, "angle": -7.49, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.2, "angle": -7.88, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 0.3667, "angle": 3.32, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.4333, "angle": 6.42, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 0.6667, "angle": -7.49}], "scale": [{"time": 0.0667, "curve": 0, "c2": 0.25, "c4": 0.75}, {"time": 0.1, "x": 0.482, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.67}, {"time": 0.1333}]}, "wings_R15": {"rotate": [{"angle": 5.75, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": 15.64, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": -2.4, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2667, "angle": -13.83, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3667, "angle": -2.37, "curve": 0.33, "c2": 0.32, "c3": 0.67, "c4": 0.68}, {"time": 0.4333, "angle": 5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.5333, "angle": 17.23, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -2.4}]}, "wings_R9": {"rotate": [{"angle": -17.95, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.1333, "angle": 17.66, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.1667, "angle": -4.55, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.3, "angle": -26.25, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.3667, "angle": -17.3, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.5667, "angle": 14.97, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.6667, "angle": -4.55}]}, "wings_R14": {"rotate": [{"angle": 7.85, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": 12.42, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -5.3, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -7.78, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 4.34, "curve": 0.349, "c2": 0.39, "c3": 0.689, "c4": 0.74}, {"time": 0.4333, "angle": 8.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4667, "angle": 11.28, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -5.3}]}, "wings_R11": {"rotate": [{"angle": -5.39, "curve": 0.345, "c2": 0.4, "c3": 0.679, "c4": 0.73}, {"time": 0.1, "angle": 11.83, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.1667, "angle": 2.26, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.3, "angle": -7.78, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 0.3667, "angle": -3.56, "curve": 0.322, "c2": 0.3, "c3": 0.697, "c4": 0.76}, {"time": 0.5, "angle": 8.8, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": 11.28, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.6667, "angle": 2.26}]}, "leg_L3": {"rotate": [{"angle": 2.58, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0333, "angle": 3.61, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -31.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -25.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.69}]}, "wings_R13": {"rotate": [{"angle": 7.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 9.12, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.1667, "angle": -4.73, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.3667, "angle": 3.31, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.4333, "angle": 4.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.73}]}, "wings_R10": {"rotate": [{"angle": -4.5, "curve": 0.321, "c2": 0.3, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": 5.86, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": -2.26, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2333, "angle": -4.73, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 0.3667, "angle": -0.35, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.5, "angle": 4.51, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 0.6667, "angle": -2.26}]}, "yinying": {"translate": [{"x": -51.7, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 32.21, "y": -2.54, "curve": "stepped"}, {"time": 0.3333, "x": 32.21, "y": -2.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 1.27, "curve": "stepped"}, {"time": 0.3333, "x": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5}]}, "head4": {"rotate": [{"angle": -35.17, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1333, "angle": -42.51, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.3, "angle": -1.97}, {"time": 0.6667, "angle": -9.81}]}, "head3": {"rotate": [{"angle": 4.32}, {"time": 0.1333, "angle": 6.05, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.3}]}}}}}