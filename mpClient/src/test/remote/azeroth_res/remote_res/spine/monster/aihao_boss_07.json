{"skeleton": {"hash": "FQUHUtLbYIdhbR1uoFyC2M3fgxs", "spine": "3.8.99", "x": -74.87, "y": -21.84, "width": 143.83, "height": 142.34, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/哀嚎洞穴/变异蹒跚者"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 29.55, "scaleX": 1.3196, "scaleY": 1.3196}, {"name": "yinying", "parent": "bone", "x": -0.79, "y": 4.38}, {"name": "bone2", "parent": "bone", "length": 13.94, "rotation": 1.49, "x": 1.84, "y": 32.28}, {"name": "body", "parent": "bone2", "length": 20.27, "rotation": 86.22, "x": 0.32, "y": 0.03}, {"name": "body2", "parent": "body", "length": 30.49, "rotation": -5.09, "x": 20.27}, {"name": "head", "parent": "body2", "length": 19.05, "rotation": -170.59, "x": 19.91, "y": -2.05}, {"name": "arm_L", "parent": "body2", "length": 29.58, "rotation": -154.27, "x": 22.1, "y": -19.33}, {"name": "arm_L4", "parent": "arm_L", "length": 26.2, "rotation": 4.38, "x": 30.52, "y": -0.17}, {"name": "foliage_6", "parent": "arm_L4", "length": 8.5, "rotation": 34.59, "x": 4.79, "y": 7.95}, {"name": "arm_L2", "parent": "arm_L4", "length": 6.83, "rotation": -41.17, "x": 6.18, "y": -8.3}, {"name": "arm_L3", "parent": "arm_L4", "length": 8.52, "rotation": -83.33, "x": 28.06, "y": -1.51}, {"name": "arm_L6", "parent": "arm_L4", "length": 6.53, "rotation": -114.35, "x": 26.14, "y": -1.62}, {"name": "torso_L2", "parent": "body2", "length": 6.07, "rotation": -118.33, "x": 26.93, "y": -13.83}, {"name": "torso_L3", "parent": "torso_L2", "length": 8.2, "rotation": 1.42, "x": 6.07}, {"name": "torso_L4", "parent": "torso_L3", "length": 5.79, "rotation": -27.1, "x": 8.2}, {"name": "foliage", "parent": "body2", "length": 11.03, "rotation": -89.58, "x": 28.92, "y": -3.3}, {"name": "foliage_2", "parent": "body2", "length": 13.51, "rotation": -58.93, "x": 31.05, "y": -1.05}, {"name": "torso_L", "parent": "body2", "length": 9.03, "rotation": -100.18, "x": 29.66, "y": -14.25}, {"name": "foliage_4", "parent": "body2", "length": 8.41, "rotation": 69.01, "x": 29.81, "y": 11.4}, {"name": "foliage_3", "parent": "body2", "length": 9.21, "rotation": 141.07, "x": 27.57, "y": 13.64}, {"name": "foliage_5", "parent": "body2", "length": 9.62, "rotation": 127.89, "x": 28.63, "y": 19.3}, {"name": "torso_R", "parent": "body2", "length": 7.39, "rotation": 146.61, "x": 23.3, "y": 19.91}, {"name": "torso_R2", "parent": "torso_R", "length": 6.45, "rotation": 1.84, "x": 7.39}, {"name": "torso_R3", "parent": "torso_R2", "length": 4.38, "rotation": -9.65, "x": 6.45}, {"name": "arm_R5", "parent": "body2", "length": 31.51, "rotation": 157.27, "x": 13.19, "y": 29.08}, {"name": "arm_R4", "parent": "arm_R5", "length": 18.57, "rotation": 40.45, "x": 32.26, "y": 0.44}, {"name": "arm_R", "parent": "arm_R4", "length": 9.63, "rotation": 26.7, "x": 17.85, "y": 0.87}, {"name": "arm_R3", "parent": "arm_R4", "length": 9.14, "rotation": 31.44, "x": 10.73, "y": 11.16}, {"name": "arm_R2", "parent": "arm_R4", "length": 11.02, "rotation": 16.22, "x": 19.05, "y": -7.9}, {"name": "foliage_7", "parent": "arm_R4", "length": 9.57, "rotation": -8.61, "x": -0.51, "y": -5.31}, {"name": "foliage_8", "parent": "arm_R4", "length": 7.67, "rotation": -32.55, "x": 2.94, "y": -9.04}, {"name": "bone3", "parent": "bone2", "x": 13.97, "y": 1.09}, {"name": "bone4", "parent": "bone3", "length": 22.75, "rotation": -103.42, "x": -23.29, "y": 9.66}, {"name": "bone5", "parent": "bone4", "length": 19.8, "rotation": -24.52, "x": 23.54, "y": -0.2}, {"name": "leg_R", "parent": "bone5", "length": 19.28, "rotation": -1.92, "x": 19.71, "y": -0.25, "transform": "noRotationOrReflection"}, {"name": "leg_L", "parent": "bone2", "length": 22.15, "rotation": -70.01, "x": 3.98, "y": 6.88}, {"name": "leg_L3", "parent": "leg_L", "length": 15.18, "rotation": -45.8, "x": 21.76, "y": -0.27}, {"name": "leg_L2", "parent": "leg_L3", "length": 21.31, "rotation": 2.83, "x": 15.11, "y": -0.18, "transform": "noRotationOrReflection"}, {"name": "foliage_9", "parent": "leg_L", "length": 4.58, "rotation": -46.83, "x": 17.45, "y": -9.03}, {"name": "leg_R2", "parent": "bone", "x": -24.42, "y": 4.15, "color": "ff3f00ff"}, {"name": "leg_L4", "parent": "bone", "x": 7.1, "y": 5.09, "color": "ff3f00ff"}, {"name": "head3", "parent": "head", "length": 7.67, "rotation": 2.37, "x": 20.94, "y": -0.16}, {"name": "head4", "parent": "head3", "length": 4.38, "rotation": -1.1, "x": 7.67}, {"name": "head5", "parent": "head4", "length": 5.73, "rotation": 0.9, "x": 4.38}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "arm_L5", "bone": "arm_L6", "attachment": "arm_L5"}, {"name": "arm_L4", "bone": "arm_L4", "attachment": "arm_L4"}, {"name": "arm_L3", "bone": "arm_L3", "attachment": "arm_L3"}, {"name": "arm_L2", "bone": "arm_L2", "attachment": "arm_L2"}, {"name": "arm_L", "bone": "arm_L", "attachment": "arm_L"}, {"name": "leg_L", "bone": "leg_L2", "attachment": "leg_L"}, {"name": "foliage_9", "bone": "foliage_9", "attachment": "foliage_9"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "arm_R5", "bone": "arm_R5", "attachment": "arm_R5"}, {"name": "arm_R4", "bone": "arm_R4", "attachment": "arm_R4"}, {"name": "arm_R3", "bone": "arm_R3", "attachment": "arm_R3"}, {"name": "arm_R2", "bone": "arm_R2", "attachment": "arm_R2"}, {"name": "arm_R", "bone": "arm_R", "attachment": "arm_R"}, {"name": "torso_R", "bone": "torso_R", "attachment": "torso_R"}, {"name": "torso_L2", "bone": "torso_L2", "attachment": "torso_L2"}, {"name": "torso_L", "bone": "torso_L", "attachment": "torso_L"}, {"name": "head", "bone": "head3", "attachment": "head"}, {"name": "close_eye_R", "bone": "head", "color": "ffffff00", "attachment": "close_eye_R"}, {"name": "close_eye_L", "bone": "head", "color": "ffffff00", "attachment": "close_eye_L"}, {"name": "foliage_8", "bone": "foliage_8", "attachment": "foliage_8"}, {"name": "foliage_7", "bone": "foliage_7", "attachment": "foliage_7"}, {"name": "foliage_6", "bone": "foliage_6", "attachment": "foliage_6"}, {"name": "foliage_5", "bone": "foliage_5", "attachment": "foliage_5"}, {"name": "foliage_4", "bone": "foliage_4", "attachment": "foliage_4"}, {"name": "foliage_3", "bone": "foliage_3", "attachment": "foliage_3"}, {"name": "foliage_2", "bone": "foliage_2", "attachment": "foliage_2"}, {"name": "foliage", "bone": "foliage", "attachment": "foliage"}], "ik": [{"name": "leg_L", "order": 1, "bones": ["leg_L", "leg_L3"], "target": "leg_L4", "bendPositive": false}, {"name": "leg_R", "bones": ["bone4", "bone5"], "target": "leg_R2", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [39.64, 9.28, 31.14, -16.34, -4.93, -4.38, 3.57, 21.25], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 27, "height": 38}}, "arm_L2": {"arm_L2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.86, 10.78, 14.29, -2.5, 0.06, -7.25, -4.37, 6.04], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 15}}, "arm_L3": {"arm_L3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-0.28, 8.44, 16.27, -0.89, 9.89, -12.21, -6.67, -2.88], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 13}}, "arm_L4": {"arm_L4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33.34, 5.37, 22.14, -21.37, -11.07, -7.46, 0.14, 19.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 29, "height": 36}}, "arm_L5": {"arm_L5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-1.38, 4.91, 10.62, 5.24, 10.9, -4.75, -1.1, -5.09], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 12, "height": 10}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.76, 4.37, 6.31, -10.8, -6.46, -1.16, 4.99, 14.01], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 16}}, "arm_R2": {"arm_R2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.62, 4.65, 8.57, -11.45, -5.74, -4.29, 2.31, 11.81], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 18, "height": 16}}, "arm_R3": {"arm_R3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [14.05, -1.37, 6.05, -10.32, -5.88, 0.35, 2.12, 9.29], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 12, "height": 16}}, "arm_R4": {"arm_R4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [27.81, 13.47, 21.53, -20.96, -11.92, -14.86, -5.64, 19.57], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 35, "height": 34}}, "arm_R5": {"arm_R5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [29.57, 26.9, 50.13, -8.57, 13.8, -29.64, -6.77, 5.83], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 41, "height": 42}}, "body": {"body": {"type": "mesh", "uvs": [0.01391, 0.15921, 0.12935, 0.0523, 0.29145, 0, 0.47075, 0, 0.6918, 0, 0.86864, 0.03703, 1, 0.20249, 1, 0.35012, 0.98163, 0.51812, 0.92268, 0.66321, 0.8711, 0.79812, 0.78022, 0.86939, 0.77531, 0.95339, 0.61812, 0.98394, 0.47566, 0.99921, 0.32829, 1, 0.19566, 0.97121, 0.08022, 0.87703, 0.07531, 0.78539, 0.01391, 0.69885, 0, 0.55376, 0, 0.40358, 0, 0.28649, 0.49285, 0.14394, 0.49285, 0.26103, 0.49285, 0.44685, 0.50513, 0.59448, 0.49777, 0.75485, 0.47075, 0.88467], "triangles": [23, 3, 4, 24, 23, 4, 5, 6, 7, 1, 21, 22, 5, 25, 24, 2, 23, 24, 5, 7, 25, 1, 20, 21, 2, 3, 23, 24, 1, 2, 25, 7, 26, 4, 5, 24, 8, 26, 7, 9, 26, 8, 22, 0, 1, 1, 24, 20, 25, 19, 24, 19, 25, 26, 9, 10, 26, 20, 24, 19, 10, 11, 26, 26, 18, 19, 26, 11, 27, 27, 18, 26, 18, 16, 17, 18, 27, 16, 13, 28, 27, 27, 11, 13, 27, 28, 16, 12, 13, 11, 14, 28, 13, 15, 16, 28, 14, 15, 28], "vertices": [2, 4, 44.11, 28.89, 0.12621, 5, 21.19, 30.89, 0.87379, 2, 4, 50.25, 22.55, 0.06495, 5, 27.87, 25.12, 0.93505, 2, 4, 53.5, 13.43, 0.02514, 5, 31.91, 16.32, 0.97486, 2, 4, 53.9, 3.22, 0.00516, 5, 33.22, 6.19, 0.99484, 2, 4, 54.41, -9.37, 2e-05, 5, 34.84, -6.31, 0.99998, 2, 4, 52.78, -19.53, 0.00397, 5, 34.11, -16.57, 0.99603, 2, 4, 43.98, -27.37, 0.02255, 5, 26.05, -25.16, 0.97745, 2, 4, 35.87, -27.69, 0.08482, 5, 18, -26.2, 0.91518, 2, 4, 26.59, -27.02, 0.21977, 5, 8.7, -26.35, 0.78023, 2, 4, 18.49, -23.98, 0.42255, 5, 0.35, -24.04, 0.57745, 2, 4, 10.95, -21.34, 0.64591, 5, -7.38, -22.08, 0.35409, 1, 4, 6.83, -16.32, 1, 2, 4, 2.2, -16.22, 0.976, 5, -16.55, -17.76, 0.024, 2, 4, 0.17, -7.34, 0.97468, 5, -19.37, -9.09, 0.02532, 2, 4, -1, 0.74, 0.99701, 5, -21.25, -1.15, 0.00299, 1, 4, -1.38, 9.13, 1, 1, 4, -0.1, 16.75, 1, 1, 4, 4.82, 23.53, 1, 1, 4, 9.84, 24.01, 1, 1, 4, 14.46, 27.7, 1, 2, 4, 22.4, 28.81, 0.904, 5, -0.43, 28.89, 0.096, 2, 4, 30.65, 29.14, 0.36303, 5, 7.76, 29.95, 0.63697, 2, 4, 37.09, 29.4, 0.21686, 5, 14.15, 30.77, 0.78314, 2, 4, 46.04, 1.64, 0.00026, 5, 25.53, 3.92, 0.99974, 2, 4, 39.61, 1.39, 0.00202, 5, 19.14, 3.09, 0.99798, 2, 4, 29.4, 0.98, 0.00882, 5, 9.01, 1.78, 0.99118, 1, 5, 1.05, 0.05, 1, 2, 4, 12.48, 0.02, 0.99996, 5, -7.76, -0.67, 4e-05, 1, 4, 5.29, 1.27, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 57, "height": 55}}, "close_eye_L": {"close_eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.15, 11.14, 15.83, 2.14, 6.83, 2.46, 7.15, 11.46], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 9, "height": 9}}, "close_eye_R": {"close_eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.65, -2.89, 16.3, -12.88, 6.3, -12.53, 6.66, -2.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 10, "height": 10}}, "foliage": {"foliage": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.76, -2.87, -0.14, -4.57, -1.11, 3.37, 12.79, 5.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 8}}, "foliage_2": {"foliage_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [10.97, -8.01, -2.76, -1.98, 1.66, 8.09, 15.39, 2.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 11}}, "foliage_3": {"foliage_3": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.5, 9.07, 14.18, 0.78, 5.89, -7.9, -2.79, 0.39], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 12, "height": 12}}, "foliage_4": {"foliage_4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-6.64, 0.15, 7.44, 7.75, 12.19, -1.05, -1.89, -8.65], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 16, "height": 10}}, "foliage_5": {"foliage_5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2.61, 7.9, 17.26, -0.73, 12.18, -9.34, -2.47, -0.71], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 17, "height": 10}}, "foliage_6": {"foliage_6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.32, -0.35, 1.75, -5.2, -3.11, 2.37, 4.46, 7.23], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 9, "height": 9}}, "foliage_7": {"foliage_7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.62, 4.15, 13.38, -3.85, -1.62, -3.39, -1.37, 4.6], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 8, "height": 15}}, "foliage_8": {"foliage_8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.6, 6.73, 12.62, -0.68, 0.58, -5.59, -2.44, 1.82], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 8, "height": 13}}, "foliage_9": {"foliage_9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.1, 4.22, 7.67, -1.2, 0.44, -4.62, -2.13, 0.8], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 6, "height": 8}}, "head": {"head": {"type": "mesh", "uvs": [0.11538, 0.14572, 0.23613, 0.04772, 0.39626, 0, 0.62988, 0, 0.77688, 0.04422, 0.89763, 0.14747, 0.97113, 0.27522, 1, 0.39772, 1, 0.54297, 1, 0.66372, 0.9617, 0.79987, 0.86457, 0.81212, 0.76482, 0.89962, 0.6782, 0.96087, 0.59157, 1, 0.45245, 0.94862, 0.28707, 0.90487, 0.16895, 0.81737, 0.06133, 0.77012, 0, 0.69837, 0, 0.58112, 0, 0.45337, 0, 0.34603, 0, 0.23228, 0.5207, 0.56777, 0.52857, 0.63427, 0.54432, 0.73577, 0.54432, 0.81627, 0.56532, 0.89502, 0.23982, 0.61152, 0.24507, 0.65527, 0.26345, 0.74802, 0.29232, 0.82327, 0.78057, 0.51527, 0.7832, 0.62377, 0.7832, 0.70427, 0.76745, 0.79527, 0.52595, 0.24752, 0.28707, 0.50302, 0.51545, 0.44702], "triangles": [12, 36, 11, 16, 17, 32, 17, 31, 32, 27, 32, 26, 17, 18, 31, 32, 31, 26, 27, 26, 36, 11, 36, 35, 36, 26, 35, 10, 11, 35, 15, 28, 14, 14, 28, 13, 13, 28, 12, 16, 32, 15, 15, 27, 28, 15, 32, 27, 12, 28, 36, 28, 27, 36, 26, 31, 25, 10, 35, 9, 31, 18, 30, 18, 19, 30, 31, 30, 25, 26, 25, 35, 25, 34, 35, 35, 34, 9, 19, 29, 30, 34, 8, 9, 30, 29, 25, 29, 24, 25, 25, 24, 34, 24, 33, 34, 29, 38, 24, 29, 20, 38, 19, 20, 29, 34, 33, 8, 20, 21, 38, 33, 7, 8, 7, 33, 6, 37, 39, 38, 38, 21, 22, 37, 38, 22, 6, 33, 37, 33, 39, 37, 37, 22, 0, 22, 23, 0, 37, 5, 6, 0, 1, 37, 1, 2, 37, 37, 4, 5, 37, 3, 4, 37, 2, 3, 24, 39, 33, 38, 39, 24], "vertices": [1, 6, 0.29, -12.62, 1, 2, 6, -4.27, -8.59, 0.99895, 42, -25.54, -7.39, 0.00105, 1, 6, -6.38, -3.39, 1, 1, 6, -6.12, 4.08, 1, 1, 6, -3.83, 8.7, 1, 2, 6, 1.26, 12.39, 0.99869, 42, -19.15, 13.35, 0.00131, 1, 6, 7.47, 14.52, 1, 1, 6, 13.38, 15.24, 1, 4, 6, 20.35, 14.99, 0.51841, 42, 0.03, 15.16, 0.34639, 44, -12.06, 15.21, 2e-05, 43, -7.92, 15.01, 0.13518, 4, 6, 26.14, 14.79, 0.23596, 42, 5.81, 14.72, 0.43178, 44, -6.28, 14.78, 0.01687, 43, -2.13, 14.68, 0.31539, 4, 6, 32.63, 13.33, 0.08055, 42, 12.24, 12.99, 0.33683, 44, 0.15, 13.08, 0.09846, 43, 4.32, 13.08, 0.48415, 4, 6, 33.11, 10.2, 0.04699, 42, 12.58, 9.85, 0.25447, 44, 0.51, 9.94, 0.19295, 43, 4.73, 9.94, 0.50559, 4, 6, 37.19, 6.86, 0.002, 42, 16.53, 6.35, 0.03401, 44, 4.46, 6.44, 0.67131, 43, 8.74, 6.51, 0.29268, 3, 42, 19.24, 3.36, 0.00031, 44, 7.19, 3.46, 0.94624, 43, 11.51, 3.58, 0.05345, 2, 44, 8.86, 0.56, 0.99959, 43, 13.23, 0.7, 0.00041, 3, 42, 18.1, -3.8, 0.00402, 44, 6.08, -3.7, 0.93699, 43, 10.51, -3.6, 0.05899, 4, 6, 36.9, -8.42, 0.00038, 42, 15.6, -8.92, 0.10568, 44, 3.59, -8.82, 0.52466, 43, 8.11, -8.76, 0.36928, 4, 6, 32.57, -12.05, 0.03319, 42, 11.13, -12.36, 0.33974, 44, -0.87, -12.28, 0.17554, 43, 3.7, -12.3, 0.45153, 4, 6, 30.18, -15.41, 0.09033, 42, 8.6, -15.62, 0.45395, 44, -3.39, -15.55, 0.06329, 43, 1.23, -15.6, 0.39243, 4, 6, 26.67, -17.25, 0.15927, 42, 5.02, -17.32, 0.48755, 44, -6.97, -17.26, 0.02497, 43, -2.32, -17.36, 0.32821, 4, 6, 21.04, -17.05, 0.34891, 42, -0.59, -16.88, 0.44517, 44, -12.58, -16.84, 0.00222, 43, -7.94, -17.04, 0.2037, 1, 6, 14.92, -16.84, 1, 1, 6, 9.77, -16.65, 1, 1, 6, 4.31, -16.46, 1, 2, 42, 0.04, -0.22, 0.99934, 43, -7.62, -0.37, 0.00066, 2, 42, 3.25, -0.22, 0.99817, 43, -4.41, -0.3, 0.00183, 2, 42, 8.14, -0.09, 0.04728, 43, 0.48, -0.08, 0.95272, 3, 42, 12, -0.38, 0.00052, 44, -0.04, -0.3, 0.43787, 43, 4.34, -0.3, 0.56161, 2, 44, 3.77, 0.09, 0.99905, 43, 8.15, 0.15, 0.00095, 4, 6, 22.78, -9.44, 0.24185, 42, 1.45, -9.34, 0.55086, 44, -10.56, -9.3, 0.00511, 43, -6.04, -9.46, 0.20218, 4, 6, 24.88, -9.34, 0.14601, 42, 3.56, -9.34, 0.56051, 44, -8.45, -9.28, 0.01798, 43, -3.93, -9.42, 0.2755, 4, 6, 29.35, -8.91, 0.03769, 42, 8.04, -9.09, 0.40607, 44, -3.97, -9.02, 0.11095, 43, 0.55, -9.08, 0.44529, 4, 6, 32.99, -8.12, 0.00661, 42, 11.71, -8.45, 0.21149, 44, -0.3, -8.37, 0.31114, 43, 4.21, -8.37, 0.47076, 3, 6, 18.77, 8.02, 0.63455, 42, -1.83, 8.26, 0.30847, 43, -9.65, 8.08, 0.05698, 4, 6, 23.98, 7.92, 0.25525, 42, 3.37, 7.95, 0.55108, 44, -8.7, 8, 0.00391, 43, -4.45, 7.86, 0.18975, 4, 6, 27.84, 7.78, 0.09786, 42, 7.22, 7.65, 0.4586, 44, -4.85, 7.72, 0.04778, 43, -0.59, 7.64, 0.39576, 4, 6, 32.19, 7.13, 0.02384, 42, 11.54, 6.81, 0.18892, 44, -0.53, 6.9, 0.25195, 43, 3.74, 6.89, 0.53528, 1, 6, 5.64, 0.33, 1, 3, 6, 17.62, -7.74, 0.5999, 42, -3.63, -7.44, 0.33432, 43, -11.15, -7.65, 0.06577, 3, 6, 15.2, -0.34, 0.9978, 42, -5.75, 0.06, 0.00193, 43, -13.41, -0.2, 0.00027], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46, 40, 76, 76, 78, 78, 66, 66, 16], "width": 32, "height": 48}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.01194, 0.12217, 0.07761, 0.03967, 0.18632, 0, 0.34258, 0, 0.47559, 0.10132, 0.60015, 0.21315, 0.7247, 0.34332, 0.79265, 0.44782, 0.80623, 0.53215, 0.85379, 0.60548, 0.91041, 0.64765, 0.924, 0.72281, 0.98062, 0.81815, 1, 0.87865, 1, 0.99965, 0.81076, 1, 0.58882, 1, 0.41671, 1, 0.09512, 0.97581, 0, 0.94281, 0, 0.83831, 0.06115, 0.77598, 0.06115, 0.63665, 0.03624, 0.53032, 0, 0.45332, 0, 0.30665, 0, 0.21865, 0.18571, 0.06648, 0.28082, 0.25348, 0.41671, 0.51748, 0.39179, 0.44232, 0.43029, 0.59815, 0.32159, 0.71365, 0.22421, 0.85665, 0.27403, 0.85298, 0.51182, 0.86398, 0.75868, 0.84565, 0.29441, 0.79431, 0.60921, 0.73931, 0.82209, 0.73565], "triangles": [16, 36, 15, 14, 15, 13, 13, 15, 36, 36, 39, 12, 39, 11, 12, 17, 35, 16, 16, 35, 36, 18, 33, 17, 33, 34, 17, 17, 34, 35, 13, 36, 12, 33, 18, 20, 33, 20, 21, 20, 18, 19, 34, 37, 35, 35, 38, 36, 35, 37, 38, 34, 33, 37, 37, 33, 21, 36, 38, 39, 9, 39, 38, 39, 10, 11, 37, 32, 38, 37, 21, 32, 21, 22, 32, 32, 31, 38, 31, 32, 29, 32, 22, 29, 22, 23, 29, 9, 38, 8, 38, 31, 8, 31, 29, 8, 29, 7, 8, 23, 30, 29, 23, 24, 30, 29, 6, 7, 29, 30, 6, 24, 28, 30, 26, 27, 28, 28, 24, 25, 30, 5, 6, 30, 28, 5, 26, 0, 27, 28, 25, 26, 4, 28, 27, 28, 4, 5, 4, 27, 3, 3, 27, 2, 0, 1, 27, 27, 1, 2, 39, 9, 10], "vertices": [2, 36, 0.69, -6.69, 0.99169, 37, -10.09, -19.58, 0.00831, 2, 36, -1.72, -3.34, 0.99986, 37, -14.17, -18.97, 0.00014, 1, 36, -1.92, 0.71, 1, 1, 36, 0.03, 5.65, 1, 1, 36, 5.64, 8.3, 1, 2, 36, 11.56, 10.52, 0.99398, 38, 14.03, 26.47, 0.00602, 1, 36, 18.2, 12.46, 1, 1, 36, 23.13, 13, 1, 1, 36, 26.6, 12.13, 1, 1, 36, 30.06, 12.51, 1, 2, 36, 32.41, 13.65, 0.952, 38, 23.66, 7.72, 0.048, 2, 36, 35.52, 12.93, 0.21487, 38, 23.96, 4.54, 0.78513, 2, 36, 39.95, 13.25, 0.00821, 38, 25.69, 0.45, 0.99179, 1, 38, 26.22, -2.12, 1, 1, 38, 25.96, -7.2, 1, 1, 38, 19.54, -6.89, 1, 2, 37, 15.43, 13.47, 0.00199, 38, 12, -6.52, 0.99801, 2, 37, 17.84, 8.14, 0.16323, 38, 6.15, -6.22, 0.83677, 2, 37, 21.41, -2.24, 0.38754, 38, -4.71, -4.66, 0.61246, 2, 37, 21.48, -5.76, 0.08067, 38, -7.88, -3.12, 0.91933, 2, 37, 17.48, -7.57, 0.00029, 38, -7.66, 1.27, 0.99971, 3, 36, 26.85, -15.19, 0.00033, 37, 14.24, -6.75, 0.26238, 38, -5.45, 3.78, 0.73728, 2, 36, 21.4, -13.05, 0.08594, 37, 8.91, -9.16, 0.91406, 3, 36, 16.94, -12.2, 0.33107, 37, 5.19, -11.77, 0.65096, 38, -5.78, 14.12, 0.01797, 3, 36, 13.48, -12.16, 0.55202, 37, 2.75, -14.22, 0.44593, 38, -6.85, 17.42, 0.00205, 1, 36, 7.75, -9.91, 1, 1, 36, 4.31, -8.55, 1, 2, 36, 0.67, -0.34, 0.99999, 37, -14.66, -15.16, 1e-05, 2, 36, 9.17, -0.2, 0.99928, 37, -8.83, -8.98, 0.00072, 1, 36, 21.17, 0.04, 1, 2, 36, 17.93, 0.4, 0.99968, 38, 6.48, 17.21, 0.00032, 3, 36, 24.5, -0.77, 0.38573, 37, 2.27, 1.61, 0.5692, 38, 7.46, 10.61, 0.04507, 3, 36, 27.66, -5.99, 0.00136, 37, 8.21, 0.24, 0.98512, 38, 3.53, 5.95, 0.01352, 1, 38, -0.08, 0.12, 1, 2, 37, 14.21, 1.18, 0.06253, 38, 1.62, 0.19, 0.93747, 1, 38, 9.67, -0.68, 1, 1, 38, 18.09, -0.33, 1, 3, 36, 30.47, -8.09, 8e-05, 37, 11.68, 0.8, 0.83699, 38, 2.43, 2.61, 0.16293, 3, 36, 32.24, 2.71, 0.20759, 37, 5.16, 9.6, 0.0451, 38, 13.24, 4.38, 0.74731, 2, 36, 34.75, 9.51, 0.20788, 38, 20.47, 4.18, 0.79212], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52, 66, 68, 42, 74, 74, 76, 76, 78, 78, 22], "width": 34, "height": 42}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.32515, 0.06848, 0.52047, 0, 0.7265, 0, 0.9138, 0.09272, 1, 0.25637, 0.99407, 0.44023, 0.93253, 0.53317, 0.88704, 0.616, 0.8469, 0.6847, 0.78804, 0.74329, 0.85493, 0.84229, 0.91233, 0.94527, 0.92035, 1, 0.47619, 1, 0, 1, 0, 0.92911, 0.05879, 0.84627, 0, 0.7796, 0.02936, 0.71091, 0.10428, 0.68666, 0.15244, 0.63009, 0.20327, 0.55331, 0.22468, 0.47856, 0.25679, 0.33309, 0.26481, 0.15732, 0.6956, 0.11085, 0.64744, 0.31693, 0.57787, 0.53109, 0.56449, 0.56948, 0.51098, 0.62605, 0.42, 0.74727, 0.28622, 0.8887, 0.51633, 0.8988, 0.36114, 0.84627, 0.44141, 0.82607, 0.618, 0.81799], "triangles": [13, 32, 12, 12, 32, 11, 11, 32, 35, 35, 10, 11, 13, 14, 31, 31, 15, 16, 31, 33, 13, 13, 33, 32, 32, 33, 34, 31, 14, 15, 32, 34, 35, 31, 16, 33, 16, 19, 33, 16, 17, 19, 19, 17, 18, 35, 9, 10, 33, 19, 30, 33, 30, 34, 30, 19, 20, 34, 30, 35, 30, 29, 35, 35, 29, 9, 20, 21, 30, 30, 21, 29, 29, 28, 9, 28, 29, 22, 29, 21, 22, 9, 28, 8, 8, 28, 7, 28, 27, 7, 7, 27, 6, 28, 22, 27, 27, 26, 6, 6, 26, 5, 26, 4, 5, 25, 4, 26, 22, 23, 27, 27, 23, 26, 3, 4, 25, 23, 24, 26, 26, 24, 25, 25, 24, 0, 25, 0, 1, 25, 2, 3, 25, 1, 2], "vertices": [1, 33, 0.76, -14.1, 1, 2, 33, -4.02, -7.73, 0.99891, 34, -21.95, -18.28, 0.00109, 1, 33, -5.59, -0.27, 1, 1, 33, -2.58, 7.45, 1, 3, 33, 4.6, 12.23, 0.99922, 34, -22.39, 3.46, 0.00018, 35, 26.66, 32.79, 0.0006, 3, 33, 13.46, 13.88, 0.98004, 34, -15.01, 8.63, 0.01176, 35, 26.81, 23.78, 0.0082, 3, 33, 18.39, 12.59, 0.89796, 34, -9.99, 9.51, 0.06451, 35, 24.73, 19.14, 0.03753, 3, 33, 22.71, 11.79, 0.73071, 34, -5.73, 10.57, 0.1643, 35, 23.21, 15.01, 0.105, 3, 33, 26.31, 11.03, 0.47959, 34, -2.14, 11.37, 0.26619, 35, 21.87, 11.59, 0.25422, 3, 33, 29.57, 9.49, 0.17732, 34, 1.46, 11.32, 0.25371, 35, 19.81, 8.63, 0.56897, 3, 33, 33.8, 12.92, 0.06833, 34, 3.9, 16.2, 0.13713, 35, 22.48, 3.88, 0.79455, 3, 33, 38.3, 16.04, 0.00529, 34, 6.69, 20.9, 0.02366, 35, 24.81, -1.07, 0.97106, 3, 33, 40.86, 16.88, 0, 34, 8.67, 22.74, 0.00019, 35, 25.22, -3.74, 0.99981, 2, 34, 18.44, 9.52, 0.06719, 35, 8.8, -4.42, 0.93281, 2, 34, 28.91, -4.65, 0.10408, 35, -8.8, -5.15, 0.89592, 2, 34, 26.11, -6.72, 0.10607, 35, -8.95, -1.68, 0.89393, 2, 34, 21.55, -7.38, 0.23125, 35, -6.94, 2.47, 0.76875, 3, 33, 37.34, -18.67, 0.0002, 34, 20.22, -11.07, 0.11335, 35, -9.25, 5.64, 0.88645, 3, 33, 33.82, -18.3, 0.00319, 34, 16.87, -12.2, 0.26167, 35, -8.3, 9.05, 0.73514, 3, 33, 32.09, -15.83, 0.02088, 34, 14.26, -10.67, 0.46172, 35, -5.58, 10.35, 0.5174, 3, 33, 29.01, -14.66, 0.09061, 34, 10.97, -10.89, 0.62706, 35, -3.92, 13.2, 0.28234, 3, 33, 24.94, -13.6, 0.23612, 34, 6.83, -11.61, 0.65659, 35, -2.19, 17.03, 0.10728, 3, 33, 21.19, -13.58, 0.49482, 34, 3.41, -13.15, 0.47838, 35, -1.55, 20.73, 0.0268, 2, 33, 13.97, -13.9, 0.99358, 35, -0.66, 27.9, 0.00642, 1, 33, 5.48, -15.39, 1, 1, 33, -0.04, -0.26, 1, 1, 33, 10.2, 0.08, 1, 2, 33, 21, -0.27, 0.97821, 34, -2.28, -1.11, 0.02179, 2, 33, 22.95, -0.36, 0.6773, 34, -0.47, -0.39, 0.3227, 2, 33, 26.07, -1.73, 0.00426, 34, 2.93, -0.33, 0.99574, 2, 34, 9.71, 0.49, 0.98441, 35, 6.21, 7.87, 0.01559, 2, 34, 18.23, 0.62, 0.58207, 35, 1.55, 0.74, 0.41793, 2, 34, 13.57, 7.77, 0.02543, 35, 10.08, 0.6, 0.97457, 2, 34, 14.91, 1.62, 0.42726, 35, 4.24, 2.93, 0.57274, 3, 33, 36.19, -2.22, 0.00281, 34, 12.35, 3.42, 0.5264, 35, 7.16, 4.04, 0.47079, 3, 33, 34.45, 4.09, 0.06585, 34, 8.15, 8.44, 0.40564, 35, 13.68, 4.71, 0.5285], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 32, 66, 66, 68, 68, 70, 70, 18], "width": 37, "height": 49}}, "torso_L": {"torso_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.64, -2.45, 0.2, -6.07, -2.52, 2.51, 8.92, 6.13], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 12, "height": 9}}, "torso_L2": {"torso_L2": {"type": "mesh", "uvs": [0, 0, 0, 0.212, 0, 0.44838, 0.10931, 0.56657, 0.25183, 0.76819, 0.43955, 0.90723, 0.63074, 0.95242, 0.78369, 1, 0.84278, 0.8829, 0.85321, 0.67781, 1, 0.54919, 1, 0.43447, 0.85669, 0.40319, 0.72112, 0.31281, 0.61336, 0.21547, 0.48474, 0.10423, 0.3735, 0, 0.21707, 0, 0.18231, 0.17376, 0.30745, 0.34757, 0.44997, 0.47271, 0.58902, 0.54919, 0.73154, 0.63261], "triangles": [7, 6, 8, 8, 6, 22, 6, 5, 22, 5, 21, 22, 21, 5, 4, 8, 22, 9, 10, 9, 12, 9, 22, 12, 22, 21, 12, 12, 11, 10, 21, 13, 12, 21, 4, 20, 20, 3, 19, 20, 4, 3, 3, 2, 19, 21, 20, 13, 20, 14, 13, 20, 19, 14, 15, 19, 18, 19, 15, 14, 2, 18, 19, 2, 1, 18, 15, 18, 16, 16, 18, 17, 1, 0, 18, 18, 0, 17], "vertices": [2, 13, -2.35, 1.77, 0.98421, 14, -8.37, 1.98, 0.01579, 3, 13, 0.25, -1.85, 0.87557, 14, -5.87, -1.7, 0.12008, 15, -11.75, -7.92, 0.00436, 3, 13, 3.14, -5.88, 0.62558, 14, -3.07, -5.8, 0.34046, 15, -7.39, -10.3, 0.03396, 3, 13, 6.46, -6.55, 0.29736, 14, 0.23, -6.56, 0.52172, 15, -4.11, -9.47, 0.18092, 3, 13, 11.36, -8.24, 0.08493, 14, 5.08, -8.37, 0.46511, 15, 1.04, -8.87, 0.44996, 3, 13, 16.26, -8.31, 0.00159, 14, 9.99, -8.56, 0.25079, 15, 5.49, -6.81, 0.74762, 2, 14, 13.84, -7.09, 0.066, 15, 8.25, -3.74, 0.934, 2, 14, 17.05, -6.1, 0.00607, 15, 10.66, -1.4, 0.99393, 1, 15, 9.1, 0.87, 1, 2, 14, 14.45, 0.31, 0.0002, 15, 5.42, 3.12, 0.9998, 2, 14, 15.47, 4.28, 0.00531, 15, 4.53, 7.12, 0.99469, 2, 14, 14.12, 6.27, 0.05918, 15, 2.41, 8.28, 0.94082, 3, 13, 17.2, 5.39, 0.00107, 14, 11.26, 5.12, 0.28719, 15, 0.39, 5.95, 0.71174, 3, 13, 13.78, 5.27, 0.02514, 14, 7.84, 5.08, 0.58095, 15, -2.64, 4.36, 0.39391, 3, 13, 10.75, 5.61, 0.1628, 14, 4.82, 5.49, 0.72276, 15, -5.52, 3.35, 0.11444, 3, 13, 7.19, 5.93, 0.41776, 14, 1.27, 5.9, 0.57185, 15, -8.86, 2.1, 0.01039, 2, 13, 4.02, 6.35, 0.71476, 14, -1.89, 6.4, 0.28524, 2, 13, 1.35, 4.43, 0.91043, 14, -4.61, 4.54, 0.08957, 2, 13, 2.89, 1.04, 0.99759, 14, -3.15, 1.12, 0.00241, 2, 14, 1.07, -0.42, 0.99854, 15, -6.16, -3.62, 0.00146, 2, 14, 5.03, -0.9, 0.95985, 15, -2.42, -2.25, 0.04015, 2, 14, 8.34, -0.58, 0.14072, 15, 0.39, -0.46, 0.85928, 1, 15, 3.36, 1.33, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 21, "height": 21}}, "torso_R": {"torso_R": {"type": "mesh", "uvs": [0.83727, 0, 0.60912, 0.0437, 0.37683, 0.1207, 0.2026, 0.1977, 0, 0.2292, 0, 0.3552, 0.13623, 0.4847, 0.05742, 0.6702, 0.37268, 0.6842, 0.39342, 0.8802, 0.48883, 0.9957, 0.58838, 0.9362, 0.6589, 1, 0.75431, 0.9887, 0.83312, 0.8172, 0.91193, 0.6282, 0.9949, 0.4602, 1, 0.2992, 1, 0.1592, 0.95756, 0.0682, 0.96171, 0, 0.8663, 0.1767, 0.7543, 0.2642, 0.66719, 0.3552, 0.51371, 0.4952, 0.39342, 0.5967], "triangles": [12, 11, 13, 10, 9, 11, 13, 11, 14, 14, 11, 8, 11, 9, 8, 14, 8, 24, 8, 25, 24, 25, 8, 6, 8, 7, 6, 25, 6, 24, 6, 5, 3, 5, 4, 3, 14, 24, 15, 24, 23, 15, 6, 3, 24, 3, 2, 24, 24, 2, 23, 15, 23, 16, 23, 22, 16, 16, 22, 17, 2, 1, 23, 23, 1, 22, 22, 21, 17, 21, 18, 17, 22, 1, 21, 1, 0, 21, 21, 19, 18, 21, 0, 19, 19, 0, 20], "vertices": [3, 22, -4.38, -4.23, 0.96724, 23, -11.9, -3.85, 0.03005, 24, -17.44, -6.87, 0.00271, 3, 22, 0.7, -7.98, 0.77675, 23, -6.94, -7.76, 0.16912, 24, -11.9, -9.89, 0.05413, 3, 22, 6.67, -11.12, 0.48047, 23, -1.08, -11.09, 0.32305, 24, -5.56, -12.2, 0.19648, 3, 22, 11.6, -13.07, 0.18411, 23, 3.79, -13.2, 0.4304, 24, -0.41, -13.46, 0.38549, 3, 22, 15.94, -16.56, 0.04263, 23, 8.01, -16.82, 0.41846, 24, 4.36, -16.32, 0.53892, 3, 22, 18.99, -13.93, 0.00582, 23, 11.15, -14.29, 0.32877, 24, 7.03, -13.3, 0.66541, 3, 22, 19.73, -8.43, 0.00158, 23, 12.06, -8.83, 0.1914, 24, 7.01, -7.76, 0.80702, 3, 22, 25.62, -6.17, 0.00022, 23, 18.02, -6.75, 0.06427, 24, 12.54, -4.72, 0.93551, 2, 23, 13.02, 0.15, 0.00022, 24, 6.45, 1.25, 0.99978, 2, 23, 17.55, 4.53, 0.00383, 24, 10.18, 6.32, 0.99617, 2, 23, 18.8, 8.86, 0.02274, 24, 10.69, 10.8, 0.97726, 2, 23, 15.63, 9.75, 0.053, 24, 7.42, 11.15, 0.947, 2, 23, 16.02, 12.51, 0.08519, 24, 7.34, 13.94, 0.91481, 3, 22, 21.05, 14.74, 0.00607, 23, 14.12, 14.29, 0.15352, 24, 5.17, 15.37, 0.84042, 3, 22, 15.5, 12.76, 0.07497, 23, 8.52, 12.5, 0.29944, 24, -0.06, 12.67, 0.62559, 3, 22, 9.53, 10.43, 0.28634, 23, 2.48, 10.35, 0.37709, 24, -5.65, 9.54, 0.33657, 3, 22, 4, 8.61, 0.59216, 23, -3.11, 8.72, 0.31131, 24, -10.89, 6.99, 0.09653, 3, 22, 0.01, 5.35, 0.8564, 23, -7.21, 5.59, 0.13508, 24, -14.4, 3.22, 0.00851, 2, 22, -3.39, 2.43, 0.97836, 23, -10.69, 2.77, 0.02164, 2, 22, -4.84, -0.34, 0.99982, 23, -12.24, 0.05, 0.00018, 2, 22, -6.57, -1.68, 0.99997, 23, -14.01, -1.23, 3e-05, 1, 22, -0.61, 0.06, 1, 1, 22, 3.49, -0.4, 1, 3, 22, 7.23, -0.28, 0.69318, 23, -0.17, -0.28, 0.3067, 24, -6.48, -1.38, 0.00012, 2, 23, 5.92, -0.69, 0.82885, 24, -0.41, -0.76, 0.17115, 2, 23, 10.49, -1.17, 0.0039, 24, 4.18, -0.48, 0.9961], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 27, "height": 32}}, "yinying": {"yinying": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [40.04, -20.93, -41.96, -20.93, -41.96, 18.07, 40.04, 18.07], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 82, "height": 39}}}}], "animations": {"attack": {"bones": {"arm_L": {"rotate": [{"angle": -1.08, "curve": "stepped"}, {"time": 0.0667, "angle": -1.08, "curve": 0.25, "c3": 0.118}, {"time": 0.3667, "angle": 166.44, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.4, "angle": 91.9, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.5, "angle": -42.05, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "angle": -1.08}], "translate": [{"time": 0.3667, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.4, "x": -9.6, "y": 1.5, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.5, "x": -10.16, "y": -10.79, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "yinying": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 8, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 1.109, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "head": {"rotate": [{"curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 6.32, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.4667}], "translate": [{"x": -0.55, "y": -0.09}]}, "body2": {"rotate": [{"angle": -0.49, "curve": 0.434, "c3": 0.735, "c4": 0.38}, {"time": 0.0667, "angle": -20.03, "curve": 0.138, "c2": 0.29, "c3": 0.202}, {"time": 0.3333, "angle": 13.93, "curve": 0, "c2": 0.76, "c3": 0.723}, {"time": 0.5, "angle": -8.54, "curve": 0.545, "c3": 0.818, "c4": 0.5}, {"time": 0.6667, "angle": -6.07, "curve": 0.4, "c2": 0.5, "c3": 0.6}, {"time": 0.8667, "angle": 2.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.49}], "translate": [{"x": -0.37, "y": -0.02, "curve": 0.434, "c3": 0.735, "c4": 0.38}, {"time": 0.0667, "x": -3.95, "y": -3.41, "curve": 0.138, "c2": 0.29, "c3": 0.202}, {"time": 0.3333, "x": 8.01, "y": 7.61, "curve": 0, "c2": 0.76, "c3": 0.723}, {"time": 0.5, "x": -5.79, "y": -1.4, "curve": 0.545, "c3": 0.818, "c4": 0.5}, {"time": 0.6667, "x": -4.62, "y": -1.1, "curve": 0.4, "c2": 0.5, "c3": 0.6}, {"time": 0.8667, "x": -0.37, "y": -0.02}]}, "bone2": {"rotate": [{"curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.28, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}], "translate": [{"curve": 0.25, "c3": 0.118}, {"time": 0.3333, "x": -1.52, "y": 1.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 5.81, "y": -4.4, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "arm_L2": {"rotate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 25.26, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "head5": {"rotate": [{"angle": 1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 25.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -24.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 15.07, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.39}], "scale": [{"x": 0.924, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.976, "y": 0.976, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.794, "y": 0.794, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.924, "y": 0.924}]}, "arm_L4": {"rotate": [{"angle": -2.86, "curve": "stepped"}, {"time": 0.0667, "angle": -2.86, "curve": 0.25, "c3": 0.118}, {"time": 0.4, "angle": 13.15, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.5333, "angle": -40.34, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "angle": 10.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.86}]}, "head4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 24.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -26.27, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 13.68, "curve": 0.25, "c3": 0.75}, {"time": 1}], "scale": [{"x": 0.953, "y": 0.953, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": 1.006, "y": 1.006, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.818, "y": 0.818, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.953, "y": 0.953}]}, "head3": {"rotate": [{"angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 23.03, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -27.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 12.3, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.37}], "scale": [{"x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.035, "y": 1.035, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.842, "y": 0.842, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.981, "y": 0.981}]}, "torso_L3": {"rotate": [{"angle": -0.34, "curve": "stepped"}, {"time": 0.3333, "angle": -0.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -22.9, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 7.22, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.34}]}, "leg_L3": {"rotate": [{"angle": -0.05}]}, "leg_L": {"rotate": [{"angle": 0.02}]}, "bone5": {"rotate": [{"angle": -3.9}]}, "arm_L3": {"rotate": [{"angle": -1.51, "curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 49.97, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4667, "angle": -33.49, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "angle": -1.51}]}, "arm_R2": {"rotate": [{"curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 30.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -17.4, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "torso_L2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 76.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -22.56, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 7.55, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -6.8, "y": -0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4667}]}, "foliage_8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 31.68, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -59.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 27.82, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "foliage_5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 25.41, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -76.46, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 17.35, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "arm_R3": {"rotate": [{"angle": -2.64, "curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": -60.41, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.4667, "angle": 6.34, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "angle": -2.64}]}, "arm_R": {"rotate": [{"angle": 0.76, "curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 39.17, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4667, "angle": -10.89, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "angle": 0.76}]}, "torso_R3": {"rotate": [{"angle": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -16.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 6.64, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.27}]}, "arm_R5": {"rotate": [{"angle": 1.8, "curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 32.83, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.5, "angle": -2.64, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "angle": 6.26, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.8}], "translate": [{"curve": 0.25, "c3": 0.118}, {"time": 0.3333, "x": -4, "y": -13.44, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.5}]}, "foliage_4": {"rotate": [{"angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 31.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -70.39, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 23.42, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 6.07}]}, "torso_R2": {"rotate": [{"angle": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -17.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 5.8, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.43}]}, "foliage_3": {"rotate": [{"angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 32.45, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -69.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 24.4, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.04}]}, "torso_R": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 29.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -17.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.37, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -1.1, "y": -2.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "torso_L": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -37.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 35.92, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -17.6, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "foliage_2": {"rotate": [{"angle": -13.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -51.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 22.26, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -31.25, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -13.65}]}, "torso_L4": {"rotate": [{"angle": -1, "curve": "stepped"}, {"time": 0.4, "angle": -1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -23.56, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 6.56, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1}]}, "foliage": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -37.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 35.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -17.6, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "arm_R4": {"rotate": [{"angle": 3.03, "curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 17.66, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.5, "angle": -35.7, "curve": 0.365, "c3": 0.697, "c4": 0.35}, {"time": 0.8333, "angle": 3.03}]}, "arm_L6": {"rotate": [{"curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 76.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -31.99, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "leg_L4": {"translate": [{"time": 0.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4, "x": 4.27, "y": 4.36, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.4667, "x": 11.62, "curve": "stepped"}, {"time": 0.6667, "x": 11.62, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "foliage_7": {"rotate": [{"angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 33.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -57.86, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 29.54, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.72}]}, "bone4": {"rotate": [{"angle": 1.83}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 3.28, "y": -0.83, "curve": 0.75, "c3": 0.75}, {"time": 0.8333}]}, "foliage_6": {"rotate": [{"angle": -7.19}]}, "body": {"rotate": [{"angle": -0.07, "curve": 0.25, "c3": 0.118}, {"time": 0.3333, "angle": 14.61, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4667, "angle": -10.74, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.07}], "translate": [{"y": -0.05, "curve": "stepped"}, {"time": 0.3333, "y": -0.05, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4667, "x": 1.02, "y": -0.08, "curve": 0.75, "c3": 0.75}, {"time": 0.8333, "y": -0.05}]}}, "drawOrder": [{"time": 0.4333, "offsets": [{"slot": "arm_L5", "offset": 21}, {"slot": "arm_L4", "offset": 21}, {"slot": "arm_L3", "offset": 21}, {"slot": "arm_L2", "offset": 21}, {"slot": "arm_L", "offset": 21}, {"slot": "torso_L2", "offset": 11}]}, {"time": 0.7333}]}, "death": {"slots": {"close_eye_L": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}]}, "close_eye_R": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}]}}, "bones": {"head": {"rotate": [{"curve": 0.25, "c3": 0.25}, {"time": 0.4, "angle": 11.45, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.6667, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 9.57}], "translate": [{"x": -0.55, "y": -0.09, "curve": "stepped"}, {"time": 0.4, "x": -0.55, "y": -0.09, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.6667, "x": -3.33, "y": 1.04}]}, "arm_L3": {"rotate": [{"angle": -1.51}]}, "arm_L": {"rotate": [{"angle": -1.08, "curve": 0.25, "c3": 0.25}, {"time": 0.2, "angle": 6.73, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "angle": 3.59, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 8.84}], "translate": [{"time": 0.2, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "x": 8.41, "y": 8.03}]}, "foliage_6": {"rotate": [{"angle": -7.19}]}, "arm_L4": {"rotate": [{"angle": -2.86, "curve": "stepped"}, {"time": 0.2333, "angle": -2.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.11, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -2.86}]}, "torso_L3": {"rotate": [{"angle": -0.34}]}, "body2": {"rotate": [{"angle": -0.49, "curve": 0, "c2": 0.73, "c3": 0.464, "c4": 0.98}, {"time": 0.2333, "angle": 17.37, "curve": 0.234, "c2": 0.58, "c3": 0.55}, {"time": 0.3333, "angle": 11.21, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.6, "angle": -7.61, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -9.81}], "translate": [{"x": -0.37, "y": -0.02, "curve": "stepped"}, {"time": 0.3333, "x": -0.37, "y": -0.02, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.6, "x": -8.47, "y": -2.76}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.25}, {"time": 0.2, "x": -2.68, "y": 0.36, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "y": -7.68}]}, "yinying": {"translate": [{"time": 0.2667, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "x": 7.9, "y": 1.58}], "scale": [{"time": 0.2667, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "x": 1.146, "y": 1.146}]}, "torso_L": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 33.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -25.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -30.71}]}, "head5": {"rotate": [{"angle": 1.39, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -8.51, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 1.39}], "scale": [{"x": 0.924, "y": 0.924, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.989, "y": 0.989, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.879, "y": 0.879, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.924, "y": 0.924}]}, "head4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 14.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -20.12, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "scale": [{"x": 0.953, "y": 0.953, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.019, "y": 1.019, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.906, "y": 0.906, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.953, "y": 0.953}]}, "head3": {"rotate": [{"angle": -1.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 12.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.37}], "scale": [{"x": 0.981, "y": 0.981, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.049, "y": 1.049, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.933, "y": 0.933, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.981, "y": 0.981}]}, "leg_R2": {"translate": [{"time": 0.2, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "x": -2.67, "y": 16.77}]}, "torso_L4": {"rotate": [{"angle": -1}]}, "bone4": {"rotate": [{"angle": 1.83}], "translate": [{"time": 0.2, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "x": 8.11, "y": 4.77}]}, "foliage_2": {"rotate": [{"angle": -13.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 20.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -38.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -44.36}]}, "leg_R": {"rotate": [{"time": 0.2, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "angle": -81.44}]}, "arm_R5": {"rotate": [{"angle": 1.8, "curve": 0.25, "c3": 0.25}, {"time": 0.2, "angle": -14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 40.33, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 38.19}], "translate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -0.72, "y": 2.43}]}, "foliage_8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -47.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 21.63, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 11.77}]}, "arm_R3": {"rotate": [{"angle": -2.64}]}, "leg_L": {"rotate": [{"angle": 0.02}], "translate": [{"time": 0.2, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.4667, "x": -8.61, "y": -2.06}]}, "torso_R2": {"rotate": [{"angle": 0.43, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -21.93, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 18.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 14.28}]}, "arm_R": {"rotate": [{"angle": 0.76}]}, "torso_R3": {"rotate": [{"angle": 1.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -21.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 19.45, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 15.12}]}, "arm_R4": {"rotate": [{"angle": 3.03, "curve": "stepped"}, {"time": 0.2333, "angle": 3.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -30.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -35.25}]}, "torso_R": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -22.36, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 18.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 13.86}]}, "foliage_5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -49.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 38.72, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 24.72}]}, "foliage_4": {"rotate": [{"angle": 6.07, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -43.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 44.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 30.79}]}, "foliage_3": {"rotate": [{"angle": 7.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -42.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 45.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 31.76}]}, "foliage_7": {"rotate": [{"angle": 1.72, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -45.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 23.35, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 13.5}]}, "foliage": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 33.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -25.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -30.71}]}, "bone5": {"rotate": [{"angle": -3.9}]}, "leg_L3": {"rotate": [{"angle": -0.05}]}, "body": {"rotate": [{"angle": -0.07, "curve": 0, "c2": 0.73, "c3": 0.464, "c4": 0.98}, {"time": 0.2667, "angle": 14.2, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.5333, "angle": -9.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.5}], "translate": [{"y": -0.05, "curve": 0, "c2": 0.73, "c3": 0.464, "c4": 0.98}, {"time": 0.2667, "x": -3.72, "y": 3.21, "curve": 0.132, "c2": 0.87, "c3": 0.75}, {"time": 0.5333, "y": -0.05}]}}, "deform": {"default": {"body": {"body": [{"curve": 0, "c2": 0.73, "c3": 0.464, "c4": 0.98}, {"time": 0.2667, "offset": 46, "vertices": [-1.15499, 4.03111, -0.54733, 4.15742, 0, 0, 0, 0, -0.28125, -0.07355, -0.28902, -0.03124, 0.03883, -3.59541, -1.74292, -5.74395, -2.57272, -4.63892, -3.79109, -4.11624]}, {"time": 0.5333}]}}}}, "idle": {"bones": {"head5": {"rotate": [{"angle": 1.39, "curve": 0.327, "c2": 0.31, "c3": 0.689, "c4": 0.74}, {"time": 0.3667, "angle": -3.58, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.5667, "angle": -5.13, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 3.8, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "angle": 1.39}], "scale": [{"x": 0.924, "y": 0.924, "curve": 0.327, "c2": 0.31, "c3": 0.689, "c4": 0.74}, {"time": 0.3667, "x": 1.025, "y": 1.025, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.5667, "x": 1.057, "y": 1.057, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 0.876, "y": 0.876, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "x": 0.924, "y": 0.924}]}, "head4": {"rotate": [{"curve": 0.351, "c2": 0.39, "c3": 0.715, "c4": 0.83}, {"time": 0.3667, "angle": -4.61, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4667, "angle": -5.13, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": 3.8, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 1.6667}], "scale": [{"x": 0.953, "y": 0.953, "curve": 0.351, "c2": 0.39, "c3": 0.715, "c4": 0.83}, {"time": 0.3667, "x": 1.046, "y": 1.046, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4667, "x": 1.057, "y": 1.057, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "x": 0.876, "y": 0.876, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 1.6667, "x": 0.953, "y": 0.953}]}, "head3": {"rotate": [{"angle": -1.37, "curve": 0.38, "c2": 0.53, "c3": 0.746}, {"time": 0.3667, "angle": -5.13, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 3.8, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 1.6667, "angle": -1.37}], "scale": [{"x": 0.981, "y": 0.981, "curve": 0.38, "c2": 0.53, "c3": 0.746}, {"time": 0.3667, "x": 1.057, "y": 1.057, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "x": 0.876, "y": 0.876, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 1.6667, "x": 0.981, "y": 0.981}]}, "foliage_9": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 15.84, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "leg_L3": {"rotate": [{"angle": -0.05}]}, "leg_L": {"rotate": [{"angle": 0.02}]}, "bone5": {"rotate": [{"angle": -3.9}]}, "bone4": {"rotate": [{"angle": 1.83}]}, "foliage_8": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 18.91, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "foliage_7": {"rotate": [{"angle": 1.72, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 18.91, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 1.72}]}, "arm_R2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 12.98, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "arm_R3": {"rotate": [{"angle": -2.64, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -15.2, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -2.64}]}, "arm_R": {"rotate": [{"angle": 0.76, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 12.98, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 0.76}]}, "arm_R4": {"rotate": [{"angle": 3.03, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.1, "angle": 1.8, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 8.31, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.03}]}, "arm_R5": {"rotate": [{"angle": 1.8, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 8.31, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 1.8}]}, "torso_R3": {"rotate": [{"angle": 1.27, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 7.31, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 1.27}]}, "torso_R2": {"rotate": [{"angle": 0.43, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 7.31, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 0.43}]}, "torso_R": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.31, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "foliage_5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 19.15, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "foliage_3": {"rotate": [{"angle": 7.04, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "angle": 19.15, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": 7.04}]}, "foliage_4": {"rotate": [{"angle": 6.07, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 19.15, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": 6.07}]}, "torso_L": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "foliage_2": {"rotate": [{"angle": -13.65, "curve": 0.34, "c2": 0.36, "c3": 0.757}, {"time": 0.6, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -17.44, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 1.6667, "angle": -13.65}]}, "foliage": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -17.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "torso_L4": {"rotate": [{"angle": -1, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -5.73, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -1}]}, "torso_L3": {"rotate": [{"angle": -0.34, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -5.73, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -0.34}]}, "torso_L2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "arm_L6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -25.83, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "arm_L3": {"rotate": [{"angle": -1.51, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -25.63, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -1.51}]}, "arm_L2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 21.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "foliage_6": {"rotate": [{"angle": -7.19, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -19.54, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -7.19}]}, "arm_L4": {"rotate": [{"angle": -2.86, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.1, "angle": -1.7, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -7.83, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -2.86}]}, "arm_L": {"rotate": [{"angle": -1.08, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -4.97, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -1.08}]}, "head": {"translate": [{"x": -0.55, "y": -0.09, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -1.73, "y": -0.29, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "x": -0.55, "y": -0.09}]}, "body2": {"rotate": [{"angle": -0.49, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -2.81, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -0.49}], "translate": [{"x": -0.37, "y": -0.02, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": -2.12, "y": -0.1, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "x": -0.37, "y": -0.02}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "body": {"rotate": [{"angle": -0.07, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -1.17, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -0.07}], "translate": [{"y": -0.05, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.02, "y": -0.85, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "y": -0.05}]}}}, "run": {"bones": {"head5": {"rotate": [{"angle": 0.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -8.53, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 0.2}], "scale": [{"x": 0.979, "y": 0.979, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "x": 1.029, "y": 1.029, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 0.892, "y": 0.892, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 0.979, "y": 0.979}]}, "head4": {"rotate": [{"angle": 3.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -8.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 3.49}], "scale": [{"x": 1.011, "y": 1.011, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "x": 1.029, "y": 1.029, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.892, "y": 0.892, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "x": 1.011, "y": 1.011}]}, "head3": {"rotate": [{"angle": 5.28, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.28}], "scale": [{"x": 1.029, "y": 1.029, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.892, "y": 0.892, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.029, "y": 1.029}]}, "leg_L4": {"translate": [{"x": -20.35, "y": 20.34}, {"time": 0.1, "x": 2.48, "y": 13.56}, {"time": 0.1667, "x": 13.67, "y": 9.55}, {"time": 0.2333, "x": -1.87, "y": 0.69}, {"time": 0.3333, "x": -12.34, "y": -0.51}, {"time": 0.4, "x": -24.47, "y": 6.06}, {"time": 0.5, "x": -40.85, "y": 33.1}, {"time": 0.6, "x": -23.46, "y": 24.86}, {"time": 0.6667, "x": -20.35, "y": 20.34}]}, "leg_R2": {"translate": [{}, {"time": 0.0667, "x": -4.45, "y": 3.96}, {"time": 0.1667, "x": -13.75, "y": 34.89}, {"time": 0.3333, "x": 15.83, "y": 8.29}, {"time": 0.5, "x": 44.9, "y": 11.97}, {"time": 0.5667, "x": 26.56, "y": 2.27}, {"time": 0.6667}]}, "leg_L2": {"rotate": [{"angle": -84.74}, {"time": 0.1, "angle": 3.17}, {"time": 0.1667, "angle": 32.04}, {"time": 0.2333, "angle": -1.09}, {"time": 0.3333}, {"time": 0.4, "angle": -11.49}, {"time": 0.5, "angle": -72.34}, {"time": 0.6667, "angle": -84.74}]}, "leg_L3": {"rotate": [{"angle": -0.05}]}, "leg_L": {"rotate": [{"angle": 0.02}], "translate": [{"x": 6.8, "y": 2.86}, {"time": 0.0667, "x": -10.29, "y": 6.04}, {"time": 0.1, "x": -8.82, "y": 4.24}, {"time": 0.1667, "x": -4.83, "y": -2.26}, {"time": 0.2333, "x": 2.39, "y": -1.49}, {"time": 0.3333, "x": -1.86, "y": 0.05}, {"time": 0.5, "x": -8.76, "y": 4.53}, {"time": 0.6333, "x": 7.27, "y": 3.1}, {"time": 0.6667, "x": 6.8, "y": 2.86}]}, "leg_R": {"rotate": [{}, {"time": 0.0667, "angle": -14.32}, {"time": 0.1667, "angle": -95.04}, {"time": 0.3333, "angle": -19.36}, {"time": 0.5, "angle": 30.95}, {"time": 0.5667, "angle": 8.34}, {"time": 0.6667}]}, "bone5": {"rotate": [{"angle": -3.9}]}, "foliage_8": {"rotate": [{"angle": -9.07, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -13.33, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 19.35, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -9.07}]}, "foliage_7": {"rotate": [{"angle": -5.41, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 19.35, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": -9.07, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": -13.33, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -5.41}]}, "arm_R4": {"rotate": [{"angle": 3.48, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": 14.36, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": 1.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": 3.48}]}, "arm_R5": {"rotate": [{"angle": 7.18, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 14.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 7.18}]}, "torso_R3": {"rotate": [{"angle": 0.45, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": -4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 8.37, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 0.45}]}, "torso_R2": {"rotate": [{"angle": -2.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 8.37, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -2.53}]}, "torso_R": {"rotate": [{"angle": -4.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -4.16}]}, "foliage_5": {"rotate": [{"angle": 13.68, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": -7.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 25.8, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": 13.68}]}, "foliage_3": {"rotate": [{"angle": -7.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 25.8, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -7.14}]}, "foliage_4": {"rotate": [{"angle": 17.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 25.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -7.14, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 17.82}]}, "torso_L": {"rotate": [{"angle": 5.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.76}]}, "foliage_2": {"rotate": [{"angle": -1.25, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -23.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.76, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -1.25}]}, "foliage": {"rotate": [{"angle": 5.76, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -23.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.76}]}, "torso_L4": {"rotate": [{"angle": 1.61, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 7.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.91, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 1.61}]}, "torso_L3": {"rotate": [{"angle": 5.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": 7.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -7.91, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": 5.2}]}, "torso_L2": {"rotate": [{"angle": 7.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 7.16}]}, "foliage_6": {"rotate": [{"angle": 23.84, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -22.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 23.84}]}, "arm_L4": {"rotate": [{"angle": -3.11, "curve": 0.345, "c2": 0.37, "c3": 0.757}, {"time": 0.2333, "angle": -12.83, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.5, "angle": -1.67, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "curve": 0.269, "c3": 0.618, "c4": 0.42}, {"time": 0.6667, "angle": -3.11}]}, "arm_L": {"rotate": [{"angle": -6.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -12.83, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -6.42}]}, "head": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 2.06, "y": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 2.06, "y": 0.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "body2": {"rotate": [{"angle": -0.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -0.97}], "translate": [{"x": 2.02, "y": 0.08, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 5.49, "y": 0.22, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 5.49, "y": 0.22, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 2.02, "y": 0.08}]}, "body": {"rotate": [{"angle": -0.97, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -1.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -0.97}], "translate": [{"x": 0.04, "y": 1.55, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 0.11, "y": 4.21, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.11, "y": 4.21, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": 0.04, "y": 1.55}]}, "bone2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 4.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 4.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.82, "y": 0.82, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.82, "y": 0.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "bone4": {"rotate": [{"angle": 1.83}], "translate": [{}, {"time": 0.1667, "x": 3.01, "y": 3.92}, {"time": 0.3333, "x": -2.08, "y": 4.45}, {"time": 0.4333, "x": -1.34, "y": 7.1}, {"time": 0.5, "x": 2.77, "y": 2.46}, {"time": 0.5667, "x": 9.42, "y": 1.28}, {"time": 0.6667}]}}, "deform": {"default": {"body": {"body": [{"time": 0.1667}, {"time": 0.3333, "offset": 40, "vertices": [-0.01115, 1.75399, -0.21043, 1.74135, -1.64, 2.98962, -1.95281, 4.62072, -2.46531, 4.36883, -1.52386, 2.79076, -1.83113, 2.59948, 0, 0, 0, 0, 0, 0, 0.06032, -0.92504, -1.19769, -3.79396, 0.2033, -1.53502]}, {"time": 0.5, "offset": 44, "vertices": [0.61612, -0.16862, 1.87041, -0.90927, 1.95425, -0.71137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.73191, -2.28646, -0.3129, -5.37808]}, {"time": 0.6, "offset": 40, "vertices": [0.70153, 1.36174, 0.57314, 1.42056, -1.17695, 2.62828, -0.43589, 3.50351, -0.75345, 3.45719, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39977, -3.37725, -1.3975, -6.27034, -1.68806, -4.57259, -0.03785, -2.46897]}, {"time": 0.6667}]}}}}}}