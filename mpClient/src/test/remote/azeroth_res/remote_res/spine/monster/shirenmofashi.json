{"skeleton": {"hash": "1a1+iLLUdv/U2M4K6j0iP99C3B0", "spine": "3.8.99", "x": -63.6, "y": -17.71, "width": 136.76, "height": 130.81, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/boss拉克佐"}, "bones": [{"name": "root", "scaleX": 0.3, "scaleY": 0.3}, {"name": "ALL", "parent": "root"}, {"name": "ALL2", "parent": "ALL", "x": 17.98, "y": 75.41, "color": "1202fbff"}, {"name": "ALL3", "parent": "ALL2", "length": 69.88, "rotation": 87.82, "x": -1.33, "y": 5.99, "color": "1202fbff"}, {"name": "ALL4", "parent": "ALL3", "length": 141.13, "rotation": 21.73, "x": 69.88, "color": "1202fbff"}, {"name": "ALL5", "parent": "ALL4", "length": 104.63, "rotation": -29.43, "x": 91.3, "y": 38.52}, {"name": "ALL6", "parent": "ALL5", "length": 39.48, "rotation": 42.5, "x": 55.76, "y": 59.66}, {"name": "ALL7", "parent": "ALL4", "length": 84.88, "rotation": 130.88, "x": 143.51, "y": 68.31, "color": "64fc05ff"}, {"name": "ALL8", "parent": "ALL7", "length": 74.78, "rotation": 24.48, "x": 84.88, "color": "64fc05ff"}, {"name": "ALL9", "parent": "ALL8", "length": 72.1, "rotation": 19.52, "x": 74.78, "color": "64fc05ff"}, {"name": "ALL10", "parent": "ALL4", "length": 90.52, "rotation": -178.45, "x": 98.97, "y": -81, "color": "f007fbff"}, {"name": "ALL11", "parent": "ALL10", "length": 78.56, "rotation": -5.89, "x": 90.52, "color": "f007fbff"}, {"name": "ALL12", "parent": "ALL11", "length": 53.97, "rotation": -11.68, "x": 78.56, "color": "f007fbff"}, {"name": "ALL13", "parent": "ALL12", "length": 110.75, "rotation": 164.16, "x": 29.27, "y": 27.69}, {"name": "ALL14", "parent": "ALL2", "length": 59.36, "rotation": -99.67, "x": -66.5, "y": 28.6, "color": "34f804ff"}, {"name": "ALL15", "parent": "ALL14", "length": 38.73, "rotation": -6.27, "x": 59.25, "y": 0.66, "color": "34f804ff"}, {"name": "target_1", "parent": "root", "x": -68.77, "y": 8.69, "color": "ff3f00ff"}, {"name": "ALL16", "parent": "target_1", "length": 29.27, "rotation": -4.83, "color": "34f804ff"}, {"name": "ALL17", "parent": "ALL2", "length": 52.26, "rotation": -75.26, "x": 31.26, "y": 19.95, "color": "da07f8ff"}, {"name": "ALL18", "parent": "ALL17", "length": 36.65, "rotation": -26.26, "x": 52.26, "color": "da07f8ff"}, {"name": "target_2", "parent": "root", "x": 55.19, "y": 9.22, "color": "ff3f00ff"}, {"name": "ALL19", "parent": "target_2", "length": 32.61, "rotation": -3.38, "color": "da07f8ff"}, {"name": "ALL20", "parent": "ALL2", "length": 25.28, "rotation": -91.51, "y": -9.31, "color": "fbec05ff"}, {"name": "ALL21", "parent": "ALL20", "length": 19.96, "rotation": 3.42, "x": 25.28, "color": "fbec05ff"}, {"name": "yinying", "parent": "root", "x": -4.27, "y": 12.62, "scaleX": 0.6751, "scaleY": 0.6751}, {"name": "ALL22", "parent": "ALL4", "length": 104.63, "rotation": -29.43, "x": 62.41, "y": -42.84}, {"name": "ALL23", "parent": "ALL22", "length": 39.48, "rotation": 42.5, "x": 55.76, "y": 59.66}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "arm_L2", "bone": "ALL10", "attachment": "arm_L2"}, {"name": "arm_L", "bone": "ALL12", "attachment": "arm_L"}, {"name": "weapons", "bone": "ALL13", "attachment": "weapons"}, {"name": "hand_L", "bone": "ALL12", "attachment": "hand_L"}, {"name": "leg_L3", "bone": "ALL18", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "ALL19", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "ALL17", "attachment": "leg_L"}, {"name": "leg_R3", "bone": "ALL15", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "ALL16", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "ALL14", "attachment": "leg_R"}, {"name": "body_2", "bone": "ALL20", "attachment": "body_2"}, {"name": "body", "bone": "ALL3", "attachment": "body"}, {"name": "arm_R", "bone": "ALL7", "attachment": "arm_R"}, {"name": "hand_R", "bone": "ALL9", "attachment": "hand_R"}, {"name": "head2", "bone": "ALL22", "attachment": "head_2"}, {"name": "head", "bone": "ALL5", "attachment": "head"}, {"name": "eye_R", "bone": "ALL5"}, {"name": "eye_L", "bone": "ALL5"}, {"name": "eye_L2", "bone": "ALL22"}], "ik": [{"name": "target_1", "bones": ["ALL14", "ALL15"], "target": "target_1", "bendPositive": false}, {"name": "target_2", "order": 1, "bones": ["ALL17", "ALL18"], "target": "target_2", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"type": "mesh", "uvs": [0.91999, 0, 1, 0.22314, 1, 0.50475, 0.95911, 0.88262, 0.66807, 1, 0.32213, 0.99999, 0.00413, 0.87832, 0, 0.72852, 0, 0.3428, 0, 0, 0.43618, 0, 0.64484, 0.61389], "triangles": [3, 4, 11, 4, 5, 11, 11, 5, 7, 3, 11, 2, 5, 6, 7, 7, 8, 11, 2, 11, 1, 1, 11, 10, 8, 10, 11, 8, 9, 10, 0, 1, 10], "vertices": [1, 11, 25.91, 74.34, 1, 2, 11, 52.77, 76.15, 0.42294, 12, -40.68, 69.35, 0.57706, 2, 11, 83.75, 67.73, 0.04968, 12, -8.64, 67.38, 0.95032, 1, 12, 34.08, 60.23, 1, 1, 12, 45.47, 27.45, 1, 1, 12, 43.12, -10.53, 1, 1, 12, 27.12, -44.58, 1, 2, 11, 79.5, -45.11, 0.26076, 12, 10.05, -43.99, 0.73924, 2, 11, 37.07, -33.57, 0.70264, 12, -33.84, -41.28, 0.29736, 1, 11, -0.64, -23.32, 1, 1, 11, 11.95, 22.98, 1, 1, 12, 1.38, 27.62, 1], "hull": 11, "edges": [0, 20, 10, 12, 16, 18, 18, 20, 12, 14, 14, 16, 4, 6, 0, 2, 2, 4, 14, 22, 22, 4, 6, 8, 8, 10], "width": 97, "height": 107}}, "arm_L2": {"arm_L2": {"type": "mesh", "uvs": [0.42566, 1e-05, 0.60849, 0.08333, 0.74412, 0.23595, 0.78566, 0.39913, 0.92418, 0.56197, 0.99999, 0.75592, 0.95287, 0.91122, 0.6373, 1, 0.41006, 0.99169, 0.21467, 0.88367, 0.09708, 0.72847, 0.07748, 0.52912, 1e-05, 0.36478, 0, 0.18751, 0.16018, 0.02126], "triangles": [6, 7, 5, 5, 7, 8, 4, 5, 8, 8, 9, 4, 4, 10, 3, 10, 4, 9, 10, 11, 3, 2, 3, 11, 2, 11, 1, 1, 13, 0, 13, 14, 0, 1, 11, 12, 1, 12, 13], "vertices": [1, 10, 3.8, 19.33, 1, 1, 10, 25.35, 34.92, 1, 2, 10, 56.09, 40.8, 0.99971, 11, -38.44, 37.05, 0.00029, 2, 10, 84.4, 35.3, 0.77282, 11, -9.71, 34.49, 0.22718, 2, 10, 116.92, 40.87, 0.11586, 11, 22.07, 43.36, 0.88414, 2, 10, 151.73, 37.35, 0.00037, 11, 57.06, 43.43, 0.99963, 1, 11, 81.62, 30.79, 1, 1, 11, 86.43, -10.41, 1, 1, 11, 77.76, -36.78, 1, 2, 10, 137.98, -60.04, 0.01653, 11, 53.37, -54.85, 0.98347, 2, 10, 107.62, -63.7, 0.13811, 11, 23.54, -61.61, 0.86189, 2, 10, 74.4, -53.44, 0.55702, 11, -10.56, -54.82, 0.44298, 2, 10, 44.32, -51.97, 0.88841, 11, -40.63, -56.43, 0.11159, 2, 10, 15.54, -40.87, 0.98889, 11, -70.39, -48.34, 0.01111, 1, 10, -4.41, -12.22, 1], "hull": 15, "edges": [0, 28, 8, 10, 10, 12, 12, 14, 20, 22, 22, 24, 24, 26, 26, 28, 4, 6, 6, 8, 14, 16, 0, 2, 2, 4, 16, 18, 18, 20], "width": 127, "height": 198}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [0.71624, 0, 0.912, 0.22461, 1, 0.46103, 0.98987, 0.57631, 0.85319, 0.66798, 0.8451, 0.792, 0.71079, 0.94415, 0.56774, 1, 0.12037, 0.9713, 0, 0.78132, 1e-05, 0.62073, 0.04053, 0.51559, 0.13583, 0.41176, 0.14386, 0.22104, 0.31468, 0.08859, 0.58806, 1e-05], "triangles": [7, 4, 6, 7, 8, 9, 7, 9, 10, 4, 7, 10, 4, 10, 11, 5, 6, 4, 12, 4, 11, 1, 4, 12, 14, 1, 12, 14, 12, 13, 1, 14, 15, 3, 4, 2, 1, 15, 0, 2, 4, 1], "vertices": [1, 7, -15.17, -20.67, 1, 2, 7, 7.45, 25.94, 0.99803, 8, -59.72, 55.68, 0.00197, 1, 7, 40, 59.59, 1, 1, 7, 59.8, 69.08, 1, 2, 7, 85.07, 59.84, 0.4187, 8, 24.97, 54.39, 0.5813, 2, 7, 106.16, 70.42, 0.14809, 8, 48.55, 55.28, 0.85191, 2, 7, 141.25, 67.17, 0.0159, 8, 79.13, 37.78, 0.9841, 2, 7, 161.08, 53.75, 0.00021, 8, 91.63, 17.35, 0.99979, 1, 8, 92.15, -49.97, 1, 2, 7, 166.97, -40.83, 1e-05, 8, 57.8, -71.17, 0.99999, 2, 7, 140.43, -55.88, 0.0258, 8, 27.41, -73.88, 0.9742, 2, 7, 120.06, -60.46, 0.1078, 8, 6.97, -69.6, 0.8922, 2, 7, 95.84, -57.77, 0.39682, 8, -13.95, -57.12, 0.60318, 2, 7, 63.73, -74.6, 0.81907, 8, -50.15, -59.14, 0.18093, 1, 7, 29.2, -64.74, 1, 1, 7, -5.68, -37.39, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 16, 18, 18, 20, 20, 22, 26, 28, 28, 30, 14, 16, 30, 0, 22, 24, 24, 26, 6, 8, 8, 10, 10, 12, 12, 14], "width": 150, "height": 186}}, "body": {"body": {"type": "mesh", "uvs": [0.78557, 0.08798, 0.86536, 0.21533, 0.9123, 0.36516, 0.95608, 0.42889, 0.97573, 0.51268, 0.95717, 0.59854, 0.99103, 0.67202, 1, 0.74974, 0.98674, 0.82356, 0.93468, 0.89472, 0.94005, 0.95558, 0.7867, 0.9974, 0.52614, 0.99107, 0.31543, 0.95984, 0.11236, 0.85469, 0.12941, 0.81055, 0.04221, 0.71471, 1e-05, 0.52901, 0, 0.39042, 0.02087, 0.23809, 0.05519, 0.06261, 0.2793, 0, 0.621, 0.01634, 0.8692, 0.63245, 0.79733, 0.63431, 0.72546, 0.61006, 0.63805, 0.64551, 0.53899, 0.66043, 0.43993, 0.63431, 0.37, 0.58208, 0.32921, 0.50559], "triangles": [28, 13, 15, 14, 15, 13, 12, 13, 27, 11, 26, 24, 11, 24, 9, 11, 9, 10, 11, 12, 26, 30, 21, 22, 19, 20, 21, 30, 19, 21, 18, 19, 30, 17, 18, 30, 30, 28, 29, 22, 1, 30, 0, 1, 22, 1, 25, 30, 25, 3, 24, 1, 2, 25, 25, 2, 3, 3, 4, 23, 5, 23, 4, 23, 24, 3, 28, 30, 27, 25, 26, 30, 26, 27, 30, 16, 17, 30, 16, 30, 29, 28, 15, 29, 13, 28, 27, 12, 27, 26, 15, 16, 29, 23, 6, 7, 6, 23, 5, 8, 23, 7, 8, 24, 23, 26, 25, 24, 9, 24, 8], "vertices": [1, 4, 120.85, -79.6, 1, 3, 3, 180.15, -49.81, 0.04491, 4, 84, -87.1, 0.95222, 2, 55.31, 184.11, 0.00286, 3, 3, 142.71, -62.66, 0.10261, 4, 44.46, -85.16, 0.88847, 2, 66.71, 146.2, 0.00891, 3, 3, 127, -73.9, 0.19533, 4, 25.71, -89.8, 0.78178, 2, 77.35, 130.08, 0.02289, 3, 3, 106, -79.48, 0.32028, 4, 4.14, -87.2, 0.6316, 2, 82.13, 108.88, 0.04812, 3, 3, 84.12, -75.8, 0.4577, 4, -14.83, -75.69, 0.45754, 2, 77.62, 87.16, 0.08476, 3, 3, 65.86, -84.73, 0.54359, 4, -35.1, -77.22, 0.3019, 2, 85.84, 68.57, 0.15451, 3, 3, 46.29, -87.65, 0.587, 4, -54.36, -72.7, 0.17747, 2, 88.02, 48.9, 0.23553, 3, 3, 27.51, -85.15, 0.56601, 4, -70.88, -63.41, 0.0915, 2, 84.8, 30.23, 0.34249, 3, 3, 9.03, -73.19, 0.3642, 4, -83.61, -45.47, 0.03709, 2, 72.15, 12.22, 0.59871, 3, 3, -6.3, -75.08, 0.16555, 4, -98.56, -41.55, 0.0108, 2, 73.46, -3.17, 0.82365, 1, 2, 36.19, -13.75, 1, 1, 2, -27.12, -12.15, 1, 1, 2, -78.33, -4.25, 1, 2, 3, 11.55, 126.87, 0.12096, 2, -127.67, 22.35, 0.87904, 2, 3, 22.86, 123.16, 0.31711, 2, -123.53, 33.52, 0.68289, 3, 3, 46.29, 145.26, 0.49456, 4, 31.86, 143.67, 0.27622, 2, -144.72, 57.77, 0.22922, 3, 3, 92.84, 157.29, 0.38255, 4, 79.56, 137.62, 0.56497, 2, -154.97, 104.75, 0.05248, 2, 3, 127.88, 158.63, 0.24708, 4, 112.61, 125.89, 0.75292, 2, 3, 166.59, 155.03, 0.10028, 4, 147.23, 108.22, 0.89972, 1, 4, 186.28, 85.5, 1, 1, 4, 182.99, 28.88, 1, 3, 3, 228.2, 11.44, 0.00866, 4, 151.31, -47.98, 0.99123, 2, -4.07, 234.46, 0.00011, 3, 3, 74.73, -54.76, 0.48741, 4, -15.76, -52.67, 0.44997, 2, 56.24, 78.58, 0.06262, 3, 3, 73.6, -37.33, 0.49646, 4, -10.36, -36.06, 0.46077, 2, 38.77, 78.11, 0.04277, 3, 3, 79.06, -19.64, 0.49635, 4, 1.26, -21.65, 0.47931, 2, 21.31, 84.24, 0.02434, 3, 3, 69.29, 1.24, 0.47889, 4, -0.08, 1.37, 0.51055, 2, 0.07, 75.28, 0.01057, 3, 3, 64.6, 25.15, 0.44295, 4, 4.41, 25.31, 0.55401, 2, -24, 71.5, 0.00305, 3, 3, 70.29, 49.46, 0.37934, 4, 18.69, 45.79, 0.62021, 2, -48.07, 78.11, 0.00045, 2, 3, 82.85, 66.94, 0.3152, 4, 36.83, 57.38, 0.6848, 2, 3, 101.81, 77.58, 0.28027, 4, 58.39, 60.25, 0.71973], "hull": 23, "edges": [0, 44, 0, 2, 2, 4, 4, 6, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 14, 16, 12, 14, 6, 8, 8, 10, 10, 12, 32, 34, 28, 30, 30, 32, 10, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60], "width": 238, "height": 252}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.92202, 0.15933, 0.98469, 0.42653, 0.99999, 0.63603, 1, 0.813, 0.98661, 0.94198, 0.57068, 1, 0, 0.96805, 0, 0.81638, 0.00918, 0.63006, 0.03378, 0.39886, 0.09707, 0.17397, 0.2862, 0, 0.72694, 0.01088, 0.56042, 0.81619, 0.55615, 0.63188, 0.55188, 0.39991, 0.53908, 0.17111], "triangles": [16, 11, 12, 16, 12, 0, 10, 11, 16, 15, 16, 0, 10, 16, 15, 13, 2, 3, 4, 13, 3, 7, 13, 6, 5, 13, 4, 8, 13, 7, 14, 13, 8, 6, 13, 5, 15, 0, 1, 14, 15, 1, 14, 1, 2, 13, 14, 2, 9, 10, 15, 14, 8, 9, 14, 9, 15], "vertices": [3, 22, -22.26, 26.22, 0.29344, 23, -45.89, 29.01, 0.01083, 2, 26.8, 12.25, 0.69573, 3, 22, 1.67, 31.05, 0.51156, 23, -21.71, 32.4, 0.1139, 2, 31, -11.8, 0.37454, 3, 22, 20.49, 32.57, 0.56286, 23, -2.84, 32.8, 0.33826, 2, 32.02, -30.65, 0.09888, 3, 22, 36.46, 33.02, 0.39719, 23, 13.13, 32.29, 0.5929, 2, 32.05, -46.62, 0.00991, 3, 22, 48.12, 32.44, 0.17837, 23, 24.73, 31.02, 0.82159, 2, 31.17, -58.26, 4e-05, 2, 22, 54, 4.68, 0.07013, 23, 28.95, 2.96, 0.92987, 3, 22, 52.13, -33.62, 0.02375, 23, 24.8, -35.16, 0.97602, 2, -34.98, -60.54, 0.00024, 3, 22, 38.48, -33.98, 0.13194, 23, 11.16, -34.7, 0.85425, 2, -34.98, -46.89, 0.01381, 3, 22, 21.7, -33.8, 0.29179, 23, -5.58, -33.53, 0.58253, 2, -34.36, -30.12, 0.12568, 3, 22, 0.86, -32.7, 0.3259, 23, -26.33, -31.19, 0.27796, 2, -32.71, -9.31, 0.39614, 3, 22, -19.49, -29, 0.218, 23, -46.41, -26.28, 0.06797, 2, -28.47, 10.93, 0.71403, 3, 22, -35.47, -16.74, 0.05972, 23, -61.64, -13.09, 0.00706, 2, -15.8, 26.59, 0.93322, 3, 22, -35.27, 12.8, 0.06177, 23, -59.68, 16.39, 0, 2, 13.73, 25.61, 0.93822, 2, 22, 37.48, 3.56, 0.01398, 23, 12.39, 2.83, 0.98602, 2, 22, 20.9, 2.84, 0.99109, 23, -4.2, 3.09, 0.00891, 2, 22, 0.04, 2, 0.94354, 2, 2, -9.4, 0.05646, 1, 2, 1.14, 11.19, 1], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 8, 10, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 12, 14, 14, 16, 4, 6, 6, 8], "width": 67, "height": 85}}, "eye_L": {"eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [36.68, -67.05, 32.22, -41.44, 61.77, -36.29, 66.24, -61.91], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 27, "height": 32}}, "eye_L2": {"eye_L2": {"x": 48.63, "y": -25.3, "rotation": -80.12, "width": 47, "height": 32}}, "eye_R": {"eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [35.13, -34.84, 26.89, 12.45, 67.28, 19.48, 75.52, -27.81], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 49, "height": 41}}, "hand_L": {"hand_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50.75, 67.79, 46.62, 0.92, -57.18, 7.33, -53.05, 74.2], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 67, "height": 104}}, "hand_R": {"hand_R": {"type": "mesh", "uvs": [0.67338, 0.00282, 0.93911, 0.13128, 0.96865, 0.36729, 0.98814, 0.61171, 1, 0.75485, 0.94714, 0.90954, 0.53203, 0.99999, 0.33016, 0.96383, 0.19398, 0.81499, 0.12393, 0.67754, 0.01183, 0.41418, 0, 0.04916, 0.29635, 1e-05, 0.28537, 0.6048, 0.53984, 0.56525, 0.80163, 0.56941], "triangles": [6, 15, 5, 6, 14, 15, 6, 7, 14, 8, 13, 7, 7, 13, 14, 4, 15, 3, 4, 5, 15, 8, 9, 13, 9, 10, 13, 15, 2, 3, 14, 13, 12, 0, 15, 14, 13, 10, 12, 2, 15, 0, 2, 0, 1, 14, 12, 0, 10, 11, 12], "vertices": [2, 8, 30.72, 17.35, 0.99998, 9, -35.72, 31.08, 2e-05, 1, 8, 43.5, 54.78, 1, 2, 8, 72.53, 61.4, 0.58413, 9, 18.4, 58.62, 0.41587, 2, 8, 102.72, 66.76, 0.19439, 9, 48.65, 53.58, 0.80561, 2, 8, 120.4, 69.96, 0.08212, 9, 66.38, 50.69, 0.91788, 2, 8, 140.3, 64.52, 0.03045, 9, 83.32, 38.91, 0.96955, 1, 9, 80.22, -18.58, 1, 2, 8, 154.52, -18.46, 0.00013, 9, 69, -44.05, 0.99987, 2, 8, 137.64, -38.56, 0.02793, 9, 46.36, -57.35, 0.97207, 2, 8, 121.37, -49.58, 0.11694, 9, 27.35, -62.3, 0.88306, 2, 8, 89.94, -67.69, 0.45934, 9, -8.33, -68.86, 0.54066, 1, 8, 44.64, -73.35, 1, 1, 8, 34.93, -33.75, 1, 2, 8, 110.36, -28.51, 0.08811, 9, 24.01, -38.77, 0.91189, 1, 9, 27.84, -4.02, 1, 2, 8, 99.71, 41.02, 0.20107, 9, 37.21, 30.33, 0.79893], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 8, 10, 12, 14, 20, 22, 22, 24, 10, 12, 4, 6, 6, 8, 14, 16, 16, 18, 18, 20, 18, 26, 26, 28, 6, 30, 30, 28], "width": 139, "height": 129}}, "head": {"head": {"type": "mesh", "uvs": [1, 0, 1, 1, 0.10914, 0.99918, 0.10318, 0.73868, 0.11867, 0.66347, 0.04272, 0.57364, 0, 0.44129, 0, 0.32216, 0.02885, 0.23954, 0.15959, 0.24625, 0.26656, 0.27671, 0.33459, 0.25262, 0.33182, 0, 0.27342, 0.34432, 0.22832, 0.49281, 0.16898, 0.63667], "triangles": [9, 7, 8, 14, 7, 9, 14, 6, 7, 5, 6, 14, 4, 5, 15, 13, 9, 10, 14, 13, 11, 15, 3, 4, 15, 14, 1, 15, 2, 3, 13, 10, 11, 1, 2, 15, 1, 14, 11, 11, 0, 1, 11, 12, 0, 15, 5, 14, 13, 14, 9], "vertices": [1, 5, 165.87, -62.82, 1, 1, 5, -9.49, -93.37, 1, 1, 5, -35.95, 59.37, 1, 1, 5, 9.56, 68.35, 1, 1, 5, 23.21, 67.99, 1, 2, 5, 36.69, 83.75, 0.56365, 6, 2.22, 30.64, 0.43635, 2, 5, 58.63, 95.12, 0.23032, 6, 26.07, 24.2, 0.76968, 2, 5, 79.52, 98.76, 0.00784, 6, 43.93, 12.77, 0.99216, 2, 5, 94.87, 96.34, 0.12225, 6, 53.61, 0.62, 0.87775, 2, 5, 97.59, 73.72, 0.37642, 6, 40.34, -17.9, 0.62358, 2, 5, 95.45, 54.45, 0.95414, 6, 25.74, -30.66, 0.04586, 1, 5, 101.7, 43.53, 1, 1, 5, 145.92, 51.72, 1, 1, 5, 83.8, 51.21, 1, 1, 5, 56.41, 54.41, 1, 1, 5, 29.41, 60.18, 1], "hull": 13, "edges": [0, 24, 8, 10, 14, 16, 20, 22, 22, 24, 0, 2, 2, 4, 4, 6, 6, 8, 16, 18, 18, 20, 10, 12, 12, 14, 20, 26, 26, 28, 28, 30, 30, 8], "width": 178, "height": 178}}, "head2": {"head_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-7.68, -88.58, -37.37, 81.86, 107.45, 107.08, 137.14, -63.35], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 173, "height": 147}}, "leg_L": {"leg_L": {"type": "mesh", "uvs": [0.67432, 0.04926, 0.89649, 0.34794, 0.98016, 0.63987, 0.99248, 0.86049, 0.79204, 1, 0.47142, 1, 0.17402, 0.94044, 0.06947, 0.80704, 1e-05, 0.58952, 0.00331, 0.34811, 0.15647, 0.07626, 0.45881, 0], "triangles": [5, 6, 7, 4, 5, 1, 4, 2, 3, 10, 8, 9, 11, 7, 8, 10, 11, 8, 1, 5, 0, 4, 1, 2, 5, 11, 0, 5, 7, 11], "vertices": [2, 18, -8.29, 20.88, 0.97786, 19, -63.26, -10, 0.02214, 2, 18, 21.94, 37.19, 0.9294, 19, -44.23, 18.6, 0.0706, 2, 18, 47.68, 39.36, 0.79042, 19, -22.53, 32.6, 0.20958, 2, 18, 65.68, 35.75, 0.47317, 19, -4.94, 37.87, 0.52683, 2, 18, 71.13, 12, 0.19394, 19, 11.02, 19.45, 0.80606, 2, 18, 61.99, -21.07, 0.1494, 19, 18.48, -14.03, 0.8506, 2, 18, 48.74, -50.42, 0.36605, 19, 20.58, -46.17, 0.63395, 2, 18, 35.09, -58.25, 0.61794, 19, 12.2, -59.49, 0.38206, 2, 18, 15.71, -60.61, 0.75454, 19, -3.81, -70.67, 0.24546, 2, 18, -3.51, -54.93, 0.84986, 19, -23.44, -74.68, 0.15014, 2, 18, -20.89, -33.12, 0.92567, 19, -49.03, -63.59, 0.07433, 2, 18, -18.37, -0.25, 0.97259, 19, -62.24, -33.39, 0.02741], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 0, 8, 10, 10, 12], "width": 110, "height": 85}}, "leg_L2": {"leg_L2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [46.48, -10.06, -46.35, -15.55, -47.71, 7.41, 45.13, 12.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 93, "height": 23}}, "leg_L3": {"leg_L3": {"type": "mesh", "uvs": [0.78268, 0.0124, 0.99012, 0.19639, 0.99839, 0.5554, 0.89768, 0.82854, 0.75726, 0.97378, 0.50981, 0.988, 0.19212, 0.98878, 0.00849, 0.93654, 0.00287, 0.59777, 0.09323, 0.27896, 0.35191, 0.01509], "triangles": [7, 8, 6, 10, 3, 5, 2, 3, 10, 4, 5, 3, 5, 6, 8, 9, 5, 8, 0, 2, 10, 2, 0, 1, 5, 9, 10], "vertices": [3, 18, 25.18, 25.92, 0.8873, 19, -36.08, 10.16, 0.06678, 21, 21.63, 70.17, 0.04592, 3, 18, 44.91, 41.34, 0.64674, 19, -25.91, 33.04, 0.16567, 21, 42.6, 56.48, 0.18759, 3, 18, 73.15, 34.36, 0.3287, 19, 2.3, 40.15, 0.18707, 21, 45.11, 27.5, 0.48423, 3, 18, 91.87, 19.05, 0.08391, 19, 26.02, 35.42, 0.13715, 21, 36.66, 4.84, 0.77894, 3, 18, 99.58, 2.79, 0.00399, 19, 40.47, 24.69, 0.02541, 21, 23.76, -7.71, 0.9706, 1, 21, -0.13, -10.27, 1, 1, 21, -30.89, -12.15, 1, 3, 18, 77.32, -66.41, 0.08792, 19, 53.31, -46.86, 0.24542, 21, -48.92, -8.98, 0.66667, 3, 18, 50.73, -59.62, 0.28329, 19, 26.65, -53.36, 0.38338, 21, -51.08, 18.38, 0.33333, 2, 18, 28.17, -44.3, 0.60376, 19, -0.46, -50.42, 0.39624, 3, 18, 14.26, -14.42, 0.83788, 19, -26.78, -30.57, 0.15483, 21, -20.07, 67.49, 0.0073], "hull": 11, "edges": [0, 20, 0, 2, 2, 4, 12, 14, 14, 16, 16, 18, 18, 20, 8, 10, 10, 12, 4, 6, 6, 8], "width": 98, "height": 82}}, "leg_R": {"leg_R": {"type": "mesh", "uvs": [0.68767, 0, 0.94964, 0.22031, 0.99994, 0.43756, 0.98187, 0.6321, 0.91128, 0.81058, 0.82224, 0.9258, 0.62077, 0.99999, 0.38853, 1, 0.16877, 0.94183, 0.00747, 0.78088, 0, 0.48461, 0.04812, 0.24906, 0.27908, 0, 0.13429, 0.83024, 0.37925, 0.73046, 0.6413, 0.84744, 0.79796, 0.78895], "triangles": [4, 16, 3, 15, 14, 16, 5, 16, 4, 15, 16, 5, 15, 7, 14, 6, 15, 5, 6, 7, 15, 8, 14, 7, 3, 16, 2, 8, 13, 14, 13, 9, 10, 0, 1, 16, 14, 12, 0, 16, 14, 0, 1, 2, 16, 11, 12, 14, 10, 11, 14, 14, 13, 10, 9, 13, 8], "vertices": [2, 14, -27.11, 13.89, 0.88211, 15, -87.25, -4.59, 0.11789, 2, 14, -9.38, 48.56, 0.78727, 15, -76.93, 32.96, 0.21273, 2, 14, 11.54, 57.6, 0.65471, 15, -58.29, 46.06, 0.34529, 2, 14, 31.3, 58.06, 0.49897, 15, -39.03, 50.53, 0.50103, 2, 14, 50.33, 51.96, 0.34044, 15, -19.16, 48.42, 0.65956, 2, 14, 63.32, 42.76, 0.21255, 15, -4.57, 42.06, 0.78745, 2, 14, 74.06, 19.42, 0.15405, 15, 10.69, 21.38, 0.84595, 2, 14, 77.88, -8.65, 0.20435, 15, 20.13, -5.33, 0.79565, 2, 14, 75.68, -36.01, 0.35957, 15, 23.53, -32.57, 0.64043, 2, 14, 62.22, -57.7, 0.57531, 15, 14.77, -56.54, 0.42469, 2, 14, 32.7, -62.64, 0.77026, 15, -13.14, -67.37, 0.22974, 2, 14, 8.33, -60.03, 0.89222, 15, -37.53, -69.77, 0.10778, 2, 14, -20.39, -35.5, 0.92313, 15, -70.64, -51.58, 0.07687, 2, 14, 65.08, -41.7, 0.64, 15, 14.31, -40.29, 0.36, 2, 14, 51.06, -13.45, 0.55303, 15, -5.15, -15.47, 0.44697, 2, 14, 58.46, 19.82, 0.29937, 15, -4.67, 18.61, 0.70063, 2, 14, 50.03, 37.97, 0.34529, 15, -16.61, 34.66, 0.65471], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 116, "height": 90}}, "leg_R2": {"leg_R2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [49.75, -9.54, -45.91, -17.62, -48.6, 14.27, 47.06, 22.35], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 96, "height": 32}}, "leg_R3": {"leg_R3": {"type": "mesh", "uvs": [0.63607, 0.01178, 0.8344, 0.0937, 0.99584, 0.34208, 0.98702, 0.6006, 0.87282, 0.78663, 0.79802, 0.93861, 0.63674, 0.99999, 0.30791, 0.98527, 0.02107, 0.90499, 0, 0.73855, 0.00211, 0.42958, 0.12649, 0.16107, 0.36901, 1e-05], "triangles": [6, 7, 12, 5, 6, 4, 0, 4, 6, 11, 7, 10, 6, 12, 0, 7, 11, 12, 4, 0, 1, 3, 1, 2, 4, 1, 3, 10, 7, 9, 7, 8, 9], "vertices": [3, 15, -42.16, -5.92, 0.20546, 14, 16.77, 3.42, 0.79445, 17, 14.72, 79.76, 9e-05, 2, 15, -42.47, 17.32, 0.42493, 14, 21.19, 26.24, 0.57507, 2, 15, -27.13, 41.74, 0.69155, 14, 41.17, 47.04, 0.30845, 2, 15, -4.62, 48.66, 0.88759, 14, 64.61, 49.24, 0.11241, 3, 15, 15.56, 42.35, 0.69601, 14, 83.09, 38.97, 0.02108, 17, 46.84, 11.71, 0.28291, 3, 15, 31.37, 39.14, 0.38827, 14, 97.92, 32.6, 0.00139, 17, 39.73, -2.77, 0.61035, 3, 15, 42.6, 24.12, 0.14607, 14, 105.87, 15.62, 0.02136, 17, 22.36, -9.84, 0.83257, 3, 15, 53.51, -10.74, 0.26675, 14, 109.46, -20.73, 0.07249, 17, -14.13, -11.58, 0.66077, 3, 15, 57.23, -43.19, 0.48211, 14, 106.51, -53.26, 0.18455, 17, -46.47, -6.98, 0.33333, 3, 15, 43.73, -50.45, 0.54345, 14, 91.82, -57.62, 0.34543, 17, -50.07, 7.92, 0.11111, 2, 15, 17.14, -59.6, 0.44315, 14, 63.93, -61.18, 0.55685, 3, 15, -10.5, -54.72, 0.24442, 14, 37.86, -50.8, 0.7555, 17, -40.5, 61.46, 9e-05, 3, 15, -33.29, -34.23, 0.14035, 14, 19.71, -26.1, 0.85956, 17, -14.91, 78.33, 9e-05], "hull": 13, "edges": [0, 24, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24], "width": 112, "height": 92}}, "weapons": {"weapons": {"x": 76.42, "y": -13.19, "rotation": -77.69, "width": 139, "height": 270}}, "yinying": {"yinying": {"x": -0.73, "y": -5.62, "width": 429, "height": 201}}}}], "animations": {"attack": {"bones": {"ALL12": {"rotate": [{"angle": 2.73, "curve": "stepped"}, {"time": 0.3, "angle": 2.73, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "angle": -44.74, "curve": 1, "c3": 0.75}, {"time": 0.8, "angle": 2.73}]}, "ALL3": {"rotate": [{"curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.1333, "angle": 0.58, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3, "angle": -6.07, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "angle": 8.97, "curve": 1, "c3": 0.75}, {"time": 0.8, "angle": -2.36, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"y": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -24.82, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "y": -0.9}]}, "ALL4": {"rotate": [{"curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.1, "angle": 2.92, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3, "angle": -11.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -13.31, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 17.72, "curve": 0.719, "c3": 0.75}, {"time": 0.8667, "angle": -2.36, "curve": 0.958, "c2": 0.01, "c3": 0.75}, {"time": 1}], "translate": [{"x": -3.85, "y": -0.15, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.1, "x": 16.13, "y": -0.26, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3, "x": -13.83, "y": -3.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 18.99, "y": -0.74, "curve": 0.719, "c3": 0.75}, {"time": 0.8667, "x": -3.85, "y": -0.15}], "scale": [{"x": 0.996, "y": 0.996}]}, "ALL5": {"rotate": [{"curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.1667, "angle": 9.12, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3333, "angle": -8.89, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "angle": 23.51, "curve": 0.756, "c3": 0.911, "c4": 0.58}, {"time": 0.6667, "angle": 25.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -8.83, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "ALL13": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -4, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "angle": -41.79, "curve": 1, "c3": 0.75}, {"time": 0.8}], "translate": [{"time": 0.3, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "x": -10.44, "y": -45.13, "curve": 1, "c3": 0.75}, {"time": 0.8}]}, "ALL10": {"rotate": [{"angle": 0.29, "curve": "stepped"}, {"time": 0.3, "angle": 0.29, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "angle": 124.98, "curve": 1, "c3": 0.75}, {"time": 0.8, "angle": -4.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.29}], "translate": [{"time": 0.3, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "x": -39.41, "y": 25.72, "curve": 1, "c3": 0.75}, {"time": 0.8}]}, "ALL15": {"rotate": [{"angle": -5.45}]}, "ALL14": {"rotate": [{"angle": 1.92}], "translate": [{"y": -1.13}]}, "ALL18": {"rotate": [{"angle": -1.75}]}, "ALL9": {"rotate": [{"angle": -2.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 38.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -2.67}]}, "ALL20": {"rotate": [{"angle": -2.14, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -10.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 62.15, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -38.83, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -2.14}]}, "ALL17": {"rotate": [{"angle": 0.71}]}, "ALL21": {"rotate": [{"angle": 5.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -43.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 58.56, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -12.15, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.52}], "translate": [{"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -0.29, "y": 10.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 2.68, "y": 0.41, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "ALL22": {"rotate": [{"curve": 0.254, "c3": 0.621, "c4": 0.47}, {"time": 0.1667, "angle": 8.1, "curve": 0.369, "c2": 0.47, "c3": 0.753}, {"time": 0.3333, "angle": -8.89, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "angle": 23.51, "curve": 0.756, "c3": 0.911, "c4": 0.58}, {"time": 0.6667, "angle": 25.32, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -8.83, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "ALL7": {"rotate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 7.68, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "ALL2": {"translate": [{"y": -1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -3.56, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "x": -24.3, "y": 2.02, "curve": 1, "c3": 0.75}, {"time": 0.8, "y": -1.13}]}, "ALL6": {"rotate": [{"angle": 11.04}]}, "ALL8": {"rotate": [{"angle": -1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 15.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -14.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": 6.35, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.05}]}, "ALL11": {"rotate": [{"angle": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 21.76, "curve": 0.25, "c3": 0.288}, {"time": 0.4333, "angle": -6.62, "curve": 1, "c3": 0.75}, {"time": 0.8, "angle": -2.95, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.48}]}, "ALL23": {"rotate": [{"angle": 11.04}]}}}, "death": {"slots": {"eye_L": {"attachment": [{"time": 0.3333, "name": "eye_L"}]}, "eye_L2": {"attachment": [{"time": 0.3333, "name": "eye_L2"}]}, "eye_R": {"attachment": [{"time": 0.3333, "name": "eye_R"}]}}, "bones": {"ALL15": {"rotate": [{"angle": -5.45}, {"time": 1, "angle": -69.22}]}, "ALL12": {"rotate": [{"angle": 2.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.0333, "angle": -1.13, "curve": 0.33, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.0667, "angle": -15.69, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.1, "angle": -2.02, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.2, "angle": 34.43, "curve": 0.328, "c2": 0.32, "c3": 0.663, "c4": 0.66}, {"time": 0.3333, "angle": -46.21, "curve": 0.333, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "angle": -46.78}], "translate": [{"time": 0.3333, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.5, "x": -12.29, "y": -4.15}]}, "ALL11": {"rotate": [{"angle": 1.48, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.0333, "angle": 51.34, "curve": 0.334, "c2": 0.33, "c3": 0.667, "c4": 0.67}, {"time": 0.0667, "angle": 82.28, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.2, "angle": 45.29, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.3333, "angle": 60.12, "curve": 0.336, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.4, "angle": -3.2, "curve": 0.339, "c2": 0.35, "c3": 0.672, "c4": 0.69}, {"time": 0.5, "angle": -13.01}]}, "ALL18": {"rotate": [{"angle": -1.75}, {"time": 1, "angle": -81.34}]}, "ALL3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": -3.16, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "angle": 6.35, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "angle": 14.19, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "angle": 9.4, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": -7.37, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": -9.58, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.5333, "angle": -6.65, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.6333, "angle": -5.09, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 1, "angle": -1.58}], "translate": [{"y": -0.9, "curve": "stepped"}, {"time": 0.4333, "y": -0.9, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.69}, {"time": 0.5333, "x": 1.92, "y": -11.94, "curve": 0.34, "c2": 0.37, "c3": 0.673, "c4": 0.7}, {"time": 0.6333, "x": -0.37, "y": 1.25, "curve": 0.341, "c2": 0.4, "c3": 0.675, "c4": 0.74}, {"time": 0.8333, "x": 0.8, "y": -5.51}]}, "ALL2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": -5.97, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "angle": 5.64, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "angle": -0.16, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "angle": -6.74, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": -9.85}], "translate": [{"y": -1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "x": -11.88, "y": -14.33, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "x": -24.47, "y": -19.7, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "x": -27.12, "y": -15.31, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "x": -23.13, "y": -8.46, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "x": -19.14, "y": -9.6, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "x": -16.66, "y": -31.01, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5333, "x": -16.66, "y": -27.01}, {"time": 0.6333, "x": -16.66, "y": -29.41}, {"time": 0.7333, "x": -16.66, "y": -30.21}]}, "ALL14": {"rotate": [{"angle": 1.92}, {"time": 1, "angle": -10.38}], "translate": [{"y": -1.13}, {"time": 0.0333, "x": -9.11, "y": 5.54}, {"time": 0.0667, "x": -8.62, "y": 17.41}, {"time": 0.1, "x": -5.63, "y": 12.83}, {"time": 0.2, "x": 3.32, "y": -0.89}, {"time": 0.3333, "x": 4.24, "y": -4.73}, {"time": 0.4, "x": -0.99, "y": 7.32}, {"time": 0.5, "x": 11.47, "y": -9.64}]}, "ALL10": {"rotate": [{"angle": 0.29, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.0333, "angle": 5.22, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.0667, "angle": -7.91, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.1, "angle": -18.29, "curve": 0.336, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": -36.1, "curve": 0.338, "c2": 0.36, "c3": 0.671, "c4": 0.69}, {"time": 0.3333, "angle": -17.89, "curve": 0.336, "c2": 0.35, "c3": 0.669, "c4": 0.68}, {"time": 0.4, "angle": -8.36, "curve": 0.337, "c2": 0.36, "c3": 0.67, "c4": 0.69}, {"time": 0.5, "angle": 7.03, "curve": 0.337, "c2": 0.37, "c3": 0.671, "c4": 0.7}, {"time": 0.6, "angle": 3.74, "curve": 0.352, "c2": 0.65, "c3": 0.686}, {"time": 1, "angle": 4.67}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "x": 37.55, "y": 34.67, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "x": 37.12, "y": 34.11, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "x": 16.89, "y": 41.85, "curve": 0.296, "c2": 0.18, "c3": 0.638, "c4": 0.55}, {"time": 0.3333, "x": 40.87, "y": 30.8, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4, "x": 31.54, "y": 2.7}]}, "ALL9": {"rotate": [{"angle": -2.67, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.0333, "angle": -16.11, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.0667, "angle": 12.41, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.1, "angle": 39.27, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2, "angle": 26.79, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.3333, "angle": 0.78, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.4333, "angle": -17.48, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.5667, "angle": -39.03, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.6667, "angle": 4.2, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.7667, "angle": 21.25, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 0.8667, "angle": -4.3, "curve": 0.365, "c2": 0.64, "c3": 0.702}, {"time": 1, "angle": -2.67}]}, "ALL4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": -36.94, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "angle": -52.08, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "angle": -23.27, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "angle": -5.96, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": -3.05, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": -2.45, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.5333, "angle": -8.98, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.6333, "angle": -9.22, "curve": 0.382, "c2": 0.55, "c3": 0.741}, {"time": 1, "angle": -10.24}], "translate": [{"x": -3.85, "y": -0.15, "curve": "stepped"}, {"time": 0.5333, "x": -3.85, "y": -0.15, "curve": 0.34, "c2": 0.36, "c3": 0.674, "c4": 0.69}, {"time": 0.6333, "x": -16.08, "y": -3.92, "curve": 0.343, "c2": 0.37, "c3": 0.677, "c4": 0.71}, {"time": 0.7333, "x": -8.82, "y": -1.74, "curve": 0.355, "c2": 0.46, "c3": 0.69, "c4": 0.81}, {"time": 0.9, "x": -8.82, "y": -3.07}], "scale": [{"x": 0.996, "y": 0.996}]}, "target_1": {"rotate": [{"time": 0.3333}, {"time": 0.4, "angle": -39.34}, {"time": 0.5, "angle": -99.45}], "translate": [{"time": 0.0333}, {"time": 0.0667, "x": -18.4, "curve": "stepped"}, {"time": 0.3333, "x": -18.4}, {"time": 0.4, "x": -35.2, "y": 9.2}, {"time": 0.5, "x": -47.2, "y": 23.2}]}, "ALL20": {"rotate": [{"angle": -2.14, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.0333, "angle": 24.48, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.1, "angle": 31.06, "curve": 0.337, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": -15.31, "curve": 0.339, "c2": 0.35, "c3": 0.672, "c4": 0.69}, {"time": 0.3333, "angle": -25.04, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.4, "angle": 11.95, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.69}, {"time": 0.5, "angle": 19.62, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.6, "angle": 2.03, "curve": 0.34, "c2": 0.37, "c3": 0.674, "c4": 0.71}, {"time": 0.7, "angle": -3.32, "curve": 0.341, "c2": 0.39, "c3": 0.675, "c4": 0.73}, {"time": 0.8, "angle": 10.07, "curve": 0.352, "c2": 0.65, "c3": 0.687}, {"time": 1, "angle": 6.3}]}, "ALL5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": 28.67, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "angle": 20.54, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "angle": 2.89, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "angle": 21.48, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": 29.77, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": 19.06, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.5333, "angle": -3.39, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.6333, "angle": -7.77, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 0.7333, "angle": -5.76, "curve": 0.346, "c2": 0.39, "c3": 0.681, "c4": 0.73}, {"time": 0.8333, "angle": -5.03, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1, "angle": -8.71}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 1.32, "y": 7.92}]}, "ALL7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": 15.05, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "angle": -1.92, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "angle": -21.91, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "angle": -23.19, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": -9.03, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": 13.83, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.5667, "angle": 29.89, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.6667, "angle": 17.15, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.7667, "angle": 16.62, "curve": 0.345, "c2": 0.37, "c3": 0.68, "c4": 0.71}, {"time": 0.8667, "angle": 17.29, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1, "angle": 16.07}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "x": -18.77, "y": 11, "curve": 0.329, "c2": 0.27, "c3": 0.662, "c4": 0.6}, {"time": 0.0667, "x": -26.13, "y": 3.7, "curve": 0.329, "c2": 0.29, "c3": 0.662, "c4": 0.62}, {"time": 0.1, "x": -9.16, "y": -8.03, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "x": -16.1, "y": -10.93, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "x": -20.6, "y": -5.12, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.4333}]}, "ALL17": {"rotate": [{"angle": 0.71}, {"time": 1, "angle": 56.77}]}, "ALL6": {"rotate": [{"angle": 11.04, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "angle": 21.23, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.1, "angle": -9.56, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2, "angle": -7.76, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.3333, "angle": 26.21, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.4, "angle": 5.19, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.5, "angle": 12.83, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.6, "angle": 1.08, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.7, "angle": -4.25, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 0.8, "angle": 3.75, "curve": 0.365, "c2": 0.64, "c3": 0.702}, {"time": 1}]}, "ALL13": {"rotate": [{}, {"time": 0.2, "angle": -46.88}, {"time": 0.4}]}, "ALL8": {"rotate": [{"angle": -1.05, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.0333, "angle": 24.75, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.0667, "angle": 39.35, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.1, "angle": 33.3, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.2, "angle": 15.17, "curve": 0.339, "c2": 0.35, "c3": 0.672, "c4": 0.69}, {"time": 0.3333, "angle": -3.81, "curve": 0.336, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 0.4333, "angle": -18.58, "curve": 0.339, "c2": 0.36, "c3": 0.672, "c4": 0.69}, {"time": 0.5667, "angle": 4.76, "curve": 0.339, "c2": 0.36, "c3": 0.673, "c4": 0.7}, {"time": 0.6667, "angle": 24.01, "curve": 0.34, "c2": 0.37, "c3": 0.674, "c4": 0.71}, {"time": 0.7667, "angle": 1.85, "curve": 0.341, "c2": 0.39, "c3": 0.675, "c4": 0.73}, {"time": 0.8667, "angle": -5.95, "curve": 0.352, "c2": 0.65, "c3": 0.686}, {"time": 1, "angle": -1.05}]}, "ALL21": {"rotate": [{"angle": 5.52, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.0333, "angle": -18.21, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.1, "angle": 14.56, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.2, "angle": 51.28, "curve": 0.333, "c2": 0.33, "c3": 0.668, "c4": 0.67}, {"time": 0.3333, "angle": -28.97, "curve": 0.335, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.4, "angle": -55.93, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.5, "angle": 13.58, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.6, "angle": 42.18, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.7, "angle": 6.34, "curve": 0.345, "c2": 0.39, "c3": 0.679, "c4": 0.72}, {"time": 0.8, "angle": -14.87, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1, "angle": 5.52}]}, "yinying": {"translate": [{"time": 0.3333}, {"time": 0.5333, "x": 2.78, "y": -1.39}], "scale": [{"time": 0.3333}, {"time": 0.5333, "x": 1.148, "y": 1.148}]}, "ALL22": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": 28.67, "curve": 0.324, "c2": 0.23, "c3": 0.658, "c4": 0.56}, {"time": 0.0667, "angle": 20.54, "curve": 0.325, "c2": 0.27, "c3": 0.659, "c4": 0.61}, {"time": 0.1, "angle": 2.89, "curve": 0.314, "c2": 0.24, "c3": 0.649, "c4": 0.58}, {"time": 0.2, "angle": 21.48, "curve": 0.317, "c2": 0.27, "c3": 0.653, "c4": 0.62}, {"time": 0.3333, "angle": 29.77, "curve": 0.329, "c2": 0.32, "c3": 0.663, "c4": 0.65}, {"time": 0.4333, "angle": 19.06, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.5333, "angle": -3.39, "curve": 0.335, "c2": 0.34, "c3": 0.67, "c4": 0.68}, {"time": 0.6333, "angle": -7.77, "curve": 0.348, "c2": 0.38, "c3": 0.686, "c4": 0.73}, {"time": 0.7333, "angle": -5.76, "curve": 0.346, "c2": 0.39, "c3": 0.681, "c4": 0.73}, {"time": 0.8333, "angle": -5.03, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1, "angle": -8.71}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 1.32, "y": 7.92}]}, "ALL23": {"rotate": [{"angle": 11.04, "curve": 0.332, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.0333, "angle": 21.23, "curve": 0.331, "c2": 0.33, "c3": 0.665, "c4": 0.66}, {"time": 0.1, "angle": -9.56, "curve": 0.332, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.2, "angle": -7.76, "curve": 0.334, "c2": 0.34, "c3": 0.668, "c4": 0.67}, {"time": 0.3333, "angle": 26.21, "curve": 0.335, "c2": 0.34, "c3": 0.669, "c4": 0.67}, {"time": 0.4, "angle": 5.19, "curve": 0.337, "c2": 0.35, "c3": 0.671, "c4": 0.68}, {"time": 0.5, "angle": 12.83, "curve": 0.339, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 0.6, "angle": 1.08, "curve": 0.342, "c2": 0.37, "c3": 0.676, "c4": 0.7}, {"time": 0.7, "angle": -4.25, "curve": 0.344, "c2": 0.39, "c3": 0.678, "c4": 0.72}, {"time": 0.8, "angle": 3.75, "curve": 0.365, "c2": 0.64, "c3": 0.702}, {"time": 1}]}}}, "idle": {"slots": {"eye_L": {"attachment": [{"time": 0.8, "name": "eye_L"}, {"time": 0.8667, "name": null}]}, "eye_L2": {"attachment": [{"time": 0.8, "name": "eye_L2"}, {"time": 0.8667, "name": null}]}, "eye_R": {"attachment": [{"time": 0.8, "name": "eye_R"}, {"time": 0.8667, "name": null}]}}, "bones": {"ALL2": {"translate": [{"y": -1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -4.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -1.13}]}, "ALL3": {"translate": [{"y": -0.9, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "y": -3.38, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "y": -0.9}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.979, "y": 0.979, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "ALL4": {"translate": [{"x": -3.85, "y": -0.15, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "x": -5.63, "y": -0.21, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "x": -3.85, "y": -0.15}], "scale": [{"x": 0.996, "y": 0.996, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "x": 0.985, "y": 0.985, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "x": 0.996, "y": 0.996}]}, "ALL5": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.005, "y": 1.005, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "ALL6": {"rotate": [{"angle": 11.04, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 16.17, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": 11.04}]}, "ALL7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.92, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.032, "y": 1.032, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "ALL8": {"rotate": [{"angle": -1.05, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -3.92, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": -1.05}]}, "ALL9": {"rotate": [{"angle": -2.67, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -3.92, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": -2.67}]}, "ALL10": {"rotate": [{"angle": 0.29, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 3.13, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 0.29}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.029, "y": 1.029, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "ALL11": {"rotate": [{"angle": 1.48, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 3.13, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": 1.48}]}, "ALL12": {"rotate": [{"angle": 2.73, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 3.13, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 2.73}]}, "ALL14": {"rotate": [{"angle": 1.92}], "translate": [{"y": -1.13}]}, "ALL15": {"rotate": [{"angle": -5.45}]}, "ALL17": {"rotate": [{"angle": 0.71}]}, "ALL18": {"rotate": [{"angle": -1.75}]}, "ALL20": {"rotate": [{"angle": -2.14, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "angle": -7.06, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 11.36, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": -2.14}]}, "ALL21": {"rotate": [{"angle": 5.52, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "angle": -7.06, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 11.36, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": 5.52}]}, "ALL22": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.005, "y": 1.005, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "ALL23": {"rotate": [{"angle": 11.04, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 16.17, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": 11.04}]}}}, "run": {"bones": {"ALL15": {"rotate": [{"angle": -5.45}]}, "ALL12": {"rotate": [{"angle": 24.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -14.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 24.05}]}, "ALL11": {"rotate": [{"angle": 55.93, "curve": 0.344, "c2": 0.37, "c3": 0.687, "c4": 0.73}, {"time": 0.3333, "angle": 47.22, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 55.93}], "translate": [{"x": -3.33, "y": -23.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -7.23, "y": 1.45, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "x": -3.33, "y": -23.79}]}, "ALL18": {"rotate": [{"angle": -1.75}]}, "ALL3": {"translate": [{"y": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": -9.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -0.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -9.62, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": -0.9}]}, "ALL2": {"translate": [{"y": 46.23, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.0667, "y": 1.77, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 0.1667, "y": -5.45, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.2333, "y": 37.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.3333, "y": 46.23, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 0.4, "y": 1.77, "curve": 0.306, "c2": 0.21, "c3": 0.643, "c4": 0.56}, {"time": 0.5, "y": -5.45, "curve": 0.313, "c2": 0.27, "c3": 0.66, "c4": 0.65}, {"time": 0.5667, "y": 37.06, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 0.6667, "y": 46.23}]}, "ALL14": {"rotate": [{"angle": 1.92}], "translate": [{"y": -1.13}, {"time": 0.0667, "x": 18.52, "y": 13.69}, {"time": 0.1667, "x": 43.57, "y": 7.32}, {"time": 0.2333, "x": 50.86, "y": -25.18}, {"time": 0.3333, "x": 60.84, "y": -16.64}, {"time": 0.4, "x": 53.89, "y": -17.69}, {"time": 0.4333, "x": 55.03, "y": -10.46}, {"time": 0.4667, "x": 64.45, "y": -1.68}, {"time": 0.5, "x": 75.94, "y": -5.32}, {"time": 0.5667, "x": 13.7, "y": -20.89}, {"time": 0.6667, "y": -1.13}]}, "ALL10": {"rotate": [{"angle": -33.15, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -33.15}], "translate": [{"x": -23.7, "y": -11.83, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -11.13, "y": -5.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -23.7, "y": -11.83}]}, "ALL9": {"rotate": [{"angle": 16.58, "curve": 0.25, "c3": 0.786, "c4": 0.72}, {"time": 0.0667, "angle": 8.23, "curve": 0.224, "c2": 0.31, "c3": 0.75}, {"time": 0.3333, "angle": 27.13, "curve": 0.25, "c3": 0.771, "c4": 0.73}, {"time": 0.5333, "angle": -2.35, "curve": 0.344, "c2": 0.66, "c3": 0.677}, {"time": 0.6667, "angle": 16.58}]}, "ALL4": {"translate": [{"x": -10.04, "y": -0.38, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -3.85, "y": -0.15, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.2667, "x": -13.65, "y": -0.52, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -3.85, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -13.65, "y": -0.52, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": -10.04, "y": -0.38}], "scale": [{"x": 0.996, "y": 0.996}]}, "target_1": {"rotate": [{"angle": -45.28}, {"time": 0.0667, "angle": -85.95}, {"time": 0.1667, "angle": -121.2}, {"time": 0.2333, "angle": -10.09}, {"time": 0.3333, "angle": 31.32}, {"time": 0.4, "angle": 35.67}, {"time": 0.4667, "angle": -1.55}, {"time": 0.5, "angle": -2.64}, {"time": 0.5667, "angle": -32.08}, {"time": 0.6667, "angle": -45.28}], "translate": [{"x": -33.82, "y": 63.14}, {"time": 0.0667, "x": -27.65, "y": 61.91}, {"time": 0.1667, "x": 0.79, "y": 48.93}, {"time": 0.2333, "x": 104.51, "y": 42.74}, {"time": 0.3333, "x": 141.71, "y": 51.31}, {"time": 0.4, "x": 141.41, "y": 11.35}, {"time": 0.5, "x": 60.29, "y": -1.52}, {"time": 0.5667, "x": -7.97, "y": 21.86}, {"time": 0.6667, "x": -33.82, "y": 63.14}]}, "ALL20": {"rotate": [{"angle": -18.51, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -18.51}]}, "ALL7": {"rotate": [{"angle": 27.88, "curve": 0.214, "c2": 0.26, "c3": 0.75}, {"time": 0.0667, "angle": 20.8, "curve": 0.25, "c3": 0.774, "c4": 0.72}, {"time": 0.2333, "angle": 6.99, "curve": 0.245, "c2": 0.32, "c3": 0.75}, {"time": 0.3667, "angle": -22.38, "curve": 0.25, "c3": 0.75, "c4": 0.7}, {"time": 0.6667, "angle": 27.88}], "translate": [{"x": 1.48, "y": -9.35}, {"time": 0.3667, "x": -5.21, "y": -15.1}, {"time": 0.6667, "x": 1.48, "y": -9.35}]}, "ALL17": {"rotate": [{"angle": 0.71}], "translate": [{"x": -13.46, "y": -8.28}, {"time": 0.0667, "x": -9.88, "y": 1.24}, {"time": 0.1667, "x": 11.21, "y": -5.02}, {"time": 0.2333, "x": -81.73, "y": -5.04}, {"time": 0.3333, "x": -69.81, "y": -16.66}, {"time": 0.4, "x": -65.89, "y": -16.1}, {"time": 0.5333, "x": 12.07, "y": -10.18}, {"time": 0.6, "x": -13.11, "y": -26.82}, {"time": 0.6667, "x": -13.46, "y": -8.28}]}, "ALL6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 20.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "ALL13": {"rotate": [{"angle": -26.27}, {"time": 0.3333, "angle": -28.11}, {"time": 0.6667, "angle": -26.27}], "translate": [{"x": 8.41, "y": -19.34}, {"time": 0.3333, "x": -0.29, "y": -24.12}, {"time": 0.6667, "x": 8.41, "y": -19.34}]}, "ALL8": {"rotate": [{"angle": 40.87, "curve": 0.289, "c2": 0.29, "c3": 0.626, "c4": 0.63}, {"time": 0.0667, "angle": 49.45, "curve": 0.276, "c2": 0.29, "c3": 0.623, "c4": 0.64}, {"time": 0.2333, "angle": 37.1, "curve": 0.226, "c2": 0.28, "c3": 0.75}, {"time": 0.5, "angle": 31.8, "curve": 0.25, "c3": 0.769, "c4": 0.69}, {"time": 0.6667, "angle": 40.87}]}, "target_2": {"rotate": [{"angle": 18.8, "curve": "stepped"}, {"time": 0.0667, "angle": 18.8}, {"time": 0.1667, "angle": -1.36}, {"time": 0.2333, "angle": -32.18}, {"time": 0.3333, "angle": -64.04}, {"time": 0.4, "angle": -102.71}, {"time": 0.5, "angle": -113.33}, {"time": 0.5667, "angle": -9.4}, {"time": 0.6667, "angle": 18.8}], "translate": [{"x": 32.7, "y": 52.99}, {"time": 0.0667, "x": 32.7, "y": 11}, {"time": 0.1667, "x": -57.46, "y": 1.95}, {"time": 0.2333, "x": -126.7, "y": 17.4}, {"time": 0.3333, "x": -160.31, "y": 67.6}, {"time": 0.4, "x": -159.99, "y": 53.56}, {"time": 0.5, "x": -102.31, "y": 47.18}, {"time": 0.5667, "x": -0.31, "y": 49.5}, {"time": 0.6667, "x": 32.7, "y": 52.99}]}, "ALL21": {"rotate": [{"angle": -15.5, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -28.7, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "angle": -7.81, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -28.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.81, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -15.5}]}, "yinying": {"scale": [{"x": 0.668, "y": 0.668, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.668, "y": 0.668, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 0.668, "y": 0.668}]}, "ALL23": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 20.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 20.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}}, "deform": {"default": {"arm_R": {"arm_R": [{"offset": 2, "vertices": [0.27621, -9.3475, -8.38022, -4.15045, -13.92651, -21.20211, -16.21881, -13.99316, -2.16971, 3.05441, 1.8708, 3.24583, 8.18594, 4.4002, 7.4137, -5.60425]}, {"time": 0.3333, "offset": 2, "vertices": [-27.54912, -21.34476, -32.46646, 12.66921, -49.49492, -10.01431, -39.31679, 5.98337, -12.74756, 4.46628, -2.71756, 13.2312]}, {"time": 0.6667, "offset": 2, "vertices": [0.27621, -9.3475, -8.38022, -4.15045, -13.92651, -21.20211, -16.21881, -13.99316, -2.16971, 3.05441, 1.8708, 3.24583, 8.18594, 4.4002, 7.4137, -5.60425]}]}}}}}}