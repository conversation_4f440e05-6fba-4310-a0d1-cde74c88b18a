{"skeleton": {"hash": "qxb99R3JTGXqI94JoC4as0y5Wqw", "spine": "3.8.99", "x": -61.87, "y": -18.2, "width": 122, "height": 86.8}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 83.35}, {"name": "yinying", "parent": "bone", "x": -0.22, "y": 4.04}, {"name": "body_5", "parent": "bone", "length": 23.49, "rotation": 21.61, "x": -4.28, "y": 22.42}, {"name": "body_6", "parent": "body_5", "length": 17.27, "rotation": -30.97, "x": 23.49}, {"name": "body_8", "parent": "body_5", "length": 22.57, "rotation": 164.15, "x": -2.71, "y": -0.7}, {"name": "body_7", "parent": "body_8", "length": 16.55, "rotation": 13.12, "x": 17.57, "y": 3.82}, {"name": "body_9", "parent": "body_7", "length": 13.43, "rotation": -39.55, "x": 16.55}, {"name": "body_10", "parent": "body_9", "length": 14.41, "rotation": -38.38, "x": 13.43}, {"name": "ear", "parent": "body_6", "length": 10.54, "rotation": -154.8, "x": 9.85, "y": 2.35}, {"name": "ear2", "parent": "ear", "length": 7.21, "rotation": -7.1, "x": 10.54}, {"name": "ear3", "parent": "ear2", "length": 7.87, "rotation": -15.75, "x": 7.21}, {"name": "head", "parent": "body_6", "length": 33.03, "rotation": 11.02, "x": 4.54, "y": -4.22}, {"name": "head_2", "parent": "body_6", "length": 25.83, "rotation": -0.11, "x": 9.83, "y": -6.82}, {"name": "head3", "parent": "head", "length": 12.12, "rotation": 133.72, "x": 12.45, "y": 17.36}, {"name": "head4", "parent": "head3", "length": 7.93, "rotation": 25.13, "x": 12.12}, {"name": "head5", "parent": "head4", "length": 11.95, "rotation": 8.39, "x": 7.93}, {"name": "body", "parent": "body_5", "length": 16.83, "rotation": 116.64, "x": 21.76, "y": 4.08}, {"name": "body2", "parent": "body", "length": 15.59, "rotation": 12.74, "x": 16.83}, {"name": "body_2", "parent": "body_5", "length": 14.3, "rotation": 119.13, "x": 5.95, "y": 8.89}, {"name": "body_3", "parent": "body_2", "length": 15.21, "rotation": 12.91, "x": 14.3}, {"name": "body_4", "parent": "body_8", "length": 10.4, "rotation": -35.32, "x": 11.3, "y": -8.42}, {"name": "body_11", "parent": "body_4", "length": 9.59, "rotation": 15.72, "x": 10.28, "y": -0.07}, {"name": "body_13", "parent": "body_8", "length": 9.78, "rotation": -12.1, "x": 21.75, "y": -4.32}, {"name": "body_14", "parent": "body_13", "length": 11.22, "rotation": 3.58, "x": 9.78}, {"name": "hand_R3", "parent": "body_5", "length": 16.66, "rotation": -110.09, "x": 10.5, "y": -1.58}, {"name": "hand_R2", "parent": "hand_R3", "length": 8.95, "rotation": -24.91, "x": 16.99, "y": 0.21}, {"name": "hand_R", "parent": "hand_R2", "length": 15.84, "rotation": 0.65, "x": 7.75, "y": 0.15, "transform": "noRotationOrReflection"}, {"name": "hand_L9", "parent": "body_8", "length": 17.62, "rotation": 106.44, "x": 8.56, "y": -3.44}, {"name": "hand_L7", "parent": "hand_L9", "length": 9.04, "rotation": -59.21, "x": 17.79, "y": -0.19}, {"name": "hand_R4", "parent": "hand_L7", "length": 11.99, "x": 8.97, "y": -0.47, "transform": "noRotationOrReflection"}, {"name": "hand_R9", "parent": "body_8", "length": 15.25, "rotation": 100.3, "x": 22.21, "y": -0.8}, {"name": "hand_R8", "parent": "hand_R9", "length": 8.32, "rotation": -59.98, "x": 15.25}, {"name": "hand_R7", "parent": "hand_R8", "length": 11.02, "rotation": 4.04, "x": 7.39, "y": 0.49, "transform": "noRotationOrReflection"}, {"name": "hand_L3", "parent": "body_5", "length": 17.17, "rotation": -83.47, "x": 23.8, "y": -9.35}, {"name": "hand_L", "parent": "hand_L3", "length": 8.55, "rotation": -57.08, "x": 16.61, "y": -0.1}, {"name": "hand_L4", "parent": "hand_L", "length": 14.18, "rotation": 2.49, "x": 7.01, "y": 0.96, "transform": "noRotationOrReflection"}, {"name": "hand_L6", "parent": "body_5", "length": 16.57, "rotation": -84.07, "x": 1.69, "y": -0.25}, {"name": "hand_L5", "parent": "hand_L6", "length": 8.99, "rotation": -59.97, "x": 16.4}, {"name": "hand_L10", "parent": "hand_L5", "length": 14.27, "rotation": 4.76, "x": 8.34, "y": 0.43, "transform": "noRotationOrReflection"}, {"name": "hand_L11", "parent": "body_8", "length": 15.89, "rotation": 105.83, "x": 6.46, "y": -2.3}, {"name": "hand_L8", "parent": "hand_L11", "length": 8.87, "rotation": -60.81, "x": 15.93, "y": 0.32}, {"name": "hand_L13", "parent": "hand_L8", "length": 13.63, "rotation": 4.65, "x": 8.55, "y": 0.14, "transform": "noRotationOrReflection"}, {"name": "leg01", "parent": "bone", "x": 3.43, "y": 0.35}, {"name": "leg02", "parent": "bone", "x": -14.3, "y": -0.33}, {"name": "leg03", "parent": "bone", "x": -30.39, "y": -1.24}, {"name": "leg04", "parent": "bone", "x": 26.19, "y": 1.64}, {"name": "leg05", "parent": "bone", "x": 0.21, "y": 0.78}, {"name": "leg06", "parent": "bone", "x": -12.42, "y": 1.05}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "hand_L9", "bone": "hand_L11", "attachment": "hand_L9"}, {"name": "hand_L8", "bone": "hand_L8", "attachment": "hand_L8"}, {"name": "hand_L7", "bone": "hand_L13", "attachment": "hand_L7"}, {"name": "hand_L6", "bone": "hand_L6", "attachment": "hand_L6"}, {"name": "hand_L5", "bone": "hand_L5", "attachment": "hand_L5"}, {"name": "hand_L4", "bone": "hand_L10", "attachment": "hand_L4"}, {"name": "hand_L3", "bone": "hand_L3", "attachment": "hand_L3"}, {"name": "hand_L2", "bone": "hand_L", "attachment": "hand_L2"}, {"name": "hand_L", "bone": "hand_L4", "attachment": "hand_L"}, {"name": "body_6", "bone": "body_7", "attachment": "body_6"}, {"name": "body_5", "bone": "body_8", "attachment": "body_5"}, {"name": "body_4", "bone": "body_13", "attachment": "body_4"}, {"name": "hand_R9", "bone": "hand_R9", "attachment": "hand_R9"}, {"name": "hand_R8", "bone": "hand_R8", "attachment": "hand_R8"}, {"name": "hand_R7", "bone": "hand_R7", "attachment": "hand_R7"}, {"name": "hand_R6", "bone": "hand_L9", "attachment": "hand_R6"}, {"name": "hand_R5", "bone": "hand_L7", "attachment": "hand_R5"}, {"name": "hand_R4", "bone": "hand_R4", "attachment": "hand_R4"}, {"name": "hand_R3", "bone": "hand_R3", "attachment": "hand_R3"}, {"name": "hand_R2", "bone": "hand_R2", "attachment": "hand_R2"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}, {"name": "body_3", "bone": "body_4", "attachment": "body_3"}, {"name": "body_2", "bone": "body_2", "attachment": "body_2"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "head_2", "bone": "head_2", "attachment": "head_2"}, {"name": "head", "bone": "head3", "attachment": "head"}, {"name": "eye", "bone": "bone", "color": "ffffff00", "attachment": "eye"}, {"name": "ear", "bone": "ear", "attachment": "ear"}], "ik": [{"name": "leg01", "bones": ["hand_R3", "hand_R2"], "target": "leg01", "bendPositive": false}, {"name": "leg02", "order": 1, "bones": ["hand_L9", "hand_L7"], "target": "leg02", "bendPositive": false}, {"name": "leg03", "order": 2, "bones": ["hand_R9", "hand_R8"], "target": "leg03", "bendPositive": false}, {"name": "leg04", "order": 3, "bones": ["hand_L3", "hand_L"], "target": "leg04", "bendPositive": false}, {"name": "leg05", "order": 4, "bones": ["hand_L6", "hand_L5"], "target": "leg05", "bendPositive": false}, {"name": "leg06", "order": 5, "bones": ["hand_L11", "hand_L8"], "target": "leg06", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0, 0.05806, 0.08441, 0.26937, 0.13616, 0.48068, 0.16721, 0.72218, 0.29141, 0.87312, 0.46736, 0.98524, 0.78821, 0.95937, 1, 0.78687, 1, 0.54968, 0.89516, 0.31681, 0.73646, 0.11412, 0.48806, 0.03218, 0.29141, 0, 0.13616, 0.02356, 0.25346, 0.23918, 0.45356, 0.35562, 0.63641, 0.62299, 0.75371, 0.73081], "triangles": [14, 13, 12, 1, 0, 13, 1, 13, 14, 11, 14, 12, 15, 11, 10, 15, 14, 11, 2, 1, 14, 15, 3, 2, 15, 2, 14, 4, 3, 15, 16, 15, 10, 16, 10, 9, 17, 16, 9, 8, 17, 9, 17, 8, 7, 16, 4, 15, 6, 17, 7, 5, 4, 16, 5, 16, 17, 5, 17, 6], "vertices": [1, 18, 20.53, -0.41, 1, 2, 17, 29.92, 6.93, 0.02578, 18, 14.3, 3.87, 0.97422, 2, 17, 23.87, 10.6, 0.15496, 18, 9.21, 8.78, 0.84504, 2, 17, 17.8, 15.53, 0.37877, 18, 4.37, 14.94, 0.62123, 2, 17, 10.88, 15.83, 0.65409, 18, -2.31, 16.75, 0.34591, 2, 17, 3.24, 13.82, 0.85823, 18, -10.21, 16.48, 0.14177, 2, 17, -5.79, 4.66, 0.96775, 18, -21.03, 9.53, 0.03225, 2, 17, -8.43, -5.1, 0.99998, 18, -25.76, 0.59, 2e-05, 2, 17, -3.38, -10.76, 0.987, 18, -22.08, -6.04, 0.013, 2, 17, 4.71, -13.53, 0.89065, 18, -14.8, -10.53, 0.10935, 2, 17, 13.77, -14.14, 0.59897, 18, -6.1, -13.12, 0.40103, 2, 17, 22.93, -9.48, 0.27862, 18, 3.86, -10.6, 0.72138, 2, 17, 29.48, -5.02, 0.04164, 18, 11.24, -7.68, 0.95836, 1, 18, 16.3, -4.01, 1, 1, 18, 8.85, -0.25, 1, 2, 17, 17.07, -0.84, 0.26144, 18, 0.05, -0.88, 0.73856, 2, 17, 5.91, 0.67, 0.9991, 18, -10.5, 3.06, 0.0009, 2, 17, 0.11, 0.12, 0.99999, 18, -16.28, 3.8, 1e-05], "hull": 14}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.24219, 0.04207, 0.10825, 0.1034, 0, 0.1954, 0, 0.29762, 0.10419, 0.36918, 0.12042, 0.57873, 0.19754, 0.82407, 0.34366, 1, 0.51007, 0.90584, 0.62372, 0.85473, 0.8023, 1, 0.92407, 0.8854, 1, 0.66051, 0.98495, 0.39473, 0.88754, 0.23118, 0.71301, 0.06762, 0.59125, 0.00118, 0.38425, 0, 0.26249, 0.27718, 0.46137, 0.42029, 0.69678, 0.57873, 0.83884, 0.77807], "triangles": [18, 0, 17, 1, 0, 18, 4, 3, 2, 1, 4, 2, 18, 4, 1, 19, 17, 16, 18, 17, 19, 5, 4, 18, 19, 5, 18, 19, 16, 15, 20, 19, 15, 20, 15, 14, 20, 14, 13, 20, 13, 12, 21, 20, 12, 19, 6, 5, 9, 19, 20, 9, 20, 21, 8, 6, 19, 11, 21, 12, 9, 8, 19, 7, 6, 8, 10, 9, 21, 10, 21, 11], "vertices": [2, 19, 26.17, -2.62, 0.01246, 20, 10.99, -5.21, 0.98754, 1, 20, 14.34, -1.7, 1, 2, 19, 29.93, 5.8, 0.00036, 20, 16.53, 2.16, 0.99964, 2, 19, 28.18, 7.93, 0.01675, 20, 15.31, 4.63, 0.98325, 2, 19, 24.22, 7.19, 0.1351, 20, 11.28, 4.79, 0.8649, 2, 19, 20.21, 11.22, 0.38401, 20, 8.27, 9.62, 0.61599, 2, 19, 13.99, 14.69, 0.67586, 20, 2.98, 14.39, 0.32414, 2, 19, 7.13, 15.23, 0.8857, 20, -3.58, 16.44, 0.1143, 2, 19, 4.36, 9.68, 0.96976, 20, -7.52, 11.65, 0.03024, 2, 19, 2.24, 6.16, 0.99486, 20, -10.37, 8.7, 0.00514, 1, 19, -4.94, 5.36, 1, 1, 19, -6.19, 0.34, 1, 2, 19, -4.35, -5.99, 0.99802, 20, -19.51, -1.67, 0.00198, 2, 19, 0.59, -11.22, 0.97573, 20, -15.87, -7.88, 0.02427, 2, 19, 5.95, -12.55, 0.85062, 20, -10.94, -10.37, 0.14938, 2, 19, 13.34, -12.21, 0.63819, 20, -3.66, -11.69, 0.36181, 2, 19, 17.68, -10.98, 0.3396, 20, 0.84, -11.46, 0.6604, 2, 19, 23.15, -6.55, 0.13138, 20, 7.16, -8.37, 0.86862, 2, 19, 21.62, 1.86, 0.00124, 20, 7.55, 0.18, 0.99876, 2, 19, 13.94, 0.57, 0.89184, 20, -0.22, 0.64, 0.10816, 1, 19, 5.03, -1.18, 1, 1, 19, -2.11, -0.07, 1], "hull": 18}}, "body_3": {"body_3": {"type": "mesh", "uvs": [0.27315, 0.09008, 0.11215, 0.22808, 0, 0.42608, 0.11215, 0.62408, 0.22715, 0.84608, 0.38355, 1, 0.59975, 0.86408, 0.78375, 0.77408, 0.99535, 0.59408, 0.97695, 0.33608, 0.88035, 0.13208, 0.73315, 0, 0.50775, 0.00608, 0.23175, 0.37208, 0.40195, 0.45008, 0.59055, 0.58808, 0.71475, 0.64808], "triangles": [15, 11, 10, 16, 15, 10, 9, 16, 10, 8, 16, 9, 7, 16, 8, 6, 15, 16, 6, 16, 7, 15, 5, 4, 15, 4, 14, 5, 15, 6, 13, 1, 0, 2, 1, 13, 14, 0, 12, 13, 0, 14, 15, 12, 11, 14, 12, 15, 3, 2, 13, 14, 4, 3, 14, 3, 13], "vertices": [2, 22, 6.02, -7.15, 0.97736, 21, 18.01, -5.32, 0.02264, 2, 22, 9.95, -2.91, 0.99733, 21, 20.65, -0.17, 0.00267, 2, 22, 12.13, 2.32, 0.90895, 21, 21.33, 5.45, 0.09105, 2, 22, 7.77, 5.93, 0.63578, 21, 16.15, 7.75, 0.36422, 2, 22, 3.2, 10.06, 0.30833, 21, 10.63, 10.49, 0.69167, 2, 22, -2.2, 12.38, 0.06338, 21, 4.81, 11.25, 0.93662, 2, 22, -7.75, 7.79, 0.00355, 21, 0.71, 5.33, 0.99645, 2, 22, -12.62, 4.46, 0.02399, 21, -3.07, 0.81, 0.97601, 2, 22, -17.79, -1.08, 0.09484, 21, -6.55, -5.92, 0.90516, 2, 22, -15.84, -6.71, 0.23996, 21, -3.15, -10.81, 0.76004, 2, 22, -11.9, -10.57, 0.43953, 21, 1.69, -13.46, 0.56047, 2, 22, -6.89, -12.46, 0.67938, 21, 7.03, -13.93, 0.32062, 2, 22, -0.35, -10.71, 0.86725, 21, 12.84, -10.47, 0.13275, 1, 22, 5.67, -0.55, 1, 2, 22, 0.29, -0.03, 0.70526, 21, 10.56, -0.02, 0.29474, 2, 22, -5.97, 1.7, 0.00068, 21, 4.08, -0.05, 0.99932, 2, 22, -9.92, 2.14, 0.00501, 21, 0.16, -0.69, 0.99499], "hull": 13}}, "body_4": {"body_4": {"type": "mesh", "uvs": [0.44633, 0.15652, 0.23933, 0.26546, 0, 0.32357, 0, 0.57778, 0.1544, 0.8102, 0.39325, 0.9482, 0.7064, 0.94093, 0.93463, 0.75936, 0.98771, 0.48336, 1, 0.20736, 0.84971, 0, 0.61086, 0, 0.23402, 0.48336, 0.4941, 0.51241, 0.72233, 0.54873], "triangles": [12, 2, 1, 12, 1, 13, 3, 2, 12, 4, 3, 12, 5, 12, 13, 4, 12, 5, 9, 14, 10, 14, 13, 0, 1, 0, 13, 14, 11, 10, 8, 14, 9, 14, 0, 11, 7, 14, 8, 6, 13, 14, 6, 14, 7, 5, 13, 6], "vertices": [2, 23, 11.43, -6.52, 0.44779, 24, 1.24, -6.61, 0.55221, 2, 23, 16.55, -3.86, 0.15121, 24, 6.52, -4.28, 0.84879, 2, 23, 22.62, -2.08, 0.00078, 24, 12.68, -2.88, 0.99922, 2, 23, 22.08, 2.72, 0.00121, 24, 12.45, 1.95, 0.99879, 2, 23, 17.6, 6.67, 0.0859, 24, 8.23, 6.16, 0.9141, 2, 23, 11.14, 8.59, 0.37983, 24, 1.9, 8.48, 0.62017, 2, 23, 3.07, 7.55, 0.71184, 24, -6.23, 7.95, 0.28816, 2, 23, -2.45, 3.47, 0.96048, 24, -11.99, 4.22, 0.03952, 2, 23, -3.24, -1.9, 0.99988, 24, -13.12, -1.08, 0.00012, 2, 23, -2.98, -7.15, 0.99889, 24, -13.18, -6.33, 0.00111, 2, 23, 1.34, -10.63, 0.96213, 24, -9.09, -10.08, 0.03787, 2, 23, 7.51, -9.94, 0.77923, 24, -2.89, -9.78, 0.22077, 1, 24, 6.46, -0.14, 1, 1, 23, 9.45, 0.07, 1, 2, 23, 3.48, 0.1, 0.99973, 24, -6.28, 0.49, 0.00027], "hull": 12}}, "body_5": {"body_5": {"type": "mesh", "uvs": [0.22774, 0.02113, 0.15577, 0.09976, 0.09356, 0.17607, 0.03623, 0.25701, 0, 0.3842, 0, 0.54144, 0.01671, 0.67557, 0.05697, 0.77501, 0.10942, 0.85132, 0.18992, 0.91838, 0.28019, 0.97851, 0.38631, 1, 0.50706, 0.99701, 0.61438, 0.97612, 0.70342, 0.90906, 0.77539, 0.83968, 0.8376, 0.76337, 0.91566, 0.73562, 0.98885, 0.67781, 1, 0.57375, 1, 0.44193, 0.98763, 0.31475, 0.93396, 0.21531, 0.88883, 0.15981, 0.8071, 0.0835, 0.69244, 0.028, 0.58876, 0, 0.52899, 0, 0.43263, 0, 0.34725, 0, 0.2765, 0, 0.08038, 0.5853, 0.24993, 0.60612, 0.35727, 0.5853, 0.45241, 0.48818, 0.52682, 0.42112, 0.61098, 0.35868, 0.74638, 0.41649, 0.89031, 0.45812], "triangles": [12, 11, 33, 11, 10, 33, 9, 32, 10, 10, 32, 33, 9, 8, 32, 7, 31, 8, 8, 31, 32, 7, 6, 31, 6, 5, 31, 32, 30, 33, 31, 2, 32, 2, 1, 32, 1, 0, 32, 32, 0, 30, 31, 4, 3, 31, 3, 2, 31, 5, 4, 14, 37, 15, 14, 36, 37, 15, 37, 16, 16, 38, 17, 16, 37, 38, 18, 17, 19, 19, 17, 38, 38, 20, 19, 37, 23, 38, 38, 21, 20, 38, 22, 21, 38, 23, 22, 36, 25, 37, 37, 24, 23, 37, 25, 24, 36, 26, 25, 33, 34, 12, 12, 34, 13, 34, 35, 13, 13, 35, 14, 35, 36, 14, 30, 29, 33, 34, 29, 28, 34, 33, 29, 34, 28, 35, 35, 27, 36, 27, 26, 36, 35, 28, 27], "vertices": [2, 3, -2.69, 28.13, 0.61076, 5, 7.85, -27.74, 0.38924, 2, 3, -10.17, 27.04, 0.44439, 5, 14.75, -24.64, 0.55561, 2, 3, -16.78, 25.72, 0.27124, 5, 20.75, -21.57, 0.72876, 2, 3, -23.06, 24.03, 0.14422, 5, 26.33, -18.23, 0.85578, 2, 3, -28.37, 19.57, 0.06514, 5, 30.22, -12.48, 0.93486, 2, 3, -31.16, 12.55, 0.02245, 5, 30.98, -4.97, 0.97755, 2, 3, -32.11, 6, 0.00392, 5, 30.11, 1.58, 0.99608, 2, 3, -30.47, 0.22, 2e-05, 5, 26.95, 6.7, 0.99998, 1, 5, 22.57, 10.82, 1, 3, 3, -21.75, -10.64, 0.00875, 4, -33.32, -32.4, 0.00184, 5, 15.6, 14.76, 0.98941, 3, 3, -15.18, -16.35, 0.07298, 4, -24.74, -33.91, 0.0173, 5, 7.72, 18.46, 0.90972, 3, 3, -6.58, -20.86, 0.21024, 4, -15.05, -33.36, 0.08094, 5, -1.79, 20.46, 0.70882, 3, 3, 3.69, -24.78, 0.33747, 4, -4.23, -31.43, 0.21712, 5, -12.74, 21.42, 0.44542, 3, 3, 13.14, -27.44, 0.36569, 4, 5.24, -28.86, 0.41836, 5, -22.55, 21.4, 0.21594, 3, 3, 21.86, -27.43, 0.2753, 4, 12.72, -24.36, 0.63258, 5, -30.94, 19.01, 0.09211, 3, 3, 29.17, -26.75, 0.15412, 4, 18.64, -20.01, 0.81115, 5, -37.79, 16.35, 0.03472, 3, 3, 35.78, -25.43, 0.06333, 4, 23.63, -15.48, 0.92603, 5, -43.79, 13.28, 0.01064, 3, 3, 42.88, -26.81, 0.01647, 4, 30.42, -13.01, 0.98149, 5, -50.99, 12.67, 0.00204, 3, 3, 50.09, -26.68, 0.00168, 4, 36.54, -9.19, 0.99824, 5, -57.9, 10.57, 8e-05, 2, 3, 52.87, -22.41, 0, 4, 36.73, -4.1, 1, 1, 4, 35.7, 2.15, 1, 1, 4, 33.6, 7.99, 1, 1, 4, 28.01, 11.9, 1, 2, 3, 50.79, -0.21, 0.00398, 4, 23.52, 13.86, 0.99602, 2, 3, 45.22, 5.93, 0.07718, 4, 15.59, 16.27, 0.92282, 2, 3, 36.5, 12.25, 0.29501, 4, 4.86, 17.2, 0.70499, 3, 3, 28.23, 16.98, 0.57552, 4, -4.67, 16.99, 0.42443, 5, -24.94, -25.45, 5e-05, 3, 3, 23.17, 18.98, 0.82322, 4, -10.04, 16.11, 0.16849, 5, -19.53, -26, 0.00829, 3, 3, 15.02, 22.21, 0.89832, 4, -18.69, 14.69, 0.05299, 5, -10.8, -26.88, 0.04869, 3, 3, 7.8, 25.07, 0.86318, 4, -26.36, 13.42, 0.00419, 5, -3.07, -27.66, 0.13263, 2, 3, 1.81, 27.44, 0.75815, 5, 3.33, -28.31, 0.24185, 2, 3, -25.13, 7.9, 0.00493, 5, 23.91, -2.15, 0.99507, 1, 5, 8.66, 0.4, 1, 3, 3, -1.71, -1.38, 0.19616, 4, -20.89, -14.15, 0.00064, 5, -1.16, 0.38, 0.8032, 3, 3, 8.06, -0.24, 0.99829, 4, -13.1, -8.14, 0.00055, 5, -10.24, -3.38, 0.00116, 1, 3, 15.54, 0.26, 1, 2, 3, 23.77, 0.22, 0.39349, 4, 0.12, 0.34, 0.60651, 3, 3, 34.2, -6.89, 0.00066, 4, 12.73, -0.4, 0.99929, 5, -37.2, -4.12, 6e-05, 1, 4, 25.98, -0.24, 1], "hull": 31}}, "body_6": {"body_6": {"type": "mesh", "uvs": [0.04313, 0, 0, 0.19778, 0.00611, 0.4262, 0.07342, 0.64985, 0.19123, 0.82592, 0.40327, 0.93537, 0.61196, 1, 0.82737, 0.94964, 0.96874, 0.83068, 1, 0.63558, 0.96874, 0.4997, 0.94854, 0.41192, 0.86103, 0.29296, 0.71966, 0.22158, 0.5581, 0.27392, 0.4302, 0.27868, 0.28547, 0.22634, 0.14747, 0.07882, 0.11045, 0, 0.10708, 0.23109, 0.17103, 0.35482, 0.26191, 0.52613, 0.39654, 0.58799, 0.53454, 0.64509, 0.78698, 0.6213], "triangles": [3, 20, 21, 3, 2, 20, 21, 20, 16, 2, 19, 20, 2, 1, 19, 20, 19, 16, 19, 1, 17, 19, 17, 16, 17, 0, 18, 0, 17, 1, 5, 23, 6, 5, 4, 22, 4, 21, 22, 5, 22, 23, 4, 3, 21, 23, 22, 14, 14, 22, 15, 22, 21, 15, 21, 16, 15, 6, 23, 24, 6, 24, 7, 24, 14, 13, 14, 24, 23, 7, 24, 8, 8, 24, 9, 24, 10, 9, 24, 11, 10, 24, 12, 11, 24, 13, 12], "vertices": [1, 8, 17.1, -0.66, 1, 1, 8, 13.1, 3.81, 1, 2, 7, 23.49, 0.96, 0.07731, 8, 7.29, 7, 0.92269, 2, 7, 18.62, 6.06, 0.35822, 8, 0.31, 7.97, 0.64178, 3, 6, 31.84, -0.79, 0.02594, 7, 12.3, 9.13, 0.66562, 8, -6.56, 6.45, 0.30845, 3, 6, 24.64, 5.03, 0.27557, 7, 3.04, 9.03, 0.67201, 8, -13.75, 0.63, 0.05242, 2, 6, 17.16, 9.57, 0.60866, 7, -5.63, 7.77, 0.39134, 2, 6, 8.33, 11.05, 0.91606, 7, -13.37, 3.29, 0.08394, 2, 6, 1.73, 9.66, 0.99976, 7, -17.58, -1.99, 0.00024, 1, 6, -1.32, 4.72, 1, 1, 6, -1.38, 0.58, 1, 2, 6, -1.42, -2.1, 0.99807, 7, -12.52, -13.06, 0.00193, 3, 6, 0.86, -6.52, 0.97304, 7, -7.95, -15.02, 0.02644, 8, -7.43, -25.05, 0.00052, 3, 6, 5.67, -10.36, 0.8367, 7, -1.79, -14.91, 0.14552, 8, -2.68, -21.14, 0.01778, 3, 6, 12.43, -11.06, 0.5674, 7, 3.87, -11.15, 0.32266, 8, -0.57, -14.68, 0.10994, 3, 6, 17.43, -12.63, 0.26333, 7, 8.73, -9.18, 0.34591, 8, 2.01, -10.11, 0.39076, 3, 6, 22.56, -15.99, 0.06633, 7, 14.82, -8.5, 0.22682, 8, 6.36, -5.8, 0.70684, 3, 6, 26.53, -21.87, 0.00424, 7, 21.62, -10.51, 0.04775, 8, 12.94, -3.15, 0.94801, 1, 8, 15.68, -3.03, 1, 1, 8, 10.01, 0.54, 1, 1, 8, 5.58, 0.14, 1, 1, 7, 12.65, -0.03, 1, 3, 6, 21.64, -4.59, 0.00259, 7, 6.85, -0.3, 0.99521, 8, -4.97, -4.31, 0.00219, 3, 6, 16.83, -1.19, 0.21721, 7, 0.98, -0.74, 0.78249, 8, -9.3, -8.31, 0.00031, 1, 6, 6.81, 1.5, 1], "hull": 19}}, "ear": {"ear": {"type": "mesh", "uvs": [0, 0.00548, 0.06972, 0.16439, 0.15997, 0.27033, 0.18371, 0.397, 0.14334, 0.46609, 0.04834, 0.57893, 0.04834, 0.62269, 0.14809, 0.63881, 0.24309, 0.73554, 0.28821, 0.84378, 0.21459, 0.95202, 0.35709, 1, 0.53046, 0.98196, 0.71096, 0.92208, 0.88434, 0.8576, 1, 0.73324, 0.97221, 0.62499, 0.88909, 0.45687, 0.78696, 0.26803, 0.60646, 0.21966, 0.44734, 0.15978, 0.31434, 0.10912, 0.15759, 0.02391, 0.04597, 0, 0.25259, 0.58354, 0.46159, 0.56972, 0.65872, 0.56281, 0.83447, 0.56281, 0.67059, 0.42694, 0.46159, 0.3256, 0.18847, 0.15978, 0.45209, 0.91287, 0.64447, 0.82306, 0.83922, 0.68948], "triangles": [25, 8, 24, 8, 7, 24, 4, 24, 7, 7, 6, 5, 7, 5, 4, 4, 3, 24, 29, 24, 3, 30, 29, 2, 29, 30, 21, 29, 3, 2, 2, 1, 30, 0, 23, 1, 1, 22, 30, 1, 23, 22, 30, 22, 21, 11, 31, 12, 10, 9, 11, 11, 9, 31, 12, 31, 32, 32, 31, 25, 9, 8, 25, 31, 9, 25, 25, 24, 29, 20, 29, 21, 28, 25, 29, 12, 32, 13, 14, 32, 33, 14, 13, 32, 33, 26, 27, 33, 32, 26, 14, 33, 15, 32, 25, 26, 33, 16, 15, 33, 27, 16, 27, 17, 16, 26, 25, 28, 17, 27, 28, 27, 26, 28, 28, 18, 17, 29, 19, 28, 28, 19, 18, 29, 20, 19], "vertices": [3, 9, 22.21, -22.55, 0.00573, 10, 14.37, -20.93, 0.39485, 11, 12.57, -18.2, 0.59941, 3, 9, 21.5, -16.89, 0.02134, 10, 12.96, -15.41, 0.3871, 11, 9.72, -13.27, 0.59156, 3, 9, 19.67, -12.74, 0.02987, 10, 10.64, -11.52, 0.33773, 11, 6.43, -10.15, 0.6324, 3, 9, 20.08, -8.51, 0.02539, 10, 10.52, -7.27, 0.23182, 11, 5.16, -6.1, 0.74279, 3, 9, 21.95, -6.67, 0.00951, 10, 12.15, -5.21, 0.1076, 11, 6.17, -3.68, 0.88289, 3, 9, 25.89, -3.92, 0.00098, 10, 15.72, -1.99, 0.02503, 11, 8.73, 0.39, 0.97399, 2, 10, 15.94, -0.57, 2e-05, 11, 8.55, 1.82, 0.99998, 3, 9, 23.36, -1.15, 0.00078, 10, 12.86, 0.44, 0.11265, 11, 5.32, 1.96, 0.88657, 3, 9, 21.3, 2.75, 0.01228, 10, 10.34, 4.06, 0.35114, 11, 1.91, 4.76, 0.63658, 3, 9, 20.89, 6.58, 0.02289, 10, 9.46, 7.81, 0.61303, 11, 0.05, 8.13, 0.36408, 3, 9, 24.13, 9.38, 0.04873, 10, 12.33, 10.98, 0.75966, 11, 1.95, 11.96, 0.19161, 3, 9, 20.18, 12.14, 0.12904, 10, 8.06, 13.24, 0.74443, 11, -2.77, 12.98, 0.12653, 3, 9, 14.68, 13.09, 0.33194, 10, 2.49, 13.5, 0.60105, 11, -8.2, 11.71, 0.06701, 3, 9, 8.58, 12.76, 0.60519, 10, -3.52, 12.42, 0.37524, 11, -13.7, 9.05, 0.01956, 3, 9, 2.66, 12.23, 0.84162, 10, -9.33, 11.16, 0.1571, 11, -18.94, 6.26, 0.00129, 2, 9, -2.02, 9.29, 0.96081, 10, -13.61, 7.67, 0.03919, 2, 9, -2.14, 5.61, 0.99427, 10, -13.27, 4, 0.00573, 3, 9, -1.09, -0.45, 0.98916, 10, -11.49, -1.88, 0.00744, 11, -17.48, -6.89, 0.00339, 3, 9, 0.35, -7.34, 0.8924, 10, -9.2, -8.54, 0.0708, 11, -13.48, -12.67, 0.0368, 3, 9, 5.47, -10.45, 0.6652, 10, -3.74, -11, 0.19354, 11, -7.55, -13.55, 0.14127, 3, 9, 9.83, -13.74, 0.37977, 10, 0.99, -13.72, 0.31869, 11, -2.26, -14.89, 0.30154, 3, 9, 13.47, -16.51, 0.14892, 10, 4.95, -16.02, 0.38668, 11, 2.17, -16.04, 0.4644, 3, 9, 17.52, -20.59, 0.04307, 10, 9.48, -19.57, 0.3959, 11, 7.49, -18.22, 0.56102, 3, 9, 20.75, -22.32, 0.00538, 10, 12.89, -20.89, 0.39588, 11, 11.13, -18.56, 0.59875, 2, 10, 9.28, -0.85, 0.00475, 11, 2.22, -0.26, 0.99525, 2, 10, 2.6, -0.28, 0.99812, 11, -4.36, -1.52, 0.00188, 2, 9, 6.95, 0.9, 0.99643, 10, -3.67, 0.45, 0.00357, 2, 9, 1.54, 2.44, 0.99961, 10, -9.23, 1.31, 0.00039, 3, 9, 5.36, -3.31, 0.96918, 10, -4.73, -3.92, 0.02151, 11, -10.42, -7.02, 0.00931, 3, 9, 10.88, -8.35, 0.3902, 10, 1.37, -8.25, 0.40229, 11, -3.38, -9.52, 0.20752, 3, 9, 17.8, -16, 0.04351, 10, 9.18, -14.98, 0.39199, 11, 5.97, -13.89, 0.56451, 3, 9, 16.47, 10.21, 0.17282, 10, 4.62, 10.86, 0.73397, 11, -5.44, 9.75, 0.09322, 3, 9, 9.74, 9.04, 0.58884, 10, -1.91, 8.87, 0.40554, 11, -11.19, 6.06, 0.00563, 2, 9, 2.54, 6.5, 0.96377, 10, -8.74, 5.46, 0.03623], "hull": 24}}, "eye": {"eye": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [42.13, 24.6, 25.13, 24.6, 25.13, 37.6, 42.13, 37.6], "hull": 4}}, "hand_L": {"hand_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.52, -3.28, -7.46, -2.28, -6.94, 9.71, 16.04, 8.71], "hull": 4}}, "hand_L2": {"hand_L2": {"type": "mesh", "uvs": [0.42353, 0.18119, 0.24503, 0.24119, 0.12603, 0.31519, 0.03083, 0.40319, 0, 0.52919, 0.01553, 0.69519, 0.09373, 0.82519, 0.24673, 0.93719, 0.44053, 1, 0.67853, 0.95719, 0.86383, 0.85319, 0.97603, 0.65719, 1, 0.45919, 1, 0.26519, 0.97773, 0.10319, 0.90633, 0, 0.67683, 0.03319, 0.55103, 0.12119, 0.4125, 0.32315, 0.56752, 0.52039, 0.5119, 0.66265, 0.44674, 0.88374, 0.78068, 0.78758, 0.81845, 0.49833, 0.74163, 0.16723, 0.15943, 0.36941, 0.21706, 0.69187], "triangles": [8, 21, 9, 8, 7, 21, 9, 22, 10, 22, 9, 20, 9, 21, 20, 10, 22, 11, 22, 23, 11, 6, 26, 7, 7, 26, 21, 21, 26, 20, 6, 5, 26, 5, 4, 26, 26, 4, 25, 26, 18, 20, 20, 19, 22, 22, 19, 23, 4, 3, 25, 18, 25, 1, 18, 26, 25, 20, 18, 19, 11, 23, 12, 19, 18, 17, 18, 0, 17, 19, 24, 23, 19, 17, 24, 12, 23, 13, 23, 24, 13, 3, 2, 25, 25, 2, 1, 18, 1, 0, 24, 14, 13, 17, 16, 24, 24, 15, 14, 24, 16, 15], "vertices": [3, 34, 10.62, 1.57, 0.9645, 35, -3.83, -4.9, 0.03295, 36, 1.4, 12.24, 0.00256, 2, 34, 9.19, -1.85, 0.86442, 35, -1.21, -7.53, 0.13558, 2, 34, 8.69, -4.5, 0.71249, 35, 1.04, -9, 0.28751, 2, 34, 8.66, -6.92, 0.55476, 35, 3.27, -9.95, 0.44524, 2, 34, 9.95, -8.74, 0.41626, 35, 5.45, -9.45, 0.58374, 2, 34, 12.35, -10.26, 0.28531, 35, 7.77, -7.81, 0.71469, 2, 34, 15.05, -10.42, 0.15045, 35, 8.94, -5.37, 0.84955, 3, 34, 18.45, -9.22, 0.05826, 35, 9.13, -1.77, 0.61439, 36, -3.45, -0.17, 0.32734, 3, 34, 21.71, -6.86, 0.00783, 35, 8.19, 2.14, 0.33149, 36, 0.29, -1.64, 0.66068, 2, 35, 5.25, 5.95, 0.00599, 36, 5.1, -1.41, 0.99401, 3, 34, 25.05, 1.31, 0.1224, 35, 1.91, 8.34, 0, 36, 8.97, -0.04, 0.8776, 3, 34, 23.85, 5.14, 0.33619, 35, -2.09, 8.69, 0, 36, 11.55, 3.04, 0.66381, 3, 34, 21.52, 7.62, 0.60783, 35, -5.27, 7.48, 0, 36, 12.38, 6.34, 0.39216, 3, 34, 18.94, 9.68, 0.78156, 35, -8.16, 5.88, 1e-05, 36, 12.72, 9.62, 0.21844, 3, 34, 16.51, 11.05, 0.87261, 35, -10.35, 4.16, 1e-05, 36, 12.56, 12.4, 0.12738, 3, 34, 14.25, 11.03, 0.92256, 35, -11.2, 2.06, 2e-05, 36, 11.33, 14.3, 0.07743, 3, 34, 11.82, 7.1, 0.95719, 35, -8.48, -1.68, 2e-05, 36, 6.7, 14.21, 0.04278, 3, 34, 11.42, 4.2, 0.98567, 35, -5.96, -3.16, 4e-05, 36, 4.05, 12.99, 0.01429, 2, 34, 12.37, -0.11, 0.99725, 35, -1.61, -3.92, 0.00275, 2, 34, 16.93, 0.22, 0.99694, 36, 3.67, 6.21, 0.00306, 3, 34, 18.12, -2.16, 0.0531, 35, 2.48, 0.61, 0.8928, 36, 2.31, 3.92, 0.0541, 2, 35, 6.4, 1.29, 0.10265, 36, 0.62, 0.31, 0.89735, 3, 34, 23.14, 0.71, 0.08708, 35, 1.74, 6.34, 0.00174, 36, 7.43, 1.24, 0.91119, 3, 34, 19.77, 4.37, 0.66479, 35, -2.93, 4.62, 0, 36, 8.7, 6.06, 0.33521, 3, 34, 14.41, 6.69, 0.93888, 35, -7.11, 0.55, 2e-05, 36, 7.75, 11.81, 0.0611, 2, 34, 9.82, -4.55, 0.67908, 35, 1.53, -7.97, 0.32092, 2, 34, 14.82, -7.08, 0.17869, 35, 5.77, -4.31, 0.82131], "hull": 18}}, "hand_L3": {"hand_L3": {"type": "mesh", "uvs": [0.24944, 0.06578, 0.08334, 0.19817, 0.03984, 0.38352, 0.02798, 0.57265, 0.05962, 0.79961, 0.18221, 0.95469, 0.45903, 1, 0.66071, 0.932, 0.85843, 0.79204, 0.99684, 0.59535, 0.95334, 0.39865, 0.88216, 0.20952, 0.74375, 0.05822, 0.42343, 0, 0.39445, 0.22539, 0.45374, 0.4458, 0.537, 0.63473, 0.59967, 0.82885], "triangles": [14, 0, 13, 14, 13, 12, 11, 15, 14, 11, 14, 12, 15, 11, 10, 1, 0, 14, 16, 15, 10, 16, 10, 9, 8, 16, 9, 3, 2, 15, 14, 2, 1, 15, 2, 14, 16, 3, 15, 16, 4, 3, 17, 16, 8, 5, 4, 16, 7, 17, 8, 16, 6, 5, 17, 6, 16, 6, 17, 7], "vertices": [2, 34, -2.13, 1.51, 0.99935, 35, -8.63, -16.71, 0.00065, 2, 34, -1.17, -3.15, 0.98487, 35, -3.96, -17.6, 0.01513, 2, 34, 2.14, -6, 0.9197, 35, -0.06, -15.63, 0.0803, 2, 34, 5.85, -8.28, 0.7457, 35, 3.46, -13.07, 0.2543, 2, 34, 10.78, -10.13, 0.49836, 35, 7.05, -9.21, 0.50164, 2, 34, 15.2, -9.44, 0.2303, 35, 8.09, -4.86, 0.7697, 2, 34, 18.99, -4.56, 0.3522, 35, 5.03, 0.5, 0.6478, 2, 34, 19.7, 0.09, 0.61401, 35, 1, 2.93, 0.38599, 2, 34, 18.92, 5.45, 0.94725, 35, -4.25, 4.25, 0.05275, 1, 34, 16.36, 10.27, 1, 1, 34, 11.92, 11.56, 1, 1, 34, 7.35, 12.23, 1, 1, 34, 2.84, 11.18, 1, 1, 34, -1.66, 5.6, 1, 1, 34, 2.61, 2.59, 1, 1, 34, 7.7, 1.35, 1, 1, 34, 12.39, 0.92, 1, 1, 34, 16.98, 0.03, 1], "hull": 14}}, "hand_L4": {"hand_L4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [17.01, -3.83, -5.91, -1.92, -4.91, 10.03, 18.01, 8.12], "hull": 4}}, "hand_L5": {"hand_L5": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [2.41, 12.39, 13.13, -4.49, -1.21, -13.61, -11.94, 3.27], "hull": 4}}, "hand_L6": {"hand_L6": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [24.78, 5.97, 14.61, -13.54, -5.79, -2.91, 4.38, 16.6], "hull": 4}}, "hand_L7": {"hand_L7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [16.21, -3.74, -6.71, -1.87, -5.74, 10.09, 17.19, 8.22], "hull": 4}}, "hand_L8": {"hand_L8": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1.13, 11.47, 14.4, -4.8, 1.23, -15.55, -12.04, 0.72], "hull": 4}}, "hand_L9": {"hand_L9": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [22.4, 5.47, 14.31, -14.98, -7.08, -6.52, 1.02, 13.94], "hull": 4}}, "hand_R": {"hand_R": {"type": "mesh", "uvs": [0.49527, 0.08623, 0.29928, 0.07732, 0.1464, 0.0595, 0, 0.01941, 0.05232, 0.41587, 0.12092, 0.70096, 0.20324, 0.97268, 0.40904, 0.98159, 0.64423, 0.97714, 0.88727, 0.9415, 1, 0.78559, 1, 0.52723, 0.90883, 0.28223, 0.84611, 0.03723, 0.75203, 0, 0.61091, 0.1085, 0.46392, 0.28224, 0.48352, 0.56287, 0.48352, 0.85242], "triangles": [15, 17, 16, 9, 12, 11, 8, 18, 17, 13, 12, 14, 10, 9, 11, 7, 6, 17, 12, 8, 15, 8, 17, 15, 14, 12, 15, 9, 8, 12, 18, 7, 17, 7, 18, 8, 16, 1, 0, 4, 3, 2, 15, 16, 0, 1, 5, 4, 2, 1, 4, 1, 17, 5, 17, 1, 16, 17, 6, 5], "vertices": [2, 26, -0.35, 1.85, 0.65855, 27, 5.78, 5.93, 0.34145, 2, 26, 2.15, -2.36, 0.90289, 27, 0.89, 6.08, 0.09711, 1, 26, 4, -5.71, 1, 1, 26, 5.55, -9.06, 1, 1, 26, 8.57, -5.65, 1, 2, 26, 10.33, -2.53, 0.94183, 27, -3.65, -0.73, 0.05817, 2, 26, 11.78, 0.79, 0.61496, 27, -1.63, -3.74, 0.38504, 2, 26, 9.15, 5.21, 0.28163, 27, 3.52, -3.9, 0.71837, 2, 26, 6.01, 10.18, 0.00646, 27, 9.4, -3.91, 0.99354, 1, 27, 15.48, -3.59, 1, 1, 27, 18.31, -1.91, 1, 2, 26, -2.89, 15.13, 0.00202, 27, 18.35, 0.93, 0.99798, 2, 26, -3.97, 11.77, 0.01177, 27, 16.1, 3.65, 0.98823, 2, 26, -5.44, 9.02, 0.03218, 27, 14.56, 6.37, 0.96782, 2, 26, -4.55, 6.8, 0.11915, 27, 12.21, 6.8, 0.88085, 2, 26, -1.67, 4.44, 0.34563, 27, 8.67, 5.65, 0.65437, 2, 26, 1.89, 2.32, 0.65599, 27, 4.98, 3.78, 0.34401, 2, 26, 4.26, 4.37, 0.0571, 27, 5.43, 0.69, 0.9429, 1, 27, 5.4, -2.5, 1], "hull": 16}}, "hand_R2": {"hand_R2": {"type": "mesh", "uvs": [0.56052, 0.16611, 0.36552, 0.14917, 0.11352, 0.04752, 0, 0, 0.02952, 0.29317, 0.08352, 0.61928, 0.25752, 0.88187, 0.58152, 0.98775, 0.84252, 0.90305, 1, 0.61505, 0.98652, 0.28893, 0.82152, 0.08988, 0.4646, 0.30261, 0.51137, 0.54543, 0.58196, 0.69762], "triangles": [14, 11, 10, 14, 10, 9, 8, 14, 9, 7, 6, 14, 7, 14, 8, 11, 13, 0, 14, 13, 11, 6, 5, 13, 6, 13, 14, 4, 3, 2, 12, 1, 0, 13, 12, 0, 1, 5, 4, 1, 4, 2, 5, 1, 12, 5, 12, 13], "vertices": [3, 25, 13.66, 3.01, 0.47043, 26, -4.35, 0.14, 0.35055, 27, 6.49, 10.22, 0.17902, 3, 25, 12.18, -1.44, 0.80252, 26, -2.76, -4.27, 0.1631, 27, 1.91, 11.25, 0.03438, 2, 25, 8.97, -6.85, 0.99961, 26, -1.94, -10.51, 0.00039, 2, 25, 7.5, -9.28, 0.9654, 26, -1.6, -13.33, 0.0346, 2, 25, 12.5, -9.87, 0.79015, 26, 2.69, -10.7, 0.20985, 2, 25, 18.19, -10.03, 0.4675, 26, 7.26, -7.31, 0.5325, 3, 25, 23.57, -7.13, 0.16838, 26, 9.7, -1.71, 0.49829, 27, -2.62, -0.64, 0.33333, 3, 25, 27.29, -0.07, 0.01068, 26, 8.27, 6.14, 0.32265, 27, 4.77, -3.65, 0.66667, 2, 26, 4.46, 11.32, 0.01167, 27, 11.19, -3.22, 0.98833, 2, 26, -1.53, 12.85, 0.12601, 27, 15.69, 1.02, 0.87399, 3, 25, 18.28, 12.36, 0.00086, 26, -6.49, 10.35, 0.31385, 27, 16.25, 6.54, 0.68529, 3, 25, 14, 9.4, 0.1371, 26, -8.03, 5.37, 0.46489, 27, 12.88, 10.51, 0.39801, 3, 25, 15.31, 0.19, 0.9432, 26, -1.31, -1.06, 0.05518, 27, 3.85, 8.29, 0.00163, 2, 26, 2.04, 1.61, 0.82076, 27, 4.3, 4.04, 0.17924, 2, 26, 3.74, 4.19, 0.15779, 27, 5.56, 1.22, 0.84221], "hull": 12}}, "hand_R3": {"hand_R3": {"type": "mesh", "uvs": [0.17951, 0.64589, 0.15894, 0.77679, 0.23608, 0.95788, 0.50865, 1, 0.81208, 0.98407, 0.96637, 0.7877, 1, 0.56952, 1, 0.41461, 0.92008, 0.28152, 0.75808, 0.19207, 0.64751, 0.18553, 0.52408, 0.24007, 0.39294, 0.11134, 0.25666, 0.01534, 0.12809, 0, 0.1358, 0.11134, 0.17437, 0.23789, 0.04837, 0.23571, 0, 0.32734, 0.06637, 0.4168, 0.14094, 0.45389, 0.03294, 0.5128, 0.09209, 0.59352, 0.41412, 0.41258, 0.51393, 0.57733, 0.53901, 0.74114, 0.58717, 0.88032], "triangles": [26, 25, 5, 2, 25, 26, 4, 26, 5, 3, 2, 26, 3, 26, 4, 15, 14, 13, 12, 16, 15, 12, 15, 13, 11, 16, 12, 23, 16, 11, 16, 18, 17, 20, 19, 16, 19, 18, 16, 23, 20, 16, 7, 24, 8, 24, 23, 11, 22, 21, 20, 0, 20, 23, 0, 23, 24, 22, 20, 0, 11, 9, 24, 25, 24, 7, 0, 24, 25, 10, 9, 11, 8, 24, 9, 6, 25, 7, 1, 0, 25, 5, 25, 6, 2, 1, 25], "vertices": [2, 25, 10.26, -10.19, 0.89476, 26, 1.13, -12.33, 0.10524, 2, 25, 14.57, -10.88, 0.66484, 26, 4.94, -10.22, 0.33516, 2, 25, 20.6, -8.88, 0.34913, 26, 8.45, -4.92, 0.65087, 2, 25, 22.19, -1.28, 0.10436, 26, 5.01, 2.03, 0.89564, 2, 25, 21.89, 7.22, 0.11933, 26, -0.48, 8.54, 0.88067, 2, 25, 15.53, 11.71, 0.36785, 26, -8.26, 8.14, 0.63215, 2, 25, 8.36, 12.85, 0.66294, 26, -14.6, 4.6, 0.33706, 2, 25, 3.25, 12.98, 0.86711, 26, -18.7, 1.55, 0.13289, 2, 25, -1.2, 10.86, 0.9499, 26, -20.89, -2.87, 0.0501, 2, 25, -4.27, 6.41, 0.98481, 26, -20.55, -8.27, 0.01519, 2, 25, -4.57, 3.32, 0.99777, 26, -18.88, -10.88, 0.00223, 2, 25, -2.87, -0.18, 0.99979, 26, -15.37, -12.58, 0.00021, 1, 25, -7.21, -3.74, 1, 1, 25, -10.48, -7.47, 1, 1, 25, -11.08, -11.06, 1, 1, 25, -7.4, -10.94, 1, 1, 25, -3.2, -9.97, 1, 1, 25, -3.36, -13.5, 1, 1, 25, -0.38, -14.93, 1, 1, 25, 2.62, -13.15, 1, 1, 25, 3.9, -11.1, 1, 2, 25, 5.77, -14.17, 0.9978, 26, 0.06, -18.25, 0.0022, 2, 25, 8.47, -12.59, 0.98018, 26, 1.21, -15.33, 0.01982, 1, 25, 2.74, -3.41, 1, 1, 25, 8.25, -0.77, 1, 1, 25, 13.68, -0.21, 1, 1, 26, 0.53, 1.44, 1], "hull": 23}}, "hand_R4": {"hand_R4": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.76, -3.32, -4.24, -3.32, -4.24, 6.68, 15.76, 6.68], "hull": 4}}, "hand_R5": {"hand_R5": {"type": "mesh", "uvs": [0.47467, 0.15453, 0.29372, 0.0772, 0.09753, 0, 0.00039, 0, 0.04992, 0.30119, 0.10134, 0.54386, 0.17753, 0.80519, 0.37372, 0.97318, 0.62134, 0.97852, 0.78324, 0.91185, 0.928, 0.80252, 1, 0.55186, 1, 0.30919, 0.92991, 0.07186, 0.76419, 0.11986, 0.59467, 0.17319, 0.48822, 0.28003, 0.5338, 0.51135, 0.60179, 0.68367], "triangles": [7, 18, 8, 8, 18, 9, 9, 18, 10, 10, 18, 11, 18, 14, 11, 5, 4, 1, 4, 2, 1, 4, 3, 2, 18, 7, 17, 7, 6, 17, 17, 5, 16, 0, 16, 1, 1, 16, 5, 6, 5, 17, 18, 17, 14, 14, 17, 15, 11, 14, 12, 14, 13, 12, 17, 16, 15, 16, 0, 15], "vertices": [2, 29, -0.15, -3.92, 0.84642, 30, 2.83, 9.34, 0.15358, 2, 29, 1.21, -7.65, 0.65632, 30, -0.96, 10.54, 0.34368, 2, 29, 2.76, -11.64, 0.48737, 30, -5.06, 11.74, 0.51263, 2, 29, 3.99, -13.27, 0.40154, 30, -7.1, 11.76, 0.59846, 2, 29, 6.97, -9.72, 0.31343, 30, -6.11, 7.23, 0.68657, 2, 29, 9.23, -6.67, 0.18415, 30, -5.07, 3.58, 0.81585, 2, 29, 11.4, -3.03, 0.36458, 30, -3.51, -0.36, 0.63542, 2, 29, 10.93, 1.78, 0.31023, 30, 0.58, -2.92, 0.68977, 2, 29, 7.86, 5.98, 0.30177, 30, 5.78, -3.06, 0.69823, 2, 29, 5.02, 8.09, 0.00057, 30, 9.19, -2.09, 0.99943, 2, 29, 1.88, 9.53, 0.08533, 30, 12.25, -0.49, 0.91467, 2, 29, -2.03, 8.47, 0.27378, 30, 13.8, 3.26, 0.72622, 2, 29, -4.94, 6.28, 0.52376, 30, 13.84, 6.9, 0.47624, 2, 29, -6.9, 2.97, 0.72738, 30, 12.4, 10.47, 0.27262, 2, 29, -4.23, 0.62, 0.87207, 30, 8.92, 9.79, 0.12793, 2, 29, -1.45, -1.74, 0.92878, 30, 5.35, 9.03, 0.07122, 2, 29, 1.18, -2.56, 0.95663, 30, 3.1, 7.45, 0.04337, 2, 29, 3.37, 0.29, 0.97604, 30, 4.02, 3.97, 0.02396, 2, 29, 4.58, 2.99, 0.24068, 30, 5.42, 1.37, 0.75932], "hull": 16}}, "hand_R6": {"hand_R6": {"type": "mesh", "uvs": [0.60336, 0.18547, 0.48597, 0.07374, 0.25901, 0, 0.09466, 0.0334, 0.18858, 0.20719, 0, 0.25995, 0.00075, 0.3965, 0.11814, 0.51133, 0.10249, 0.70685, 0.21988, 0.85891, 0.47423, 0.97995, 0.76771, 0.97374, 0.93988, 0.83098, 1, 0.5734, 0.97901, 0.42133, 0.92814, 0.19788, 0.78727, 0.19478, 0.34999, 0.3184, 0.51729, 0.49587, 0.60979, 0.75688], "triangles": [10, 9, 19, 4, 3, 2, 4, 2, 1, 17, 4, 1, 17, 1, 0, 6, 5, 4, 6, 4, 17, 18, 17, 0, 7, 6, 17, 7, 17, 18, 16, 18, 0, 19, 18, 13, 16, 14, 18, 15, 14, 16, 13, 18, 14, 12, 19, 13, 18, 8, 7, 19, 8, 18, 19, 9, 8, 11, 19, 12, 10, 19, 11], "vertices": [1, 28, 1.23, 5.35, 1, 1, 28, -2.79, 4.07, 1, 1, 28, -6.74, 0.05, 1, 1, 28, -7.27, -3.82, 1, 2, 28, -1.79, -3.72, 0.99995, 29, -6.71, -18.73, 5e-05, 2, 28, -2.01, -8.31, 0.99725, 29, -2.83, -21.22, 0.00275, 2, 28, 1.66, -9.8, 0.97485, 29, 0.28, -18.77, 0.02515, 2, 28, 5.76, -8.55, 0.8757, 29, 1.25, -14.6, 0.1243, 2, 28, 10.87, -11.03, 0.68462, 29, 5.95, -11.4, 0.31538, 2, 28, 15.98, -10.2, 0.37369, 29, 7.77, -6.56, 0.62631, 2, 28, 21.44, -6.11, 0.27333, 29, 6.95, 0.21, 0.72667, 2, 28, 23.82, 0.21, 0.4617, 29, 2.67, 5.43, 0.5383, 2, 28, 21.49, 5.44, 0.79503, 29, -3.03, 6.01, 0.20497, 2, 28, 15.09, 9.54, 0.99459, 29, -9.78, 2.52, 0.00541, 1, 28, 10.83, 10.76, 1, 1, 28, 4.39, 12.13, 1, 1, 28, 3.08, 9.16, 1, 2, 28, 2.6, -1.5, 0.99947, 29, -6.44, -13.82, 0.00053, 1, 28, 8.82, 0.12, 1, 2, 28, 16.63, -0.78, 0.84017, 29, -0.07, -1.3, 0.15983], "hull": 17}}, "hand_R7": {"hand_R7": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [14.21, -3.44, -5.74, -2.03, -5.11, 6.95, 14.84, 5.54], "hull": 4}}, "hand_R8": {"hand_R8": {"type": "mesh", "uvs": [0.51736, 0.21527, 0.28813, 0.16619, 0.04508, 0, 0, 0.28219, 0.01746, 0.57665, 0.0727, 0.83542, 0.27155, 0.97819, 0.51736, 1, 0.74107, 0.92465, 0.90679, 0.70603, 0.96755, 0.39819, 1, 0, 0.74107, 0.08588, 0.43631, 0.37265, 0.4435, 0.5956, 0.45619, 0.81399], "triangles": [6, 15, 7, 7, 15, 8, 15, 6, 14, 9, 8, 14, 8, 15, 14, 9, 12, 10, 9, 14, 0, 6, 5, 14, 1, 14, 4, 4, 14, 5, 4, 3, 1, 3, 2, 1, 13, 14, 1, 0, 12, 9, 14, 13, 0, 10, 12, 11, 13, 1, 0], "vertices": [3, 31, 12.41, 1.38, 0.74622, 32, -2.59, -1.79, 0.13648, 33, 5.99, 8.31, 0.11731, 3, 31, 10.42, -3.05, 0.65693, 32, 0.29, -5.7, 0.33092, 33, 1.24, 9.34, 0.01215, 2, 31, 6.89, -7.33, 0.44862, 32, 2.27, -10.88, 0.55138, 2, 31, 10.14, -9.28, 0.30626, 32, 5.57, -9.02, 0.69374, 2, 31, 13.91, -10.03, 0.18078, 32, 8.07, -6.1, 0.81922, 3, 31, 17.46, -9.88, 0.0679, 32, 9.69, -2.93, 0.59877, 33, -3.99, 1.04, 0.33333, 3, 31, 20.44, -6.41, 0.01341, 32, 8.13, 1.37, 0.31993, 33, 0.02, -1.15, 0.66667, 1, 33, 5.14, -1.86, 1, 3, 31, 22.59, 3.24, 0.05547, 32, 0.79, 7.99, 0, 33, 9.91, -1.27, 0.94452, 3, 31, 20.86, 7.39, 0.21085, 32, -3.67, 8.52, 0, 33, 13.61, 1.27, 0.78915, 3, 31, 17.39, 9.75, 0.40894, 32, -7.44, 6.67, 0, 33, 15.21, 5.16, 0.59105, 2, 31, 12.63, 11.89, 0.58165, 33, 16.32, 10.26, 0.41835, 2, 31, 12.14, 6.36, 0.74745, 33, 10.81, 9.59, 0.25255, 2, 31, 13.88, -0.84, 0.80621, 32, 0.06, -1.6, 0.19379, 2, 32, 2.04, 0.52, 0.93689, 33, 4.03, 3.51, 0.06311, 2, 32, 3.9, 2.68, 0.12597, 33, 4.06, 0.66, 0.87403], "hull": 13}}, "hand_R9": {"hand_R9": {"type": "mesh", "uvs": [0.50976, 0, 0.3546, 0.06921, 0.1551, 0.0619, 0.0316, 0.07651, 0.08702, 0.21828, 0.13135, 0.29282, 0.07435, 0.36444, 0, 0.47113, 0.08068, 0.51643, 0.1551, 0.56905, 0.15351, 0.68933, 0.17251, 0.83256, 0.30393, 0.92756, 0.48443, 0.9904, 0.72668, 0.95825, 0.85979, 0.83779, 0.96944, 0.70282, 0.99636, 0.51283, 1, 0.31844, 0.92036, 0.15621, 0.81902, 0.06706, 0.66861, 0.00129, 0.42317, 0.20549, 0.49191, 0.40335, 0.57684, 0.57417, 0.60014, 0.79912], "triangles": [12, 11, 10, 25, 12, 10, 14, 25, 15, 13, 12, 25, 13, 25, 14, 22, 1, 0, 4, 3, 2, 5, 4, 2, 22, 20, 23, 19, 18, 23, 9, 8, 6, 7, 6, 8, 5, 2, 1, 5, 1, 22, 23, 9, 5, 23, 5, 22, 9, 6, 5, 21, 22, 0, 20, 22, 21, 23, 18, 24, 19, 23, 20, 17, 24, 18, 9, 23, 24, 10, 9, 24, 16, 24, 17, 15, 25, 24, 10, 24, 25, 16, 15, 24], "vertices": [1, 31, -4.2, 4.43, 1, 1, 31, -3.5, 0.35, 1, 1, 31, -5, -4.2, 1, 2, 31, -5.46, -7.15, 0.99978, 32, -3.96, -21.55, 0.00022, 2, 31, -1.55, -6.89, 0.99692, 32, -2.26, -18.01, 0.00308, 2, 31, 0.61, -6.4, 0.98708, 32, -1.62, -15.9, 0.01292, 2, 31, 2.02, -8.23, 0.97112, 32, 0.66, -15.57, 0.02888, 2, 31, 4.19, -10.72, 0.94949, 32, 3.89, -14.9, 0.05051, 2, 31, 5.86, -9.18, 0.89927, 32, 3.38, -12.69, 0.10073, 2, 31, 7.67, -7.84, 0.75706, 32, 3.1, -10.46, 0.24294, 2, 31, 10.66, -8.75, 0.52708, 32, 5.36, -8.29, 0.47292, 2, 31, 14.37, -9.34, 0.26614, 32, 7.7, -5.36, 0.73386, 2, 31, 17.61, -6.99, 0.09133, 32, 7.25, -1.38, 0.90867, 2, 31, 20.38, -3.28, 0.12377, 32, 5.38, 2.86, 0.87623, 2, 31, 21.19, 2.54, 0.37013, 32, 0.71, 6.42, 0.62987, 2, 31, 19.06, 6.48, 0.69392, 32, -3.76, 6.5, 0.30608, 2, 31, 16.42, 9.98, 0.91595, 32, -8.11, 5.92, 0.08405, 2, 31, 11.85, 11.96, 0.99059, 32, -12.09, 2.92, 0.00941, 1, 31, 7.01, 13.44, 1, 1, 31, 2.43, 12.77, 1, 1, 31, -0.47, 11.08, 1, 1, 31, -3.11, 8.08, 1, 1, 31, 0.36, 0.95, 1, 1, 31, 5.76, 1.12, 1, 1, 31, 10.59, 1.85, 1, 2, 31, 16.37, 0.77, 0.40585, 32, -0.11, 1.35, 0.59415], "hull": 22}}, "head": {"head": {"type": "mesh", "uvs": [0.13928, 0.3775, 0.04659, 0.45358, 0.07142, 0.50248, 0.22369, 0.54233, 0.21707, 0.64739, 0.23362, 0.7615, 0.29321, 0.88286, 0.39583, 0.91184, 0.50507, 0.90822, 0.53983, 0.99878, 0.64576, 0.94807, 0.7699, 0.96799, 0.94369, 0.97524, 1, 0.82309, 0.92217, 0.71441, 0.86424, 0.55139, 0.7881, 0.47531, 0.77155, 0.41833, 0.71528, 0.31327, 0.62755, 0.20097, 0.515, 0.10135, 0.51169, 0.19011, 0.44548, 0.11403, 0.3561, 0.0452, 0.24521, 0, 0.16742, 0, 0.22866, 0.06512, 0.275, 0.15569, 0.19059, 0.13758, 0.0979, 0.14663, 0, 0.13758, 0, 0.22995, 0.07969, 0.30603, 0.17735, 0.49083, 0.32797, 0.53793, 0.43886, 0.56691, 0.56797, 0.5977, 0.50342, 0.52344, 0.60107, 0.55242, 0.70535, 0.59589, 0.79142, 0.64298, 0.75666, 0.5017, 0.54314, 0.46366, 0.46038, 0.37491, 0.3859, 0.29159, 0.26673, 0.25174, 0.14093, 0.22638, 0.06314, 0.19559, 0.68052, 0.80057, 0.33624, 0.64842], "triangles": [1, 0, 33, 33, 0, 45, 0, 46, 45, 0, 32, 46, 31, 47, 32, 32, 47, 46, 46, 28, 45, 45, 28, 27, 31, 30, 47, 47, 29, 46, 46, 29, 28, 47, 30, 29, 33, 45, 34, 34, 45, 44, 45, 27, 44, 44, 27, 22, 44, 22, 21, 22, 27, 23, 27, 26, 23, 26, 24, 23, 26, 25, 24, 39, 36, 38, 35, 37, 36, 36, 37, 38, 39, 38, 41, 37, 35, 43, 37, 42, 38, 38, 42, 41, 35, 34, 43, 34, 44, 43, 37, 43, 42, 42, 43, 19, 41, 42, 18, 42, 19, 18, 19, 43, 21, 43, 44, 21, 21, 20, 19, 41, 17, 16, 36, 49, 35, 2, 1, 33, 2, 33, 3, 3, 33, 34, 9, 8, 10, 13, 12, 14, 10, 48, 11, 12, 11, 14, 14, 48, 40, 14, 11, 48, 10, 8, 48, 8, 7, 49, 7, 6, 49, 8, 49, 36, 8, 36, 48, 6, 5, 49, 48, 36, 39, 48, 39, 40, 5, 4, 49, 49, 3, 34, 3, 49, 4, 40, 15, 14, 49, 34, 35, 39, 41, 40, 40, 41, 15, 15, 41, 16, 17, 41, 18], "vertices": [4, 12, -10.49, 22.73, 0.04112, 14, 19.74, 12.86, 0.03982, 15, 12.36, 8.41, 0.1599, 16, 5.61, 7.67, 0.75916, 4, 12, -15.98, 18.86, 0.08326, 14, 20.73, 19.51, 0.0841, 15, 16.08, 14.01, 0.2554, 16, 10.11, 12.67, 0.57724, 1, 12, -14.61, 16.22, 1, 1, 12, -5.85, 13.86, 1, 1, 12, -6.39, 8.3, 1, 4, 12, -5.61, 2.23, 0.86075, 14, 1.55, 23.51, 0.07954, 15, 0.41, 25.77, 0.03678, 16, -3.67, 26.59, 0.02293, 4, 12, -2.34, -4.3, 0.97186, 14, -5.43, 25.66, 0.01738, 15, -4.99, 30.68, 0.00709, 16, -8.3, 32.24, 0.00367, 1, 12, 3.57, -6.01, 1, 1, 12, 9.9, -6, 1, 1, 12, 11.78, -10.86, 1, 1, 12, 18, -8.35, 1, 1, 12, 25.17, -9.61, 1, 1, 12, 35.23, -10.29, 1, 2, 12, 38.73, -2.32, 0.99257, 14, -32.39, -5.39, 0.00743, 1, 12, 34.38, 3.56, 1, 1, 12, 31.28, 12.3, 1, 1, 12, 26.98, 16.46, 1, 2, 12, 26.11, 19.5, 0.99618, 14, -7.89, -11.35, 0.00382, 3, 12, 23.01, 25.16, 0.17396, 14, -1.65, -13.02, 0.82034, 15, -18, -5.94, 0.0057, 3, 12, 18.09, 31.26, 0.01584, 14, 6.15, -13.69, 0.94665, 15, -11.22, -9.86, 0.03751, 3, 12, 11.72, 36.73, 3e-05, 14, 14.5, -12.86, 0.89299, 15, -3.3, -12.66, 0.10698, 3, 14, 11.34, -9.38, 0.64905, 15, -4.69, -8.16, 0.34981, 16, -13.67, -6.23, 0.00114, 3, 14, 16.9, -9.55, 0.35378, 15, 0.28, -10.68, 0.62793, 16, -9.13, -9.45, 0.01829, 3, 14, 23.15, -8.51, 0.08992, 15, 6.38, -12.39, 0.86932, 16, -3.34, -12.03, 0.04076, 3, 14, 29.41, -5.69, 0.00815, 15, 13.24, -12.5, 0.9319, 16, 3.43, -13.14, 0.05995, 2, 15, 17.5, -10.99, 0.93043, 16, 7.86, -12.27, 0.06957, 3, 14, 27.67, -2.56, 0.00011, 15, 13, -8.93, 0.83323, 16, 3.71, -9.57, 0.16666, 3, 14, 22.39, -1.03, 0.00011, 15, 8.86, -5.3, 0.52588, 16, 0.15, -5.38, 0.474, 3, 14, 26.55, 1.72, 0.00011, 15, 13.8, -4.57, 0.21931, 16, 5.14, -5.38, 0.78057, 2, 15, 18.7, -2.32, 0.00566, 16, 10.32, -3.87, 0.99434, 1, 16, 15.99, -3.25, 1, 4, 12, -18.33, 30.78, 0.00108, 14, 30.98, 12.97, 0.00056, 15, 22.58, 3.73, 0.0043, 16, 15.04, 1.55, 0.99405, 4, 12, -13.83, 26.62, 0.01588, 14, 24.86, 12.59, 0.01695, 15, 16.88, 5.99, 0.071, 16, 9.73, 4.62, 0.89617, 4, 12, -8.45, 16.66, 0.18014, 14, 13.94, 15.59, 0.21759, 15, 8.28, 13.34, 0.23464, 16, 2.29, 13.14, 0.36763, 4, 12, 0.21, 13.92, 0.27201, 14, 5.97, 11.23, 0.39887, 15, -0.79, 12.78, 0.155, 16, -6.76, 13.91, 0.17412, 4, 12, 6.59, 12.19, 0.37689, 14, 0.32, 7.81, 0.53488, 15, -7.37, 12.08, 0.05989, 16, -13.37, 14.18, 0.02834, 4, 12, 14.03, 10.34, 0.31762, 14, -6.16, 3.71, 0.66873, 15, -14.97, 11.12, 0.01061, 16, -21.03, 14.34, 0.00304, 4, 12, 10.4, 14.39, 0.30322, 14, -0.73, 3.54, 0.69521, 15, -10.13, 8.66, 0.00134, 16, -16.6, 11.2, 0.00023, 4, 12, 16.02, 12.69, 0.35229, 14, -5.84, 0.65, 0.64616, 15, -15.98, 8.22, 0.00132, 16, -22.46, 11.62, 0.00023, 2, 12, 22, 10.21, 0.5707, 14, -11.76, -1.96, 0.4293, 2, 12, 26.91, 7.57, 0.60686, 14, -17.07, -3.69, 0.39314, 2, 12, 25.12, 15.11, 0.42448, 14, -10.38, -7.6, 0.57552, 1, 14, -0.15, -0.34, 1, 1, 14, 6.57, -0.31, 1, 2, 14, 12.75, -0.42, 0.21948, 15, 0.39, -0.65, 0.78052, 2, 15, 7.62, -0.34, 0.69471, 16, -0.36, -0.29, 0.30529, 1, 16, 7.06, -0.2, 1, 1, 16, 11.8, -0.94, 1, 1, 12, 20.24, -0.59, 1, 1, 12, 0.52, 8.05, 1], "hull": 33}}, "head_2": {"head_2": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [33.05, -3.77, -1.47, -9.52, -4.26, 7.25, 30.26, 13], "hull": 4}}, "yinying": {"yinying": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [50.25, -22.24, -48.02, -22.24, -48.02, 18.16, 50.25, 18.16], "hull": 4}}}}], "animations": {"attack": {"bones": {"body_5": {"rotate": [{"angle": -0.49, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.49, "curve": "stepped"}, {"time": 1.1333, "angle": -0.49}], "translate": [{"y": 0.5, "curve": 0.25, "c3": 0}, {"time": 0.2667, "x": -8.34, "y": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 10.67, "y": -1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 10.67, "y": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -2.67, "y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "y": 0.5}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "yinying": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 10.44, "curve": "stepped"}, {"time": 0.6333, "x": 10.44, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.151, "y": 1.101, "curve": "stepped"}, {"time": 0.6333, "x": 1.151, "y": 1.101, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "leg06": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "leg05": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "leg04": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.2667, "x": -9.35, "y": -0.6, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3333, "x": 3.18, "y": 5.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "x": 15.72, "y": 0.4, "curve": "stepped"}, {"time": 0.6333, "x": 15.72, "y": 0.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7333, "x": 7.86, "y": 4.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "leg03": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "leg02": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4, "x": 5.38, "y": 2.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4667, "x": 10.76, "y": -2.11, "curve": "stepped"}, {"time": 0.6667, "x": 10.76, "y": -2.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.7667, "x": 5.38, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.1333}]}, "leg01": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.2667, "x": -6.97, "y": 0.2, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "x": 6.57, "y": 5.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4333, "x": 20.1, "y": 0.4, "curve": "stepped"}, {"time": 0.7333, "x": 20.1, "y": 0.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "x": 10.05, "y": 4.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L13": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L8": {"rotate": [{"angle": -1.85, "curve": "stepped"}, {"time": 0.2667, "angle": -1.85, "curve": "stepped"}, {"time": 0.4, "angle": -1.85, "curve": "stepped"}, {"time": 0.6333, "angle": -1.85, "curve": "stepped"}, {"time": 0.8333, "angle": -1.85, "curve": "stepped"}, {"time": 1.1333, "angle": -1.85}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L11": {"rotate": [{"angle": 1.19, "curve": "stepped"}, {"time": 0.2667, "angle": 1.19, "curve": "stepped"}, {"time": 0.4, "angle": 1.19, "curve": "stepped"}, {"time": 0.6333, "angle": 1.19, "curve": "stepped"}, {"time": 0.8333, "angle": 1.19, "curve": "stepped"}, {"time": 1.1333, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L10": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L5": {"rotate": [{"angle": -0.8, "curve": "stepped"}, {"time": 0.2667, "angle": -0.8, "curve": "stepped"}, {"time": 0.4, "angle": -0.8, "curve": "stepped"}, {"time": 0.6333, "angle": -0.8, "curve": "stepped"}, {"time": 0.8333, "angle": -0.8, "curve": "stepped"}, {"time": 1.1333, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L6": {"rotate": [{"angle": 0.43, "curve": "stepped"}, {"time": 0.2667, "angle": 0.43, "curve": "stepped"}, {"time": 0.4, "angle": 0.43, "curve": "stepped"}, {"time": 0.6333, "angle": 0.43, "curve": "stepped"}, {"time": 0.8333, "angle": 0.43, "curve": "stepped"}, {"time": 1.1333, "angle": 0.43}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L4": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L": {"rotate": [{"angle": -10.52, "curve": "stepped"}, {"time": 0.2667, "angle": -10.52, "curve": "stepped"}, {"time": 0.4, "angle": -10.52, "curve": "stepped"}, {"time": 0.6333, "angle": -10.52, "curve": "stepped"}, {"time": 0.8333, "angle": -10.52, "curve": "stepped"}, {"time": 1.1333, "angle": -10.52}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L3": {"rotate": [{"angle": 7.02, "curve": "stepped"}, {"time": 0.2667, "angle": 7.02, "curve": "stepped"}, {"time": 0.4, "angle": 7.02, "curve": "stepped"}, {"time": 0.6333, "angle": 7.02, "curve": "stepped"}, {"time": 0.8333, "angle": 7.02, "curve": "stepped"}, {"time": 1.1333, "angle": 7.02}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 3.93, "y": -3.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_R7": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_R8": {"rotate": [{"angle": -0.56, "curve": "stepped"}, {"time": 0.2667, "angle": -0.56, "curve": "stepped"}, {"time": 0.4, "angle": -0.56, "curve": "stepped"}, {"time": 0.6333, "angle": -0.56, "curve": "stepped"}, {"time": 0.8333, "angle": -0.56, "curve": "stepped"}, {"time": 1.1333, "angle": -0.56}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_R9": {"rotate": [{"angle": -0.13, "curve": "stepped"}, {"time": 0.2667, "angle": -0.13, "curve": "stepped"}, {"time": 0.4, "angle": -0.13, "curve": "stepped"}, {"time": 0.6333, "angle": -0.13, "curve": "stepped"}, {"time": 0.8333, "angle": -0.13, "curve": "stepped"}, {"time": 1.1333, "angle": -0.13}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_R4": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L7": {"rotate": [{"angle": -0.86, "curve": "stepped"}, {"time": 0.2667, "angle": -0.86, "curve": "stepped"}, {"time": 0.4, "angle": -0.86, "curve": "stepped"}, {"time": 0.6333, "angle": -0.86, "curve": "stepped"}, {"time": 0.8333, "angle": -0.86, "curve": "stepped"}, {"time": 1.1333, "angle": -0.86}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_L9": {"rotate": [{"angle": 0.25, "curve": "stepped"}, {"time": 0.2667, "angle": 0.25, "curve": "stepped"}, {"time": 0.4, "angle": 0.25, "curve": "stepped"}, {"time": 0.6333, "angle": 0.25, "curve": "stepped"}, {"time": 0.8333, "angle": 0.25, "curve": "stepped"}, {"time": 1.1333, "angle": 0.25}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.92, "y": 2.12, "curve": "stepped"}, {"time": 0.6333, "x": -0.92, "y": 2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_R": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_R2": {"rotate": [{"angle": -13.24, "curve": "stepped"}, {"time": 0.2667, "angle": -13.24, "curve": "stepped"}, {"time": 0.4, "angle": -13.24, "curve": "stepped"}, {"time": 0.6333, "angle": -13.24, "curve": "stepped"}, {"time": 0.8333, "angle": -13.24, "curve": "stepped"}, {"time": 1.1333, "angle": -13.24}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "hand_R3": {"rotate": [{"angle": 4.8, "curve": "stepped"}, {"time": 0.2667, "angle": 4.8, "curve": "stepped"}, {"time": 0.4, "angle": 4.8, "curve": "stepped"}, {"time": 0.6333, "angle": 4.8, "curve": "stepped"}, {"time": 0.8333, "angle": 4.8, "curve": "stepped"}, {"time": 1.1333, "angle": 4.8}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.51, "y": -3.95, "curve": "stepped"}, {"time": 0.6333, "x": 1.51, "y": -3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "body_14": {"rotate": [{"angle": -3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 15.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -11.97, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.73}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.1333}]}, "body_13": {"rotate": [{"angle": 2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 6.92, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 21.88, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -5.71, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 9.59, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 2.53}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}]}, "body_11": {"rotate": [{"angle": 6.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.27, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 26.24, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 13.94, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 6.89}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}]}, "body_4": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 9.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 24.92, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.67, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 12.62, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 5.57}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}]}, "body_3": {"rotate": [{"angle": -1.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 17.93, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -9.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -1.41}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}]}, "body_2": {"rotate": [{"angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 18.89, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -0.46}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "body2": {"rotate": [{"angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 16.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.07, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -2.99}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "body": {"rotate": [{"angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -0.62, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 14.34, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -13.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.1333}]}, "head5": {"rotate": [{"angle": -0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 22.96, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -10.43, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -0.06}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}]}, "head4": {"rotate": [{"angle": -0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 12.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -0.85}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}]}, "head3": {"rotate": [{"angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 13.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -6.51, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.35, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -1.6}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "head": {"rotate": [{"angle": 2.6, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": -0.18, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 36.73, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.5333, "angle": 17.81, "curve": "stepped"}, {"time": 0.6333, "angle": 17.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.6, "curve": "stepped"}, {"time": 1.1333, "angle": 2.6}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "ear3": {"rotate": [{"angle": 6.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.3, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -15.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 1.28, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 6.35}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"x": 0.969, "curve": "stepped"}, {"time": 0.2667, "x": 0.969, "curve": "stepped"}, {"time": 0.5333, "x": 0.969, "curve": "stepped"}, {"time": 0.7667, "x": 0.969, "curve": "stepped"}, {"time": 0.9667, "x": 0.969, "curve": "stepped"}, {"time": 1.1333, "x": 0.969}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}]}, "ear2": {"rotate": [{"angle": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -18.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 25.24, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -1.67, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 3.4}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"x": 0.976, "curve": "stepped"}, {"time": 0.2667, "x": 0.976, "curve": "stepped"}, {"time": 0.4667, "x": 0.976, "curve": "stepped"}, {"time": 0.7, "x": 0.976, "curve": "stepped"}, {"time": 0.9, "x": 0.976, "curve": "stepped"}, {"time": 1.1333, "x": 0.976}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}]}, "ear": {"rotate": [{"angle": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.05, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.85, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 16.2, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.21, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -0.13}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.02, "y": -3.78, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"x": 0.984, "curve": "stepped"}, {"time": 0.2667, "x": 0.984, "curve": "stepped"}, {"time": 0.4, "x": 0.984, "curve": "stepped"}, {"time": 0.6333, "x": 0.984, "curve": "stepped"}, {"time": 0.8333, "x": 0.984, "curve": "stepped"}, {"time": 1.1333, "x": 0.984}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "body_10": {"rotate": [{"angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -11.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 34.32, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -37.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -2.8, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -9.58}], "translate": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.1333}]}, "body_9": {"rotate": [{"angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -17.54, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -24.77, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -9.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -15.97}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.9, "curve": "stepped"}, {"time": 1.1333}]}, "body_7": {"rotate": [{"angle": -6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 17.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -15.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.34}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -5.26, "y": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 5.5, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "body_8": {"rotate": [{"angle": 0.45, "curve": "stepped"}, {"time": 0.2667, "angle": 0.45, "curve": "stepped"}, {"time": 0.4, "angle": 0.45, "curve": "stepped"}, {"time": 0.6333, "angle": 0.45, "curve": "stepped"}, {"time": 0.8333, "angle": 0.45, "curve": "stepped"}, {"time": 1.1333, "angle": 0.45}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "body_6": {"rotate": [{"angle": 0.06, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": -7.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 17.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.06, "curve": "stepped"}, {"time": 1.1333, "angle": 0.06}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}, "head_2": {"rotate": [{"angle": -4.63, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -28.38, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.5333, "angle": 15.19, "curve": "stepped"}, {"time": 0.6333, "angle": 15.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.63, "curve": "stepped"}, {"time": 1.1333, "angle": -4.63}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -0.48, "y": 0.64, "curve": "stepped"}, {"time": 0.5333, "x": -0.48, "y": 0.64, "curve": "stepped"}, {"time": 0.6333, "x": -0.48, "y": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.1333}]}}, "deform": {"default": {"body_5": {"body_5": [{"time": 0.2667, "curve": 0, "c2": 0.73, "c3": 0.75}, {"time": 0.4, "offset": 76, "vertices": [-5.50472, -1.3507, -5.03282, -2.6071, 4.94838, 2.76402, -11.00384, 0.34489, -10.77641, -2.25196, 10.70033, 2.58961, -9.06434, -7.55827, -7.03321, -9.47754, 6.73174, 9.69398, -18.47877, 0.3735, -18.04848, -3.9816, -17.38918, -4.02362, -12.77163, -0.42646, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.55709, -1.82134, -2.05717, -2.3715, -8.31656, 0.9489, -8.3065, -1.03302, 8.26994, 1.29368, -14.68172, 6.52438], "curve": 0.25, "c3": 0.75}, {"time": 0.6333}]}}}}, "idle": {"bones": {"root": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg06": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg05": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg04": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg03": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg02": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg01": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L13": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L8": {"rotate": [{"angle": -1.85, "curve": "stepped"}, {"time": 0.8333, "angle": -1.85, "curve": "stepped"}, {"time": 1.6667, "angle": -1.85}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L11": {"rotate": [{"angle": 1.19, "curve": "stepped"}, {"time": 0.8333, "angle": 1.19, "curve": "stepped"}, {"time": 1.6667, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L10": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L5": {"rotate": [{"angle": -0.8, "curve": "stepped"}, {"time": 0.8333, "angle": -0.8, "curve": "stepped"}, {"time": 1.6667, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L6": {"rotate": [{"angle": 0.43, "curve": "stepped"}, {"time": 0.8333, "angle": 0.43, "curve": "stepped"}, {"time": 1.6667, "angle": 0.43}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L4": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L": {"rotate": [{"angle": -10.52, "curve": "stepped"}, {"time": 0.8333, "angle": -10.52, "curve": "stepped"}, {"time": 1.6667, "angle": -10.52}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L3": {"rotate": [{"angle": 7.02, "curve": "stepped"}, {"time": 0.8333, "angle": 7.02, "curve": "stepped"}, {"time": 1.6667, "angle": 7.02}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R7": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R8": {"rotate": [{"angle": -0.56, "curve": "stepped"}, {"time": 0.8333, "angle": -0.56, "curve": "stepped"}, {"time": 1.6667, "angle": -0.56}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R9": {"rotate": [{"angle": -0.13, "curve": "stepped"}, {"time": 0.8333, "angle": -0.13, "curve": "stepped"}, {"time": 1.6667, "angle": -0.13}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R4": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L7": {"rotate": [{"angle": -0.86, "curve": "stepped"}, {"time": 0.8333, "angle": -0.86, "curve": "stepped"}, {"time": 1.6667, "angle": -0.86}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L9": {"rotate": [{"angle": 0.25, "curve": "stepped"}, {"time": 0.8333, "angle": 0.25, "curve": "stepped"}, {"time": 1.6667, "angle": 0.25}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R2": {"rotate": [{"angle": -13.24, "curve": "stepped"}, {"time": 0.8333, "angle": -13.24, "curve": "stepped"}, {"time": 1.6667, "angle": -13.24}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R3": {"rotate": [{"angle": 4.8, "curve": "stepped"}, {"time": 0.8333, "angle": 4.8, "curve": "stepped"}, {"time": 1.6667, "angle": 4.8}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_14": {"rotate": [{"angle": -3.73, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 3.91, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -14.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.1667, "angle": -16.86, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -3.73}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}]}, "body_13": {"rotate": [{"angle": 2.53, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 3.91, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -6.64, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 2.53}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}]}, "body_11": {"rotate": [{"angle": 6.89, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -10.37, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.8333, "angle": -12.95, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 6.89}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_4": {"rotate": [{"angle": 5.57, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 6.89, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": 5.57}], "translate": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.5, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.5, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.5, "curve": "stepped"}, {"time": 1.6667}]}, "body_3": {"rotate": [{"angle": -1.41, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3333, "angle": -9.85, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5, "angle": -12.06, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -1.41}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.6667}]}, "body_2": {"rotate": [{"angle": -0.46, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -3.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -0.46}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}]}, "body2": {"rotate": [{"angle": -2.99, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 10.49, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -2.99}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}]}, "body": {"rotate": [{"angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 3.42, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "head5": {"rotate": [{"angle": -0.06, "curve": 0.349, "c2": 0.39, "c3": 0.691, "c4": 0.75}, {"time": 0.2, "angle": -2.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3667, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 4.43, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 1.6667, "angle": -0.06}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 1.2, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 1.2, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 1.2, "curve": "stepped"}, {"time": 1.6667}]}, "head4": {"rotate": [{"angle": -0.85, "curve": 0.348, "c2": 0.39, "c3": 0.685, "c4": 0.73}, {"time": 0.1333, "angle": -2.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 4.43, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -0.85}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}]}, "head3": {"rotate": [{"angle": -1.6, "curve": 0.343, "c2": 0.37, "c3": 0.677, "c4": 0.71}, {"time": 0.0667, "angle": -2.26, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2333, "angle": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 4.43, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -1.6}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.6667}]}, "head": {"rotate": [{"angle": 2.6, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 2.99, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 2.6}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}]}, "ear3": {"rotate": [{"angle": 6.35, "curve": 0.284, "c2": 0.17, "c3": 0.653, "c4": 0.62}, {"time": 0.4, "angle": -1.59, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7333, "angle": -6.66, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 7.13, "curve": 0.3, "c3": 0.636, "c4": 0.36}, {"time": 1.6667, "angle": 6.35}], "translate": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"x": 0.969, "curve": 0.284, "c2": 0.17, "c3": 0.653, "c4": 0.62}, {"time": 0.4, "x": 0.988, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 0.967, "curve": 0.3, "c3": 0.636, "c4": 0.36}, {"time": 1.6667, "x": 0.969}], "shear": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.5667, "curve": "stepped"}, {"time": 1.6667}]}, "ear2": {"rotate": [{"angle": 3.4, "curve": 0.329, "c2": 0.32, "c3": 0.697, "c4": 0.77}, {"time": 0.4, "angle": -4.87, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "angle": -6.66, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 7.13, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "angle": 3.4}], "translate": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 1.4, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"x": 0.976, "curve": 0.329, "c2": 0.32, "c3": 0.697, "c4": 0.77}, {"time": 0.4, "x": 0.996, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": 0.967, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "x": 0.976}], "shear": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 1.4, "curve": "stepped"}, {"time": 1.6667}]}, "ear": {"rotate": [{"angle": -0.13, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "angle": -6.66, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 7.13, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": -0.13}], "translate": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"x": 0.984, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 0.967, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "x": 0.984}], "shear": [{"curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.6667}]}, "body_10": {"rotate": [{"angle": -9.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -19.46, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 7.4, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -9.58}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.6667}]}, "body_9": {"rotate": [{"angle": -15.97, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -19.46, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 7.4, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -15.97}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}]}, "body_7": {"rotate": [{"angle": -6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.4, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.34}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_8": {"rotate": [{"angle": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.56, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.45}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_6": {"rotate": [{"angle": 0.06, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 0.34, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -1.79, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.06}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}]}, "body_5": {"rotate": [{"angle": -0.49, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.49}], "translate": [{"y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 0.5}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "yinying": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "head_2": {"rotate": [{"angle": -4.63, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -5.32, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -4.63}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 1, "curve": "stepped"}, {"time": 1.6667}]}}}, "run": {"bones": {"leg06": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"x": 12.3}, {"time": 0.3333, "x": -14.16}, {"time": 0.5, "x": -0.93, "y": 5.74}, {"time": 0.6667, "x": 12.3}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "leg05": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"x": -14.01}, {"time": 0.1667, "x": -4.36, "y": 7.89}, {"time": 0.3333, "x": 5.29}, {"time": 0.6667, "x": -14.01}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "leg04": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"x": 3.29}, {"time": 0.3333, "x": -16.36, "y": -0.25}, {"time": 0.5, "x": -6.53, "y": 8.34}, {"time": 0.6667, "x": 3.29}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "leg03": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"x": -8.44}, {"time": 0.1667, "x": 4.25, "y": 5.06}, {"time": 0.3333, "x": 11.87}, {"time": 0.6667, "x": -8.44}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "leg02": {"rotate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"x": 11.58, "y": 2.29}, {"time": 0.3333, "x": -6.43}, {"time": 0.5, "x": 2.57, "y": 7.32}, {"time": 0.6667, "x": 11.58, "y": 2.29}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "leg01": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"x": -6.49}, {"time": 0.1667, "x": 3.81, "y": 8.38}, {"time": 0.3333, "x": 8.46, "y": 0.44}, {"time": 0.6667, "x": -6.49}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "body_5": {"rotate": [{"angle": -1.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.43, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.43}], "translate": [{"y": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 2.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": -1.6}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}]}, "body_6": {"rotate": [{"angle": -6.9, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": -3.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -8.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.7, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -6.9}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2333, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.6667}]}, "body_7": {"rotate": [{"angle": 8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.39, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 8.26}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"x": 1.046, "y": 1.046, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.908, "y": 0.908, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 1.046, "y": 1.046}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "body_9": {"rotate": [{"angle": 0.82, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": 8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -22.46, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": 0.82}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"x": 1.013, "y": 1.013, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "x": 1.046, "y": 1.046, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 0.908, "y": 0.908, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "x": 1.013, "y": 1.013}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}]}, "body_10": {"rotate": [{"angle": -11.16, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": 8.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -22.46, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -11.16}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"x": 0.959, "y": 0.959, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "x": 1.046, "y": 1.046, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.908, "y": 0.908, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": 0.959, "y": 0.959}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667}]}, "yinying": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}], "translate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.891, "y": 0.891, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.891, "y": 0.891, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}]}, "hand_R3": {"rotate": [{"angle": -8.75}, {"time": 0.1667, "angle": 18.12}, {"time": 0.3333, "angle": 33.34}, {"time": 0.6667, "angle": -8.75}], "translate": [{"x": -0.7, "y": -1.92}, {"time": 0.1667, "x": 0.38, "y": 0.86}, {"time": 0.3333}, {"time": 0.4667, "x": -0.93, "y": -2.25}, {"time": 0.6667, "x": -0.7, "y": -1.92}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "head_2": {"rotate": [{"angle": -6.69, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.69}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "head": {"rotate": [{"angle": 3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 3.73}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "ear": {"rotate": [{"angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9.11}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "ear2": {"rotate": [{"angle": -5.77, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.1, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 4.68, "curve": 0.243, "c3": 0.655, "c4": 0.63}, {"time": 0.6667, "angle": -5.77}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6667}]}, "ear3": {"rotate": [{"angle": -0.39, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.2, "angle": -9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": 4.68, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -0.39}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6667}]}, "head3": {"rotate": [{"angle": 5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.35}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "head5": {"rotate": [{"angle": 5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.35}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "head4": {"rotate": [{"angle": 5.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 5.35}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "body": {"rotate": [{"angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.27}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "body2": {"rotate": [{"angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 9.27}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.6667}]}, "body_3": {"rotate": [{"angle": 2.92, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.99, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 2.92}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667}]}, "body_2": {"rotate": [{"angle": 2.92, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -7.99, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": 2.92}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667}]}, "body_4": {"rotate": [{"angle": -5.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -5.75}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667}]}, "body_11": {"rotate": [{"angle": -5.75, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.2667, "angle": 9.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -7.99, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -5.75}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.6667}]}, "body_14": {"rotate": [{"angle": -5.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 9.27, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -5.75}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667}]}, "body_13": {"rotate": [{"angle": -5.75, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0667, "angle": -7.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 9.27, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -5.75}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6667}]}}}, "skill": {"bones": {"body_5": {"rotate": [{"angle": -0.49, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": -4.77}], "translate": [{"y": 0.5, "curve": 0.25, "c3": 0}, {"time": 0.2667, "x": -8.34, "y": -5.58}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "yinying": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "leg06": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "leg05": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "leg04": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.2667, "x": -9.35, "y": -0.6}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "leg03": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "leg02": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "leg01": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.2667, "x": -6.97, "y": 0.2}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L13": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L8": {"rotate": [{"angle": -1.85, "curve": "stepped"}, {"time": 0.2667, "angle": -1.85}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L11": {"rotate": [{"angle": 1.19, "curve": "stepped"}, {"time": 0.2667, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L10": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L5": {"rotate": [{"angle": -0.8, "curve": "stepped"}, {"time": 0.2667, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L6": {"rotate": [{"angle": 0.43, "curve": "stepped"}, {"time": 0.2667, "angle": 0.43}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L4": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L": {"rotate": [{"angle": -10.52, "curve": "stepped"}, {"time": 0.2667, "angle": -10.52}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L3": {"rotate": [{"angle": 7.02, "curve": "stepped"}, {"time": 0.2667, "angle": 7.02}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_R7": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_R8": {"rotate": [{"angle": -0.56, "curve": "stepped"}, {"time": 0.2667, "angle": -0.56}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_R9": {"rotate": [{"angle": -0.13, "curve": "stepped"}, {"time": 0.2667, "angle": -0.13}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_R4": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L7": {"rotate": [{"angle": -0.86, "curve": "stepped"}, {"time": 0.2667, "angle": -0.86}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_L9": {"rotate": [{"angle": 0.25, "curve": "stepped"}, {"time": 0.2667, "angle": 0.25}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_R": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_R2": {"rotate": [{"angle": -13.24, "curve": "stepped"}, {"time": 0.2667, "angle": -13.24}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "hand_R3": {"rotate": [{"angle": 4.8, "curve": "stepped"}, {"time": 0.2667, "angle": 4.8}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_14": {"rotate": [{"angle": -3.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.45}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_13": {"rotate": [{"angle": 2.53, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 12.72}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_11": {"rotate": [{"angle": 6.89, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.14}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_4": {"rotate": [{"angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.74}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_3": {"rotate": [{"angle": -1.41, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 3.99}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_2": {"rotate": [{"angle": -0.46, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 4.28}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body2": {"rotate": [{"angle": -2.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 1.75}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body": {"rotate": [{"angle": -5, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -0.62}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "head5": {"rotate": [{"angle": -0.06, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -8.81}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "head4": {"rotate": [{"angle": -0.85, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -9.6}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "head3": {"rotate": [{"angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -6.1}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "head": {"rotate": [{"angle": 2.6, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": -0.18}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "ear3": {"rotate": [{"angle": 6.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 13.3}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"x": 0.969, "curve": "stepped"}, {"time": 0.2667, "x": 0.969}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "ear2": {"rotate": [{"angle": 3.4, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 10.35}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"x": 0.976, "curve": "stepped"}, {"time": 0.2667, "x": 0.976}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "ear": {"rotate": [{"angle": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.05}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"x": 0.984, "curve": "stepped"}, {"time": 0.2667, "x": 0.984}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_10": {"rotate": [{"angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 5.08}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_9": {"rotate": [{"angle": -15.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -13.95}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_7": {"rotate": [{"angle": -6.34, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -7.91}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -5.26, "y": 2.13}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_8": {"rotate": [{"angle": 0.45, "curve": "stepped"}, {"time": 0.2667, "angle": 0.45}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "body_6": {"rotate": [{"angle": 0.06, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": -7.85}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}, "head_2": {"rotate": [{"angle": -4.63, "curve": 0.25, "c3": 0}, {"time": 0.2667, "angle": 0.64}], "translate": [{"curve": "stepped"}, {"time": 0.2667}], "scale": [{"curve": "stepped"}, {"time": 0.2667}], "shear": [{"curve": "stepped"}, {"time": 0.2667}]}}, "deform": {"default": {"body_5": {"body_5": [{"time": 0.2667}]}}}}, "skill1": {"bones": {"body_5": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.97, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.77}], "translate": [{"x": -8.34, "y": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -8.26, "y": -2.81, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -8.34, "y": -5.58}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "yinying": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg06": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg05": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg04": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"x": -9.35, "y": -0.6, "curve": "stepped"}, {"time": 0.8333, "x": -9.35, "y": -0.6, "curve": "stepped"}, {"time": 1.6667, "x": -9.35, "y": -0.6}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg03": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg02": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "leg01": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"x": -6.97, "y": 0.2, "curve": "stepped"}, {"time": 0.8333, "x": -6.97, "y": 0.2, "curve": "stepped"}, {"time": 1.6667, "x": -6.97, "y": 0.2}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L13": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L8": {"rotate": [{"angle": -1.85, "curve": "stepped"}, {"time": 0.8333, "angle": -1.85, "curve": "stepped"}, {"time": 1.6667, "angle": -1.85}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L11": {"rotate": [{"angle": 1.19, "curve": "stepped"}, {"time": 0.8333, "angle": 1.19, "curve": "stepped"}, {"time": 1.6667, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L10": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L5": {"rotate": [{"angle": -0.8, "curve": "stepped"}, {"time": 0.8333, "angle": -0.8, "curve": "stepped"}, {"time": 1.6667, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L6": {"rotate": [{"angle": 0.43, "curve": "stepped"}, {"time": 0.8333, "angle": 0.43, "curve": "stepped"}, {"time": 1.6667, "angle": 0.43}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L4": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L": {"rotate": [{"angle": -10.52, "curve": "stepped"}, {"time": 0.8333, "angle": -10.52, "curve": "stepped"}, {"time": 1.6667, "angle": -10.52}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L3": {"rotate": [{"angle": 7.02, "curve": "stepped"}, {"time": 0.8333, "angle": 7.02, "curve": "stepped"}, {"time": 1.6667, "angle": 7.02}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R7": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R8": {"rotate": [{"angle": -0.56, "curve": "stepped"}, {"time": 0.8333, "angle": -0.56, "curve": "stepped"}, {"time": 1.6667, "angle": -0.56}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R9": {"rotate": [{"angle": -0.13, "curve": "stepped"}, {"time": 0.8333, "angle": -0.13, "curve": "stepped"}, {"time": 1.6667, "angle": -0.13}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R4": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L7": {"rotate": [{"angle": -0.86, "curve": "stepped"}, {"time": 0.8333, "angle": -0.86, "curve": "stepped"}, {"time": 1.6667, "angle": -0.86}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_L9": {"rotate": [{"angle": 0.25, "curve": "stepped"}, {"time": 0.8333, "angle": 0.25, "curve": "stepped"}, {"time": 1.6667, "angle": 0.25}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R": {"rotate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R2": {"rotate": [{"angle": -13.24, "curve": "stepped"}, {"time": 0.8333, "angle": -13.24, "curve": "stepped"}, {"time": 1.6667, "angle": -13.24}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "hand_R3": {"rotate": [{"angle": 4.8, "curve": "stepped"}, {"time": 0.8333, "angle": 4.8, "curve": "stepped"}, {"time": 1.6667, "angle": 4.8}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_14": {"rotate": [{"angle": 3.45, "curve": 0.357, "c2": 0.42, "c3": 0.711, "c4": 0.82}, {"time": 0.3, "angle": 1.01, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4, "angle": 0.66, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 6.62, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.6667, "angle": 3.45}], "translate": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 1.2333, "curve": "stepped"}, {"time": 1.6667}]}, "body_13": {"rotate": [{"angle": 12.72, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": 6.92, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 25.21, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": 12.72}], "translate": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}]}, "body_11": {"rotate": [{"angle": 13.14, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.2, "angle": 11.62, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.3, "angle": 11.27, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 17.24, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": 13.14}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 1.1333, "curve": "stepped"}, {"time": 1.6667}]}, "body_4": {"rotate": [{"angle": 11.74, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 9.95, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 20.26, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 11.74}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.6667}]}, "body_3": {"rotate": [{"angle": 3.99, "curve": 0.352, "c2": 0.42, "c3": 0.688, "c4": 0.76}, {"time": 0.1, "angle": 3.32, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.2, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 8.94, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 3.99}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 1.0333, "curve": "stepped"}, {"time": 1.6667}]}, "body_2": {"rotate": [{"angle": 4.28, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 3.93, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 9.9, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 4.28}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.6667}]}, "body2": {"rotate": [{"angle": 1.75, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 7.36, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 1.75}], "translate": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1, "curve": "stepped"}, {"time": 0.9333, "curve": "stepped"}, {"time": 1.6667}]}, "body": {"rotate": [{"angle": -0.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.35, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.62}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "head5": {"rotate": [{"angle": -8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -8.81}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "head4": {"rotate": [{"angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.15, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -9.6}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "head3": {"rotate": [{"angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.65, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.1}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "head": {"rotate": [{"angle": -0.18, "curve": "stepped"}, {"time": 0.8333, "angle": -0.18, "curve": "stepped"}, {"time": 1.6667, "angle": -0.18}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "ear3": {"rotate": [{"angle": 13.3, "curve": "stepped"}, {"time": 0.8333, "angle": 13.3, "curve": "stepped"}, {"time": 1.6667, "angle": 13.3}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"x": 0.969, "curve": "stepped"}, {"time": 0.8333, "x": 0.969, "curve": "stepped"}, {"time": 1.6667, "x": 0.969}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "ear2": {"rotate": [{"angle": 10.35, "curve": "stepped"}, {"time": 0.8333, "angle": 10.35, "curve": "stepped"}, {"time": 1.6667, "angle": 10.35}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"x": 0.976, "curve": "stepped"}, {"time": 0.8333, "x": 0.976, "curve": "stepped"}, {"time": 1.6667, "x": 0.976}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "ear": {"rotate": [{"angle": 5.05, "curve": "stepped"}, {"time": 0.8333, "angle": 5.05, "curve": "stepped"}, {"time": 1.6667, "angle": 5.05}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"x": 0.984, "curve": "stepped"}, {"time": 0.8333, "x": 0.984, "curve": "stepped"}, {"time": 1.6667, "x": 0.984}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_10": {"rotate": [{"angle": 5.08, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.2667, "angle": -9.58, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 45.29, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": 5.08}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.1, "curve": "stepped"}, {"time": 1.6667}]}, "body_9": {"rotate": [{"angle": -13.95, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -17.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 21.85, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -13.95}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.6667}]}, "body_7": {"rotate": [{"angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.91}], "translate": [{"x": -5.26, "y": 2.13, "curve": "stepped"}, {"time": 0.8333, "x": -5.26, "y": 2.13, "curve": "stepped"}, {"time": 1.6667, "x": -5.26, "y": 2.13}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_8": {"rotate": [{"angle": 0.45, "curve": "stepped"}, {"time": 0.8333, "angle": 0.45, "curve": "stepped"}, {"time": 1.6667, "angle": 0.45}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "body_6": {"rotate": [{"angle": -7.85, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.94, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.85}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}, "head_2": {"rotate": [{"angle": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -10.25, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.64}], "translate": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}], "shear": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}}, "deform": {"default": {"body_5": {"body_5": [{"curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 1.6667}]}}}}, "skill2": {"bones": {"body_5": {"rotate": [{"angle": -4.77, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 6.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -0.49, "curve": "stepped"}, {"time": 0.8667, "angle": -0.49}], "translate": [{"x": -8.34, "y": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 10.67, "y": -1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 10.67, "y": -3.26, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -2.67, "y": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "y": 0.5}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "yinying": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 10.44, "curve": "stepped"}, {"time": 0.3667, "x": 10.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.151, "y": 1.101, "curve": "stepped"}, {"time": 0.3667, "x": 1.151, "y": 1.101, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "bone": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "root": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "leg06": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "leg05": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "leg04": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"x": -9.35, "y": -0.6, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 3.18, "y": 5.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1333, "x": 15.72, "y": 0.4, "curve": "stepped"}, {"time": 0.3667, "x": 15.72, "y": 0.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "x": 7.86, "y": 4.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "leg03": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "leg02": {"rotate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1333, "x": 5.38, "y": 2.09, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.2, "x": 10.76, "y": -2.11, "curve": "stepped"}, {"time": 0.4, "x": 10.76, "y": -2.11, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5, "x": 5.38, "y": 3.1, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.8667}]}, "leg01": {"rotate": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"x": -6.97, "y": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 6.57, "y": 5.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "x": 20.1, "y": 0.4, "curve": "stepped"}, {"time": 0.4667, "x": 20.1, "y": 0.4, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.5667, "x": 10.05, "y": 4.36, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L13": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L8": {"rotate": [{"angle": -1.85, "curve": "stepped"}, {"time": 0.1333, "angle": -1.85, "curve": "stepped"}, {"time": 0.3667, "angle": -1.85, "curve": "stepped"}, {"time": 0.5667, "angle": -1.85, "curve": "stepped"}, {"time": 0.8667, "angle": -1.85}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L11": {"rotate": [{"angle": 1.19, "curve": "stepped"}, {"time": 0.1333, "angle": 1.19, "curve": "stepped"}, {"time": 0.3667, "angle": 1.19, "curve": "stepped"}, {"time": 0.5667, "angle": 1.19, "curve": "stepped"}, {"time": 0.8667, "angle": 1.19}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L10": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L5": {"rotate": [{"angle": -0.8, "curve": "stepped"}, {"time": 0.1333, "angle": -0.8, "curve": "stepped"}, {"time": 0.3667, "angle": -0.8, "curve": "stepped"}, {"time": 0.5667, "angle": -0.8, "curve": "stepped"}, {"time": 0.8667, "angle": -0.8}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L6": {"rotate": [{"angle": 0.43, "curve": "stepped"}, {"time": 0.1333, "angle": 0.43, "curve": "stepped"}, {"time": 0.3667, "angle": 0.43, "curve": "stepped"}, {"time": 0.5667, "angle": 0.43, "curve": "stepped"}, {"time": 0.8667, "angle": 0.43}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L4": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L": {"rotate": [{"angle": -10.52, "curve": "stepped"}, {"time": 0.1333, "angle": -10.52, "curve": "stepped"}, {"time": 0.3667, "angle": -10.52, "curve": "stepped"}, {"time": 0.5667, "angle": -10.52, "curve": "stepped"}, {"time": 0.8667, "angle": -10.52}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L3": {"rotate": [{"angle": 7.02, "curve": "stepped"}, {"time": 0.1333, "angle": 7.02, "curve": "stepped"}, {"time": 0.3667, "angle": 7.02, "curve": "stepped"}, {"time": 0.5667, "angle": 7.02, "curve": "stepped"}, {"time": 0.8667, "angle": 7.02}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 3.93, "y": -3.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_R7": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_R8": {"rotate": [{"angle": -0.56, "curve": "stepped"}, {"time": 0.1333, "angle": -0.56, "curve": "stepped"}, {"time": 0.3667, "angle": -0.56, "curve": "stepped"}, {"time": 0.5667, "angle": -0.56, "curve": "stepped"}, {"time": 0.8667, "angle": -0.56}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_R9": {"rotate": [{"angle": -0.13, "curve": "stepped"}, {"time": 0.1333, "angle": -0.13, "curve": "stepped"}, {"time": 0.3667, "angle": -0.13, "curve": "stepped"}, {"time": 0.5667, "angle": -0.13, "curve": "stepped"}, {"time": 0.8667, "angle": -0.13}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_R4": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L7": {"rotate": [{"angle": -0.86, "curve": "stepped"}, {"time": 0.1333, "angle": -0.86, "curve": "stepped"}, {"time": 0.3667, "angle": -0.86, "curve": "stepped"}, {"time": 0.5667, "angle": -0.86, "curve": "stepped"}, {"time": 0.8667, "angle": -0.86}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_L9": {"rotate": [{"angle": 0.25, "curve": "stepped"}, {"time": 0.1333, "angle": 0.25, "curve": "stepped"}, {"time": 0.3667, "angle": 0.25, "curve": "stepped"}, {"time": 0.5667, "angle": 0.25, "curve": "stepped"}, {"time": 0.8667, "angle": 0.25}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -0.92, "y": 2.12, "curve": "stepped"}, {"time": 0.3667, "x": -0.92, "y": 2.12, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_R": {"rotate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_R2": {"rotate": [{"angle": -13.24, "curve": "stepped"}, {"time": 0.1333, "angle": -13.24, "curve": "stepped"}, {"time": 0.3667, "angle": -13.24, "curve": "stepped"}, {"time": 0.5667, "angle": -13.24, "curve": "stepped"}, {"time": 0.8667, "angle": -13.24}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "hand_R3": {"rotate": [{"angle": 4.8, "curve": "stepped"}, {"time": 0.1333, "angle": 4.8, "curve": "stepped"}, {"time": 0.3667, "angle": 4.8, "curve": "stepped"}, {"time": 0.5667, "angle": 4.8, "curve": "stepped"}, {"time": 0.8667, "angle": 4.8}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 1.51, "y": -3.95, "curve": "stepped"}, {"time": 0.3667, "x": 1.51, "y": -3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "body_14": {"rotate": [{"angle": 3.45, "curve": 0.357, "c2": 0.42, "c3": 0.711, "c4": 0.82}, {"time": 0.3333, "angle": 15.62, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 3.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -3.73}], "translate": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.7667, "curve": "stepped"}, {"time": 0.8667}]}, "body_13": {"rotate": [{"angle": 12.72, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.2667, "angle": 21.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -5.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 9.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 2.53}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}]}, "body_11": {"rotate": [{"angle": 13.14, "curve": 0.359, "c2": 0.43, "c3": 0.702, "c4": 0.8}, {"time": 0.2667, "angle": 26.24, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 13.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 6.89}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}]}, "body_4": {"rotate": [{"angle": 11.74, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 24.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -2.67, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 12.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 5.57}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}]}, "body_3": {"rotate": [{"angle": 3.99, "curve": 0.352, "c2": 0.42, "c3": 0.688, "c4": 0.76}, {"time": 0.2, "angle": 17.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -9.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 5.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.41}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}]}, "body_2": {"rotate": [{"angle": 4.28, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1333, "angle": 18.89, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -8.7, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 6.6, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -0.46}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "body2": {"rotate": [{"angle": 1.75, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1333, "angle": 16.36, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -2.99}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "body": {"rotate": [{"angle": -0.62, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 14.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -13.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 2.05, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -5}], "translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.0667, "curve": "stepped"}, {"time": 0.3, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.8667}]}, "head5": {"rotate": [{"angle": -8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 22.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -10.43, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 13.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -0.06}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}]}, "head4": {"rotate": [{"angle": -9.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 22.17, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -11.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 12.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -0.85}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}]}, "head3": {"rotate": [{"angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 13.48, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -6.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.35, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -1.6}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "head": {"rotate": [{"angle": -0.18, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 36.73, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.2667, "angle": 17.81, "curve": "stepped"}, {"time": 0.3667, "angle": 17.81, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 2.6, "curve": "stepped"}, {"time": 0.8667, "angle": 2.6}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "ear3": {"rotate": [{"angle": 13.3, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -15.44, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 28.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.28, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 6.35}], "translate": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"x": 0.969, "curve": "stepped"}, {"time": 0.2667, "x": 0.969, "curve": "stepped"}, {"time": 0.5, "x": 0.969, "curve": "stepped"}, {"time": 0.7, "x": 0.969, "curve": "stepped"}, {"time": 0.8667, "x": 0.969}], "shear": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}]}, "ear2": {"rotate": [{"angle": 10.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -18.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 25.24, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.67, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 3.4}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"x": 0.976, "curve": "stepped"}, {"time": 0.2, "x": 0.976, "curve": "stepped"}, {"time": 0.4333, "x": 0.976, "curve": "stepped"}, {"time": 0.6333, "x": 0.976, "curve": "stepped"}, {"time": 0.8667, "x": 0.976}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}]}, "ear": {"rotate": [{"angle": 5.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -18.85, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 16.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -0.13}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -0.02, "y": -3.78, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"x": 0.984, "curve": "stepped"}, {"time": 0.1333, "x": 0.984, "curve": "stepped"}, {"time": 0.3667, "x": 0.984, "curve": "stepped"}, {"time": 0.5667, "x": 0.984, "curve": "stepped"}, {"time": 0.8667, "x": 0.984}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "body_10": {"rotate": [{"angle": 5.08, "curve": 0.382, "c2": 0.58, "c3": 0.734}, {"time": 0.1333, "angle": -11.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 34.32, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -37.82, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -2.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -9.58}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 0.7, "curve": "stepped"}, {"time": 0.8667}]}, "body_9": {"rotate": [{"angle": -13.95, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2, "angle": 7.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -24.77, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -15.97}], "translate": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4333, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667}]}, "body_7": {"rotate": [{"angle": -7.91, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 17.07, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -15.14, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -6.34}], "translate": [{"x": -5.26, "y": 2.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 5.5, "y": -7.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "body_8": {"rotate": [{"angle": 0.45, "curve": "stepped"}, {"time": 0.1333, "angle": 0.45, "curve": "stepped"}, {"time": 0.3667, "angle": 0.45, "curve": "stepped"}, {"time": 0.5667, "angle": 0.45, "curve": "stepped"}, {"time": 0.8667, "angle": 0.45}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "body_6": {"rotate": [{"angle": -7.85, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 17.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -16.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.06, "curve": "stepped"}, {"time": 0.8667, "angle": 0.06}], "translate": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}, "head_2": {"rotate": [{"angle": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -28.38, "curve": 0, "c2": 1, "c3": 0.75}, {"time": 0.2667, "angle": 15.19, "curve": "stepped"}, {"time": 0.3667, "angle": 15.19, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.63, "curve": "stepped"}, {"time": 0.8667, "angle": -4.63}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -0.48, "y": 0.64, "curve": "stepped"}, {"time": 0.2667, "x": -0.48, "y": 0.64, "curve": "stepped"}, {"time": 0.3667, "x": -0.48, "y": 0.64, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "scale": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}], "shear": [{"curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8667}]}}, "deform": {"default": {"body_5": {"body_5": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "offset": 76, "vertices": [-5.50472, -1.3507, -5.03282, -2.6071, 4.94838, 2.76402, -11.00384, 0.34489, -10.77641, -2.25196, 10.70033, 2.58961, -9.06434, -7.55827, -7.03321, -9.47754, 6.73174, 9.69398, -18.47877, 0.3735, -18.04848, -3.9816, -17.38918, -4.02362, -12.77163, -0.42646, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.55709, -1.82134, -2.05717, -2.3715, -8.31656, 0.9489, -8.3065, -1.03302, 8.26994, 1.29368, -14.68172, 6.52438], "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}}}}}}