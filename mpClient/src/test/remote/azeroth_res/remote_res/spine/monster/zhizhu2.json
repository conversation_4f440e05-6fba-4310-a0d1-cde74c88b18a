{"skeleton": {"hash": "osl5sxti22zIt5onm6D9LCgZf+0", "spine": "3.8.99", "x": -44.66, "y": -18.11, "width": 89.85, "height": 69.7, "images": "./images/", "audio": "D:/spine动效/怪物/幽影谷"}, "bones": [{"name": "root", "scaleX": 0.4, "scaleY": 0.4}, {"name": "zong", "parent": "root"}, {"name": "body", "parent": "zong", "x": -15.25, "y": 72.19}, {"name": "body5", "parent": "body", "length": 16.14, "rotation": -24.44, "x": 9.69, "y": -0.67}, {"name": "body6", "parent": "body5", "length": 15.01, "rotation": -7.83, "x": 16.14}, {"name": "head", "parent": "body6", "length": 26.05, "rotation": -3.86, "x": 15.86, "y": 0.54}, {"name": "tooth", "parent": "head", "length": 22.09, "rotation": -33.51, "x": 26.47, "y": -9.63}, {"name": "tooth2", "parent": "head", "length": 21.84, "rotation": -60.01, "x": 42.62, "y": 5.07}, {"name": "arm_R3", "parent": "body", "length": 36.86, "rotation": -124.85, "x": 9.41, "y": -45.37}, {"name": "arm_R4", "parent": "arm_R3", "length": 31.68, "rotation": 19.42, "x": 39.14, "y": -4.53}, {"name": "arm_R6", "parent": "zong", "length": 35.05, "rotation": -101.72, "x": -41.22, "y": 31.26, "color": "ff3f00ff"}, {"name": "arm_R5", "parent": "body", "length": 37.71, "rotation": -147.3, "x": -19.33, "y": -22.01}, {"name": "arm_R7", "parent": "arm_R5", "length": 52.27, "rotation": 18.15, "x": 37.71}, {"name": "arm_R9", "parent": "zong", "length": 53.99, "rotation": -106.46, "x": -72.34, "y": 50.31, "color": "ff3f00ff"}, {"name": "arm_R", "parent": "body5", "length": 22, "rotation": 103.16, "x": 26, "y": -40.53}, {"name": "arm_R2", "parent": "arm_R", "length": 32.94, "rotation": -141, "x": 25.36, "y": 0.3}, {"name": "arm_R10", "parent": "zong", "length": 42.61, "rotation": -65.67, "x": 21.21, "y": 18.95, "color": "ff3f00ff"}, {"name": "arm_L2", "parent": "body5", "length": 35.44, "rotation": -19.09, "x": 41.68, "y": 4.37}, {"name": "arm_L3", "parent": "arm_L2", "length": 22.85, "rotation": -31.21, "x": 34.89, "y": -1.14}, {"name": "arm_L5", "parent": "zong", "length": 25.67, "rotation": -84.15, "x": 68.97, "y": 65.33, "color": "ff3f00ff"}, {"name": "arm_L4", "parent": "body", "length": 25.71, "rotation": 48.45, "x": 70.12, "y": -23.4}, {"name": "arm_L6", "parent": "arm_L4", "length": 34.24, "rotation": -111.95, "x": 26.31, "y": -0.05}, {"name": "arm_L8", "parent": "zong", "length": 40.11, "rotation": -96.03, "x": 87.98, "y": 37.76, "color": "ff3f00ff"}, {"name": "arm_L", "parent": "body", "length": 27.06, "rotation": -77.16, "x": 34.6, "y": 5.66}, {"name": "arm_L7", "parent": "arm_L", "length": 27.25, "rotation": -21.42, "x": 27.06}, {"name": "arm_L9", "parent": "zong", "length": 26.97, "rotation": -97.83, "x": 44.7, "y": 77.44, "color": "ff3f00ff"}, {"name": "yinying", "parent": "root", "length": 198.16, "rotation": -1.08, "x": -98.61, "y": 1.76}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "arm_L9", "bone": "arm_L7", "attachment": "arm_L9"}, {"name": "arm_L8", "bone": "arm_L", "attachment": "arm_L8"}, {"name": "arm_L7", "bone": "arm_L3", "attachment": "arm_L7"}, {"name": "arm_L6", "bone": "arm_L2", "attachment": "arm_L6"}, {"name": "arm_L5", "bone": "arm_L4", "attachment": "arm_L5"}, {"name": "arm_L4", "bone": "arm_L6", "attachment": "arm_L4"}, {"name": "arm_L3", "bone": "arm_L8", "attachment": "arm_L3"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "arm_L2", "bone": "tooth2", "attachment": "arm_L2"}, {"name": "arm_L", "bone": "tooth2", "attachment": "arm_L"}, {"name": "arm_R9", "bone": "tooth", "attachment": "arm_R9"}, {"name": "arm_R8", "bone": "tooth", "attachment": "arm_R8"}, {"name": "arm_R7", "bone": "arm_R5", "attachment": "arm_R7"}, {"name": "arm_R6", "bone": "arm_R7", "attachment": "arm_R6"}, {"name": "arm_R5", "bone": "arm_R9", "attachment": "arm_R5"}, {"name": "arm_R4", "bone": "arm_R3", "attachment": "arm_R4"}, {"name": "arm_R10", "bone": "arm_R"}, {"name": "arm_R3", "bone": "arm_R4", "attachment": "arm_R3"}, {"name": "arm_R2", "bone": "arm_R6", "attachment": "arm_R2"}, {"name": "arm_R", "bone": "arm_R2", "attachment": "arm_R"}], "ik": [{"name": "arm_L5", "order": 5, "bones": ["arm_L2", "arm_L3"], "target": "arm_L5", "bendPositive": false}, {"name": "arm_L8", "order": 1, "bones": ["arm_L4", "arm_L6"], "target": "arm_L8", "bendPositive": false}, {"name": "arm_L9", "order": 4, "bones": ["arm_L", "arm_L7"], "target": "arm_L9", "bendPositive": false}, {"name": "arm_R6", "order": 3, "bones": ["arm_R3", "arm_R4"], "target": "arm_R6"}, {"name": "arm_R9", "bones": ["arm_R5", "arm_R7"], "target": "arm_R9"}, {"name": "arm_R10", "order": 2, "bones": ["arm_R", "arm_R2"], "target": "arm_R10", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"x": 15.01, "y": 3.78, "rotation": 96.15, "width": 16, "height": 23}}, "arm_L2": {"arm_L2": {"x": 2.3, "y": 0.4, "rotation": 96.15, "width": 18, "height": 21}}, "arm_L3": {"arm_L3": {"x": 22.58, "y": 3.3, "rotation": 96.03, "width": 25, "height": 50}}, "arm_L4": {"arm_L4": {"x": 19.27, "y": 1.87, "rotation": 63.16, "width": 37, "height": 50}}, "arm_L5": {"arm_L5": {"x": 9.05, "y": 1.4, "rotation": -47.99, "width": 35, "height": 33}}, "arm_L6": {"arm_L6": {"x": 19.57, "y": 0.72, "rotation": -51.24, "width": 41, "height": 48}}, "arm_L7": {"arm_L7": {"type": "mesh", "uvs": [0.4526, 0.01639, 0.58042, 0.04915, 0.73224, 0.15183, 0.921, 0.34537, 0.96613, 0.43284, 0.9661, 0.58245, 0.96605, 0.75527, 0.91229, 0.90266, 0.82857, 0.98352, 0.7068, 0.9837, 0.58566, 0.86652, 0.47676, 0.74395, 0.37798, 0.63276, 0.25004, 0.51935, 0.11232, 0.39727, 0.03399, 0.28408, 0.03405, 0.14967, 0.10357, 0.0826, 0.16936, 0.0505, 0.31023, 0.01643], "triangles": [5, 12, 4, 11, 12, 5, 6, 11, 5, 10, 11, 6, 7, 10, 6, 7, 9, 10, 8, 9, 7, 17, 19, 16, 16, 19, 15, 19, 14, 15, 19, 17, 18, 2, 13, 14, 3, 4, 13, 2, 14, 19, 1, 19, 0, 2, 19, 1, 3, 13, 2, 13, 4, 12], "vertices": [1, 18, -5.42, 8.77, 1, 1, 18, -1.8, 10.89, 1, 1, 18, 5.76, 11.37, 1, 2, 18, 18.51, 9.89, 0.97986, 19, -8.23, 6.99, 0.02014, 2, 18, 23.66, 8.24, 0.72582, 19, -2.88, 7.76, 0.27418, 2, 18, 31.29, 3.5, 0.01211, 19, 6.05, 6.84, 0.98789, 1, 19, 16.37, 5.78, 1, 1, 19, 25, 3.33, 1, 1, 19, 29.58, 0.42, 1, 1, 19, 29.23, -3.09, 1, 1, 19, 21.88, -5.87, 1, 2, 18, 32.03, -13.67, 0.01581, 19, 14.24, -8.26, 0.98419, 2, 18, 24.85, -12.58, 0.23347, 19, 7.31, -10.43, 0.76653, 2, 18, 17.11, -12.14, 0.73519, 19, 0.17, -13.43, 0.26481, 2, 18, 8.78, -11.67, 0.97615, 19, -7.53, -16.65, 0.02385, 2, 18, 1.81, -10.01, 0.99997, 19, -14.52, -18.22, 3e-05, 1, 18, -5.03, -5.76, 1, 1, 18, -7.39, -1.92, 1, 1, 18, -8.02, 0.72, 1, 1, 18, -7.6, 5.27, 1], "hull": 20, "edges": [0, 38, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 24, 26, 26, 28, 8, 10, 10, 12, 20, 22, 22, 24], "width": 29, "height": 60}}, "arm_L8": {"arm_L8": {"x": 13.08, "y": 0.21, "rotation": -61.62, "width": 34, "height": 51}}, "arm_L9": {"arm_L9": {"type": "mesh", "uvs": [0.33142, 0.01526, 0.53341, 0.01538, 0.6881, 0.05104, 0.88367, 0.23348, 0.96768, 0.39071, 0.96749, 0.48557, 0.96704, 0.70387, 0.93233, 0.80114, 0.85698, 0.90471, 0.76506, 0.98629, 0.62174, 0.9844, 0.50667, 0.9038, 0.366, 0.64089, 0.26004, 0.54158, 0.13029, 0.41999, 0.03275, 0.2973, 0.03299, 0.17153, 0.0996, 0.10918, 0.26629, 0.03112], "triangles": [9, 10, 8, 10, 11, 8, 8, 11, 7, 11, 12, 7, 7, 12, 6, 6, 12, 5, 12, 13, 5, 5, 13, 4, 3, 14, 2, 17, 18, 2, 13, 14, 4, 3, 4, 14, 18, 1, 2, 18, 0, 1, 15, 2, 14, 15, 16, 17, 17, 2, 15], "vertices": [1, 24, -6.48, 1.22, 1, 1, 24, -3.7, 6.6, 1, 1, 24, 0.45, 9.68, 1, 1, 24, 13.52, 9.55, 1, 2, 24, 23.62, 7.18, 0.98364, 25, -7.1, 3.78, 0.01636, 2, 24, 29.01, 4.39, 0.43418, 25, -1.08, 4.6, 0.56582, 1, 25, 12.76, 6.49, 1, 1, 25, 19.07, 6.31, 1, 1, 25, 25.94, 4.97, 1, 1, 25, 31.49, 2.95, 1, 1, 25, 31.96, -1.32, 1, 1, 25, 27.32, -5.45, 1, 2, 24, 29.58, -16.2, 0.25805, 25, 11.22, -11.92, 0.74195, 2, 24, 22.48, -16.12, 0.65365, 25, 5.36, -15.93, 0.34635, 2, 24, 13.78, -16.01, 0.93489, 25, -1.82, -20.85, 0.06511, 2, 24, 5.46, -15.02, 0.99704, 25, -9.2, -24.82, 0.00296, 1, 24, -1.7, -11.32, 1, 1, 24, -4.33, -7.72, 1, 1, 24, -6.48, -0.98, 1], "hull": 19, "edges": [0, 36, 0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 28, 30, 30, 32, 32, 34, 34, 36, 8, 10, 10, 12, 24, 26, 26, 28], "width": 30, "height": 64}}, "arm_R": {"arm_R": {"type": "mesh", "uvs": [0.35158, 0.01051, 0.42648, 0.05634, 0.49086, 0.13455, 0.5759, 0.23785, 0.64105, 0.39714, 0.68691, 0.50928, 0.68576, 0.56472, 0.77047, 0.70803, 0.81891, 0.78999, 0.99252, 0.95353, 0.96293, 0.99022, 0.84652, 0.98897, 0.62219, 0.92802, 0.45345, 0.82618, 0.33721, 0.75602, 0.25009, 0.65818, 0.16677, 0.56461, 0.11068, 0.44972, 0.11073, 0.32704, 0.11081, 0.13468, 0.20378, 0.03337, 0.27741, 0.01112, 0.31202, 0.15533, 0.29764, 0.32412, 0.36478, 0.44925, 0.49428, 0.56275, 0.57102, 0.69661, 0.65256, 0.77519, 0.75328, 0.88286], "triangles": [10, 11, 9, 12, 28, 11, 11, 28, 9, 28, 8, 9, 12, 27, 28, 12, 13, 27, 28, 27, 8, 13, 26, 27, 13, 14, 26, 27, 7, 8, 27, 26, 7, 14, 15, 26, 26, 6, 7, 15, 25, 26, 26, 25, 6, 15, 16, 25, 6, 25, 5, 5, 25, 4, 16, 17, 24, 16, 24, 25, 23, 17, 18, 17, 23, 24, 25, 24, 4, 4, 24, 23, 4, 23, 3, 3, 23, 22, 23, 18, 22, 22, 2, 3, 22, 18, 19, 19, 20, 22, 20, 21, 22, 22, 1, 2, 1, 21, 0, 1, 22, 21], "vertices": [1, 15, -9.89, 10.57, 1, 1, 15, -4.49, 12.53, 1, 1, 15, 3.28, 12.77, 1, 1, 15, 13.56, 13.07, 1, 2, 15, 28.04, 10.01, 0.90081, 16, -6.55, 12.4, 0.09919, 2, 15, 38.68, 8.37, 0.39969, 16, 4.56, 7.88, 0.60031, 2, 15, 43.56, 7.5, 0.1391, 16, 9.32, 5.6, 0.8609, 1, 16, 22.99, 4.72, 1, 1, 16, 30.71, 4.1, 1, 1, 16, 47.84, 6.65, 1, 1, 16, 50.16, 3.84, 1, 1, 16, 47.46, -1.84, 1, 1, 16, 37.53, -10.64, 1, 1, 16, 25.52, -15.21, 1, 2, 15, 59.85, -10.89, 0.00133, 16, 17.23, -18.36, 0.99867, 2, 15, 49.84, -15.78, 0.14909, 16, 5.7, -19.65, 0.85091, 2, 15, 35.15, -19.24, 0.65132, 16, -10.42, -20.12, 0.34868, 2, 15, 20.45, -17.75, 0.97501, 16, -25.47, -14.91, 0.02499, 1, 15, 10.22, -13.06, 1, 1, 15, -5.32, -5.87, 1, 1, 15, -11.4, 2.47, 1, 1, 15, -11.53, 6.92, 1, 1, 15, 0.91, 3.22, 1, 1, 15, 14.22, -3.79, 1, 1, 15, 25.85, -5.17, 1, 2, 15, 43.09, -2.51, 0.05099, 16, 4.79, -3.58, 0.94901, 1, 16, 17.63, -4.67, 1, 1, 16, 25.81, -3.54, 1, 1, 16, 36.79, -2.53, 1], "hull": 22, "edges": [0, 42, 0, 2, 10, 12, 16, 18, 18, 20, 20, 22, 22, 24, 32, 34, 38, 40, 40, 42, 24, 26, 26, 28, 12, 14, 14, 16, 28, 30, 30, 32, 6, 8, 8, 10, 34, 36, 36, 38, 2, 4, 4, 6], "width": 54, "height": 89}}, "arm_R2": {"arm_R2": {"x": 21.63, "y": 3.06, "rotation": 101.72, "width": 28, "height": 57}}, "arm_R3": {"arm_R3": {"x": 17.98, "y": -4.29, "rotation": 115.8, "width": 43, "height": 55}}, "arm_R4": {"arm_R4": {"x": 22.11, "y": -1.81, "rotation": -129.82, "width": 40, "height": 52}}, "arm_R5": {"arm_R5": {"x": 27.1, "y": 3.03, "rotation": 106.46, "width": 33, "height": 69}}, "arm_R6": {"arm_R6": {"x": 32.55, "y": -0.69, "rotation": 134.08, "width": 55, "height": 70}}, "arm_R7": {"arm_R7": {"x": 12.39, "y": 2.58, "rotation": -92.11, "width": 34, "height": 67}}, "arm_R8": {"arm_R8": {"x": 17.43, "y": 2.45, "rotation": 69.65, "width": 25, "height": 22}}, "arm_R9": {"arm_R9": {"x": 6.33, "y": -3.03, "rotation": 69.65, "width": 23, "height": 27}}, "arm_R10": {"arm_R4": {"x": 17.17, "y": -0.27, "scaleX": 0.8506, "scaleY": 0.7276, "rotation": -120.89, "width": 40, "height": 52}}, "body": {"body": {"x": -0.86, "y": 2.27, "width": 111, "height": 109}}, "head": {"head": {"x": 10.51, "y": 3.51, "rotation": 36.14, "width": 66, "height": 63}}, "yinying": {"yinying": {"x": 99.11, "y": 9.01, "scaleX": 1.1079, "scaleY": 1.1079, "width": 201, "height": 94}}}}], "animations": {"attack": {"bones": {"arm_L6": {"rotate": [{"angle": -9.29}]}, "arm_L8": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -14.29, "y": 17.02, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.66, "y": 8.44, "curve": "stepped"}, {"time": 0.3333, "x": -24.66, "y": 8.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "arm_L": {"rotate": [{"angle": 5.03}]}, "arm_L7": {"rotate": [{"angle": -14.77}]}, "body": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 3.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 25.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 1.44, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"y": -2.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -14.93, "y": -20.08, "curve": "stepped"}, {"time": 0.3333, "x": -14.93, "y": -20.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 4.54, "y": -5.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 26.51, "y": -24.16, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 30.64, "y": -26.64, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -2.56}]}, "body6": {"rotate": [{"angle": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.17, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.67, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.74, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.28}], "translate": [{"x": -0.03, "y": 0.07}]}, "body5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -3.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.95, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 5.55, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -5.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.59, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "head": {"rotate": [{"angle": 0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -3.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.79, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 5.7, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.66, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -4.86, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.15}]}, "tooth": {"rotate": [{"angle": 3.77, "curve": "stepped"}, {"time": 0.1667, "angle": 3.77, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 15.16, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -33.02, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -39.14, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 11.96, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.77}]}, "tooth2": {"rotate": [{"angle": -4.04, "curve": "stepped"}, {"time": 0.1667, "angle": -4.04, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -9.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 37.53, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 44.33, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -12.13, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.04}]}, "arm_R3": {"rotate": [{"angle": -6.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -10.99, "y": 14.37, "curve": "stepped"}, {"time": 0.3333, "x": -10.99, "y": 14.37, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.3667, "x": -33.75, "y": 7.85, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.4, "x": -43.36, "y": 11.55, "curve": "stepped"}, {"time": 0.6667, "x": -43.36, "y": 11.55, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -23.54, "y": 0.85, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "arm_R4": {"rotate": [{"angle": 10.42}]}, "arm_R5": {"rotate": [{"angle": -5.63}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -26.39, "y": 10.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -29.91, "y": 10.87, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -14.6, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "arm_R7": {"rotate": [{"angle": 10.48}]}, "arm_R9": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -5.29, "y": 6.49, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -9.09, "curve": "stepped"}, {"time": 0.3333, "x": -9.09, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "arm_R": {"rotate": [{"angle": -3.68}], "translate": [{"x": 2.1, "y": 4.31, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -20.34, "y": 26.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -17.65, "y": 28.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -33.36, "y": 1.38, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -23.52, "y": 8.79, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.5333, "x": -40.82, "y": 16.52, "curve": 0.25, "c4": 0.79}, {"time": 0.5667, "x": -34.15, "y": 16.39, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.6667, "x": -28.48, "y": 18.09, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -21.89, "y": 12.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 2.1, "y": 4.31}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.908, "curve": "stepped"}, {"time": 0.3333, "x": 0.908, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.767, "curve": "stepped"}, {"time": 0.8333, "x": 0.767, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "arm_R2": {"rotate": [{"angle": 11.75}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 0.827, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "arm_R10": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -19.67, "y": 9.97, "curve": "stepped"}, {"time": 0.3333, "x": -19.67, "y": 9.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4}]}, "arm_L2": {"rotate": [{"angle": -0.47}]}, "arm_L3": {"rotate": [{"angle": 2.91}]}, "arm_L4": {"rotate": [{"angle": 3.81}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -24.14, "y": 9.83, "curve": "stepped"}, {"time": 0.3333, "x": -24.14, "y": 9.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -47.7, "y": -0.23, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -52.73, "y": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.21, "y": -5.65, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "yinying": {"translate": [{}, {"time": 0.1667, "x": -16.87, "curve": "stepped"}, {"time": 0.3333, "x": -16.87}, {"time": 0.4, "x": 1.88, "curve": "stepped"}, {"time": 0.6667, "x": 1.88}, {"time": 0.8333}], "scale": [{}, {"time": 0.1667, "x": 1.055, "curve": "stepped"}, {"time": 0.3333, "x": 1.055}, {"time": 0.4, "x": 1.092, "curve": "stepped"}, {"time": 0.6667, "x": 1.092}, {"time": 0.8333}]}}}, "idle": {"bones": {"body": {"translate": [{"y": -11.4, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -3.79, "y": -2.53, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": -11.4}]}, "body6": {"rotate": [{"angle": 0.28, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 4.69, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 0.28}], "translate": [{"x": -0.03, "y": 0.07, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": -0.57, "y": 1.25, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "x": -0.03, "y": 0.07}]}, "body5": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.01, "y": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "head": {"rotate": [{"angle": 0.15, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.19, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.15}]}, "tooth": {"rotate": [{"angle": 3.77, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 10.24, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 3.77}]}, "tooth2": {"rotate": [{"angle": -4.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -10.98, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -4.04}]}, "arm_R3": {"rotate": [{"angle": -6.29}]}, "arm_R4": {"rotate": [{"angle": 10.42}]}, "arm_R5": {"rotate": [{"angle": -5.63}]}, "arm_R7": {"rotate": [{"angle": 10.48}]}, "arm_R": {"rotate": [{"angle": -3.68}], "translate": [{"x": 1.85, "y": 12.56, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "x": -8.65, "y": 1.27, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 1.6667, "x": 1.85, "y": 12.56}]}, "arm_R2": {"rotate": [{"angle": 11.75}]}, "arm_L2": {"rotate": [{"angle": -0.47}]}, "arm_L3": {"rotate": [{"angle": 2.91}]}, "arm_L4": {"rotate": [{"angle": 3.81}]}, "arm_L6": {"rotate": [{"angle": -9.29}]}, "arm_L": {"rotate": [{"angle": 5.03}]}, "arm_L7": {"rotate": [{"angle": -14.77}]}}}, "run": {"bones": {"arm_R6": {"translate": [{}, {"time": 0.1667, "x": -15.31, "y": 18.77}, {"time": 0.3333, "x": -4.28, "y": 36.72}, {"time": 0.5, "x": 15.64, "y": 7.49}, {"time": 0.6667}]}, "arm_R10": {"rotate": [{"angle": -9.06}, {"time": 0.1667, "angle": -32.82}, {"time": 0.3333, "angle": -42}, {"time": 0.5, "angle": -32.82}, {"time": 0.6667, "angle": -9.06}], "translate": [{"x": -13.61, "y": 5.09}, {"time": 0.1667, "x": -29.02, "y": 11.61}, {"time": 0.3333, "x": -44.54, "y": 10.37}, {"time": 0.5, "x": -24.74, "y": 28.9}, {"time": 0.6667, "x": -13.61, "y": 5.09}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 16.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 10.18, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "arm_R3": {"rotate": [{"angle": -6.29}]}, "arm_R4": {"rotate": [{"angle": 10.42}]}, "arm_R5": {"rotate": [{"angle": -5.63}]}, "arm_R7": {"rotate": [{"angle": 10.48}]}, "arm_R9": {"rotate": [{}, {"time": 0.3333, "angle": 26.62}, {"time": 0.5, "angle": 7.47}, {"time": 0.6667}], "translate": [{"x": -30.85, "y": 6.7}, {"time": 0.1667, "x": -18.52, "y": 35.69}, {"time": 0.3333, "x": -4.12, "y": 3.19}, {"time": 0.5, "x": -12.04, "y": 5.64}, {"time": 0.6667, "x": -30.85, "y": 6.7}]}, "arm_R": {"rotate": [{"angle": -3.68}], "translate": [{"x": -12.88, "y": 9.09}, {"time": 0.0667, "x": -15.47, "y": 3.12}, {"time": 0.1333, "x": -21.58, "y": -3.92}, {"time": 0.2333, "x": -28.61, "y": 7}, {"time": 0.2667, "x": -35.78, "y": 8.18}, {"time": 0.3333, "x": -35.33, "y": 5.94}, {"time": 0.5, "x": -34.8, "y": 20.92}, {"time": 0.5333, "x": -20.76, "y": 16.92}, {"time": 0.5667, "x": -22.37, "y": 17.48}, {"time": 0.6333, "x": -5.36, "y": 10.45}, {"time": 0.6667, "x": -12.88, "y": 9.09}]}, "arm_R2": {"rotate": [{"angle": 11.75}]}, "arm_L2": {"rotate": [{"angle": -0.47}]}, "arm_L3": {"rotate": [{"angle": 2.91}]}, "arm_L5": {"translate": [{"x": -1.95, "y": -1.69}, {"time": 0.1667, "x": -17.28, "y": 6.99}, {"time": 0.3333, "x": -22.24, "y": 4.76}, {"time": 0.5, "x": -12.08, "y": 36.94}, {"time": 0.6667, "x": -1.95, "y": -1.69}]}, "arm_L4": {"rotate": [{"angle": 3.81}], "translate": [{"x": -40.01, "y": 2.47}]}, "arm_L6": {"rotate": [{"angle": -9.29}]}, "arm_L8": {"rotate": [{"angle": 20.5}, {"time": 0.1667, "angle": 1.15}, {"time": 0.3333, "angle": 30.81}, {"time": 0.5, "angle": 26.01}, {"time": 0.6667, "angle": 20.5}], "translate": [{"x": -26.79, "y": 20.5}, {"time": 0.1667, "x": -32.81, "y": 43.09}, {"time": 0.3333, "x": -28.23, "y": 43.77}, {"time": 0.5, "x": -14.89, "y": 20.36}, {"time": 0.6667, "x": -26.79, "y": 20.5}]}, "arm_L": {"rotate": [{"angle": 5.03}], "translate": [{"x": 13.34, "y": 5.93}]}, "arm_L7": {"rotate": [{"angle": -14.77}]}, "arm_L9": {"rotate": [{"angle": 19.38}], "translate": [{"x": 2.56, "y": 16.56}, {"time": 0.1667, "x": 15.44, "y": 27.91}, {"time": 0.3333, "x": 40.43, "y": 1.81}, {"time": 0.5, "x": 18.48, "y": 6.34}, {"time": 0.6667, "x": 2.56, "y": 16.56}]}, "yinying": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 6.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.927, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}}}}}