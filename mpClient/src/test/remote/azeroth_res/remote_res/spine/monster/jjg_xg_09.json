{"skeleton": {"hash": "AfSEJ1sPI10ZgjkBDd5IMAFi15k", "spine": "3.8.99", "x": -56.94, "y": -17.73, "width": 129, "height": 116.62, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/荆棘谷/扎克乌尔"}, "bones": [{"name": "root", "color": "9c9c9c00"}, {"name": "bone", "parent": "root", "length": 35.19, "color": "9c9c9c00"}, {"name": "body", "parent": "bone", "length": 30.51, "rotation": -7.13, "x": -6.34, "y": 23.24}, {"name": "body3", "parent": "body", "length": 27.5, "rotation": 81.52, "x": 1.56, "y": 1.41}, {"name": "body4", "parent": "body3", "length": 32.31, "rotation": 47.77, "x": 27.5}, {"name": "head", "parent": "body4", "length": 21.27, "rotation": -60.85, "x": 4.2, "y": -5.28}, {"name": "arm_R", "parent": "body4", "length": 39.81, "rotation": 135.36, "x": 34.51, "y": 13.48}, {"name": "hand_R", "parent": "arm_R", "length": 23.53, "rotation": 27.73, "x": 40.65, "y": 0.19}, {"name": "metal_R", "parent": "body4", "length": 33.78, "rotation": 91.2, "x": 26.92, "y": 6.26}, {"name": "metal_L", "parent": "body4", "length": 19.33, "rotation": -174.95, "x": 2.92, "y": -25.97}, {"name": "arm_L2", "parent": "body4", "length": 37.83, "rotation": 167.97, "x": 7.08, "y": -21.59}, {"name": "arm_L", "parent": "arm_L2", "length": 15.84, "rotation": -20.67, "x": 38.15, "y": -0.43}, {"name": "weapons", "parent": "arm_L", "length": 32.89, "rotation": 101.69, "x": 15.84, "y": -0.3}, {"name": "leg_R", "parent": "body", "length": 17.82, "rotation": -84.39, "x": -7.6, "y": 7.62}, {"name": "leg_R3", "parent": "leg_R", "length": 17.77, "rotation": -43.21, "x": 18.06, "y": 0.01}, {"name": "leg_R2", "parent": "leg_R3", "length": 21.24, "rotation": -0.95, "x": 17.69, "y": -0.08, "transform": "noRotationOrReflection"}, {"name": "leg_L", "parent": "body", "length": 16.07, "rotation": -52.92, "x": 14.17, "y": 6.06}, {"name": "leg_L3", "parent": "leg_L", "length": 15.91, "rotation": -65.29, "x": 15.34, "y": 0.15}, {"name": "leg_L2", "parent": "leg_L3", "length": 20.78, "rotation": -1.63, "x": 15.84, "y": 0.1, "transform": "noRotationOrReflection"}, {"name": "yinying", "parent": "bone", "x": 0.42, "y": 4.74}, {"name": "leg_R4", "parent": "root", "x": -25.95, "y": 1.14, "color": "ff0000ff"}, {"name": "leg_L4", "parent": "root", "x": 7.07, "y": 1.37, "color": "ff0000ff"}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "arm_L2", "bone": "arm_L2", "attachment": "arm_L2"}, {"name": "arm_L", "bone": "arm_L", "attachment": "arm_L"}, {"name": "weapons", "bone": "weapons", "attachment": "weapons"}, {"name": "hand_L", "bone": "arm_L", "attachment": "hand_L"}, {"name": "metal_L", "bone": "metal_L", "attachment": "metal_L"}, {"name": "leg_L3", "bone": "leg_L3", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L", "attachment": "leg_L"}, {"name": "hair_7", "bone": "leg_L3", "attachment": "hair_7"}, {"name": "hair_6", "bone": "leg_L3", "attachment": "hair_6"}, {"name": "hair_5", "bone": "leg_L3", "attachment": "hair_5"}, {"name": "metal_L2", "bone": "leg_L3", "attachment": "metal_L2"}, {"name": "leg_R3", "bone": "leg_R3", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R2", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "hair_3", "bone": "leg_R3", "attachment": "hair_3"}, {"name": "hair", "bone": "leg_R3", "attachment": "hair"}, {"name": "hair_8", "bone": "leg_R3", "attachment": "hair_4"}, {"name": "hair_9", "bone": "leg_R3", "attachment": "hair_2"}, {"name": "metal_R2", "bone": "leg_R", "attachment": "metal_R2"}, {"name": "body", "bone": "body3", "attachment": "body"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye_R", "bone": "head", "color": "ffffff00", "attachment": "eye_R"}, {"name": "eye_L", "bone": "head", "color": "ffffff00", "attachment": "eye_L"}, {"name": "arm_R", "bone": "arm_R", "attachment": "arm_R"}, {"name": "metal_R", "bone": "metal_R", "attachment": "metal_R"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}], "ik": [{"name": "leg_L", "order": 1, "bones": ["leg_L", "leg_L3"], "target": "leg_L4", "bendPositive": false}, {"name": "leg_R", "bones": ["leg_R", "leg_R3"], "target": "leg_R4", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"x": 5.56, "y": 0.09, "rotation": 90.54, "width": 34, "height": 32}}, "arm_L2": {"arm_L2": {"x": 21.35, "y": 1.51, "rotation": 69.86, "width": 34, "height": 48}}, "arm_R": {"arm_R": {"x": 23.92, "y": -1.3, "rotation": 102.48, "width": 41, "height": 52}}, "body": {"body": {"type": "mesh", "uvs": [0.25982, 0.00588, 0.11029, 0.07838, 0.00697, 0.20404, 0, 0.38771, 0, 0.55688, 0.03416, 0.72363, 0.09126, 0.84204, 0.28429, 0.92421, 0.42566, 0.94596, 0.65676, 1, 0.86882, 0.98946, 0.98029, 0.85654, 1, 0.70913, 0.95582, 0.56413, 1, 0.45538, 0.95854, 0.32488, 0.89601, 0.30313, 0.83619, 0.16282, 0.76007, 0.06616, 0.52897, 0.02024, 0.29516, 0.07587, 0.42566, 0.27162, 0.54801, 0.45528, 0.5181, 0.62928, 0.44198, 0.83228, 0.74647, 0.88303, 0.7791, 0.64862, 0.76279, 0.45045, 0.63772, 0.21603, 0.50451, 0.08795, 0.17282, 0.77912, 0.21632, 0.60995, 0.21904, 0.46978, 0.18641, 0.30787, 0.15107, 0.12903], "triangles": [4, 3, 32, 3, 33, 32, 32, 21, 22, 32, 33, 21, 22, 28, 27, 22, 21, 28, 16, 28, 17, 16, 27, 28, 3, 2, 33, 2, 34, 33, 33, 20, 21, 33, 34, 20, 21, 29, 28, 21, 20, 29, 28, 29, 18, 28, 18, 17, 18, 29, 19, 2, 1, 34, 20, 34, 0, 29, 20, 19, 34, 1, 0, 20, 0, 19, 9, 25, 10, 25, 9, 24, 10, 25, 11, 9, 8, 24, 8, 7, 24, 6, 30, 7, 7, 30, 24, 24, 23, 25, 25, 23, 26, 25, 26, 11, 26, 22, 27, 22, 26, 23, 11, 26, 12, 6, 5, 30, 30, 31, 24, 24, 31, 23, 30, 5, 31, 5, 4, 31, 26, 13, 12, 26, 27, 13, 31, 32, 23, 23, 32, 22, 31, 4, 32, 13, 27, 14, 14, 16, 15, 16, 14, 27], "vertices": [2, 3, 53.7, 27.14, 0.00294, 4, 37.71, -1.16, 0.99706, 2, 3, 46.1, 34.95, 0.02303, 4, 38.38, 9.72, 0.97697, 2, 3, 35.61, 38.89, 0.1043, 4, 34.24, 20.13, 0.8957, 2, 3, 22.75, 35.76, 0.27742, 4, 23.29, 27.55, 0.72258, 2, 3, 11.02, 32.49, 0.51727, 4, 12.98, 34.04, 0.48273, 2, 3, 0.05, 27.15, 0.73234, 4, 1.65, 38.58, 0.26766, 2, 3, -7.18, 21.34, 0.88349, 4, -7.51, 40.02, 0.11651, 2, 3, -9.56, 7.85, 0.95684, 4, -19.1, 32.72, 0.04316, 2, 3, -8.63, -1.29, 0.99383, 4, -25.24, 25.89, 0.00617, 2, 3, -8.4, -16.58, 0.99995, 4, -36.41, 15.44, 5e-05, 1, 3, -4.02, -29.45, 1, 2, 3, 7.11, -33.74, 0.9981, 4, -38.69, -7.58, 0.0019, 2, 3, 17.68, -32.11, 0.96359, 4, -30.38, -14.3, 0.03641, 2, 3, 26.97, -26.57, 0.87448, 4, -20.03, -17.47, 0.12552, 2, 3, 35.27, -27.19, 0.7244, 4, -14.91, -24.03, 0.2756, 2, 3, 43.61, -22.11, 0.5667, 4, -5.54, -26.79, 0.4333, 2, 3, 44.04, -17.83, 0.35532, 4, -2.09, -24.23, 0.64468, 2, 3, 52.74, -11.43, 0.1806, 4, 8.5, -26.37, 0.8194, 2, 3, 58.13, -4.87, 0.03948, 4, 16.99, -25.95, 0.96052, 2, 3, 57.34, 10.27, 0.00663, 4, 27.66, -15.19, 0.99337, 1, 4, 32.24, -0.39, 1, 2, 3, 38.13, 11.77, 1e-05, 4, 15.86, 0.04, 0.99999, 2, 3, 27.5, 0.67, 0.36875, 4, 0.5, 0.45, 0.63125, 1, 3, 14.92, -0.85, 1, 1, 3, -0.47, -0.09, 1, 1, 3, 1.25, -19.84, 1, 2, 3, 18.07, -17.32, 0.99107, 4, -19.16, -4.66, 0.00893, 2, 3, 31.53, -12.47, 0.69662, 4, -6.53, -11.37, 0.30338, 2, 3, 45.63, -0.23, 0.02195, 4, 12.02, -13.58, 0.97805, 1, 4, 24.37, -11.27, 1, 2, 3, -1.42, 17.53, 0.88549, 4, -6.46, 33.19, 0.11451, 2, 3, 11.06, 18.12, 0.6959, 4, 2.37, 24.35, 0.3041, 2, 3, 20.83, 20.67, 0.38693, 4, 10.82, 18.83, 0.61307, 2, 3, 31.5, 25.82, 0.10782, 4, 21.8, 14.39, 0.89218, 2, 3, 43.29, 31.46, 0.01213, 4, 33.91, 9.45, 0.98787], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 64, "height": 72}}, "eye_L": {"eye_L": {"x": 17.23, "y": -9.43, "rotation": -61.32, "width": 9, "height": 8}}, "eye_R": {"eye_R": {"x": 13.87, "y": 2.98, "rotation": -61.32, "width": 10, "height": 10}}, "hair": {"hair": {"x": -2.55, "y": 10.09, "rotation": 134.73, "width": 9, "height": 11}}, "hair_3": {"hair_3": {"x": 2.37, "y": 3.71, "rotation": 134.73, "width": 17, "height": 11}}, "hair_5": {"hair_5": {"x": 3.23, "y": 3.89, "rotation": 125.34, "width": 13, "height": 11}}, "hair_6": {"hair_6": {"x": -0.53, "y": 9.19, "rotation": 125.34, "width": 12, "height": 11}}, "hair_7": {"hair_7": {"x": 8.85, "y": -3.17, "rotation": 125.34, "width": 17, "height": 12}}, "hair_8": {"hair_4": {"x": 11.17, "y": -6.59, "rotation": 134.73, "width": 12, "height": 11}}, "hair_9": {"hair_2": {"x": 8, "y": -2.68, "rotation": 134.73, "width": 12, "height": 12}}, "hand_L": {"hand_L": {"x": 15.05, "y": 1.68, "rotation": 90.54, "width": 27, "height": 17}}, "hand_R": {"hand_R": {"x": 7.35, "y": -2.87, "rotation": 74.74, "width": 41, "height": 37}}, "head": {"head": {"x": 16.9, "y": 5.77, "rotation": -61.32, "width": 54, "height": 48}}, "leg_L": {"leg_L": {"x": 11.17, "y": 1.16, "rotation": 60.05, "width": 30, "height": 23}}, "leg_L2": {"leg_L2": {"x": 9.35, "y": 1.36, "rotation": 1.63, "width": 27, "height": 9}}, "leg_L3": {"leg_L3": {"type": "mesh", "uvs": [0.56235, 0, 0.3936, 0.04603, 0.19271, 0.17557, 0.06146, 0.39716, 0.02932, 0.64603, 0.03467, 0.91875, 0.26503, 0.98353, 0.4311, 0.98353, 0.68289, 0.98693, 0.83824, 0.92216, 0.93467, 0.72444, 1, 0.42444, 0.96949, 0.1858, 0.78467, 0.02898, 0.49476, 0.30514, 0.41927, 0.15213, 0.415, 0.48965, 0.30575, 0.66749, 0.18589, 0.87948, 0.40558, 0.89198, 0.65471, 0.9009, 0.63271, 0.72375, 0.71755, 0.45057, 0.69527, 0.18478, 0.25919, 0.35069, 0.16899, 0.62048], "triangles": [10, 22, 11, 21, 22, 10, 19, 17, 21, 9, 20, 21, 19, 21, 20, 10, 9, 21, 6, 18, 19, 7, 19, 20, 6, 19, 7, 8, 20, 9, 7, 20, 8, 23, 0, 13, 14, 0, 23, 3, 2, 24, 12, 22, 23, 12, 23, 13, 22, 12, 11, 14, 23, 22, 16, 24, 14, 25, 3, 24, 17, 25, 24, 4, 3, 25, 16, 17, 24, 22, 16, 14, 21, 16, 22, 17, 16, 21, 18, 25, 17, 4, 25, 18, 18, 17, 19, 5, 4, 18, 5, 18, 6, 15, 1, 0, 2, 1, 15, 14, 15, 0, 24, 2, 15, 24, 15, 14], "vertices": [3, 16, 10.42, 4.83, 0.47849, 17, -6.29, -2.57, 0.48866, 18, 10.15, 19.85, 0.03285, 3, 16, 8.9, 0.25, 0.68774, 17, -2.73, -5.83, 0.31006, 18, 5.45, 18.72, 0.0022, 2, 16, 8.5, -6.05, 0.61212, 17, 2.85, -8.77, 0.38788, 2, 16, 10.84, -11.69, 0.29107, 17, 8.95, -8.95, 0.70893, 2, 16, 15.11, -15.24, 0.08092, 17, 13.94, -6.52, 0.91908, 3, 16, 20.36, -18.15, 0.01193, 17, 18.75, -2.93, 0.65474, 18, -4.13, -0.72, 0.33333, 3, 16, 24.85, -13.31, 0, 17, 16.18, 3.16, 0.33333, 18, 2.35, -1.99, 0.66667, 1, 18, 7, -1.87, 1, 1, 18, 14.05, -1.78, 1, 3, 16, 31.82, 1.21, 0.01311, 17, 5.79, 15.47, 0.0619, 18, 18.37, -0.25, 0.92499, 3, 16, 29.44, 5.74, 0.02416, 17, 0.68, 15.16, 0.25365, 18, 20.96, 4.17, 0.72218, 3, 16, 24.68, 10.66, 0.02563, 17, -5.76, 12.83, 0.51091, 18, 22.63, 10.81, 0.46346, 3, 16, 19.72, 12.59, 0.01342, 17, -9.55, 9.1, 0.7508, 18, 21.65, 16.04, 0.23579, 3, 16, 14.12, 9.88, 0.14698, 17, -9.37, 2.88, 0.74556, 18, 16.39, 19.36, 0.10746, 2, 16, 15.25, -0.2, 0.76499, 17, 0.28, -0.23, 0.23501, 1, 16, 11.27, -0.32, 1, 2, 17, 4.89, 0.3, 0.99331, 18, 6.29, 8.98, 0.00669, 2, 17, 9.85, 0.07, 0.99616, 18, 3.32, 4.99, 0.00384, 2, 17, 15.59, 0.03, 0.87523, 18, 0.08, 0.25, 0.12477, 3, 16, 25.11, -8.9, 2e-05, 17, 12.26, 5.2, 0.00536, 18, 6.24, 0.12, 0.99462, 3, 16, 28.82, -2.98, 0.00033, 17, 8.38, 11.01, 0.00073, 18, 13.22, 0.1, 0.99893, 3, 16, 25.14, -1.54, 0.06881, 17, 5.56, 8.25, 0.17653, 18, 12.51, 3.98, 0.75466, 3, 16, 21.17, 3.55, 0.16038, 17, -0.72, 6.71, 0.5228, 18, 14.74, 10.04, 0.31682, 3, 16, 15.81, 5.98, 0.0058, 17, -5.13, 2.82, 0.91489, 18, 13.97, 15.87, 0.07931, 2, 16, 12.77, -6.4, 0.38069, 17, 4.92, -5.03, 0.61931, 2, 16, 16.61, -11.58, 0.03894, 17, 11.22, -3.65, 0.96106], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 28, "height": 22}}, "leg_R": {"leg_R": {"x": 11.82, "y": 1.3, "rotation": 91.52, "width": 34, "height": 28}}, "leg_R2": {"leg_R2": {"x": 10.44, "y": 1.89, "rotation": 0.95, "width": 27, "height": 10}}, "leg_R3": {"leg_R3": {"type": "mesh", "uvs": [0.21416, 0.0555, 0.10416, 0.18819, 0.01666, 0.37569, 0.00166, 0.59204, 0, 0.82569, 0.16166, 0.93819, 0.37166, 0.96704, 0.61166, 0.97858, 0.79166, 0.90646, 0.90666, 0.74781, 0.96666, 0.58338, 1, 0.36127, 0.91166, 0.17954, 0.73416, 0.06704, 0.52916, 0.018, 0.36166, 0.02954, 0.52997, 0.18797, 0.53617, 0.41004, 0.39943, 0.5495, 0.27278, 0.70619, 0.25119, 0.87365, 0.5312, 0.87451, 0.70721, 0.59919, 0.77295, 0.27564, 0.30999, 0.19245, 0.2361, 0.39779, 0.1398, 0.61773], "triangles": [16, 14, 13, 15, 14, 16, 24, 0, 15, 24, 15, 16, 1, 0, 24, 23, 13, 12, 16, 13, 23, 23, 12, 11, 25, 1, 24, 17, 16, 23, 17, 24, 16, 10, 23, 11, 22, 23, 10, 2, 1, 25, 24, 17, 25, 18, 25, 17, 26, 3, 2, 22, 17, 23, 25, 26, 2, 19, 26, 25, 18, 19, 25, 4, 3, 26, 20, 26, 19, 5, 4, 26, 22, 18, 17, 19, 18, 21, 9, 22, 10, 22, 21, 18, 6, 20, 19, 8, 22, 9, 21, 22, 8, 20, 5, 26, 21, 6, 19, 5, 20, 6, 7, 21, 8, 6, 21, 7], "vertices": [2, 14, 0.19, -12.64, 0.25334, 13, 9.46, -9.26, 0.74666, 3, 15, -0.09, 17.82, 0.00028, 14, 4.96, -12.56, 0.47968, 13, 12.97, -12.5, 0.52004, 3, 15, -2.66, 12.92, 0.01885, 14, 10.27, -10.99, 0.69717, 13, 17.89, -15.04, 0.28397, 3, 15, -3.06, 7.29, 0.23642, 14, 14.59, -7.35, 0.65794, 13, 23.53, -15.39, 0.10564, 3, 15, -3.04, 1.21, 0.56947, 14, 18.94, -3.11, 0.40895, 13, 29.6, -15.33, 0.02158, 3, 15, 1.84, -1.66, 0.88423, 14, 17.6, 2.39, 0.11534, 13, 32.44, -10.43, 0.00043, 1, 15, 8.15, -2.35, 1, 1, 15, 15.35, -2.57, 1, 3, 15, 20.73, -0.64, 0.93484, 14, 3.71, 15.24, 0.02752, 13, 31.27, 8.46, 0.03764, 3, 15, 24.14, 3.52, 0.78402, 14, -1.64, 14.79, 0.06982, 13, 27.09, 11.83, 0.14616, 3, 15, 25.89, 7.81, 0.55524, 14, -5.95, 13.06, 0.0982, 13, 22.78, 13.56, 0.34656, 3, 15, 26.83, 13.6, 0.3449, 14, -10.75, 9.7, 0.0817, 13, 16.99, 14.45, 0.5734, 3, 15, 24.13, 18.3, 0.17865, 14, -12.25, 4.5, 0.03947, 13, 12.31, 11.72, 0.78189, 3, 15, 18.78, 21.17, 0.07412, 14, -10.58, -1.35, 0.01109, 13, 9.48, 6.34, 0.91479, 3, 15, 12.61, 22.38, 0.01629, 14, -7.15, -6.61, 0.02273, 13, 8.32, 0.17, 0.96098, 3, 15, 7.59, 22.02, 3e-05, 14, -3.4, -9.97, 0.09878, 13, 8.71, -4.85, 0.9012, 2, 15, 12.68, 17.96, 0.00044, 13, 12.74, 0.27, 0.99956, 2, 15, 12.93, 12.19, 0.01644, 14, -0.06, 0.71, 0.98356, 2, 15, 8.87, 8.52, 0.01006, 14, 5.4, 0.35, 0.98994, 2, 15, 5.11, 4.4, 0.04434, 14, 10.97, 0.52, 0.95566, 2, 15, 4.51, 0.04, 0.99614, 14, 14.52, 3.12, 0.00386, 2, 15, 12.91, 0.11, 0.99663, 14, 8.62, 9.1, 0.00337, 3, 15, 18.11, 7.32, 0.50673, 14, -0.18, 7.82, 0.35558, 13, 23.33, 5.78, 0.13769, 3, 15, 20, 15.75, 0.14383, 14, -7.54, 3.3, 0.05483, 13, 14.89, 7.6, 0.80134, 2, 14, 0.7, -8.09, 0.21589, 13, 12.97, -6.32, 0.78411, 2, 14, 6.05, -5.91, 0.69963, 13, 18.35, -8.44, 0.30037, 3, 15, 1.1, 6.66, 0.01043, 14, 12.14, -3.94, 0.94858, 13, 24.12, -11.23, 0.041], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 30, "height": 26}}, "metal_L": {"metal_L": {"x": 11.49, "y": 0.5, "rotation": 52.78, "width": 21, "height": 25}}, "metal_L2": {"metal_L2": {"x": 0.39, "y": 6.16, "rotation": 125.34, "width": 18, "height": 19}}, "metal_R": {"metal_R": {"x": 16.71, "y": -0.23, "rotation": 146.63, "width": 38, "height": 33}}, "metal_R2": {"metal_R2": {"x": 20.24, "y": 4.53, "rotation": 91.52, "width": 22, "height": 21}}, "weapons": {"weapons": {"x": 15.23, "y": 2.37, "rotation": -11.16, "width": 56, "height": 34}}, "yinying": {"yinying": {"x": -0.61, "y": -2.47, "width": 86, "height": 40}}}}], "animations": {"attack": {"bones": {"arm_L2": {"rotate": [{"angle": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 12.82, "curve": 0.366, "c3": 0.679, "c4": 0.4}, {"time": 0.1667, "angle": 33.19, "curve": 0.198, "c2": 0.33, "c3": 0}, {"time": 0.3667, "angle": 179.7, "curve": 0.25, "c3": 0}, {"time": 0.5, "angle": 30.55, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 29.89, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": 23.96, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "angle": -4.38, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.82}], "translate": [{"time": 0.3667, "curve": 0.25, "c3": 0}, {"time": 0.5, "x": -8.29, "y": 3.97, "curve": "stepped"}, {"time": 0.7, "x": -8.29, "y": 3.97, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "x": -6.36, "y": 0.79, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667}]}, "body": {"rotate": [{"curve": 0.25, "c3": 0.352}, {"time": 0.3333, "angle": 9.08, "curve": 0.25, "c3": 0}, {"time": 0.4667}], "translate": [{"curve": 0.25, "c3": 0.352}, {"time": 0.3333, "x": -3.01, "y": 1.34, "curve": 0.25, "c3": 0}, {"time": 0.4667, "x": 6.26, "y": -1.9, "curve": "stepped"}, {"time": 0.7, "x": 6.26, "y": -1.9, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "leg_L4": {"translate": [{"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 8.27, "y": 1.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 9.52, "curve": "stepped"}, {"time": 0.8333, "x": 9.52, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 4.76, "y": 6.99, "curve": 0.25, "c3": 0.75}, {"time": 1.0333}]}, "yinying": {"translate": [{"time": 0.3333, "curve": 0.25, "c3": 0}, {"time": 0.4667, "x": 3.86, "curve": "stepped"}, {"time": 0.8, "x": 3.86, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}], "scale": [{"time": 0.3333, "curve": 0.25, "c3": 0}, {"time": 0.4667, "x": 1.077, "curve": "stepped"}, {"time": 0.8, "x": 1.077, "curve": 0.25, "c3": 0.75}, {"time": 0.9667}]}, "leg_L3": {"rotate": [{"angle": -0.5}]}, "leg_L": {"rotate": [{"angle": 0.26}]}, "metal_L": {"rotate": [{"angle": 4.15, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 17.65, "curve": 0.25, "c3": 0.352}, {"time": 0.3667, "angle": -179.96, "curve": 0.25, "c3": 0}, {"time": 0.5, "angle": 15.21, "curve": "stepped"}, {"time": 0.7, "angle": 15.21, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": 9.67, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "angle": -7.28, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 4.15}], "translate": [{"time": 0.3667, "curve": 0.25, "c3": 0}, {"time": 0.5, "x": 0.13, "y": 0.3, "curve": "stepped"}, {"time": 0.7, "x": 0.13, "y": 0.3, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "x": -0.5, "y": -2.17, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667}]}, "arm_L": {"rotate": [{"angle": 3.82, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -20.07, "curve": 0.366, "c3": 0.679, "c4": 0.4}, {"time": 0.1667, "angle": -16.23, "curve": 0.198, "c2": 0.33, "c3": 0}, {"time": 0.3667, "angle": 22.49, "curve": 0.25, "c3": 0}, {"time": 0.5, "angle": -22.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -22.91, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": -14.46, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "angle": -4.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.82}]}, "leg_R": {"rotate": [{"angle": 0.13}]}, "leg_R3": {"rotate": [{"angle": -0.49}]}, "metal_R": {"rotate": [{"angle": -6.33, "curve": 0.368, "c3": 0.671, "c4": 0.42}, {"time": 0.1, "angle": -0.3, "curve": 0.197, "c2": 0.37, "c3": 0.43}, {"time": 0.3333, "angle": 38.53, "curve": 0.25, "c3": 0}, {"time": 0.4667, "angle": -27.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -28.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -5.16, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -6.33}], "translate": [{"curve": 0.368, "c3": 0.671, "c4": 0.42}, {"time": 0.1, "x": 2.43, "y": 3.3, "curve": 0.197, "c2": 0.37, "c3": 0.43}, {"time": 0.3333, "x": 13.1, "y": 3.05, "curve": 0.25, "c3": 0}, {"time": 0.4667}]}, "hand_R": {"rotate": [{"angle": -2.64, "curve": 0.368, "c3": 0.671, "c4": 0.42}, {"time": 0.1, "angle": -0.28, "curve": 0.197, "c2": 0.37, "c3": 0.43}, {"time": 0.3333, "angle": 39.13, "curve": 0.25, "c3": 0}, {"time": 0.4667, "angle": -21.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -20.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.64}]}, "body4": {"rotate": [{"angle": 0.84, "curve": 0.366, "c3": 0.645, "c4": 0.45}, {"time": 0.1333, "angle": -11.58, "curve": 0.2, "c2": 0.44, "c3": 0.465}, {"time": 0.3333, "angle": 14.05, "curve": 0.571, "c3": 0.571, "c4": 0.5}, {"time": 0.4, "angle": 17.01, "curve": 0.12, "c2": 0.5, "c3": 0.36}, {"time": 0.4667, "angle": -4.44, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -5.1, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "angle": -5.78, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "angle": 2.54, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.84}], "translate": [{"x": 0.03, "curve": "stepped"}, {"time": 0.7, "x": 0.03, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.8333, "x": 0.55, "y": -1.49, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.9667, "x": 0.03}]}, "arm_R": {"rotate": [{"angle": -2.64, "curve": 0.368, "c3": 0.671, "c4": 0.42}, {"time": 0.1, "angle": -0.28, "curve": 0.197, "c2": 0.37, "c3": 0.43}, {"time": 0.3333, "angle": 42.8, "curve": 0.25, "c3": 0}, {"time": 0.4667, "angle": -19.66, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -20.54, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -0.77, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.64}], "translate": [{"curve": 0.25, "c3": 0.352}, {"time": 0.3333, "x": 0.97, "y": 4.65, "curve": 0.25, "c3": 0}, {"time": 0.4667}]}, "head": {"rotate": [{"angle": -0.02, "curve": 0.357, "c3": 0.607, "c4": 0.5}, {"time": 0.1667, "angle": -3.45, "curve": 0.212, "c2": 0.5, "c3": 0.501}, {"time": 0.3333, "angle": 6.59, "curve": 0.571, "c3": 0.571, "c4": 0.5}, {"time": 0.4, "angle": 20.6, "curve": 0.12, "c2": 0.5, "c3": 0.36}, {"time": 0.4667, "angle": -0.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -1.89, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.8667, "angle": -6.03, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1, "angle": 4.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.02}]}, "body3": {"rotate": [{"angle": 1.09, "curve": 0.368, "c3": 0.671, "c4": 0.42}, {"time": 0.1, "angle": -1.08, "curve": 0.197, "c2": 0.37, "c3": 0.43}, {"time": 0.3333, "angle": 19.25, "curve": 0.571, "c3": 0.571, "c4": 0.5}, {"time": 0.4, "angle": 9.29, "curve": 0.12, "c2": 0.5, "c3": 0.36}, {"time": 0.4667, "angle": -4.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -4.85, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8, "angle": -4.82, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.9667, "angle": 2.79, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 1.09}], "translate": [{"x": -0.07, "y": 0.56, "curve": "stepped"}, {"time": 0.7, "x": -0.07, "y": 0.56, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.8, "x": 2.09, "y": 1.28, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.9667, "x": -0.07, "y": 0.56}]}}}, "idle": {"bones": {"leg_L3": {"rotate": [{"angle": -0.5}]}, "leg_L": {"rotate": [{"angle": 0.26}]}, "leg_R3": {"rotate": [{"angle": -0.49}]}, "leg_R": {"rotate": [{"angle": 0.13}]}, "arm_L": {"rotate": [{"angle": 3.82, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 3.82}]}, "arm_L2": {"rotate": [{"angle": 3.82, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": 4.87, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 3.82}]}, "metal_L": {"rotate": [{"angle": 4.15, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 4.15}]}, "metal_R": {"rotate": [{"angle": -6.33, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -7.38, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -2.52, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -6.33}]}, "hand_R": {"rotate": [{"angle": -2.64, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 1.9, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -2.64}]}, "arm_R": {"rotate": [{"angle": -2.64, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -3.91, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 1.9, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -2.64}]}, "head": {"rotate": [{"angle": -0.02, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": 1.64, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.61, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": -0.02}]}, "body4": {"rotate": [{"angle": 0.84, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -0.98, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 0.84}], "translate": [{"x": 0.03, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "x": 0.41, "y": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": -1.74, "y": -0.52, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "x": 0.03}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "body3": {"rotate": [{"angle": 1.09, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 1.22, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.98, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 1.09}], "translate": [{"x": -0.07, "y": 0.56, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "x": -0.08, "y": 0.63, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.06, "y": -0.52, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "x": -0.07, "y": 0.56}]}}}, "run": {"bones": {"leg_L4": {"translate": [{"x": -16.33, "y": 18.3}, {"time": 0.1667, "x": 22.28, "y": 18.8}, {"time": 0.2333, "x": 7.83, "y": 4.15}, {"time": 0.2667, "x": 0.97, "y": 0.36}, {"time": 0.3333, "x": -7.82, "y": -1.02}, {"time": 0.4, "x": -21.76, "y": 9.49}, {"time": 0.5, "x": -30.87, "y": 33.73}, {"time": 0.6667, "x": -16.33, "y": 18.3}]}, "leg_R4": {"translate": [{"x": 7.96}, {"time": 0.1, "x": -4.41, "y": 15.31}, {"time": 0.1667, "x": -15.17, "y": 29.23}, {"time": 0.3333, "x": 17.61, "y": 12.27}, {"time": 0.4, "x": 36.84, "y": 9.19}, {"time": 0.5, "x": 43.66, "y": 18.63}, {"time": 0.5667, "x": 33.19, "y": 6.42}, {"time": 0.6667, "x": 7.96}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.813, "y": 0.813, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.813, "y": 0.813, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "leg_L2": {"rotate": [{"angle": -75.3}, {"time": 0.1667, "angle": 31.58}, {"time": 0.2667, "angle": 2.2}, {"time": 0.3333}, {"time": 0.4, "angle": -28.23}, {"time": 0.5, "angle": -96.74}, {"time": 0.6667}]}, "leg_L3": {"rotate": [{"angle": -0.5}]}, "leg_L": {"rotate": [{"angle": 0.26}], "translate": [{"x": 1.78, "y": 6.11}, {"time": 0.1667, "curve": "stepped"}, {"time": 0.3333}, {"time": 0.5, "x": -3.63, "y": 0.51}, {"time": 0.6667, "x": 1.78, "y": 6.11}]}, "leg_R2": {"rotate": [{}, {"time": 0.1, "angle": -41.8}, {"time": 0.1667, "angle": -78.83}, {"time": 0.3333, "angle": -23.2}, {"time": 0.4, "angle": 22}, {"time": 0.5, "angle": 30.89}, {"time": 0.6667}]}, "leg_R3": {"rotate": [{"angle": -0.49}]}, "leg_R": {"rotate": [{"angle": 0.13}], "translate": [{"time": 0.3333}, {"time": 0.5, "x": 5.36, "y": -0.15}, {"time": 0.6667}]}, "arm_L": {"rotate": [{"angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -13.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -1.62}]}, "arm_L2": {"rotate": [{"angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -13.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -1.62}]}, "metal_L": {"rotate": [{"angle": -1.62, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -13.37, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.13, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -1.62}]}, "metal_R": {"rotate": [{"angle": -0.38, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 15.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -16.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -0.38}]}, "hand_R": {"rotate": [{"angle": 8.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 23.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 8.41}]}, "arm_R": {"rotate": [{"angle": 8.41, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 23.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.53, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 8.41}]}, "head": {"rotate": [{"angle": -1.3, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -3.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -1.3}], "translate": [{"x": -0.62, "y": 0.34, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -1.69, "y": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -1.69, "y": 0.92, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": -0.62, "y": 0.34}]}, "body4": {"rotate": [{"angle": -2.98, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.98}], "translate": [{"x": -1.59, "y": -0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -1.59, "y": -0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -1.59, "y": -0.5}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 12.06, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 12.06, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "body3": {"rotate": [{"angle": -2.59, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -2.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -2.59}], "translate": [{"x": 0.1, "y": -0.82, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "x": 0.12, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 0.12, "y": -0.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "x": 0.1, "y": -0.82}]}}}}}