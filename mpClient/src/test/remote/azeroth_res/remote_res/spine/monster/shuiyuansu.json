{"skeleton": {"hash": "aUms6kBIHcz1GgcbUssOjeXQYSs", "spine": "3.8.99", "x": -65.36, "y": -21.99, "width": 123.05, "height": 137.89, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/阿拉希高地/水元素"}, "bones": [{"name": "root", "scaleX": 1.15, "scaleY": 1.15}, {"name": "bone", "parent": "root", "length": 51.15}, {"name": "body", "parent": "bone", "length": 14.26, "rotation": 89.82, "x": -0.63, "y": -0.49}, {"name": "body2", "parent": "body", "length": 16.93, "rotation": -0.75, "x": 14.26}, {"name": "body4", "parent": "body2", "length": 24.71, "rotation": -0.55, "x": 17, "y": -0.37}, {"name": "body5", "parent": "body4", "length": 23.32, "rotation": -1.03, "x": 24.71}, {"name": "body6", "parent": "body5", "length": 18.31, "rotation": 3.32, "x": 23.32}, {"name": "head", "parent": "body5", "length": 13.74, "rotation": -174.3, "x": 13.58, "y": -2.27}, {"name": "hand_L2", "parent": "body5", "length": 24.57, "rotation": -168.26, "x": 11.59, "y": -29.31}, {"name": "hand_L", "parent": "hand_L2", "length": 24.58, "rotation": -22.97, "x": 24.7, "y": -0.02}, {"name": "hand_R2", "parent": "body5", "length": 30.91, "rotation": 174.49, "x": 12.26, "y": 36.32}, {"name": "hand_R", "parent": "hand_R2", "length": 26.97, "rotation": 24.16, "x": 31.29, "y": 0.05}, {"name": "walter_7", "parent": "bone", "x": 0.87, "y": 1.81}, {"name": "yinying", "parent": "bone", "length": 7.7, "x": 0.04, "y": 3.39, "color": "ff0000ff"}, {"name": "body7", "parent": "body5", "x": 8.51, "y": -26.35}, {"name": "walter_2_di", "parent": "body7", "x": -12.7, "y": 1.11}, {"name": "walter_2_di2", "parent": "body7", "x": 3.92, "y": -8.75}, {"name": "walter_2_di3", "parent": "body7", "x": 12.53, "y": -6.12}, {"name": "walter_2", "parent": "body5", "x": 27.04, "y": -39.56}, {"name": "body8", "parent": "body5", "x": 12.52, "y": 25.09}, {"name": "walter", "parent": "body8", "x": -2.93, "y": 8.46}, {"name": "walter2", "parent": "body8", "x": 7.98, "y": 19.16}, {"name": "walter3", "parent": "body8", "x": -15.88, "y": -0.7}, {"name": "body3", "parent": "body2", "length": 7.97, "rotation": -92, "x": -0.95, "y": -2.79}, {"name": "walter_3", "parent": "body3", "rotation": 92, "x": 12.36, "y": 12.62}, {"name": "walter_4", "parent": "body3", "rotation": 92, "x": -2.13, "y": 4.45}, {"name": "walter_5", "parent": "body3", "rotation": 92, "x": -14.69, "y": 0.95}, {"name": "walter_8", "parent": "bone", "x": 0.87, "y": 1.81}, {"name": "walter_9", "parent": "bone", "x": 0.87, "y": 1.81}, {"name": "body9", "parent": "body5", "x": 12.52, "y": 25.09}, {"name": "walter4", "parent": "body9", "x": -2.93, "y": 8.46}, {"name": "walter5", "parent": "body9", "x": 7.98, "y": 19.16}, {"name": "walter6", "parent": "body9", "x": -15.88, "y": -0.7}, {"name": "body10", "parent": "body5", "x": 8.51, "y": -26.35}, {"name": "walter_2_di4", "parent": "body10", "x": -12.7, "y": 1.11}, {"name": "walter_2_di5", "parent": "body10", "x": 3.92, "y": -8.75}, {"name": "walter_2_di6", "parent": "body10", "x": 12.53, "y": -6.12}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "walter_7", "bone": "walter_7", "attachment": "walter_7"}, {"name": "walter_9", "bone": "walter_9", "attachment": "walter_7"}, {"name": "walter_8", "bone": "walter_8", "attachment": "walter_7"}, {"name": "hand_L2", "bone": "hand_L2", "attachment": "hand_L2"}, {"name": "hand_L", "bone": "hand_L", "attachment": "hand_L"}, {"name": "body", "bone": "body4", "attachment": "body"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye_R", "bone": "head", "color": "ffffff00", "attachment": "eye_R"}, {"name": "eye_L", "bone": "head", "color": "ffffff00", "attachment": "eye_L"}, {"name": "walter_3", "bone": "walter_5", "attachment": "walter_3"}, {"name": "walter_2_di", "bone": "walter_2_di3", "attachment": "walter_2_di"}, {"name": "walter_2_di2", "bone": "walter_2_di6", "attachment": "walter_2_di"}, {"name": "walter_2", "bone": "walter_2", "attachment": "walter_2"}, {"name": "hand_R2", "bone": "hand_R2", "attachment": "hand_R2"}, {"name": "walter", "bone": "walter3", "attachment": "walter"}, {"name": "walter2", "bone": "walter6", "attachment": "walter"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.34767, 0.08786, 0.19399, 0.13974, 0.08895, 0.19982, 0.01698, 0.28447, 0.00726, 0.38414, 0.03643, 0.48791, 0.1123, 0.55891, 0.20372, 0.64084, 0.30098, 0.69682, 0.33629, 0.78043, 0.36352, 0.87191, 0.33629, 0.93062, 0.34212, 0.9866, 0.46856, 0.99889, 0.58916, 1, 0.6942, 0.97022, 0.65724, 0.91833, 0.67864, 0.85007, 0.67864, 0.7818, 0.70393, 0.72855, 0.72533, 0.68895, 0.79924, 0.64799, 0.85954, 0.59747, 0.90623, 0.52784, 0.9568, 0.44983, 0.99182, 0.36517, 0.9607, 0.25594, 0.86344, 0.16992, 0.73505, 0.10302, 0.65919, 0.06616, 0.52886, 0, 0.50552, 0.10575, 0.54248, 0.22181, 0.54442, 0.31465, 0.5522, 0.41979, 0.52497, 0.53039, 0.51719, 0.656, 0.52497, 0.74885, 0.51719, 0.85671, 0.50357, 0.92635, 0.33629, 0.18085, 0.28766, 0.28462, 0.29155, 0.40887, 0.31294, 0.52629, 0.38102, 0.6519, 0.66891, 0.65873, 0.75645, 0.54267, 0.78563, 0.41296, 0.76423, 0.27369, 0.7156, 0.17812, 0.62807, 0.09619], "triangles": [17, 38, 18, 38, 10, 37, 16, 38, 17, 39, 10, 38, 39, 38, 16, 11, 10, 39, 13, 12, 11, 39, 13, 11, 14, 39, 16, 14, 16, 15, 13, 39, 14, 7, 6, 43, 7, 43, 44, 8, 7, 44, 9, 8, 44, 18, 37, 19, 38, 37, 18, 44, 36, 37, 9, 37, 10, 44, 37, 9, 35, 43, 34, 46, 47, 23, 35, 34, 46, 22, 46, 23, 21, 46, 22, 44, 43, 35, 36, 44, 35, 45, 36, 35, 46, 45, 35, 21, 45, 46, 20, 45, 21, 19, 45, 20, 37, 36, 45, 37, 45, 19, 48, 49, 27, 48, 27, 26, 32, 49, 48, 2, 1, 41, 3, 2, 41, 33, 32, 48, 41, 33, 42, 32, 41, 40, 33, 41, 32, 25, 47, 48, 25, 48, 26, 33, 48, 47, 34, 33, 47, 42, 33, 34, 24, 47, 25, 4, 41, 42, 41, 4, 3, 5, 4, 42, 43, 42, 34, 6, 5, 42, 23, 47, 24, 46, 34, 47, 43, 6, 42, 50, 30, 29, 31, 0, 30, 31, 30, 50, 28, 50, 29, 49, 28, 27, 49, 50, 28, 40, 1, 0, 40, 0, 31, 32, 31, 50, 32, 50, 49, 40, 31, 32, 41, 1, 40], "vertices": [4, 6, 13.08, 13.99, 0.79155, 5, 35.57, 14.73, 0.19362, 4, 60.54, 14.08, 0.00142, 3, 77.67, 13.13, 0.01341, 4, 6, 7.84, 25.29, 0.57169, 5, 29.68, 25.7, 0.38223, 4, 54.86, 25.16, 0.0098, 3, 72.09, 24.26, 0.03627, 4, 6, 1.7, 33.04, 0.35864, 5, 23.11, 33.08, 0.53698, 4, 48.41, 32.66, 0.03155, 3, 65.72, 31.82, 0.07284, 4, 6, -7.03, 38.42, 0.22706, 5, 14.08, 37.95, 0.57717, 4, 39.47, 37.69, 0.0726, 3, 56.83, 36.93, 0.12317, 4, 6, -17.38, 39.27, 0.13238, 5, 3.69, 38.2, 0.53867, 4, 29.09, 38.13, 0.13213, 3, 46.45, 37.48, 0.19683, 4, 6, -28.2, 37.29, 0.06749, 5, -7, 35.6, 0.44025, 4, 18.36, 35.72, 0.19496, 3, 35.7, 35.17, 0.2973, 4, 6, -35.66, 31.86, 0.02693, 5, -14.13, 29.74, 0.29304, 4, 11.12, 29.99, 0.22458, 3, 28.41, 29.51, 0.45545, 5, 6, -44.28, 25.31, 0.00814, 5, -22.35, 22.7, 0.15114, 4, 2.78, 23.1, 0.17674, 3, 20, 22.7, 0.66342, 2, 34.55, 22.44, 0.00055, 5, 6, -50.2, 18.29, 0.00081, 5, -27.85, 15.35, 0.04903, 4, -2.86, 15.85, 0.09216, 3, 14.29, 15.51, 0.81751, 2, 28.75, 15.32, 0.0405, 4, 5, -36.43, 12.4, 0.00837, 4, -11.49, 13.05, 0.02007, 3, 5.64, 12.79, 0.68022, 2, 20.06, 12.71, 0.29134, 3, 5, -45.84, 9.99, 0.00015, 3, -3.84, 10.64, 0.38382, 2, 10.56, 10.69, 0.61603, 2, 3, -9.98, 12.53, 0.09059, 2, 4.44, 12.66, 0.90941, 2, 3, -15.79, 12.01, 0.0081, 2, -1.38, 12.22, 0.9919, 1, 2, -2.62, 2.98, 1, 2, 3, -16.89, -6.04, 0.00013, 2, -2.71, -5.82, 0.99987, 3, 4, -30.54, -13.58, 0.0031, 3, -13.67, -13.66, 0.0194, 2, 0.41, -13.48, 0.9775, 3, 4, -25.22, -10.74, 0.03413, 3, -8.32, -10.87, 0.13921, 2, 5.8, -10.76, 0.82666, 4, 5, -42.56, -12.89, 0.00012, 4, -18.08, -12.12, 0.16091, 3, -1.2, -12.32, 0.30386, 2, 12.9, -12.3, 0.5351, 4, 5, -35.47, -12.58, 0.00289, 4, -10.98, -11.94, 0.41266, 3, 5.9, -12.2, 0.35692, 2, 20, -12.28, 0.22752, 4, 5, -29.86, -14.18, 0.01402, 4, -5.4, -13.64, 0.68744, 3, 11.47, -13.96, 0.25348, 2, 25.55, -14.11, 0.04506, 4, 5, -25.67, -15.56, 0.05045, 4, -1.24, -15.09, 0.85741, 3, 15.61, -15.45, 0.08872, 2, 29.67, -15.66, 0.00341, 4, 5, -21.18, -20.76, 0.1187, 4, 3.16, -20.38, 0.86487, 3, 19.96, -20.78, 0.0164, 2, 33.95, -21.04, 3e-05, 4, 6, -40.44, -22.63, 0.00075, 5, -15.74, -24.93, 0.23537, 4, 8.53, -24.64, 0.76385, 3, 25.29, -25.1, 3e-05, 3, 6, -33.24, -26.14, 0.00756, 5, -8.36, -28.02, 0.39175, 4, 15.85, -27.86, 0.6007, 3, 6, -25.18, -29.94, 0.02909, 5, -0.09, -31.35, 0.55251, 4, 24.06, -31.34, 0.4184, 3, 6, -16.41, -32.62, 0.08783, 5, 8.82, -33.52, 0.66344, 4, 32.93, -33.67, 0.24874, 3, 6, -5.02, -30.51, 0.21653, 5, 20.07, -30.75, 0.65924, 4, 44.22, -31.1, 0.12423, 3, 6, 4.02, -23.53, 0.46222, 5, 28.69, -23.26, 0.49339, 4, 52.98, -23.77, 0.04439, 3, 6, 11.11, -14.26, 0.72534, 5, 35.23, -13.59, 0.26539, 4, 59.7, -14.23, 0.00927, 3, 6, 15.02, -8.78, 0.92317, 5, 38.82, -7.89, 0.07666, 4, 63.39, -8.59, 0.00017, 3, 6, 22.03, 0.64, 0.94121, 5, 45.28, 1.92, 0.05667, 3, 87.02, 0.05, 0.00212, 3, 6, 11.06, 2.5, 0.99826, 5, 34.21, 3.13, 0.00155, 3, 76, 1.58, 0.00019, 2, 6, -1.05, -0.03, 0.06751, 5, 22.27, -0.09, 0.93249, 2, 5, 12.63, -0.66, 0.99994, 4, 37.33, -0.88, 6e-05, 2, 5, 1.74, -1.7, 0.87224, 4, 26.42, -1.74, 0.12776, 1, 4, 14.87, -0.05, 1, 1, 4, 1.79, 0.18, 1, 2, 4, -7.84, -0.63, 0.04662, 3, 9.15, -0.93, 0.95338, 2, 4, -19.07, -0.36, 0.00112, 2, 12.17, -0.52, 0.99888, 1, 2, 4.93, 0.45, 1, 4, 6, 3.42, 14.96, 0.61158, 5, 25.87, 15.13, 0.37187, 4, 50.85, 14.66, 0.00064, 3, 67.99, 13.8, 0.01591, 4, 6, -7.32, 18.66, 0.24278, 5, 14.93, 18.21, 0.67179, 4, 39.97, 17.93, 0.03332, 3, 57.14, 17.18, 0.05212, 4, 6, -20.24, 18.56, 0.05365, 5, 2.04, 17.36, 0.58433, 4, 27.06, 17.32, 0.22511, 3, 44.22, 16.68, 0.13691, 4, 6, -32.48, 17.17, 0.00689, 5, -10.1, 15.26, 0.2057, 4, 14.89, 15.44, 0.45148, 3, 32.04, 14.92, 0.33593, 3, 5, -22.93, 9.72, 0.01777, 4, 1.96, 10.13, 0.14799, 3, 19.06, 9.74, 0.83424, 3, 5, -22.72, -11.31, 0.02774, 4, 1.8, -10.9, 0.95711, 3, 18.69, -11.28, 0.01515, 2, 5, -10.38, -17.16, 0.23085, 4, 14.03, -16.97, 0.76915, 3, 6, -21.17, -17.5, 0.01726, 5, 3.19, -18.7, 0.63115, 4, 27.57, -18.75, 0.35159, 3, 6, -6.67, -16.14, 0.1933, 5, 17.59, -16.5, 0.74968, 4, 42.01, -16.81, 0.05701, 3, 6, 3.32, -12.73, 0.60578, 5, 27.37, -12.52, 0.38987, 4, 51.85, -13.01, 0.00435, 2, 6, 11.93, -6.46, 0.97139, 5, 35.6, -5.76, 0.02861], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60], "width": 73, "height": 104}}, "eye_L": {"eye_L": {"x": 15.16, "y": 8.78, "rotation": 86.82, "width": 12, "height": 17}}, "eye_R": {"eye_R": {"x": 14.16, "y": -9.2, "rotation": 86.82, "width": 16, "height": 17}}, "hand_L": {"hand_L": {"x": 12.64, "y": 1.28, "rotation": 103.75, "width": 35, "height": 44}}, "hand_L2": {"hand_L2": {"x": 14.46, "y": 1.89, "rotation": 80.78, "width": 29, "height": 38}}, "hand_R": {"hand_R": {"x": 10.42, "y": 1.7, "rotation": 73.87, "width": 44, "height": 48}}, "hand_R2": {"hand_R2": {"x": 17.95, "y": -1.8, "rotation": 98.03, "width": 35, "height": 48}}, "head": {"head": {"x": 13.58, "y": -1.65, "rotation": 86.82, "width": 39, "height": 41}}, "walter": {"walter": {"type": "mesh", "uvs": [0.1728, 0.09094, 0.04274, 0.11364, 0, 0.19938, 0.12711, 0.25233, 0.20796, 0.32799, 0.43292, 0.34312, 0.5208, 0.45912, 0.49268, 0.59277, 0.59462, 0.72642, 0.75632, 0.79198, 0.80553, 0.96346, 0.99183, 0.9912, 0.99183, 0.80207, 0.99535, 0.63564, 0.91801, 0.53225, 0.82311, 0.41877, 0.70359, 0.33555, 0.78444, 0.26746, 0.71062, 0.14642, 0.59462, 0.05564, 0.43644, 0, 0.24662, 0, 0.29935, 0.15903, 0.47159, 0.23468, 0.58056, 0.36077, 0.67547, 0.50451, 0.76335, 0.63816, 0.85474, 0.75416, 0.86177, 0.85503], "triangles": [22, 21, 20, 0, 21, 22, 23, 20, 19, 23, 19, 18, 22, 20, 23, 3, 1, 0, 3, 0, 22, 2, 1, 3, 4, 3, 22, 4, 22, 23, 5, 4, 23, 16, 23, 18, 17, 16, 18, 24, 5, 23, 16, 24, 23, 6, 5, 24, 25, 24, 16, 25, 16, 15, 6, 24, 25, 25, 15, 14, 7, 6, 25, 26, 25, 14, 7, 25, 26, 26, 14, 13, 8, 7, 26, 27, 26, 13, 9, 8, 26, 27, 9, 26, 12, 27, 13, 28, 27, 12, 9, 27, 28, 10, 9, 28, 28, 12, 11, 10, 28, 11], "vertices": [2, 20, 14.42, 15.38, 0.0002, 21, 3.51, 4.68, 0.9998, 1, 21, 2.28, 8.92, 1, 2, 20, 9.18, 20.86, 0.00113, 21, -1.72, 10.16, 0.99887, 2, 20, 6.94, 16.56, 0.01765, 21, -3.97, 5.86, 0.98235, 2, 20, 3.58, 13.74, 0.19213, 21, -7.33, 3.04, 0.80787, 3, 22, 16.16, 15.46, 0.01539, 20, 3.21, 6.29, 0.50279, 21, -7.7, -4.4, 0.48183, 3, 22, 10.95, 12.32, 0.12429, 20, -2, 3.16, 0.71069, 21, -12.91, -7.53, 0.16502, 3, 22, 4.77, 12.98, 0.34815, 20, -8.18, 3.82, 0.64569, 21, -19.09, -6.88, 0.00616, 2, 22, -1.22, 9.35, 0.65141, 20, -14.17, 0.19, 0.34859, 2, 22, -4, 3.89, 0.87584, 20, -16.95, -5.27, 0.12416, 2, 22, -11.81, 1.92, 0.98531, 20, -24.76, -7.24, 0.01469, 1, 22, -12.82, -4.28, 1, 2, 22, -4.12, -3.9, 0.97436, 20, -17.07, -13.06, 0.02564, 2, 22, 3.53, -3.68, 0.83992, 20, -9.42, -12.84, 0.16008, 2, 22, 8.17, -0.92, 0.56704, 20, -4.78, -10.08, 0.43296, 3, 22, 13.25, 2.44, 0.26103, 20, 0.3, -6.72, 0.69389, 21, -10.61, -17.42, 0.04508, 3, 22, 16.9, 6.55, 0.06214, 20, 3.95, -2.61, 0.79393, 21, -6.96, -13.31, 0.14393, 3, 22, 20.14, 4.02, 0.00169, 20, 7.19, -5.14, 0.68274, 21, -3.72, -15.84, 0.31557, 2, 20, 12.65, -2.46, 0.47616, 21, 1.74, -13.16, 0.52384, 2, 20, 16.65, 1.54, 0.26267, 21, 5.74, -9.15, 0.73733, 2, 20, 18.98, 6.87, 0.10117, 21, 8.07, -3.83, 0.89883, 2, 20, 18.7, 13.13, 0.02118, 21, 7.8, 2.43, 0.97882, 1, 21, 0.56, 0.37, 1, 2, 20, 8.25, 5.24, 0.34526, 21, -2.66, -5.46, 0.65474, 2, 20, 2.61, 1.39, 0.85967, 21, -8.3, -9.31, 0.14033, 2, 22, 9.09, 7.13, 0.21247, 20, -3.86, -2.03, 0.78753, 2, 22, 3.08, 3.97, 0.7089, 20, -9.87, -5.19, 0.2911, 2, 22, -2.12, 0.72, 0.99466, 20, -15.07, -8.44, 0.00534, 1, 22, -6.75, 0.28, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 33, "height": 46}}, "walter2": {"walter": {"type": "mesh", "uvs": [0.1728, 0.09094, 0.04274, 0.11364, 0, 0.19938, 0.12711, 0.25233, 0.20796, 0.32799, 0.43292, 0.34312, 0.5208, 0.45912, 0.49268, 0.59277, 0.59462, 0.72642, 0.75632, 0.79198, 0.80553, 0.96346, 0.99183, 0.9912, 0.99183, 0.80207, 0.99535, 0.63564, 0.91801, 0.53225, 0.82311, 0.41877, 0.70359, 0.33555, 0.78444, 0.26746, 0.71062, 0.14642, 0.59462, 0.05564, 0.43644, 0, 0.24662, 0, 0.29935, 0.15903, 0.47159, 0.23468, 0.58056, 0.36077, 0.67547, 0.50451, 0.76335, 0.63816, 0.85474, 0.75416, 0.86177, 0.85503], "triangles": [22, 21, 20, 0, 21, 22, 23, 20, 19, 23, 19, 18, 22, 20, 23, 3, 1, 0, 3, 0, 22, 2, 1, 3, 4, 3, 22, 4, 22, 23, 5, 4, 23, 16, 23, 18, 17, 16, 18, 24, 5, 23, 16, 24, 23, 6, 5, 24, 25, 24, 16, 25, 16, 15, 6, 24, 25, 25, 15, 14, 7, 6, 25, 26, 25, 14, 7, 25, 26, 26, 14, 13, 8, 7, 26, 27, 26, 13, 9, 8, 26, 27, 9, 26, 12, 27, 13, 28, 27, 12, 9, 27, 28, 10, 9, 28, 28, 12, 11, 10, 28, 11], "vertices": [2, 30, 14.42, 15.38, 0.0002, 31, 3.51, 4.68, 0.9998, 1, 31, 2.28, 8.92, 1, 2, 30, 9.18, 20.86, 0.00113, 31, -1.72, 10.16, 0.99887, 2, 30, 6.94, 16.56, 0.01765, 31, -3.97, 5.86, 0.98235, 2, 30, 3.58, 13.74, 0.19213, 31, -7.33, 3.04, 0.80787, 3, 32, 16.16, 15.46, 0.01539, 30, 3.21, 6.29, 0.50279, 31, -7.7, -4.4, 0.48183, 3, 32, 10.95, 12.32, 0.12429, 30, -2, 3.16, 0.71069, 31, -12.91, -7.53, 0.16502, 3, 32, 4.77, 12.98, 0.34815, 30, -8.18, 3.82, 0.64569, 31, -19.09, -6.88, 0.00616, 2, 32, -1.22, 9.35, 0.65141, 30, -14.17, 0.19, 0.34859, 2, 32, -4, 3.89, 0.87584, 30, -16.95, -5.27, 0.12416, 2, 32, -11.81, 1.92, 0.98531, 30, -24.76, -7.24, 0.01469, 1, 32, -12.82, -4.28, 1, 2, 32, -4.12, -3.9, 0.97436, 30, -17.07, -13.06, 0.02564, 2, 32, 3.53, -3.68, 0.83992, 30, -9.42, -12.84, 0.16008, 2, 32, 8.17, -0.92, 0.56704, 30, -4.78, -10.08, 0.43296, 3, 32, 13.25, 2.44, 0.26103, 30, 0.3, -6.72, 0.69389, 31, -10.61, -17.42, 0.04508, 3, 32, 16.9, 6.55, 0.06214, 30, 3.95, -2.61, 0.79393, 31, -6.96, -13.31, 0.14393, 3, 32, 20.14, 4.02, 0.00169, 30, 7.19, -5.14, 0.68274, 31, -3.72, -15.84, 0.31557, 2, 30, 12.65, -2.46, 0.47616, 31, 1.74, -13.16, 0.52384, 2, 30, 16.65, 1.54, 0.26267, 31, 5.74, -9.15, 0.73733, 2, 30, 18.98, 6.87, 0.10117, 31, 8.07, -3.83, 0.89883, 2, 30, 18.7, 13.13, 0.02118, 31, 7.8, 2.43, 0.97882, 1, 31, 0.56, 0.37, 1, 2, 30, 8.25, 5.24, 0.34526, 31, -2.66, -5.46, 0.65474, 2, 30, 2.61, 1.39, 0.85967, 31, -8.3, -9.31, 0.14033, 2, 32, 9.09, 7.13, 0.21247, 30, -3.86, -2.03, 0.78753, 2, 32, 3.08, 3.97, 0.7089, 30, -9.87, -5.19, 0.2911, 2, 32, -2.12, 0.72, 0.99466, 30, -15.07, -8.44, 0.00534, 1, 32, -6.75, 0.28, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 33, "height": 46}}, "walter_2": {"walter_2": {"x": 1.03, "y": -0.72, "rotation": -87.48, "width": 9, "height": 11}}, "walter_2_di": {"walter_2_di": {"type": "mesh", "uvs": [0.58674, 0, 0.3529, 0.05408, 0.14974, 0.1395, 0.28007, 0.24027, 0.35674, 0.3476, 0.32607, 0.47684, 0.24174, 0.58198, 0.13057, 0.67398, 0.01557, 0.76379, 0.00024, 0.89302, 0, 0.99817, 0.18807, 0.88864, 0.3759, 0.83826, 0.57524, 0.7375, 0.72473, 0.64769, 0.77073, 0.52941, 0.7784, 0.44398, 0.8934, 0.3235, 0.94707, 0.21398, 0.80907, 0.15265, 0.65957, 0.18988, 0.7324, 0.09569, 0.70173, 0, 0.49474, 0.19427, 0.56757, 0.31474, 0.6289, 0.40893, 0.55607, 0.53598, 0.47174, 0.62579, 0.3529, 0.7156, 0.18807, 0.80103], "triangles": [0, 21, 23, 21, 0, 22, 23, 1, 0, 21, 20, 23, 3, 2, 1, 23, 3, 1, 24, 23, 20, 4, 3, 23, 18, 20, 19, 17, 20, 18, 24, 4, 23, 24, 20, 17, 25, 24, 17, 16, 25, 17, 15, 25, 16, 4, 25, 5, 25, 4, 24, 26, 25, 15, 26, 5, 25, 27, 5, 26, 6, 5, 27, 14, 26, 15, 27, 26, 14, 13, 27, 14, 28, 6, 27, 7, 6, 28, 28, 27, 13, 29, 7, 28, 8, 7, 29, 12, 28, 13, 29, 28, 12, 29, 9, 8, 11, 29, 12, 11, 9, 29, 10, 9, 11], "vertices": [1, 17, 8.24, -1.34, 1, 1, 17, 5.73, 4.17, 1, 2, 16, 10.54, 11.52, 0.03168, 17, 1.93, 8.88, 0.96832, 3, 15, 23.07, -1.66, 0.00531, 16, 6.45, 8.2, 0.23283, 17, -2.16, 5.57, 0.76186, 3, 15, 18.65, -3.7, 0.07302, 16, 2.03, 6.17, 0.49254, 17, -6.58, 3.53, 0.43444, 3, 15, 13.19, -3.2, 0.24901, 16, -3.42, 6.67, 0.6182, 17, -12.04, 4.03, 0.13279, 3, 15, 8.69, -1.37, 0.52946, 16, -7.92, 8.49, 0.46463, 17, -16.54, 5.86, 0.00591, 2, 15, 4.72, 1.13, 0.79442, 16, -11.9, 10.99, 0.20558, 2, 15, 0.83, 3.72, 0.95176, 16, -15.79, 13.58, 0.04824, 2, 15, -4.61, 3.85, 0.99934, 16, -21.23, 13.71, 0.00066, 2, 15, -9.02, 3.66, 0.99994, 16, -25.64, 13.52, 6e-05, 2, 15, -4.23, -0.65, 0.96827, 16, -20.85, 9.21, 0.03173, 2, 15, -1.92, -5.06, 0.821, 16, -18.54, 4.8, 0.179, 2, 15, 2.52, -9.65, 0.59206, 16, -14.1, 0.21, 0.40794, 2, 15, 6.45, -13.07, 0.3342, 16, -10.17, -3.21, 0.6658, 3, 15, 11.46, -13.96, 0.15713, 16, -5.16, -4.09, 0.83599, 17, -13.78, -6.73, 0.00688, 3, 15, 15.05, -13.98, 0.0528, 16, -1.57, -4.12, 0.83588, 17, -10.18, -6.75, 0.11132, 3, 15, 20.23, -16.52, 0.009, 16, 3.61, -6.65, 0.71661, 17, -5.01, -9.29, 0.27438, 2, 16, 8.26, -7.74, 0.53938, 17, -0.36, -10.37, 0.46062, 2, 16, 10.69, -4.32, 0.37127, 17, 2.07, -6.95, 0.62873, 2, 16, 8.97, -0.8, 0.20106, 17, 0.35, -3.44, 0.79894, 2, 16, 13, -2.37, 0.06084, 17, 4.38, -5.01, 0.93916, 2, 16, 16.98, -1.46, 6e-05, 17, 8.36, -4.1, 0.99994, 2, 16, 8.61, 3.14, 0.00583, 17, 0, 0.51, 0.99417, 2, 16, 3.63, 1.17, 0.60331, 17, -4.98, -1.46, 0.39669, 2, 15, 16.36, -10.33, 0.00373, 16, -0.26, -0.47, 0.99627, 2, 15, 10.96, -8.82, 0.20532, 16, -5.66, 1.04, 0.79468, 2, 15, 7.1, -6.97, 0.43802, 16, -9.52, 2.9, 0.56198, 2, 15, 3.2, -4.28, 0.7422, 16, -13.41, 5.58, 0.2578, 2, 15, -0.55, -0.49, 0.99869, 16, -17.17, 9.38, 0.00131], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 24, "height": 42}}, "walter_2_di2": {"walter_2_di": {"type": "mesh", "uvs": [0.58674, 0, 0.3529, 0.05408, 0.14974, 0.1395, 0.28007, 0.24027, 0.35674, 0.3476, 0.32607, 0.47684, 0.24174, 0.58198, 0.13057, 0.67398, 0.01557, 0.76379, 0.00024, 0.89302, 0, 0.99817, 0.18807, 0.88864, 0.3759, 0.83826, 0.57524, 0.7375, 0.72473, 0.64769, 0.77073, 0.52941, 0.7784, 0.44398, 0.8934, 0.3235, 0.94707, 0.21398, 0.80907, 0.15265, 0.65957, 0.18988, 0.7324, 0.09569, 0.70173, 0, 0.49474, 0.19427, 0.56757, 0.31474, 0.6289, 0.40893, 0.55607, 0.53598, 0.47174, 0.62579, 0.3529, 0.7156, 0.18807, 0.80103], "triangles": [0, 21, 23, 21, 0, 22, 23, 1, 0, 21, 20, 23, 3, 2, 1, 23, 3, 1, 24, 23, 20, 4, 3, 23, 18, 20, 19, 17, 20, 18, 24, 4, 23, 24, 20, 17, 25, 24, 17, 16, 25, 17, 15, 25, 16, 4, 25, 5, 25, 4, 24, 26, 25, 15, 26, 5, 25, 27, 5, 26, 6, 5, 27, 14, 26, 15, 27, 26, 14, 13, 27, 14, 28, 6, 27, 7, 6, 28, 28, 27, 13, 29, 7, 28, 8, 7, 29, 12, 28, 13, 29, 28, 12, 29, 9, 8, 11, 29, 12, 11, 9, 29, 10, 9, 11], "vertices": [1, 36, 8.24, -1.34, 1, 1, 36, 5.73, 4.17, 1, 2, 35, 10.54, 11.52, 0.03168, 36, 1.93, 8.88, 0.96832, 3, 34, 23.07, -1.66, 0.00531, 35, 6.45, 8.2, 0.23283, 36, -2.16, 5.57, 0.76186, 3, 34, 18.65, -3.7, 0.07302, 35, 2.03, 6.17, 0.49254, 36, -6.58, 3.53, 0.43444, 3, 34, 13.19, -3.2, 0.24901, 35, -3.42, 6.67, 0.6182, 36, -12.04, 4.03, 0.13279, 3, 34, 8.69, -1.37, 0.52946, 35, -7.92, 8.49, 0.46463, 36, -16.54, 5.86, 0.00591, 2, 34, 4.72, 1.13, 0.79442, 35, -11.9, 10.99, 0.20558, 2, 34, 0.83, 3.72, 0.95176, 35, -15.79, 13.58, 0.04824, 2, 34, -4.61, 3.85, 0.99934, 35, -21.23, 13.71, 0.00066, 2, 34, -9.02, 3.66, 0.99994, 35, -25.64, 13.52, 6e-05, 2, 34, -4.23, -0.65, 0.96827, 35, -20.85, 9.21, 0.03173, 2, 34, -1.92, -5.06, 0.821, 35, -18.54, 4.8, 0.179, 2, 34, 2.52, -9.65, 0.59206, 35, -14.1, 0.21, 0.40794, 2, 34, 6.45, -13.07, 0.3342, 35, -10.17, -3.21, 0.6658, 3, 34, 11.46, -13.96, 0.15713, 35, -5.16, -4.09, 0.83599, 36, -13.78, -6.73, 0.00688, 3, 34, 15.05, -13.98, 0.0528, 35, -1.57, -4.12, 0.83588, 36, -10.18, -6.75, 0.11132, 3, 34, 20.23, -16.52, 0.009, 35, 3.61, -6.65, 0.71661, 36, -5.01, -9.29, 0.27438, 2, 35, 8.26, -7.74, 0.53938, 36, -0.36, -10.37, 0.46062, 2, 35, 10.69, -4.32, 0.37127, 36, 2.07, -6.95, 0.62873, 2, 35, 8.97, -0.8, 0.20106, 36, 0.35, -3.44, 0.79894, 2, 35, 13, -2.37, 0.06084, 36, 4.38, -5.01, 0.93916, 2, 35, 16.98, -1.46, 6e-05, 36, 8.36, -4.1, 0.99994, 2, 35, 8.61, 3.14, 0.00583, 36, 0, 0.51, 0.99417, 2, 35, 3.63, 1.17, 0.60331, 36, -4.98, -1.46, 0.39669, 2, 34, 16.36, -10.33, 0.00373, 35, -0.26, -0.47, 0.99627, 2, 34, 10.96, -8.82, 0.20532, 35, -5.66, 1.04, 0.79468, 2, 34, 7.1, -6.97, 0.43802, 35, -9.52, 2.9, 0.56198, 2, 34, 3.2, -4.28, 0.7422, 35, -13.41, 5.58, 0.2578, 2, 34, -0.55, -0.49, 0.99869, 35, -17.17, 9.38, 0.00131], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 24, "height": 42}}, "walter_3": {"walter_3": {"type": "mesh", "uvs": [0.11565, 0.51712, 0.04603, 0.61629, 0.02822, 0.80612, 0.10108, 0.94779, 0.22736, 0.94212, 0.26298, 0.87129, 0.3666, 0.85712, 0.47508, 0.77495, 0.52041, 0.70979, 0.5965, 0.81462, 0.69527, 0.78912, 0.7406, 0.63895, 0.86688, 0.60212, 0.96241, 0.45479, 0.98346, 0.30462, 0.95269, 0.08362, 0.85069, 0, 0.76003, 0.02129, 0.74707, 0.17146, 0.63698, 0.21112, 0.53012, 0.31312, 0.48317, 0.47179, 0.35689, 0.51995, 0.26298, 0.54545, 0.1966, 0.47179, 0.12536, 0.73812, 0.2986, 0.69846, 0.43136, 0.62762, 0.55117, 0.57096, 0.6726, 0.47746, 0.77622, 0.35846, 0.87822, 0.25362], "triangles": [26, 23, 22, 25, 0, 24, 25, 24, 23, 25, 23, 26, 1, 0, 25, 2, 1, 25, 5, 25, 26, 5, 26, 6, 4, 25, 5, 3, 2, 25, 3, 25, 4, 20, 19, 29, 28, 21, 20, 29, 28, 20, 27, 22, 21, 27, 21, 28, 26, 22, 27, 8, 27, 28, 7, 27, 8, 11, 28, 29, 10, 28, 11, 9, 8, 28, 10, 9, 28, 6, 26, 27, 6, 27, 7, 31, 16, 15, 31, 15, 14, 18, 16, 31, 16, 18, 17, 30, 18, 31, 13, 31, 14, 30, 29, 19, 30, 19, 18, 13, 12, 30, 13, 30, 31, 11, 29, 30, 12, 11, 30], "vertices": [2, 25, 1.08, 17.16, 0.06763, 26, 5.58, -0.39, 0.93237, 2, 25, -1.35, 20.04, 0.01966, 26, 3.16, 2.5, 0.98034, 2, 25, -5.91, 20.71, 0.00497, 26, -1.41, 3.17, 0.99503, 2, 25, -9.26, 17.6, 0.04844, 26, -4.76, 0.06, 0.95156, 2, 25, -9.04, 12.3, 0.13347, 26, -4.54, -5.24, 0.86653, 2, 25, -7.32, 10.83, 0.32145, 26, -2.81, -6.71, 0.67855, 2, 25, -6.91, 6.48, 0.55893, 26, -2.4, -11.06, 0.44107, 3, 24, -11.82, 15.86, 0.00239, 25, -4.86, 1.96, 0.79188, 26, -0.36, -15.58, 0.20573, 3, 24, -10.23, 13.98, 0.02499, 25, -3.27, 0.08, 0.91089, 26, 1.24, -17.46, 0.06412, 3, 24, -12.69, 10.74, 0.0785, 25, -5.73, -3.15, 0.90854, 26, -1.23, -20.69, 0.01296, 2, 24, -12.01, 6.61, 0.21665, 25, -5.05, -7.29, 0.78335, 2, 24, -8.37, 4.76, 0.47541, 25, -1.42, -9.14, 0.52459, 2, 24, -7.4, -0.53, 0.7511, 25, -0.44, -14.43, 0.2489, 2, 24, -3.8, -4.48, 0.9439, 25, 3.16, -18.38, 0.0561, 2, 24, -0.19, -5.31, 0.99587, 25, 6.77, -19.2, 0.00413, 2, 24, 5.1, -3.93, 0.99996, 25, 12.06, -17.83, 4e-05, 2, 24, 7.03, 0.39, 0.99554, 25, 13.99, -13.51, 0.00446, 2, 24, 6.46, 4.19, 0.95083, 25, 13.42, -9.71, 0.04917, 2, 24, 2.85, 4.67, 0.77725, 25, 9.81, -9.23, 0.22275, 2, 24, 1.82, 9.28, 0.50958, 25, 8.78, -4.62, 0.49042, 3, 24, -0.7, 13.73, 0.22786, 25, 6.26, -0.17, 0.76347, 26, 10.76, -17.71, 0.00866, 3, 24, -4.54, 15.64, 0.06815, 25, 2.42, 1.74, 0.81197, 26, 6.92, -15.8, 0.11988, 3, 24, -5.78, 20.92, 0.0069, 25, 1.18, 7.02, 0.64758, 26, 5.68, -10.52, 0.34553, 2, 25, 0.5, 10.96, 0.37778, 26, 5, -6.58, 0.62222, 2, 25, 2.22, 13.77, 0.17409, 26, 6.73, -3.77, 0.82591, 2, 25, -4.22, 16.66, 0.01819, 26, 0.29, -0.88, 0.98181, 2, 25, -3.15, 9.4, 0.43074, 26, 1.36, -8.14, 0.56926, 2, 25, -1.36, 3.85, 0.82989, 26, 3.15, -13.69, 0.17011, 2, 24, -6.87, 12.74, 0.03267, 25, 0.09, -1.15, 0.96733, 2, 24, -4.55, 7.68, 0.37697, 25, 2.41, -6.22, 0.62303, 2, 24, -1.62, 3.37, 0.80048, 25, 5.34, -10.52, 0.19952, 1, 24, 0.97, -0.87, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 42, "height": 24}}, "walter_7": {"walter_7": {"x": -2.2, "y": -1.52, "width": 61, "height": 17}}, "walter_8": {"walter_7": {"x": -2.2, "y": -1.52, "width": 61, "height": 17}}, "walter_9": {"walter_7": {"x": -2.2, "y": -1.52, "width": 61, "height": 17}}, "yinying": {"yinying": {"x": -1.38, "y": -3.51, "width": 80, "height": 38}}}}], "animations": {"attack": {"slots": {"walter": {"color": [{"color": "ffffff00"}]}, "walter_2": {"color": [{"color": "ffffff00"}]}, "walter_2_di": {"color": [{"color": "ffffff00"}]}, "walter_7": {"color": [{"color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "walter_8": {"color": [{"color": "ffffffb6"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffffb6"}]}, "walter_9": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}}, "bones": {"walter6": {"translate": [{"x": 1.25, "y": -0.01}], "scale": [{"x": 0.846, "y": 0.846}]}, "walter_2_di3": {"rotate": [{"angle": 18.89}], "translate": [{"x": -1.6, "y": 5.43}], "scale": [{"x": 0.519, "y": 0.519}]}, "walter_2": {"translate": [{"x": -12.8, "y": 9.58}]}, "walter_2_di4": {"translate": [{"x": 0.03, "y": 0.23}], "scale": [{"x": 0.756, "y": 0.756}]}, "walter_2_di5": {"translate": [{"x": -1.03, "y": 2.45}], "scale": [{"x": 0.495, "y": 0.495}]}, "walter_2_di6": {"rotate": [{"angle": 10.92}], "translate": [{"x": -0.92, "y": 3.14}], "scale": [{"x": 0.838, "y": 0.838}]}, "body10": {"rotate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "angle": -56.77, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": -167.11, "curve": 0.688, "c3": 0.75}, {"time": 0.8}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": 0.64, "y": 0.11, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": 12.2, "y": 9.01, "curve": 0.688, "c3": 0.75}, {"time": 0.8}]}, "walter_5": {"translate": [{"x": 1.07, "y": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.18, "y": -3.17, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.07, "y": 4.79}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.728, "y": 0.728, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "walter_4": {"translate": [{"x": 0.24, "y": 1.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "x": 0.27, "y": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.18, "y": -3.17, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 0.24, "y": 1.86}], "scale": [{"x": 0.9, "y": 0.9, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.728, "y": 0.728, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 0.9, "y": 0.9}]}, "body2": {"rotate": [{"angle": 3.03, "curve": 0.25, "c3": 0}, {"time": 0.3, "angle": 20.88, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": -42.5, "curve": 0.688, "c3": 0.75}, {"time": 0.8, "angle": 5.86, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.03}], "scale": [{"x": 1.018, "y": 1.018, "curve": 0.25, "c3": 0}, {"time": 0.3, "x": 1.035, "y": 1.035, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": 1.018, "y": 1.018}]}, "walter3": {"translate": [{"x": 1.61, "y": 2.34}], "scale": [{"x": 0.48, "y": 0.48}]}, "body4": {"rotate": [{"angle": 1.78, "curve": 0.387, "c3": 0.701, "c4": 0.38}, {"time": 0.0667, "angle": -19.72, "curve": 0.181, "c2": 0.29, "c3": 0.327}, {"time": 0.3, "angle": 7.55, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": -0.33, "curve": 0.618, "c3": 0.814, "c4": 0.67}, {"time": 0.7333, "angle": -8.16, "curve": 0.313, "c2": 0.6, "c3": 0.621}, {"time": 0.8333, "angle": 4.61, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.78}], "translate": [{"x": -0.35, "y": -0.01, "curve": 0.387, "c3": 0.701, "c4": 0.38}, {"time": 0.0667, "x": -2.01, "y": -8.44, "curve": 0.181, "c2": 0.29, "c3": 0.327}, {"time": 0.3, "x": -0.35, "y": -0.01, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": -9.01, "y": -4.29, "curve": 0.618, "c3": 0.814, "c4": 0.67}, {"time": 0.7333, "x": -6.19, "y": -9.32, "curve": 0.313, "c2": 0.6, "c3": 0.621}, {"time": 0.8333, "x": -0.11, "y": 2.92, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -0.35, "y": -0.01}], "scale": [{"x": 1.012, "y": 1.013, "curve": 0.387, "c3": 0.701, "c4": 0.38}, {"time": 0.0667, "x": 1.02, "y": 1.061, "curve": 0.181, "c2": 0.29, "c3": 0.327}, {"time": 0.3, "x": 1.043, "y": 1.044, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": 1.012, "y": 1.013}]}, "body6": {"rotate": [{"angle": -4.32, "curve": 0.25, "c3": 0}, {"time": 0.3, "angle": 1.45, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": -31.73, "curve": 0.688, "c3": 0.75}, {"time": 0.8, "angle": -1.49, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.32}], "translate": [{"x": -1.63, "y": -0.07}], "scale": [{"x": 0.974, "y": 0.978}]}, "body5": {"rotate": [{"angle": 1.2, "curve": 0.411, "c3": 0.666, "c4": 0.45}, {"time": 0.1333, "angle": -19.51, "curve": 0.178, "c2": 0.44, "c3": 0.418}, {"time": 0.3, "angle": 6.97, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": -16.36, "curve": 0.537, "c3": 0.807, "c4": 0.52}, {"time": 0.6333, "angle": -12.71, "curve": 0.369, "c2": 0.36, "c3": 0.689, "c4": 0.71}, {"time": 0.7333, "angle": -22.98, "curve": 0.31, "c2": 0.61, "c3": 0.624}, {"time": 0.8667, "angle": 4.04, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 1.2}], "translate": [{"x": -1.2, "y": -0.04, "curve": "stepped"}, {"time": 0.3, "x": -1.2, "y": -0.04, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": 0.35, "y": 0.16, "curve": 0.537, "c3": 0.807, "c4": 0.52}, {"time": 0.6333, "x": -1.87, "y": -11.79, "curve": 0.369, "c2": 0.36, "c3": 0.689, "c4": 0.71}, {"time": 0.7333, "x": -1.01, "y": -13.69, "curve": 0.31, "c2": 0.61, "c3": 0.624}, {"time": 0.8667, "x": -0.95, "y": 1.76, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.2, "y": -0.04}], "scale": [{"x": 0.995, "y": 0.997, "curve": 0.269, "c3": 0.285}, {"time": 0.3, "x": 1.024, "y": 1.027, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": 0.995, "y": 0.997}]}, "walter_2_di2": {"translate": [{"x": -1.29, "y": 5.56}], "scale": [{"x": -0.1, "y": -0.1}]}, "hand_R2": {"rotate": [{"angle": -4.25, "curve": 0.25, "c3": 0}, {"time": 0.3, "angle": -174.81, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": 105.47, "curve": 0.535, "c2": -0.02, "c3": 0.75}, {"time": 0.8667, "angle": -8.82, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -4.25}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": -5.19, "y": -7.24, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": -24.45, "y": -24.73, "curve": 0.535, "c2": -0.02, "c3": 0.75}, {"time": 0.8667}]}, "hand_R": {"rotate": [{"angle": 0.51, "curve": "stepped"}, {"time": 0.3, "angle": 0.51, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": 13.61, "curve": 0.535, "c2": -0.02, "c3": 0.75}, {"time": 0.8667, "angle": -4.06, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.51}]}, "hand_L2": {"rotate": [{"angle": 4.94, "curve": 0.25, "c3": 0}, {"time": 0.3, "angle": 171.78, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": 130.28, "curve": 0.625, "c3": 0.813, "c4": 0.68}, {"time": 0.7667, "angle": 48.13, "curve": 0.311, "c2": 0.61, "c3": 0.623}, {"time": 0.8667, "angle": 1.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.94}], "translate": [{"time": 0.3, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": -11, "y": 8.29, "curve": 0.625, "c3": 0.813, "c4": 0.68}, {"time": 0.7667, "x": -5.71, "y": 16.04, "curve": 0.311, "c2": 0.61, "c3": 0.623}, {"time": 0.8667}]}, "hand_L": {"rotate": [{"angle": 0.39, "curve": "stepped"}, {"time": 0.3, "angle": 0.39, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": 39.19, "curve": 0.625, "c3": 0.813, "c4": 0.68}, {"time": 0.7667, "angle": 18.67, "curve": 0.311, "c2": 0.61, "c3": 0.623}, {"time": 0.8667, "angle": -3.35, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 0.39}]}, "walter2": {"translate": [{"x": -5.88, "y": -0.13}], "scale": [{"x": 0.33, "y": 0.33}]}, "walter": {"translate": [{"x": 0.14, "y": -0.33}], "scale": [{"x": 0.419, "y": 0.419}]}, "walter4": {"translate": [{"x": 0.49, "y": -1.38}], "scale": [{"x": 0.815, "y": 0.815}]}, "walter5": {"translate": [{"x": -2.61, "y": -1.28}], "scale": [{"x": 0.769, "y": 0.769}]}, "body7": {"rotate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "angle": -56.77, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": -167.11, "curve": 0.688, "c3": 0.75}, {"time": 0.8}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": 0.64, "y": 0.11, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": 12.2, "y": 9.01, "curve": 0.688, "c3": 0.75}, {"time": 0.8}]}, "walter_2_di": {"translate": [{"x": 0.07, "y": 1.18}], "scale": [{"x": 0.474, "y": 0.474}]}, "walter_3": {"translate": [{"x": -2.15, "y": -7.81, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -2.47, "y": -8.98, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "x": -2.15, "y": -7.81}], "scale": [{"x": 0.763, "y": 0.763, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.728, "y": 0.728, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "x": 0.763, "y": 0.763}]}, "body": {"rotate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "angle": 2.67, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": -3.72, "curve": 0.688, "c3": 0.75}, {"time": 0.8}]}, "body8": {"rotate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "angle": -154.3, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": 131.06, "curve": 0.688, "c3": 0.75}, {"time": 0.8}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": -10, "y": 10.77, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": -18.06, "y": -4.2, "curve": 0.688, "c3": 0.75}, {"time": 0.8}], "scale": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": 0.81, "y": 0.81, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4}]}, "body9": {"rotate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "angle": -154.3, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "angle": 131.06, "curve": 0.688, "c3": 0.75}, {"time": 0.8}], "translate": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": -10, "y": 10.77, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4, "x": -18.06, "y": -4.2, "curve": 0.688, "c3": 0.75}, {"time": 0.8}], "scale": [{"curve": 0.25, "c3": 0}, {"time": 0.3, "x": 0.81, "y": 0.81, "curve": 0, "c2": 0.78, "c3": 0.108}, {"time": 0.4}]}, "walter_7": {"scale": [{"x": 0.566, "y": 0.566}, {"time": 0.8, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 1, "x": 0.566, "y": 0.566}]}, "walter_8": {"scale": [{"x": 1.055, "y": 1.055}, {"time": 0.2, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 0.3667, "x": 0.566, "y": 0.566}, {"time": 1, "x": 1.055, "y": 1.055}]}, "walter_9": {"scale": [{"x": 0.729, "y": 0.729}, {"time": 0.6, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 0.8, "x": 0.566, "y": 0.566}, {"time": 1, "x": 0.729, "y": 0.729}]}}, "drawOrder": [{"time": 0.1, "offsets": [{"slot": "walter_2_di", "offset": -10}, {"slot": "walter_2_di2", "offset": -10}]}, {"time": 0.7333}]}, "idle": {"slots": {"walter": {"color": [{"color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}, "walter2": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00"}, {"time": 1.4, "color": "ffffffff"}]}, "walter_2": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}]}, "walter_2_di": {"color": [{"color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}, "walter_2_di2": {"color": [{"time": 0.3667, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff"}]}, "walter_7": {"color": [{"color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "walter_8": {"color": [{"color": "ffffffb6"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffffb6"}]}, "walter_9": {"color": [{"time": 0.5333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}]}}, "bones": {"body2": {"rotate": [{"angle": 3.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -4.31, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "scale": [{"x": 1.018, "y": 1.018, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.98, "y": 0.987, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.018, "y": 1.018}]}, "body4": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": 2.09, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -0.33, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 1.78}], "translate": [{"x": -0.35, "y": -0.01, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -2.65, "y": -0.05, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": -0.35, "y": -0.01}], "scale": [{"x": 1.012, "y": 1.013, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "x": 1.021, "y": 1.021, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.953, "y": 0.959, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": 1.012, "y": 1.013}]}, "body6": {"rotate": [{"angle": -4.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "angle": 5.91, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -10.28, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "angle": -4.32}], "translate": [{"x": -1.63, "y": -0.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -2.57, "y": -0.11, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "x": -1.63, "y": -0.07}], "scale": [{"x": 0.974, "y": 0.978, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "x": 1.034, "y": 1.034, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 0.94, "y": 0.946, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "x": 0.974, "y": 0.978}]}, "body5": {"rotate": [{"angle": 1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 2.09, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -0.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 1.2}], "translate": [{"x": -1.2, "y": -0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": -3.25, "y": -0.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "x": -1.2, "y": -0.04}], "scale": [{"x": 0.995, "y": 0.997, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "x": 1.016, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.958, "y": 0.964, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "x": 0.995, "y": 0.997}]}, "hand_R2": {"rotate": [{"angle": -4.25, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": -6.51, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 3.89, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": -4.25}]}, "hand_R": {"rotate": [{"angle": 0.51, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.1, "angle": -0.27, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "angle": -1.43, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.89, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 0.51}]}, "hand_L2": {"rotate": [{"angle": 4.94, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2333, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": -3.01, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 4.94}]}, "hand_L": {"rotate": [{"angle": 0.39, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.1, "angle": 1.18, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.3333, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3.01, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 0.39}]}, "walter_7": {"scale": [{"x": 0.566, "y": 0.566}, {"time": 1.3333, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 1.6667, "x": 0.566, "y": 0.566}]}, "walter_8": {"scale": [{"x": 1.055, "y": 1.055}, {"time": 0.3333, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 0.6667, "x": 0.566, "y": 0.566}, {"time": 1.6667, "x": 1.055, "y": 1.055}]}, "walter_9": {"scale": [{"x": 0.729, "y": 0.729}, {"time": 1, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 1.3333, "x": 0.566, "y": 0.566}, {"time": 1.6667, "x": 0.729, "y": 0.729}]}, "head": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.886, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "walter2": {"translate": [{"x": -5.88, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 1.6667, "x": -5.88, "y": -0.13}], "scale": [{"x": 0.33, "y": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 1.6667, "x": 0.33, "y": 0.33}]}, "walter": {"translate": [{"x": 0.14, "y": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 1.6667, "x": 0.14, "y": -0.33}], "scale": [{"x": 0.419, "y": 0.419, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 1.6667, "x": 0.419, "y": 0.419}]}, "walter3": {"translate": [{"x": 1.61, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 1.6667, "x": 1.61, "y": 2.34}], "scale": [{"x": 0.48, "y": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 1.6667, "x": 0.48, "y": 0.48}]}, "walter4": {"translate": [{"x": 0.49, "y": -1.38, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7667, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 0.9333, "x": 0.14, "y": -0.33, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.6667, "x": 0.49, "y": -1.38}], "scale": [{"x": 0.815, "y": 0.815, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7667, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 0.9333, "x": 0.419, "y": 0.419, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.6667, "x": 0.815, "y": 0.815}]}, "walter5": {"translate": [{"x": -2.61, "y": -1.28, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7667, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 0.9333, "x": -5.88, "y": -0.13, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.6667, "x": -2.61, "y": -1.28}], "scale": [{"x": 0.769, "y": 0.769, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7667, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 0.9333, "x": 0.33, "y": 0.33, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.6667, "x": 0.769, "y": 0.769}]}, "walter6": {"translate": [{"x": 1.25, "y": -0.01, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7667, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 0.9333, "x": 1.61, "y": 2.34, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.6667, "x": 1.25, "y": -0.01}], "scale": [{"x": 0.846, "y": 0.846, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.7667, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 0.9333, "x": 0.48, "y": 0.48, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1.6667, "x": 0.846, "y": 0.846}]}, "walter_2": {"translate": [{"x": -12.8, "y": 9.58}, {"time": 0.6333, "x": 4.46, "y": -1.45, "curve": "stepped"}, {"time": 1.6667}]}, "walter_2_di": {"translate": [{"x": 0.07, "y": 1.18}, {"time": 1.5, "x": -0.03, "y": -1.07, "curve": "stepped"}, {"time": 1.6667, "x": 0.07, "y": 1.18}], "scale": [{"x": 0.474, "y": 0.474}, {"time": 1.5, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 1.6667, "x": 0.474, "y": 0.474}]}, "walter_2_di2": {"translate": [{"x": -1.29, "y": 5.56}, {"time": 1.5, "x": -0.67, "y": -1.81, "curve": "stepped"}, {"time": 1.6667, "x": -1.29, "y": 5.56}], "scale": [{"x": -0.1, "y": -0.1}, {"time": 1.5, "x": 1.309, "y": 1.309, "curve": "stepped"}, {"time": 1.6667, "x": -0.1, "y": -0.1}]}, "walter_2_di3": {"rotate": [{"angle": 18.89}, {"time": 1.5, "curve": "stepped"}, {"time": 1.6667, "angle": 18.89}], "translate": [{"x": -1.6, "y": 5.43}, {"time": 1.5, "curve": "stepped"}, {"time": 1.6667, "x": -1.6, "y": 5.43}], "scale": [{"x": 0.519, "y": 0.519}, {"time": 1.5, "x": 1.275, "y": 1.275, "curve": "stepped"}, {"time": 1.6667, "x": 0.519, "y": 0.519}]}, "walter_2_di4": {"translate": [{"x": 0.03, "y": 0.23}, {"time": 0.8667, "x": -0.03, "y": -1.07, "curve": "stepped"}, {"time": 1.0333, "x": 0.07, "y": 1.18}, {"time": 1.6667, "x": 0.03, "y": 0.23}], "scale": [{"x": 0.756, "y": 0.756}, {"time": 0.8667, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 1.0333, "x": 0.474, "y": 0.474}, {"time": 1.6667, "x": 0.756, "y": 0.756}]}, "walter_2_di5": {"translate": [{"x": -1.03, "y": 2.45}, {"time": 0.8667, "x": -0.67, "y": -1.81, "curve": "stepped"}, {"time": 1.0333, "x": -1.29, "y": 5.56}, {"time": 1.6667, "x": -1.03, "y": 2.45}], "scale": [{"x": 0.495, "y": 0.495}, {"time": 0.8667, "x": 1.309, "y": 1.309, "curve": "stepped"}, {"time": 1.0333, "x": -0.1, "y": -0.1}, {"time": 1.6667, "x": 0.495, "y": 0.495}]}, "walter_2_di6": {"rotate": [{"angle": 10.92}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.0333, "angle": 18.89}, {"time": 1.6667, "angle": 10.92}], "translate": [{"x": -0.92, "y": 3.14}, {"time": 0.8667, "curve": "stepped"}, {"time": 1.0333, "x": -1.6, "y": 5.43}, {"time": 1.6667, "x": -0.92, "y": 3.14}], "scale": [{"x": 0.838, "y": 0.838}, {"time": 0.8667, "x": 1.275, "y": 1.275, "curve": "stepped"}, {"time": 1.0333, "x": 0.519, "y": 0.519}, {"time": 1.6667, "x": 0.838, "y": 0.838}]}, "walter_5": {"translate": [{"x": 1.07, "y": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.18, "y": -3.17, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.07, "y": 4.79}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.728, "y": 0.728, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "walter_4": {"translate": [{"x": 0.24, "y": 1.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "x": 0.27, "y": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.18, "y": -3.17, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "x": 0.24, "y": 1.86}], "scale": [{"x": 0.9, "y": 0.9, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 0.728, "y": 0.728, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "x": 0.9, "y": 0.9}]}, "walter_3": {"translate": [{"x": -2.15, "y": -7.81, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -2.47, "y": -8.98, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "x": -2.15, "y": -7.81}], "scale": [{"x": 0.763, "y": 0.763, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 0.728, "y": 0.728, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "x": 0.763, "y": 0.763}]}}}, "run": {"slots": {"walter": {"color": [{"color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00"}]}, "walter2": {"color": [{"time": 0.1667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}]}, "walter_2": {"color": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00"}]}, "walter_2_di": {"color": [{"color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00"}]}, "walter_2_di2": {"color": [{"time": 0.2333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff"}]}, "walter_7": {"color": [{"color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "walter_8": {"color": [{"color": "ffffffb6"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffffb6"}]}, "walter_9": {"color": [{"time": 0.3333, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}}, "bones": {"body2": {"rotate": [{"angle": 3.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.03}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -4.31, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 1}], "scale": [{"x": 1.018, "y": 1.018, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.98, "y": 0.987, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.018, "y": 1.018}]}, "body4": {"rotate": [{"angle": 1.78, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 2.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.33, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 1.78}], "translate": [{"x": -0.35, "y": -0.01, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -2.65, "y": -0.05, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "x": -0.35, "y": -0.01}], "scale": [{"x": 1.012, "y": 1.013, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "x": 1.021, "y": 1.021, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.953, "y": 0.959, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "x": 1.012, "y": 1.013}]}, "body6": {"rotate": [{"angle": -4.32, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 5.91, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -10.28, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": -4.32}], "translate": [{"x": -1.63, "y": -0.07, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": -2.57, "y": -0.11, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "x": -1.63, "y": -0.07}], "scale": [{"x": 0.974, "y": 0.978, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "x": 1.034, "y": 1.034, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "x": 0.94, "y": 0.946, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "x": 0.974, "y": 0.978}]}, "body5": {"rotate": [{"angle": 1.2, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 2.09, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -0.33, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 1.2}], "translate": [{"x": -1.2, "y": -0.04, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -3.25, "y": -0.1, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": -1.2, "y": -0.04}], "scale": [{"x": 0.995, "y": 0.997, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "x": 1.016, "y": 1.016, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.958, "y": 0.964, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 0.995, "y": 0.997}]}, "hand_R2": {"rotate": [{"angle": -4.25, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1333, "angle": -6.51, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 3.89, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1, "angle": -4.25}]}, "hand_R": {"rotate": [{"angle": 0.51, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.0667, "angle": -0.27, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2, "angle": -1.43, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 3.89, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.51}]}, "hand_L2": {"rotate": [{"angle": 4.94, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.1333, "angle": 7.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.01, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1, "angle": 4.94}]}, "hand_L": {"rotate": [{"angle": 0.39, "curve": 0.341, "c2": 0.36, "c3": 0.677, "c4": 0.7}, {"time": 0.0667, "angle": 1.18, "curve": 0.381, "c2": 0.59, "c3": 0.728}, {"time": 0.2, "angle": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -3.01, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.39}]}, "walter_7": {"scale": [{"x": 0.566, "y": 0.566}, {"time": 0.8, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 1, "x": 0.566, "y": 0.566}]}, "walter_8": {"scale": [{"x": 1.055, "y": 1.055}, {"time": 0.2, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 0.4, "x": 0.566, "y": 0.566}, {"time": 1, "x": 1.055, "y": 1.055}]}, "walter_9": {"scale": [{"x": 0.729, "y": 0.729}, {"time": 0.6, "x": 1.218, "y": 1.218, "curve": "stepped"}, {"time": 0.8, "x": 0.566, "y": 0.566}, {"time": 1, "x": 0.729, "y": 0.729}]}, "head": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.886, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "walter2": {"translate": [{"x": -5.88, "y": -0.13, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 1, "x": -5.88, "y": -0.13}], "scale": [{"x": 0.33, "y": 0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 1, "x": 0.33, "y": 0.33}]}, "walter": {"translate": [{"x": 0.14, "y": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 1, "x": 0.14, "y": -0.33}], "scale": [{"x": 0.419, "y": 0.419, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 1, "x": 0.419, "y": 0.419}]}, "walter3": {"translate": [{"x": 1.61, "y": 2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 1, "x": 1.61, "y": 2.34}], "scale": [{"x": 0.48, "y": 0.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 1, "x": 0.48, "y": 0.48}]}, "walter4": {"translate": [{"x": 0.49, "y": -1.38, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 0.5667, "x": 0.14, "y": -0.33, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1, "x": 0.49, "y": -1.38}], "scale": [{"x": 0.815, "y": 0.815, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 0.5667, "x": 0.419, "y": 0.419, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1, "x": 0.815, "y": 0.815}]}, "walter5": {"translate": [{"x": -2.61, "y": -1.28, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 0.5667, "x": -5.88, "y": -0.13, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1, "x": -2.61, "y": -1.28}], "scale": [{"x": 0.769, "y": 0.769, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 0.5667, "x": 0.33, "y": 0.33, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1, "x": 0.769, "y": 0.769}]}, "walter6": {"translate": [{"x": 1.25, "y": -0.01, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "x": 0.86, "y": -2.5, "curve": "stepped"}, {"time": 0.5667, "x": 1.61, "y": 2.34, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1, "x": 1.25, "y": -0.01}], "scale": [{"x": 0.846, "y": 0.846, "curve": 0.374, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "x": 1.235, "y": 1.235, "curve": "stepped"}, {"time": 0.5667, "x": 0.48, "y": 0.48, "curve": 0.251, "c3": 0.624, "c4": 0.49}, {"time": 1, "x": 0.846, "y": 0.846}]}, "walter_2": {"translate": [{"x": -12.8, "y": 9.58}, {"time": 0.3667, "x": 4.46, "y": -1.45, "curve": "stepped"}, {"time": 1}]}, "walter_2_di": {"translate": [{"x": 0.07, "y": 1.18}, {"time": 0.9, "x": -0.03, "y": -1.07, "curve": "stepped"}, {"time": 1, "x": 0.07, "y": 1.18}], "scale": [{"x": 0.474, "y": 0.474}, {"time": 0.9, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 1, "x": 0.474, "y": 0.474}]}, "walter_2_di2": {"translate": [{"x": -1.29, "y": 5.56}, {"time": 0.9, "x": -0.67, "y": -1.81, "curve": "stepped"}, {"time": 1, "x": -1.29, "y": 5.56}], "scale": [{"x": -0.1, "y": -0.1}, {"time": 0.9, "x": 1.309, "y": 1.309, "curve": "stepped"}, {"time": 1, "x": -0.1, "y": -0.1}]}, "walter_2_di3": {"rotate": [{"angle": 18.89}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "angle": 18.89}], "translate": [{"x": -1.6, "y": 5.43}, {"time": 0.9, "curve": "stepped"}, {"time": 1, "x": -1.6, "y": 5.43}], "scale": [{"x": 0.519, "y": 0.519}, {"time": 0.9, "x": 1.275, "y": 1.275, "curve": "stepped"}, {"time": 1, "x": 0.519, "y": 0.519}]}, "walter_2_di4": {"translate": [{"x": 0.03, "y": 0.23}, {"time": 0.5333, "x": -0.03, "y": -1.07, "curve": "stepped"}, {"time": 0.6333, "x": 0.07, "y": 1.18}, {"time": 1, "x": 0.03, "y": 0.23}], "scale": [{"x": 0.756, "y": 0.756}, {"time": 0.5333, "x": 1.141, "y": 1.141, "curve": "stepped"}, {"time": 0.6333, "x": 0.474, "y": 0.474}, {"time": 1, "x": 0.756, "y": 0.756}]}, "walter_2_di5": {"translate": [{"x": -1.03, "y": 2.45}, {"time": 0.5333, "x": -0.67, "y": -1.81, "curve": "stepped"}, {"time": 0.6333, "x": -1.29, "y": 5.56}, {"time": 1, "x": -1.03, "y": 2.45}], "scale": [{"x": 0.495, "y": 0.495}, {"time": 0.5333, "x": 1.309, "y": 1.309, "curve": "stepped"}, {"time": 0.6333, "x": -0.1, "y": -0.1}, {"time": 1, "x": 0.495, "y": 0.495}]}, "walter_2_di6": {"rotate": [{"angle": 10.92}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "angle": 18.89}, {"time": 1, "angle": 10.92}], "translate": [{"x": -0.92, "y": 3.14}, {"time": 0.5333, "curve": "stepped"}, {"time": 0.6333, "x": -1.6, "y": 5.43}, {"time": 1, "x": -0.92, "y": 3.14}], "scale": [{"x": 0.838, "y": 0.838}, {"time": 0.5333, "x": 1.275, "y": 1.275, "curve": "stepped"}, {"time": 0.6333, "x": 0.519, "y": 0.519}, {"time": 1, "x": 0.838, "y": 0.838}]}, "walter_5": {"translate": [{"x": 1.07, "y": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.18, "y": -3.17, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 1.07, "y": 4.79}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.728, "y": 0.728, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "walter_4": {"translate": [{"x": 0.24, "y": 1.86, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "x": 0.27, "y": 4.79, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.18, "y": -3.17, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 0.24, "y": 1.86}], "scale": [{"x": 0.9, "y": 0.9, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 0.728, "y": 0.728, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 0.9, "y": 0.9}]}, "walter_3": {"translate": [{"x": -2.15, "y": -7.81, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -2.47, "y": -8.98, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "x": -2.15, "y": -7.81}], "scale": [{"x": 0.763, "y": 0.763, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.728, "y": 0.728, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1, "x": 0.763, "y": 0.763}]}}}}}