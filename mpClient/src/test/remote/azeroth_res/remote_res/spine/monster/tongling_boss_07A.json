{"skeleton": {"hash": "n35dsMBSSxErQNVyf9eGD2JnUO0", "spine": "3.8.99", "x": -62.3, "y": -23.67, "width": 124, "height": 166.26, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/通灵学院/boss博学者普科尔特"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 70.93}, {"name": "body", "parent": "bone", "length": 15.93, "x": -3.34, "y": 58.6}, {"name": "body_3", "parent": "body", "length": 8.48, "rotation": 88.26, "y": -2.31}, {"name": "body_4", "parent": "body_3", "length": 20.46, "rotation": 8.95, "x": 8.48}, {"name": "body_5", "parent": "body_4", "length": 24.9, "rotation": 4.1, "x": 20.46}, {"name": "hand_R3", "parent": "body_5", "length": 42.72, "rotation": 147.53, "x": 18.75, "y": 14.28}, {"name": "hand_R", "parent": "hand_R3", "length": 19.41, "rotation": 14.32, "x": 42.66, "y": -0.57}, {"name": "hand_R4", "parent": "hand_R", "length": 25.81, "rotation": 21.85, "x": 20.21, "y": -0.16}, {"name": "weapons", "parent": "hand_R4", "length": 44.26, "rotation": 60.88, "x": 12.86, "y": -2.65}, {"name": "hand_L3", "parent": "body_5", "length": 46.05, "rotation": 177.68, "x": 13.1, "y": -28.36}, {"name": "hand_L", "parent": "hand_L3", "length": 18.3, "rotation": 1.53, "x": 45.97, "y": -0.51}, {"name": "hand_L4", "parent": "hand_L", "length": 20.3, "rotation": -9.8, "x": 18.5, "y": -0.3}, {"name": "body_2", "parent": "body", "length": 24.17, "rotation": -86.82, "x": -14.5, "y": -4.04}, {"name": "leg_R3", "parent": "body_2", "length": 23.74, "rotation": -29.81, "x": 23.73, "y": 0.78}, {"name": "leg_R5", "parent": "leg_R3", "length": 18.52, "rotation": -29.54, "x": 23.24, "y": 0.48, "transform": "noRotationOrReflection"}, {"name": "leg_L", "parent": "body", "length": 20.99, "rotation": -61.47, "x": 5.89, "y": -4.46}, {"name": "leg_L3", "parent": "leg_L", "length": 22.42, "rotation": -32.19, "x": 20.5, "y": -0.14}, {"name": "leg_L5", "parent": "leg_L3", "length": 19.61, "rotation": -33.84, "x": 22.78, "y": 0.02, "transform": "noRotationOrReflection"}, {"name": "head", "parent": "body_5", "length": 14.93, "rotation": -49.77, "x": -8.47, "y": -12.68}, {"name": "hair_2", "parent": "head", "length": 4.23, "rotation": 167.12, "x": 39.39, "y": 11.78}, {"name": "hair_3", "parent": "hair_2", "length": 7.76, "rotation": 33.16, "x": 3.97, "y": 0.21}, {"name": "hair_4", "parent": "hair_3", "length": 6.5, "rotation": 14.3, "x": 7.76}, {"name": "hair_5", "parent": "hair_4", "length": 4.99, "rotation": 11.47, "x": 6.5}, {"name": "hair", "parent": "head", "length": 4.91, "rotation": -99.26, "x": 45.41, "y": -0.58}, {"name": "hair2", "parent": "hair", "length": 7.26, "rotation": -41.41, "x": 4.91}, {"name": "hair3", "parent": "hair2", "length": 6.99, "rotation": -8.1, "x": 7.26}, {"name": "hair4", "parent": "hair3", "length": 5.66, "rotation": -6.26, "x": 7.08, "y": 0.12}, {"name": "hair_6", "parent": "head", "length": 8.54, "rotation": 162.97, "x": 28.83, "y": 24.18}, {"name": "hair_7", "parent": "hair_6", "length": 15.26, "rotation": 42.16, "x": 8.54}, {"name": "hair_8", "parent": "hair_7", "length": 10.78, "rotation": 21.55, "x": 15.26}, {"name": "hair_9", "parent": "hair_8", "length": 9.93, "rotation": 7.21, "x": 10.78}, {"name": "hair_10", "parent": "hair_9", "length": 7.1, "rotation": 3.57, "x": 9.93}, {"name": "hair_11", "parent": "head", "length": 12.54, "rotation": -140.03, "x": 43.79, "y": -2.43}, {"name": "hair_12", "parent": "hair_11", "length": 13.88, "rotation": -4.69, "x": 12.54}, {"name": "hair_13", "parent": "hair_12", "length": 9.58, "rotation": -9.42, "x": 13.88}, {"name": "hair_14", "parent": "hair_13", "length": 7.8, "rotation": -3.79, "x": 9.58}, {"name": "hair_15", "parent": "head", "length": 7.24, "rotation": -161.07, "x": 22.07, "y": 36.23, "color": "00a6ffff"}, {"name": "hair_17", "parent": "hair_15", "length": 9.8, "rotation": 21.47, "x": 6.93, "y": -0.11, "color": "00a6ffff"}, {"name": "hair_18", "parent": "hair_17", "length": 10.45, "rotation": -1.93, "x": 9.8, "color": "00a6ffff"}, {"name": "hair_19", "parent": "hair_18", "length": 10.75, "rotation": 13.61, "x": 10.45, "y": 0.33, "color": "00a6ffff"}, {"name": "yinying", "parent": "bone", "length": 6.37, "rotation": 92.2, "x": -1.97, "y": 3.58}, {"name": "leg_R", "parent": "bone", "x": -26.58, "y": 9.71, "color": "ff3f00ff"}, {"name": "leg_L2", "parent": "bone", "x": 10.69, "y": 13.64, "color": "ff3f00ff"}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "hand_L3", "bone": "hand_L3", "attachment": "hand_L3"}, {"name": "hand_L2", "bone": "hand_L4", "attachment": "hand_L2"}, {"name": "hand_L", "bone": "hand_L", "attachment": "hand_L"}, {"name": "leg_L3", "bone": "leg_L5", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L3", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L", "attachment": "leg_L"}, {"name": "arrows_3", "bone": "body_5", "attachment": "arrows_3"}, {"name": "body_3", "bone": "body_3", "attachment": "body_3"}, {"name": "arrows_2", "bone": "head", "attachment": "arrows_2"}, {"name": "hair_5", "bone": "hair_11", "attachment": "hair_5"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "eye_close_R", "bone": "head", "color": "ffffff00", "attachment": "eye_close_R"}, {"name": "eye_close_L", "bone": "head", "color": "ffffff00", "attachment": "eye_close_L"}, {"name": "hair_4", "bone": "hair_17", "attachment": "hair_4"}, {"name": "hair_3", "bone": "hair_6", "attachment": "hair_3"}, {"name": "hair_2", "bone": "hair_2", "attachment": "hair_2"}, {"name": "hair", "bone": "hair", "attachment": "hair"}, {"name": "arrows", "bone": "head", "attachment": "arrows"}, {"name": "leg_R3", "bone": "leg_R5", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R3", "attachment": "leg_R2"}, {"name": "body_2", "bone": "leg_L", "attachment": "body_2"}, {"name": "leg_R", "bone": "body_2", "attachment": "leg_R"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "hand_R3", "bone": "hand_R3", "attachment": "hand_R3"}, {"name": "weapons", "bone": "weapons", "attachment": "weapons"}, {"name": "hand_R2", "bone": "hand_R4", "attachment": "hand_R2"}, {"name": "hand_R", "bone": "hand_R", "attachment": "hand_R"}], "ik": [{"name": "leg_L", "order": 1, "bones": ["leg_L", "leg_L3"], "target": "leg_L2", "bendPositive": false}, {"name": "leg_R", "bones": ["body_2", "leg_R3"], "target": "leg_R", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"arrows": {"arrows": {"x": 22.45, "y": 43.04, "rotation": -51.54, "width": 32, "height": 24}}, "arrows_2": {"arrows_2": {"x": 43.24, "y": -17.7, "rotation": -51.54, "width": 25, "height": 15}}, "arrows_3": {"arrows_3": {"x": 34.96, "y": 19.16, "rotation": -101.31, "width": 38, "height": 54}}, "body": {"body": {"x": -1.96, "width": 38, "height": 14}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.4584, 0, 0.29995, 0.05231, 0.14726, 0.15171, 0.03202, 0.30296, 0, 0.55792, 0.06371, 0.78696, 0.22792, 0.93388, 0.41519, 0.9771, 0.62261, 0.98574, 0.75802, 0.86906, 0.85885, 0.70053, 0.95104, 0.4931, 1, 0.27271, 0.90207, 0.12578, 0.7465, 0.03935, 0.60821, 0, 0.15242, 0.31412, 0.35984, 0.31262, 0.59321, 0.31524, 0.82077, 0.30494, 0.26831, 0.51639, 0.26626, 0.77569, 0.49295, 0.49315, 0.49393, 0.79565, 0.69747, 0.48302, 0.66383, 0.7728], "triangles": [17, 1, 0, 19, 14, 13, 19, 13, 12, 18, 0, 15, 18, 15, 14, 18, 14, 19, 17, 0, 18, 2, 1, 17, 24, 18, 19, 11, 19, 12, 22, 18, 24, 11, 24, 19, 10, 24, 11, 25, 22, 24, 25, 24, 10, 23, 22, 25, 9, 25, 10, 8, 23, 25, 8, 25, 9, 16, 2, 17, 3, 2, 16, 22, 17, 18, 20, 16, 17, 20, 17, 22, 16, 20, 4, 16, 4, 3, 21, 5, 20, 20, 5, 4, 20, 22, 23, 21, 20, 23, 6, 5, 21, 7, 21, 23, 6, 21, 7, 7, 23, 8], "vertices": [1, 2, -5.67, 4.05, 1, 1, 2, -12.33, 2.62, 1, 2, 13, -4.11, -4.06, 0.33333, 2, -18.76, -0.13, 0.66667, 3, 13, -0.15, -9.12, 0.66667, 16, -14.07, -25.94, 0, 2, -23.62, -4.34, 0.33333, 2, 13, 6.91, -10.85, 1, 16, -8.44, -30.53, 0, 2, 13, 13.46, -8.53, 0.99999, 16, -1.53, -31.24, 1e-05, 2, 13, 17.94, -1.87, 0.93482, 16, 5.38, -27.15, 0.06518, 2, 13, 19.58, 5.92, 0.73912, 16, 10.2, -20.81, 0.26088, 3, 13, 20.29, 14.6, 0.46586, 16, 14.57, -13.28, 0.53414, 2, 1.1, -23.58, 0, 3, 13, 17.34, 20.46, 0.20057, 16, 14.42, -6.72, 0.79937, 2, 6.8, -20.34, 5e-05, 3, 13, 12.86, 24.95, 0.06294, 16, 12.3, -0.74, 0.86876, 2, 11.06, -15.64, 0.06829, 3, 13, 7.27, 29.13, 0.00287, 16, 9.04, 5.43, 0.6892, 2, 14.96, -9.85, 0.30792, 3, 13, 1.22, 31.52, 0, 16, 4.6, 10.19, 0.3588, 2, 17.04, -3.69, 0.6412, 3, 13, -3.11, 27.64, 0, 16, -0.98, 8.54, 0.09401, 2, 12.95, 0.44, 0.90599, 3, 13, -5.88, 21.24, 0, 16, -6.22, 3.95, 0.0003, 2, 6.43, 2.89, 0.9997, 3, 13, -7.3, 15.5, 0, 16, -9.97, -0.62, 0.0003, 2, 0.62, 4.02, 0.9997, 1, 13, 0.44, -4.09, 1, 3, 13, 0.87, 4.61, 0.76689, 16, -7.26, -13.97, 0.04493, 2, -9.85, -4.68, 0.18818, 3, 13, 1.48, 14.39, 0.11967, 16, -2.51, -5.39, 0.43833, 2, -0.05, -4.8, 0.44201, 3, 13, 1.71, 23.95, 0, 16, 1.8, 3.14, 0.64381, 2, 9.51, -4.56, 0.35619, 3, 13, 6.36, 0.46, 0.99634, 16, -4.08, -20.07, 0.00225, 2, -13.72, -10.37, 0.00141, 2, 13, 13.6, -0.02, 0.99999, 16, 2.26, -23.62, 1e-05, 3, 13, 6.22, 9.92, 0.49563, 16, -0.15, -11.47, 0.35722, 2, -4.28, -9.76, 0.14715, 3, 13, 14.68, 9.5, 0.62601, 16, 7.31, -15.48, 0.36836, 2, -4.28, -18.23, 0.00562, 3, 13, 6.41, 18.51, 0.06971, 16, 3.71, -3.79, 0.91966, 2, 4.31, -9.52, 0.01063, 3, 13, 14.43, 16.66, 0.26189, 16, 10.16, -8.91, 0.73756, 2, 2.86, -17.63, 0.00055], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 42, "height": 28}}, "body_3": {"body_3": {"type": "mesh", "uvs": [0.50444, 0, 0.32772, 0.02953, 0.151, 0.09003, 0.03436, 0.20765, 0, 0.35216, 0.00962, 0.50339, 0.07324, 0.67142, 0.15453, 0.82937, 0.25703, 0.93691, 0.37013, 0.96716, 0.56453, 0.99404, 0.76246, 0.94699, 0.89324, 0.83609, 0.96039, 0.67478, 1, 0.50675, 0.9922, 0.33199, 0.93565, 0.15388, 0.80134, 0.05978, 0.6635, 0.00601, 0.47617, 0.26478, 0.51505, 0.4765, 0.53979, 0.6647, 0.55746, 0.83273, 0.55393, 0.93691, 0.34186, 0.86298, 0.29944, 0.6647, 0.21462, 0.41937, 0.16513, 0.21101, 0.70591, 0.1438, 0.7766, 0.35552, 0.79781, 0.58404, 0.79781, 0.76216], "triangles": [28, 18, 17, 27, 2, 1, 3, 2, 27, 19, 1, 0, 27, 1, 19, 28, 19, 0, 28, 0, 18, 16, 29, 28, 16, 28, 17, 29, 16, 15, 19, 28, 29, 26, 27, 19, 20, 19, 29, 26, 19, 20, 26, 5, 4, 29, 15, 14, 20, 29, 30, 25, 26, 20, 26, 4, 27, 27, 4, 3, 6, 5, 26, 30, 29, 14, 21, 20, 30, 25, 20, 21, 6, 26, 25, 13, 30, 14, 31, 30, 13, 21, 30, 31, 7, 6, 25, 22, 21, 31, 12, 31, 13, 24, 25, 21, 7, 25, 24, 11, 31, 12, 24, 21, 22, 23, 24, 22, 8, 7, 24, 11, 22, 31, 23, 22, 11, 9, 24, 23, 8, 24, 9, 10, 23, 11, 9, 23, 10], "vertices": [2, 4, 49.81, -3.32, 0.00736, 5, 29.03, -5.41, 0.99264, 2, 4, 49.31, 7.07, 0.00544, 5, 29.27, 5, 0.99456, 3, 3, 52.09, 24.79, 0.0007, 4, 46.93, 17.71, 0.0303, 5, 27.67, 15.77, 0.969, 3, 3, 44.72, 31.34, 0.0059, 4, 40.67, 25.32, 0.09063, 5, 21.96, 23.81, 0.90347, 3, 3, 35.85, 33.06, 0.02462, 4, 32.17, 28.4, 0.20015, 5, 13.7, 27.49, 0.77524, 3, 3, 26.64, 32.23, 0.08354, 4, 22.95, 29.01, 0.3505, 5, 4.55, 28.76, 0.56596, 3, 3, 16.51, 28.23, 0.2208, 4, 12.32, 26.63, 0.45479, 5, -6.23, 27.15, 0.32441, 3, 3, 7.02, 23.22, 0.42979, 4, 2.17, 23.17, 0.4414, 5, -16.6, 24.41, 0.12881, 3, 3, 0.64, 17.08, 0.66122, 4, -5.09, 18.09, 0.30824, 5, -24.2, 19.87, 0.03054, 3, 3, -1, 10.47, 0.8499, 4, -7.74, 11.82, 0.14581, 5, -27.29, 13.8, 0.00429, 3, 3, -2.3, -0.85, 0.78928, 4, -10.79, 0.84, 0.21056, 5, -31.11, 3.07, 0.00016, 3, 3, 0.92, -12.24, 0.5478, 4, -9.38, -10.91, 0.44771, 5, -30.55, -8.75, 0.00449, 3, 3, 7.91, -19.62, 0.22186, 4, -3.62, -19.29, 0.7378, 5, -25.4, -17.52, 0.04034, 3, 3, 17.86, -23.21, 0.05477, 4, 5.65, -24.39, 0.8013, 5, -16.52, -23.27, 0.14393, 3, 3, 28.18, -25.2, 0.0052, 4, 15.53, -27.95, 0.666, 5, -6.92, -27.53, 0.3288, 2, 4, 26.16, -28.84, 0.44476, 5, 3.62, -29.18, 0.55524, 2, 4, 37.35, -26.95, 0.24537, 5, 14.92, -28.09, 0.75463, 2, 4, 44.03, -19.95, 0.10869, 5, 22.08, -21.58, 0.89131, 2, 4, 48.29, -12.43, 0.03772, 5, 26.86, -14.38, 0.96228, 2, 4, 33.99, 0.33, 0.00047, 5, 13.51, -0.63, 0.99953, 2, 4, 20.89, -0.28, 0.23713, 5, 0.41, -0.31, 0.76287, 1, 4, 9.32, -0.26, 1, 1, 3, 7.52, -0.14, 1, 2, 3, 1.16, -0.13, 0.99897, 4, -7.25, 1.01, 0.00103, 3, 3, 5.3, 12.3, 0.6707, 4, -1.23, 12.65, 0.32089, 5, -20.74, 14.16, 0.00841, 3, 3, 17.31, 15.12, 0.1731, 4, 11.08, 13.57, 0.69176, 5, -8.4, 14.2, 0.13513, 3, 3, 32.12, 20.5, 0.02206, 4, 26.54, 16.57, 0.24311, 5, 7.24, 16.09, 0.73483, 3, 3, 44.74, 23.75, 0.00073, 4, 39.51, 17.82, 0.04493, 5, 20.27, 16.41, 0.95434, 2, 4, 39.64, -13.81, 0.06955, 5, 18.14, -15.15, 0.93045, 2, 4, 26.31, -16.26, 0.36413, 5, 4.67, -16.63, 0.63587, 3, 3, 23.11, -13.62, 0.00086, 4, 12.33, -15.73, 0.84195, 5, -9.24, -15.11, 0.15719, 3, 3, 12.25, -13.95, 0.1017, 4, 1.55, -14.36, 0.88001, 5, -19.89, -12.97, 0.01829], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 58, "height": 61}}, "eye_close_L": {"eye_close_L": {"x": 21.95, "y": -10.99, "rotation": -51.54, "width": 12, "height": 10}}, "eye_close_R": {"eye_close_R": {"x": 14.28, "y": 5.9, "rotation": -51.54, "width": 16, "height": 13}}, "hair": {"hair": {"type": "mesh", "uvs": [0.2472, 0.08506, 0.07963, 0.14456, 0, 0.23976, 0.11242, 0.35026, 0.19984, 0.48941, 0.2472, 0.62881, 0.27634, 0.75736, 0.32006, 0.96369, 0.48399, 0.95519, 0.66977, 0.80729, 0.81548, 0.65939, 0.93934, 0.51659, 0.94298, 0.3432, 0.8847, 0.1732, 0.82641, 0.0593, 0.62606, 0, 0.40749, 0.027, 0.41477, 0.1443, 0.5532, 0.23439, 0.54956, 0.34829, 0.55685, 0.48089, 0.5277, 0.59479, 0.51313, 0.69849, 0.45849, 0.79029], "triangles": [3, 2, 17, 17, 1, 0, 1, 17, 2, 18, 3, 17, 14, 17, 15, 14, 18, 17, 0, 16, 17, 17, 16, 15, 18, 14, 13, 4, 3, 19, 20, 19, 12, 19, 3, 18, 19, 18, 12, 18, 13, 12, 22, 5, 21, 10, 21, 11, 5, 4, 21, 21, 20, 11, 21, 4, 20, 11, 20, 12, 4, 19, 20, 7, 23, 8, 7, 6, 23, 8, 23, 9, 23, 22, 9, 9, 22, 10, 23, 6, 22, 6, 5, 22, 22, 21, 10], "vertices": [2, 25, -4.3, -4.21, 0.0006, 24, -1.1, -0.32, 0.9994, 1, 24, -1.36, -3.26, 1, 1, 24, 0, -6, 1, 4, 27, -9, -7.82, 3e-05, 26, -2.72, -6.67, 0.18037, 25, 3.63, -6.22, 0.1087, 24, 3.51, -7.07, 0.71091, 4, 27, -5.22, -5.66, 0.06756, 26, 1.27, -4.93, 0.3971, 25, 7.82, -5.06, 0.15351, 24, 7.43, -8.97, 0.38183, 4, 27, -1.31, -4.04, 0.24621, 26, 5.34, -3.75, 0.55009, 25, 12.01, -4.46, 0.15521, 24, 10.97, -11.29, 0.04849, 4, 27, 2.34, -2.74, 0.53591, 26, 9.11, -2.86, 0.41333, 25, 15.88, -4.11, 0.04651, 24, 14.09, -13.59, 0.00425, 4, 27, 8.22, -0.7, 0.80171, 26, 15.17, -1.47, 0.1966, 25, 22.08, -3.59, 0.00169, 24, 19.09, -17.3, 0, 3, 27, 7.43, 1.47, 0.90084, 26, 14.63, 0.78, 0.09833, 25, 21.86, -1.3, 0.00083, 3, 27, 2.51, 2.97, 0.78486, 26, 9.9, 2.8, 0.17349, 25, 17.46, 1.37, 0.04165, 4, 27, -2.28, 3.91, 0.5142, 26, 5.24, 4.26, 0.29717, 25, 13.05, 3.48, 0.1884, 24, 17, -6.02, 0.00023, 4, 27, -6.85, 4.6, 0.24354, 26, 0.78, 5.44, 0.3114, 25, 8.8, 5.28, 0.38852, 24, 14.99, -1.86, 0.05654, 4, 27, -11.92, 3.44, 0.06982, 26, -4.39, 4.84, 0.19754, 25, 3.6, 5.41, 0.51697, 24, 11.18, 1.68, 0.21567, 4, 27, -16.69, 1.45, 0.00715, 26, -9.35, 3.39, 0.07386, 25, -1.52, 4.67, 0.37021, 24, 6.86, 4.51, 0.54878, 4, 27, -19.82, -0.14, 3e-05, 26, -12.64, 2.15, 0.00491, 25, -4.95, 3.91, 0.16926, 24, 3.78, 6.2, 0.8258, 2, 25, -6.77, 1.13, 0.0006, 24, 0.58, 5.32, 0.9994, 2, 25, -6, -1.94, 0.0006, 24, -0.88, 2.51, 0.9994, 1, 24, 1.79, 0.22, 1, 2, 25, 0.25, 0, 0.93416, 24, 5.09, -0.16, 0.06584, 3, 26, -3.55, -0.61, 0.00019, 25, 3.66, -0.1, 0.99827, 24, 7.59, -2.5, 0.00153, 1, 26, 0.39, -0.01, 1, 2, 27, -3.22, -0.46, 0.00089, 26, 3.83, 0.02, 0.99911, 1, 27, -0.15, 0.07, 1, 2, 27, 2.71, -0.03, 0.9988, 26, 9.77, -0.2, 0.0012], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 14, "height": 30}}, "hair_2": {"hair_2": {"type": "mesh", "uvs": [0.82268, 0.02502, 0.60819, 0.00191, 0.29294, 0.03465, 0.14994, 0.14827, 0.06869, 0.32353, 0.03619, 0.48723, 0.09794, 0.65672, 0.21494, 0.81079, 0.34494, 0.92249, 0.52369, 1, 0.56919, 0.90708, 0.60494, 0.77612, 0.64718, 0.63938, 0.71721, 0.49712, 0.83746, 0.37193, 1, 0.28142, 1, 0.13505, 0.55797, 0.18127, 0.49622, 0.30645, 0.42797, 0.4509, 0.39604, 0.57024, 0.37979, 0.69735, 0.41879, 0.84564, 0.75029, 0.0772, 0.33104, 0.22935, 0.23679, 0.41424, 0.22704, 0.62224, 0.54229, 0.62224, 0.61704, 0.42772, 0.75354, 0.25246], "triangles": [4, 3, 24, 29, 23, 16, 17, 24, 2, 24, 3, 2, 17, 1, 23, 17, 2, 1, 23, 0, 16, 23, 1, 0, 13, 28, 14, 28, 19, 18, 19, 25, 18, 28, 29, 14, 28, 18, 29, 25, 24, 18, 25, 4, 24, 14, 29, 15, 18, 17, 29, 18, 24, 17, 29, 16, 15, 17, 23, 29, 7, 6, 26, 11, 27, 12, 21, 20, 27, 21, 26, 20, 6, 5, 26, 12, 27, 13, 20, 19, 27, 27, 28, 13, 27, 19, 28, 20, 26, 25, 26, 5, 25, 20, 25, 19, 5, 4, 25, 9, 8, 10, 8, 22, 10, 8, 7, 22, 10, 22, 11, 7, 21, 22, 22, 21, 11, 7, 26, 21, 21, 27, 11], "vertices": [2, 21, -5.22, 2.61, 0.09934, 20, -1.83, -0.46, 0.90066, 2, 21, -4.75, -0.84, 0.01149, 20, 0.46, -3.09, 0.98851, 3, 22, -11.1, -2.7, 0.00205, 21, -2.33, -5.36, 0.11285, 20, 4.95, -5.56, 0.88511, 3, 22, -7.88, -4.78, 0.08427, 21, 1.3, -6.57, 0.30509, 20, 8.65, -4.59, 0.61064, 3, 22, -3.08, -5.75, 0.35627, 21, 6.2, -6.33, 0.3514, 20, 12.62, -1.71, 0.29232, 4, 23, -6.22, -4.83, 0.05268, 22, 1.37, -5.97, 0.63484, 21, 10.56, -5.45, 0.24813, 20, 15.79, 1.42, 0.06435, 4, 23, -1.55, -4.46, 0.36535, 22, 5.87, -4.68, 0.57328, 21, 14.6, -3.08, 0.05589, 20, 17.88, 5.61, 0.00548, 3, 23, 2.82, -3.15, 0.69868, 22, 9.89, -2.53, 0.30127, 21, 17.97, -0.01, 5e-05, 2, 23, 6.08, -1.49, 0.97934, 22, 12.76, -0.25, 0.02066, 1, 23, 8.53, 1.07, 1, 2, 23, 6.14, 2.12, 0.96157, 22, 12.1, 3.3, 0.03843, 3, 23, 2.71, 3.16, 0.68607, 22, 8.54, 3.63, 0.30295, 21, 15.13, 5.63, 0.01098, 3, 23, -0.86, 4.32, 0.35278, 22, 4.81, 4.06, 0.46824, 21, 11.41, 5.12, 0.17898, 4, 23, -4.52, 5.93, 0.05788, 22, 0.9, 4.92, 0.44932, 21, 7.41, 4.99, 0.46904, 20, 7.45, 8.44, 0.02377, 4, 23, -7.61, 8.29, 4e-05, 22, -2.6, 6.61, 0.18505, 21, 3.6, 5.76, 0.6777, 20, 3.83, 7, 0.1372, 3, 22, -5.22, 9.04, 0.01976, 21, 0.47, 7.47, 0.60708, 20, 0.27, 6.71, 0.37316, 3, 22, -9.16, 8.77, 0.00026, 21, -3.29, 6.24, 0.31898, 20, -2.19, 3.63, 0.68076, 2, 21, 0.11, -0.09, 0.70978, 20, 4.11, 0.19, 0.29022, 2, 22, -4, 1.04, 5e-05, 21, 3.63, 0.02, 0.99995, 2, 22, -0.03, 0.22, 0.41356, 21, 7.67, 0.2, 0.58644, 1, 22, 3.22, -0.08, 1, 2, 23, 0.13, -0.13, 0.73911, 22, 6.66, -0.1, 0.26089, 1, 23, 4.18, -0.04, 1, 1, 20, -0.05, -0.09, 1, 3, 22, -5.9, -1.74, 0.00539, 21, 2.47, -3.14, 0.54527, 20, 7.75, -1.07, 0.44935, 3, 22, -0.81, -2.9, 0.47671, 21, 7.69, -3.01, 0.48211, 20, 12.05, 1.89, 0.04118, 2, 23, -2.2, -2.29, 0.06602, 22, 4.8, -2.68, 0.93398, 3, 23, -1.54, 2.71, 0.099, 22, 4.46, 2.35, 0.88129, 21, 11.5, 3.38, 0.01972, 2, 22, -0.86, 3.19, 0.24865, 21, 6.13, 2.88, 0.75135, 3, 22, -5.73, 5.05, 0.00265, 21, 0.96, 3.48, 0.80889, 20, 2.87, 3.64, 0.18845], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 16, "height": 27}}, "hair_3": {"hair_3": {"type": "mesh", "uvs": [0.69222, 0.03687, 0.41456, 0.00665, 0.10856, 0.05198, 0.02356, 0.18043, 0, 0.32146, 0, 0.45746, 0.02356, 0.61361, 0.10856, 0.7345, 0.21056, 0.8428, 0.35789, 0.93346, 0.55056, 1, 0.61856, 0.92843, 0.60156, 0.82013, 0.57889, 0.71183, 0.59589, 0.59095, 0.66956, 0.47006, 0.87922, 0.35421, 1, 0.21065, 1, 0.12502, 0.38623, 0.15776, 0.30123, 0.2862, 0.25023, 0.4348, 0.26723, 0.52043, 0.28989, 0.63124, 0.34656, 0.70428, 0.38056, 0.81257, 0.48823, 0.92843, 0.46556, 0.62872, 0.49389, 0.44487, 0.60723, 0.30383, 0.80556, 0.17287], "triangles": [19, 2, 1, 19, 1, 0, 30, 0, 18, 19, 0, 30, 3, 2, 19, 30, 18, 17, 29, 19, 30, 16, 30, 17, 29, 30, 16, 20, 3, 19, 20, 19, 29, 4, 3, 20, 21, 4, 20, 28, 20, 29, 15, 28, 29, 21, 20, 28, 5, 4, 21, 16, 15, 29, 22, 21, 28, 5, 21, 22, 14, 28, 15, 22, 28, 14, 6, 5, 22, 27, 22, 14, 23, 6, 22, 27, 23, 22, 24, 23, 27, 13, 27, 14, 24, 27, 13, 7, 6, 23, 7, 23, 24, 25, 24, 13, 7, 24, 25, 8, 7, 25, 25, 13, 12, 26, 25, 12, 26, 12, 11, 9, 8, 25, 9, 25, 26, 10, 26, 11, 9, 26, 10], "vertices": [3, 30, -19.58, 14.38, 0.00161, 29, -8.23, 6.18, 0.02965, 28, -1.71, -0.94, 0.96875, 2, 29, -8.28, -0.68, 0.14682, 28, 2.86, -6.06, 0.85318, 2, 29, -4.2, -7.26, 0.46179, 28, 10.3, -8.2, 0.53821, 3, 30, -14.2, -2.61, 0.00089, 29, 3.01, -7.65, 0.79143, 28, 15.91, -3.64, 0.20769, 3, 30, -6.74, -4.26, 0.2446, 29, 10.56, -6.44, 0.73703, 28, 20.69, 2.31, 0.01836, 3, 31, -10.84, -3.98, 0.06697, 30, 0.53, -5.31, 0.51096, 29, 17.7, -4.75, 0.42206, 4, 32, -12.82, -4.89, 4e-05, 31, -2.56, -5.68, 0.3886, 30, 8.95, -5.95, 0.52175, 29, 25.78, -2.25, 0.08961, 3, 32, -5.98, -5.08, 0.16749, 31, 4.28, -5.45, 0.55449, 30, 15.71, -4.87, 0.27803, 3, 32, 0.35, -4.67, 0.50082, 31, 10.56, -4.64, 0.48751, 30, 21.84, -3.28, 0.01167, 2, 32, 6.13, -2.92, 0.83411, 31, 16.22, -2.54, 0.16589, 1, 32, 11.03, 0.28, 1, 2, 32, 7.91, 3.08, 0.91444, 31, 17.63, 3.57, 0.08556, 4, 32, 2.24, 4.6, 0.5933, 31, 11.88, 4.73, 0.39098, 30, 21.97, 6.18, 0.01572, 29, 33.43, 13.82, 1e-05, 5, 32, -3.46, 5.99, 0.25996, 31, 6.1, 5.76, 0.51617, 30, 16.11, 6.48, 0.20521, 29, 27.86, 11.94, 0.0171, 28, 21.18, 27.55, 0.00156, 5, 32, -9.5, 8.5, 0.01219, 31, -0.09, 7.89, 0.43857, 30, 9.7, 7.82, 0.3749, 29, 21.42, 10.83, 0.14441, 28, 17.15, 22.4, 0.02993, 4, 31, -5.91, 11.33, 0.13315, 30, 3.5, 10.5, 0.404, 29, 14.66, 11.05, 0.29756, 28, 11.99, 18.03, 0.16529, 4, 31, -10.6, 17.84, 0.00795, 30, -1.98, 16.37, 0.22125, 29, 7.41, 14.5, 0.33919, 28, 4.3, 15.72, 0.43161, 3, 30, -9.24, 20.35, 0.05317, 29, -0.8, 15.53, 0.2387, 28, -2.48, 10.98, 0.70813, 3, 30, -13.81, 21.01, 0.00834, 29, -5.3, 14.47, 0.08555, 28, -5.1, 7.17, 0.90611, 3, 30, -14.17, 6.18, 3e-05, 29, -0.18, 0.54, 0.115, 28, 8.04, 0.28, 0.88496, 3, 30, -7.59, 3.17, 0.00044, 29, 7.04, 0.16, 0.99824, 28, 13.65, 4.84, 0.00131, 3, 30, 0.17, 0.81, 0.57157, 29, 15.13, 0.82, 0.42749, 28, 19.21, 10.76, 0.00095, 4, 31, -5.86, 1.3, 0.00093, 30, 4.81, 0.55, 0.9927, 29, 19.53, 2.28, 0.00608, 28, 21.49, 14.8, 0.00029, 3, 31, 0.06, 0.23, 0.53081, 30, 10.81, 0.24, 0.46912, 29, 25.23, 4.19, 7e-05, 2, 31, 4.22, 0.49, 0.99946, 30, 14.91, 1.02, 0.00054, 2, 32, 0.13, -0.28, 0.64919, 31, 10.08, -0.28, 0.35081, 1, 32, 6.89, 0.12, 1, 4, 31, 1.05, 4.33, 0.62045, 30, 11.28, 4.43, 0.37061, 29, 24.12, 8.26, 0.00858, 28, 20.88, 22.31, 0.00036, 4, 31, -8.34, 7.63, 0.00904, 30, 1.55, 6.52, 0.52425, 29, 14.31, 6.63, 0.41993, 28, 14.7, 14.52, 0.04678, 3, 30, -5.6, 10.3, 0.08975, 29, 6.27, 7.52, 0.60668, 28, 8.14, 9.78, 0.30357, 3, 30, -11.92, 16.02, 0.01088, 29, -1.71, 10.52, 0.13515, 28, 0.21, 6.65, 0.85397], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 24, "height": 54}}, "hair_4": {"hair_4": {"type": "mesh", "uvs": [0.71193, 0, 0.40193, 0.01064, 0.18193, 0.15246, 0.04193, 0.33064, 0.04193, 0.49428, 0.11193, 0.66155, 0.26193, 0.79973, 0.49193, 0.91973, 0.84193, 1, 0.87193, 0.89064, 0.80193, 0.77428, 0.82193, 0.63973, 0.86193, 0.48337, 0.93193, 0.31246, 1, 0.15246, 0.65193, 0.167, 0.53193, 0.32337, 0.54193, 0.49064, 0.51193, 0.63609, 0.63193, 0.78882, 0.65078, 0.89702], "triangles": [16, 15, 13, 13, 15, 14, 2, 1, 15, 15, 0, 14, 15, 1, 0, 3, 16, 17, 17, 16, 12, 12, 16, 13, 3, 2, 16, 16, 2, 15, 6, 5, 18, 5, 4, 18, 18, 17, 11, 11, 17, 12, 18, 4, 17, 4, 3, 17, 7, 20, 8, 8, 20, 9, 20, 7, 19, 7, 6, 19, 20, 19, 9, 19, 10, 9, 6, 18, 19, 10, 19, 11, 11, 19, 18], "vertices": [2, 38, -8.89, 4.01, 0.0641, 37, -2.82, 0.37, 0.9359, 2, 38, -8.59, -0.96, 0.37222, 37, -0.72, -4.15, 0.62778, 3, 39, -12.1, -5.1, 0.02826, 38, -2.47, -4.69, 0.62864, 37, 6.34, -5.38, 0.3431, 3, 39, -4.26, -7.34, 0.28602, 38, 5.29, -7.19, 0.65493, 37, 14.48, -4.87, 0.05905, 4, 40, -9.11, -5.69, 0.00507, 39, 2.94, -7.34, 0.61423, 38, 12.48, -7.43, 0.32478, 37, 21.26, -2.46, 0.05591, 4, 40, -1.69, -6.33, 0.24395, 39, 10.3, -6.22, 0.68043, 38, 19.88, -6.56, 0.06837, 37, 27.82, 1.06, 0.00726, 3, 40, 4.78, -5.43, 0.5768, 39, 16.38, -3.82, 0.42314, 37, 32.75, 5.35, 5e-05, 2, 40, 10.78, -3.09, 0.90507, 39, 21.66, -0.14, 0.09493, 2, 40, 15.53, 1.52, 0.99952, 39, 25.19, 5.46, 0.00048, 1, 40, 10.97, 3.12, 1, 2, 40, 5.73, 3.23, 0.9328, 39, 15.26, 4.82, 0.0672, 4, 40, 0.05, 4.94, 0.62114, 39, 9.34, 5.14, 0.30441, 38, 19.3, 4.82, 0.07436, 37, 23.12, 11.44, 9e-05, 4, 40, -6.49, 7.18, 0.28781, 39, 2.46, 5.78, 0.32, 38, 12.44, 5.7, 0.30457, 37, 16.42, 9.74, 0.08762, 4, 40, -13.53, 10.04, 0.02168, 39, -5.06, 6.9, 0.2528, 38, 4.97, 7.07, 0.32659, 37, 8.96, 8.28, 0.39894, 3, 39, -12.1, 7.99, 0.01559, 38, -2.03, 8.39, 0.25223, 37, 1.96, 6.96, 0.73218, 2, 38, -1.58, 2.81, 0.00535, 37, 4.43, 1.92, 0.99465, 2, 39, -4.58, 0.5, 0.00019, 38, 5.23, 0.66, 0.99981, 3, 40, -7.38, 2.13, 0.00526, 39, 2.78, 0.66, 0.98323, 38, 12.59, 0.57, 0.01152, 2, 40, -1.27, 0.16, 0.08972, 39, 9.18, 0.18, 0.91028, 1, 40, 5.71, 0.44, 1, 1, 40, 10.41, -0.39, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 16, "height": 44}}, "hair_5": {"hair_5": {"type": "mesh", "uvs": [0.57656, 0, 0.34417, 0.03729, 0.21067, 0.10493, 0.12167, 0.20995, 0.07717, 0.33989, 0.0475, 0.45381, 0.03761, 0.57663, 0.07717, 0.68496, 0.10683, 0.83982, 0.09695, 0.94484, 0.12167, 1, 0.29967, 0.96798, 0.51722, 0.84338, 0.67544, 0.7259, 0.81883, 0.6191, 0.91772, 0.49094, 0.97211, 0.35744, 0.98694, 0.22038, 0.94244, 0.11536, 0.82378, 0, 0.51723, 0.0584, 0.54689, 0.16876, 0.55184, 0.30226, 0.55184, 0.4233, 0.47767, 0.58528, 0.41339, 0.66538, 0.36395, 0.76328, 0.28484, 0.83982], "triangles": [4, 3, 22, 16, 22, 17, 3, 21, 22, 3, 2, 21, 2, 20, 21, 22, 21, 17, 21, 18, 17, 2, 1, 20, 19, 18, 20, 20, 0, 19, 18, 21, 20, 20, 1, 0, 24, 5, 23, 24, 23, 15, 23, 4, 22, 4, 23, 5, 15, 23, 16, 23, 22, 16, 12, 26, 13, 26, 8, 7, 26, 25, 13, 26, 7, 25, 13, 25, 14, 14, 25, 24, 7, 6, 25, 25, 6, 24, 14, 24, 15, 6, 5, 24, 10, 9, 11, 27, 9, 8, 9, 27, 11, 11, 27, 12, 27, 26, 12, 27, 8, 26], "vertices": [1, 33, -2.53, 1.57, 1, 2, 34, -13.06, -3.74, 0.00033, 33, -0.78, -2.67, 0.99967, 2, 34, -9.55, -5.96, 0.04371, 33, 2.54, -5.16, 0.95629, 3, 35, -16.67, -10.13, 1e-05, 34, -4.22, -7.26, 0.29461, 33, 7.75, -6.9, 0.70539, 3, 35, -10.15, -9.49, 0.02006, 34, 2.31, -7.7, 0.60444, 33, 14.22, -7.87, 0.3755, 3, 35, -4.48, -8.77, 0.18757, 34, 8.03, -7.92, 0.72688, 33, 19.9, -8.55, 0.08555, 4, 36, -7.51, -8.12, 0.02266, 35, 1.56, -7.61, 0.47464, 34, 14.17, -7.76, 0.49959, 33, 26.03, -8.89, 0.00311, 3, 36, -2.51, -5.91, 0.34914, 35, 6.69, -5.73, 0.46144, 34, 19.54, -6.75, 0.18942, 3, 36, 4.77, -3.21, 0.68248, 35, 14.13, -3.52, 0.29393, 34, 27.24, -5.78, 0.0236, 2, 36, 9.86, -1.9, 0.99315, 35, 19.29, -2.55, 0.00685, 1, 36, 12.38, -0.7, 1, 2, 36, 9.94, 1.93, 0.96873, 35, 19.62, 1.27, 0.03127, 3, 36, 2.85, 3.93, 0.64621, 35, 12.69, 3.73, 0.35198, 34, 27.01, 1.6, 0.00182, 3, 36, -3.58, 5, 0.31287, 35, 6.33, 5.23, 0.55237, 34, 20.99, 4.12, 0.13475, 4, 36, -9.44, 5.97, 0.01081, 35, 0.56, 6.58, 0.53453, 34, 15.51, 6.4, 0.45348, 33, 28.53, 5.11, 0.00118, 3, 35, -6.08, 6.92, 0.21382, 34, 9.01, 7.82, 0.7048, 33, 22.17, 7.06, 0.08138, 3, 35, -12.81, 6.42, 0.01343, 34, 2.29, 8.43, 0.62648, 33, 15.52, 8.21, 0.36009, 2, 34, -4.56, 8.31, 0.31069, 33, 8.68, 8.66, 0.68931, 2, 34, -9.76, 7.22, 0.05757, 33, 3.41, 8, 0.94243, 2, 34, -15.4, 4.77, 0.00295, 33, -2.42, 6.01, 0.99705, 1, 33, 0.36, 0.42, 1, 1, 33, 5.89, 0.81, 1, 2, 34, -0.04, 0.72, 0.50944, 33, 12.56, 0.72, 0.49056, 1, 34, 6, 1.06, 1, 2, 35, 0.25, 0.22, 0.80946, 34, 14.16, 0.17, 0.19054, 2, 35, 4.41, -0.04, 0.99988, 34, 18.23, -0.76, 0.00012, 2, 36, -0.21, 0.15, 0.2856, 35, 9.38, 0.16, 0.7144, 2, 36, 3.86, -0.14, 0.9992, 35, 13.43, -0.39, 0.0008], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 18, "height": 50}}, "hand_L": {"hand_L": {"x": 7.44, "y": -0.43, "rotation": 79.48, "width": 23, "height": 30}}, "hand_L2": {"hand_L2": {"x": 11.68, "y": 2.2, "rotation": 89.27, "width": 26, "height": 33}}, "hand_L3": {"hand_L3": {"x": 24.73, "y": 0.26, "rotation": 81.01, "width": 30, "height": 53}}, "hand_R": {"hand_R": {"x": 8.04, "y": -0.07, "rotation": 96.84, "width": 26, "height": 33}}, "hand_R2": {"hand_R2": {"x": 15.02, "y": 2.23, "rotation": 75, "width": 33, "height": 37}}, "hand_R3": {"hand_R3": {"x": 20.93, "y": 0.1, "rotation": 111.16, "width": 35, "height": 49}}, "head": {"head": {"x": 23.03, "y": 7.74, "rotation": -51.54, "width": 56, "height": 65}}, "leg_L": {"leg_L": {"x": 15.59, "y": 1.37, "rotation": 61.47, "width": 33, "height": 31}}, "leg_L2": {"leg_L2": {"x": 8.91, "y": 1.05, "rotation": 93.66, "width": 24, "height": 33}}, "leg_L3": {"leg_L3": {"x": 8.93, "y": 0.89, "rotation": 33.84, "width": 36, "height": 23}}, "leg_R": {"leg_R": {"x": 17.36, "y": -2.43, "rotation": 86.82, "width": 30, "height": 25}}, "leg_R2": {"leg_R2": {"type": "mesh", "uvs": [0.60604, 0, 0.36404, 0.06991, 0.13172, 0.14952, 0.03976, 0.30236, 0.01556, 0.47749, 0.01072, 0.6176, 0.01072, 0.76725, 0.10752, 0.90736, 0.24304, 0.9997, 0.44148, 1, 0.6254, 1, 0.72704, 0.8787, 0.8432, 0.70357, 0.96904, 0.49341, 1, 0.25141, 0.89644, 0.09857, 0.59955, 0.14613, 0.63441, 0.35622, 0.54333, 0.54427, 0.43769, 0.72599, 0.31249, 0.86316], "triangles": [9, 20, 11, 10, 9, 11, 16, 1, 0, 16, 0, 15, 14, 17, 16, 14, 16, 15, 2, 1, 16, 2, 17, 3, 17, 2, 16, 18, 3, 17, 13, 17, 14, 18, 17, 13, 4, 3, 18, 5, 4, 18, 12, 18, 13, 19, 5, 18, 19, 18, 12, 6, 5, 19, 20, 6, 19, 11, 19, 12, 20, 19, 11, 7, 6, 20, 8, 7, 20, 8, 20, 9], "vertices": [2, 14, -11.6, -7.39, 0.07467, 13, 9.99, 0.1, 0.92533, 2, 14, -6.51, -11.61, 0.01199, 13, 12.32, -6.09, 0.98801, 2, 14, -1.21, -15.45, 0.08327, 13, 15.04, -12.05, 0.91673, 2, 14, 5.02, -14.9, 0.28133, 13, 20.72, -14.65, 0.71867, 2, 14, 11.24, -12.46, 0.55507, 13, 27.33, -15.6, 0.44493, 2, 14, 16.05, -10.18, 0.80912, 13, 32.64, -16, 0.19088, 2, 14, 21.13, -7.63, 0.94431, 13, 38.32, -16.31, 0.05569, 3, 14, 24.81, -3.08, 0.91007, 13, 43.76, -14.17, 0.00809, 15, -3.48, -1.74, 0.08184, 3, 14, 26.43, 1.52, 0.58474, 13, 47.45, -10.97, 9e-05, 15, 1.2, -3.13, 0.41517, 2, 14, 24.21, 5.96, 0.27133, 15, 5.52, -0.71, 0.72867, 2, 14, 22.15, 10.07, 0.15845, 15, 9.53, 1.54, 0.84155, 2, 14, 16.89, 10.28, 0.44872, 15, 9.49, 6.81, 0.55128, 2, 14, 9.64, 9.89, 0.76063, 15, 8.75, 14.03, 0.23937, 3, 14, 1.09, 9.12, 0.8507, 13, 29.19, 8.17, 0.10465, 15, 7.58, 22.53, 0.04465, 3, 14, -7.47, 5.69, 0.6351, 13, 20.05, 9.43, 0.36331, 15, 3.75, 30.93, 0.00159, 2, 14, -11.51, 0.78, 0.30336, 13, 14.11, 7.15, 0.69664, 1, 13, 15.53, -0.36, 1, 2, 14, 0.18, -0.69, 0.09825, 13, 23.54, 0.09, 0.90175, 2, 14, 7.59, 0.48, 0.99912, 15, -0.75, 15.63, 0.00088, 2, 14, 14.95, 1.21, 0.96539, 15, 0.34, 8.32, 0.03461, 2, 14, 21.01, 0.75, 0.77598, 15, 0.16, 2.24, 0.22402], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 25, "height": 38}}, "leg_R3": {"leg_R3": {"x": 7.67, "y": 0.52, "rotation": 29.54, "width": 35, "height": 24}}, "weapons": {"weapons": {"x": 17.68, "y": -1.56, "rotation": 14.12, "width": 76, "height": 35}}, "yinying": {"yinying": {"x": -3.25, "y": -0.06, "rotation": -92.2, "width": 101, "height": 48}}}}], "animations": {"attack": {"bones": {"hair_10": {"rotate": [{"angle": -7.52, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 3.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -23.71, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -50.9, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -7.72, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": -20.84, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.52}]}, "hand_R3": {"rotate": [{"angle": -2.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": 55.83, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": -130.94, "curve": "stepped"}, {"time": 0.4667, "angle": -130.94, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "angle": 73.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "angle": -30.26, "curve": "stepped"}, {"time": 0.8333, "angle": -30.26, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -2.63}], "translate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "x": 1.96, "y": 4.31, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "x": -11.71, "y": 7.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333, "x": -11.99, "y": -3.16, "curve": "stepped"}, {"time": 0.4667, "x": -11.99, "y": -3.16, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5, "x": 1.41, "y": 7.16, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.5667, "x": -1.89, "y": -6.12, "curve": "stepped"}, {"time": 0.8333, "x": -1.89, "y": -6.12, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "hair_19": {"rotate": [{"angle": -19.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -8.66, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -35.87, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -76.02, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -10.17, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -24.19, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -19.68}]}, "body_5": {"rotate": [{"curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "angle": -10.12, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7.41, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 9.98, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "angle": 9.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "angle": -4.65, "curve": "stepped"}, {"time": 0.8333, "angle": -4.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9667, "angle": -4.13, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "translate": [{"x": -0.95, "y": 0.05}]}, "leg_R3": {"rotate": [{"angle": 0.16}]}, "hair_3": {"rotate": [{"angle": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -9.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.1, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -48.92, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.33}]}, "hair_4": {"rotate": [{"angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -7.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -20.75, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -43.91, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -12.03, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -9.79, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.56}]}, "hair_9": {"rotate": [{"angle": -5.7, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 5.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -21.89, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -49.08, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -19.02, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -5.7}]}, "leg_L2": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 16.76, "curve": "stepped"}, {"time": 0.9333, "x": 16.76, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "x": 8.38, "y": 4.64, "curve": 0.25, "c3": 0.75}, {"time": 1.2}]}, "hair_13": {"rotate": [{"angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 22.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -4.22, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 50.32, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -13.86, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 13.92, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 11.97}]}, "hair4": {"rotate": [{"angle": -1.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 9.35, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 36.69, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -27.49, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.66}]}, "head": {"rotate": [{"angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.29, "curve": "stepped"}, {"time": 0.4667, "angle": 4.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.79}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 6.27, "y": 4.36, "curve": "stepped"}, {"time": 0.4667, "x": 6.27, "y": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "hair2": {"rotate": [{"angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 1.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -14.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 26.07, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -16.55, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -5.78, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3.05}]}, "hand_L3": {"rotate": [{"angle": 5.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -11.8, "curve": "stepped"}, {"time": 0.4667, "angle": -11.8, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 19.12, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 17.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 0.82, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 5.27}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -8.38, "y": 6.54, "curve": "stepped"}, {"time": 0.4667, "x": -8.38, "y": 6.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5667}]}, "yinying": {"translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 8.64, "curve": "stepped"}, {"time": 0.9333, "x": 8.64, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}], "scale": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "y": 1.189, "curve": "stepped"}, {"time": 0.9333, "y": 1.189, "curve": 0.25, "c3": 0.75}, {"time": 1.1667}]}, "body": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.96, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 12.11, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -2.73, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.78, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 2.71, "curve": 0.25, "c3": 0.75}, {"time": 1.3333}], "translate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "x": -1.85, "y": -4.39, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "x": -5.02, "y": -2.29, "curve": "stepped"}, {"time": 0.4667, "x": -5.02, "y": -2.29, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 18.7, "y": -10.84, "curve": "stepped"}, {"time": 0.8333, "x": 18.7, "y": -10.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "leg_L3": {"rotate": [{"angle": 0.27}]}, "hair_18": {"rotate": [{"angle": -6.13, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 4.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -22.32, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -62.48, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 3.38, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -10.64, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -6.13}]}, "hand_R": {"rotate": [{"angle": -1.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 7.71, "curve": "stepped"}, {"time": 0.4667, "angle": 7.71, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -29.25, "curve": "stepped"}, {"time": 0.8333, "angle": -29.25, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -1.29}], "translate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -0.95, "y": -1.98, "curve": "stepped"}, {"time": 0.8333, "x": -0.95, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "hair_14": {"rotate": [{"angle": 14.59, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 25.6, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 52.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -11.24, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "angle": 16.54, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 14.59}]}, "hand_L": {"rotate": [{"angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 30.94, "curve": "stepped"}, {"time": 0.4667, "angle": 30.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 28.27, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 26.55, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 2.41}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -2.44, "y": 3.84, "curve": "stepped"}, {"time": 0.4667, "x": -2.44, "y": 3.84, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "x": -1.22, "y": 4.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "x": -1.31, "y": 5.19, "curve": "stepped"}, {"time": 0.8333, "x": -1.31, "y": 5.19, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "hair_17": {"rotate": [{"angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 2.87, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.68, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -67.49, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.91}]}, "weapons": {"rotate": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.47, "curve": "stepped"}, {"time": 0.8333, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}, "hair3": {"rotate": [{"angle": -4.42, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 6.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -20.61, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 33.93, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -30.25, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -4.42}]}, "hair_7": {"rotate": [{"angle": -0.16, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -11.93, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -43.39, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 9.95, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -8.07, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.16}]}, "hair_12": {"rotate": [{"angle": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 5.65, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -10.9, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 29.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -12.63, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.88}]}, "hair_8": {"rotate": [{"angle": -1.72, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 3.06, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -13.49, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -44.95, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 10.4, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -19.72, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -1.72}]}, "hair_5": {"rotate": [{"angle": -7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -10.96, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "angle": -23.99, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -47.15, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -15.27, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": -13.03, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -7.8}]}, "hair_11": {"rotate": [{"angle": -0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 4.46, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -12.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 28.8, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -13.82, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -0.32}]}, "body_4": {"rotate": [{"angle": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": 10.81, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.5333, "angle": 9.57, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.5667, "angle": -4.33, "curve": "stepped"}, {"time": 0.8333, "angle": -4.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.9333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.31}], "translate": [{"x": -0.35, "y": -0.03}]}, "body_3": {"rotate": [{"angle": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.16, "curve": "stepped"}, {"time": 0.4667, "angle": 8.16, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -3.9, "curve": "stepped"}, {"time": 0.8333, "angle": -3.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.9, "angle": -8.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.1, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 0.75}], "translate": [{"y": -0.06}]}, "body_2": {"rotate": [{"angle": -0.33}]}, "leg_L": {"rotate": [{"angle": -0.28}]}}, "deform": {"default": {"body_2": {"body_2": [{"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "offset": 26, "vertices": [0.55562, -6.32135, -6.13955, -1.68357, -0.65542, -11.83507, -11.77563, -1.54002, -11.19826, 3.80833, -4.86871, -4.45234, -5.22998, 3.91521, -2.99708, 5.95698, -0.64609, -1.93502, -2.02123, 0.27614, -1.68838, 1.14504, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21646, -2.79534, -2.78702, -0.30546, -2.63281, 0.9639, -0.55975, -1.33293, -1.41359, 0.30295, -1.13204, 0.89918, 0.37849, -0.58994, -0.51403, -0.4765, -0.66815, -0.21183, 0, 0, 0, 0, -0.35765, -6.02996, -5.9953, -0.76649, -5.70533, 1.97252, -0.06462, -10.51751, -10.37202, -1.85455, -10.09921, 2.86158, -2.43041, -3.67993, -4.05834, 1.71076, -2.89658, 3.33396, -0.29191, -11.59358, -11.46476, -1.85605, -11.06842, 3.39959], "curve": "stepped"}, {"time": 0.8333, "offset": 26, "vertices": [0.55562, -6.32135, -6.13955, -1.68357, -0.65542, -11.83507, -11.77563, -1.54002, -11.19826, 3.80833, -4.86871, -4.45234, -5.22998, 3.91521, -2.99708, 5.95698, -0.64609, -1.93502, -2.02123, 0.27614, -1.68838, 1.14504, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21646, -2.79534, -2.78702, -0.30546, -2.63281, 0.9639, -0.55975, -1.33293, -1.41359, 0.30295, -1.13204, 0.89918, 0.37849, -0.58994, -0.51403, -0.4765, -0.66815, -0.21183, 0, 0, 0, 0, -0.35765, -6.02996, -5.9953, -0.76649, -5.70533, 1.97252, -0.06462, -10.51751, -10.37202, -1.85455, -10.09921, 2.86158, -2.43041, -3.67993, -4.05834, 1.71076, -2.89658, 3.33396, -0.29191, -11.59358, -11.46476, -1.85605, -11.06842, 3.39959], "curve": 0.25, "c3": 0.75}, {"time": 1.1}]}}}}, "die": {"slots": {"eye_close_L": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff"}]}, "eye_close_R": {"color": [{"color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff"}]}, "weapons": {"attachment": [{"time": 0.0667, "name": null}]}}, "bones": {"head": {"rotate": [{"angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -1.52, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 0.79}]}, "leg_L5": {"rotate": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 66.53, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 115.6}]}, "leg_L3": {"rotate": [{"angle": 0.27}]}, "hair_4": {"rotate": [{"angle": -4.56, "curve": "stepped"}, {"time": 0.0667, "angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 14.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -27.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -8.65, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -12.5}]}, "hand_L": {"rotate": [{"angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 56.3, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 36.7, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 52.85, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -21.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -5.57}]}, "hand_R": {"rotate": [{"angle": -1.29, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 40.25, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 1.22, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 14.24, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -34.08, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -18.63}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -2.43, "y": 3.06, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "x": -0.97, "y": -2.44}]}, "body_2": {"rotate": [{"angle": -0.33}]}, "leg_R5": {"rotate": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 35.98, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 102.86}]}, "hand_L3": {"rotate": [{"angle": 5.27, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -2, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": -10.61, "curve": "stepped"}, {"time": 0.3333, "angle": -10.61, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -32.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -30.34}]}, "leg_L2": {"translate": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "x": -44.85, "y": 79.17, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "x": -69.38, "y": 43.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -63.6, "y": 7.09, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -63.6, "y": 11.54, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -63.6, "y": 7.09}]}, "hand_R3": {"rotate": [{"angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 9.84, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 8.75, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 21.86, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -5.87, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -1.51}], "translate": [{"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": 4.74, "y": -9.48}]}, "body_3": {"rotate": [{"angle": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -10.23, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 19.61, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 9.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 4.75}], "translate": [{"y": -0.06}]}, "body_4": {"rotate": [{"angle": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -9.42, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 19.18, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 4.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 12.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.34}], "translate": [{"x": -0.35, "y": -0.03}]}, "body_5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -5.08, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 18.87, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "angle": 4, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 17.28, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 12.72}], "translate": [{"x": -0.95, "y": 0.05}]}, "body": {"rotate": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "angle": 86.35, "curve": "stepped"}, {"time": 0.3333, "angle": 86.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 88.87}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -13.76, "y": -5.14, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "x": -72.57, "y": 44.43, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "x": -97.09, "y": -27.51, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -97.09, "y": -25.72, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -97.09, "y": -35.13}]}, "leg_R": {"translate": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "x": -4.82, "y": 64.07, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "x": -29.75, "y": 25.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -26.28, "y": -6.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -26.28, "y": -1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -26.28, "y": -6.15}]}, "hair_11": {"rotate": [{"angle": -0.32, "curve": "stepped"}, {"time": 0.0667, "angle": -0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 18.63, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -15.01, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.54, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -0.32}]}, "hair_17": {"rotate": [{"angle": -1.91, "curve": "stepped"}, {"time": 0.0667, "angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 17.04, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.61, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.94, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -1.91}]}, "hair_18": {"rotate": [{"angle": -6.13, "curve": "stepped"}, {"time": 0.0667, "angle": -6.13, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 12.82, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -20.83, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.28, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -6.13}]}, "hair_19": {"rotate": [{"angle": -19.68, "curve": "stepped"}, {"time": 0.1667, "angle": -19.68, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": -0.73, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -34.37, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -15.82, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -19.68}]}, "hair_10": {"rotate": [{"angle": -7.52, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 23.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 26.39, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -17.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -11.38, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -15.23}]}, "hair_8": {"rotate": [{"angle": -1.72, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 15.54, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 24.94, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -20.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -5.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -9.43}]}, "hair_14": {"rotate": [{"angle": 14.59, "curve": "stepped"}, {"time": 0.0667, "angle": 14.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 33.54, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -13.71, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 4.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.99}]}, "hair_12": {"rotate": [{"angle": 0.88, "curve": "stepped"}, {"time": 0.0667, "angle": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 19.83, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -27.42, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -8.87, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -12.72}]}, "leg_R3": {"rotate": [{"angle": 0.16}]}, "hair_13": {"rotate": [{"angle": 11.97, "curve": "stepped"}, {"time": 0.1667, "angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 30.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -16.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 2.22, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -1.63}]}, "hair4": {"rotate": [{"angle": -1.66, "curve": "stepped"}, {"time": 0.1667, "angle": -1.66, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 17.29, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -16.36, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 2.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -1.66}]}, "hair_9": {"rotate": [{"angle": -5.7, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 25.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 28.2, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -15.7, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -9.56, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -13.41}]}, "hair2": {"rotate": [{"angle": -3.05, "curve": "stepped"}, {"time": 0.0667, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 15.9, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -17.75, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.81, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -3.05}]}, "hair_5": {"rotate": [{"angle": -7.8, "curve": "stepped"}, {"time": 0.1667, "angle": -7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "angle": 11.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -30.44, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -11.89, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -15.74}]}, "hair3": {"rotate": [{"angle": -4.42, "curve": "stepped"}, {"time": 0.0667, "angle": -4.42, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 14.53, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -19.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -0.57, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -4.42}]}, "hair_7": {"rotate": [{"angle": -0.16, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": 17.1, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 26.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -18.74, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.02, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -7.87}]}, "yinying": {"translate": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "x": -67.98, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "x": -98.19}], "scale": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.2333, "x": 0.765, "y": 0.765, "curve": 0.395, "c3": 0.888, "c4": 0.35}, {"time": 0.3333, "x": 1.415, "y": 1.415}]}, "hair_3": {"rotate": [{"angle": -0.33, "curve": "stepped"}, {"time": 0.0667, "angle": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 18.62, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -22.98, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.42, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -8.28}]}, "bone": {"translate": [{"time": 0.0667, "curve": 0.004, "c2": 0.5, "c3": 0.638}, {"time": 0.3333, "x": 49}]}, "leg_L": {"rotate": [{"angle": -0.28}]}}}, "idle": {"slots": {"eye_close_L": {"color": [{"time": 0.1333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}, "eye_close_R": {"color": [{"time": 0.1333, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff00"}]}}, "bones": {"yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.071, "y": 1.071, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "hair_19": {"rotate": [{"angle": -19.68, "curve": 0.313, "c2": 0.18, "c3": 0.648, "c4": 0.52}, {"time": 0.0667, "angle": -18.55, "curve": 0.283, "c2": 0.17, "c3": 0.659, "c4": 0.64}, {"time": 0.5, "angle": -4.12, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.8, "angle": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "angle": -19.85, "curve": 0.321, "c3": 0.655, "c4": 0.34}, {"time": 1.6667, "angle": -19.68}]}, "hair_18": {"rotate": [{"angle": -6.13, "curve": 0.321, "c2": 0.28, "c3": 0.655, "c4": 0.62}, {"time": 0.0667, "angle": -5.26, "curve": 0.313, "c2": 0.27, "c3": 0.687, "c4": 0.73}, {"time": 0.5, "angle": 1.4, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.7, "angle": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "angle": -7.05, "curve": 0.292, "c3": 0.63, "c4": 0.37}, {"time": 1.6667, "angle": -6.13}]}, "hair_17": {"rotate": [{"angle": -1.91, "curve": 0.326, "c2": 0.31, "c3": 0.66, "c4": 0.65}, {"time": 0.0667, "angle": -1.25, "curve": 0.341, "c2": 0.36, "c3": 0.717, "c4": 0.83}, {"time": 0.5, "angle": 2.8, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.6, "angle": 3.18, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "angle": -3.35, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 1.6667, "angle": -1.91}]}, "hair_14": {"rotate": [{"angle": 14.59, "curve": 0.283, "c2": 0.17, "c3": 0.659, "c4": 0.64}, {"time": 0.4333, "angle": 0.73, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.7333, "angle": -6.28, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 15.84, "curve": 0.3, "c3": 0.636, "c4": 0.36}, {"time": 1.6667, "angle": 14.59}]}, "hair_13": {"rotate": [{"angle": 11.97, "curve": 0.313, "c2": 0.27, "c3": 0.687, "c4": 0.73}, {"time": 0.4333, "angle": -2.44, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6333, "angle": -6.28, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 15.84, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "angle": 11.97}]}, "hair_12": {"rotate": [{"angle": 0.88, "curve": 0.341, "c2": 0.36, "c3": 0.717, "c4": 0.83}, {"time": 0.4333, "angle": -5.66, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.5333, "angle": -6.28, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 4.26, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": 0.88}]}, "hair_11": {"rotate": [{"angle": -0.32, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "angle": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 1.18, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": -0.32}]}, "hair_10": {"rotate": [{"angle": -7.52, "curve": 0.324, "c2": 0.27, "c3": 0.658, "c4": 0.61}, {"time": 0.0333, "angle": -7.01, "curve": 0.283, "c2": 0.17, "c3": 0.659, "c4": 0.64}, {"time": 0.4667, "angle": 4.04, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.7667, "angle": 9.63, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": -8, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 1.6667, "angle": -7.52}]}, "hair_9": {"rotate": [{"angle": -5.7, "curve": 0.328, "c2": 0.31, "c3": 0.661, "c4": 0.65}, {"time": 0.0333, "angle": -4.92, "curve": 0.313, "c2": 0.27, "c3": 0.687, "c4": 0.73}, {"time": 0.4667, "angle": 6.57, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6667, "angle": 9.63, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -8, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "angle": -5.7}]}, "hair_8": {"rotate": [{"angle": -1.72, "curve": 0.33, "c2": 0.32, "c3": 0.664, "c4": 0.66}, {"time": 0.0333, "angle": -1.23, "curve": 0.341, "c2": 0.36, "c3": 0.717, "c4": 0.83}, {"time": 0.4667, "angle": 4.71, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.5667, "angle": 5.27, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": -4.3, "curve": 0.266, "c3": 0.618, "c4": 0.42}, {"time": 1.6667, "angle": -1.72}]}, "hair_7": {"rotate": [{"angle": -0.16, "curve": 0.333, "c2": 0.33, "c3": 0.666, "c4": 0.66}, {"time": 0.0333, "angle": 0.07, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4667, "angle": 2.36, "curve": 0.25, "c3": 0.75}, {"time": 1.3, "angle": -1.99, "curve": 0.254, "c3": 0.62, "c4": 0.47}, {"time": 1.6667, "angle": -0.16}]}, "hair4": {"rotate": [{"angle": -1.66, "curve": 0.313, "c2": 0.27, "c3": 0.687, "c4": 0.73}, {"time": 0.4333, "angle": -13.98, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6333, "angle": -17.26, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": 1.64, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "angle": -1.66}]}, "hair3": {"rotate": [{"angle": -4.42, "curve": 0.341, "c2": 0.36, "c3": 0.717, "c4": 0.83}, {"time": 0.4333, "angle": -16.15, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.5333, "angle": -17.26, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": 1.64, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": -4.42}]}, "hair2": {"rotate": [{"angle": -3.05, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "angle": -8.26, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 1.64, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": -3.05}]}, "hair_5": {"rotate": [{"angle": -7.8, "curve": 0.313, "c2": 0.27, "c3": 0.687, "c4": 0.73}, {"time": 0.4333, "angle": 6.67, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.6333, "angle": 10.53, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "angle": -11.68, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "angle": -7.8}]}, "hair_4": {"rotate": [{"angle": -4.56, "curve": 0.341, "c2": 0.36, "c3": 0.717, "c4": 0.83}, {"time": 0.4333, "angle": 9.23, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.5333, "angle": 10.53, "curve": 0.25, "c3": 0.75}, {"time": 1.3667, "angle": -11.68, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "angle": -4.56}]}, "hair_3": {"rotate": [{"angle": -0.33, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "angle": 2.97, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -3.3, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": -0.33}]}, "head": {"rotate": [{"angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.79}]}, "leg_L3": {"rotate": [{"angle": 0.27}]}, "leg_R3": {"rotate": [{"angle": 0.16}]}, "body_2": {"rotate": [{"angle": -0.33}]}, "hand_L": {"rotate": [{"angle": 2.41, "curve": 0.355, "c2": 0.41, "c3": 0.713, "c4": 0.83}, {"time": 0.3333, "angle": 5.64, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4333, "angle": 6.05, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": -0.93, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": 2.41}]}, "hand_L3": {"rotate": [{"angle": 5.27, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 6.05, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 3.93, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": 5.27}]}, "hand_R": {"rotate": [{"angle": -1.29, "curve": 0.355, "c2": 0.41, "c3": 0.713, "c4": 0.83}, {"time": 0.3333, "angle": -5.34, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.4333, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 2.91, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": -1.29}]}, "hand_R3": {"rotate": [{"angle": -2.63, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": -5.86, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 2.91, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "angle": -2.63}]}, "body_5": {"rotate": [{"curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "angle": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": -1.83, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667}], "translate": [{"x": -0.95, "y": 0.05, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "x": -3.01, "y": 0.14, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "x": -0.95, "y": 0.05}]}, "body_4": {"rotate": [{"angle": 0.31, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -2.22, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 0.31}], "translate": [{"x": -0.35, "y": -0.03, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "x": -2, "y": -0.16, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "x": -0.35, "y": -0.03}]}, "body_3": {"rotate": [{"angle": 0.75, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 0.84, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -0.85, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 0.75}], "translate": [{"y": -0.06, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "x": 0.03, "y": -1, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "y": -0.06}]}, "body": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -3.8, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "leg_L": {"rotate": [{"angle": -0.28}]}}}, "run": {"bones": {"leg_L2": {"translate": [{"x": -21.27, "y": 17.43, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 10.95, "y": 30.55, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.2333, "x": 3.08, "y": 12.86, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3, "x": -9.62, "y": -0.22, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.3333, "x": -12.9, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4, "x": -28.39, "y": 13.38, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "x": -43.34, "y": 38.96, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -21.27, "y": 17.43}]}, "leg_R": {"translate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "x": -9.18, "y": 10.78, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "x": -22.36, "y": 43.58, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 14.96, "y": 13.42, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 44.89, "y": 37.15, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.5667, "x": 27.42, "y": 14.9, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.6667}]}, "yinying": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 0.698, "y": 0.698, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 0.698, "y": 0.698, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "head": {"rotate": [{"angle": -0.45, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -3.45, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -3.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.6667, "angle": -0.45}]}, "leg_L5": {"rotate": [{"angle": -65.7, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.1667, "angle": 25.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.4667, "angle": -56.44, "curve": 0.33, "c2": 0.32, "c3": 0.665, "c4": 0.66}, {"time": 0.5, "angle": -71.68, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.6667, "angle": -65.7}]}, "leg_L3": {"rotate": [{"angle": 0.27}]}, "leg_L": {"rotate": [{"angle": -0.28}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -8, "y": 3.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "leg_R5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -75.63, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 48.21, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "leg_R3": {"rotate": [{"angle": 0.16}]}, "body_2": {"rotate": [{"angle": -0.33}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": 5.93, "y": 0.26, "curve": 0.25, "c3": 0.75}, {"time": 0.3333}]}, "hand_L4": {"rotate": [{"angle": -6.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -6.66}]}, "hand_L": {"rotate": [{"angle": -1.66, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -13.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 10.18, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -1.66}], "translate": [{"x": -1.37, "y": 1.52, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -2.74, "y": 3.04, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "x": -1.37, "y": 1.52}]}, "hand_L3": {"rotate": [{"angle": -3.53, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": -13.51, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 6.45, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -3.53}]}, "hand_R4": {"rotate": [{"angle": -3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -3.33}]}, "hand_R": {"rotate": [{"angle": 4.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 17.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.42}]}, "hand_R3": {"rotate": [{"angle": 4.42, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "angle": 17.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -8.39, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": 4.42}]}, "body_5": {"rotate": [{"angle": -0.91, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "angle": -0.91}], "translate": [{"x": -0.63, "y": 0.02, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1, "x": -1.72, "y": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "x": -1.72, "y": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.6667, "x": -0.63, "y": 0.02}]}, "body_4": {"rotate": [{"angle": -1.57, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "angle": -1.57}], "translate": [{"x": -1.3, "y": -0.1, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": -2.06, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -2.06, "y": -0.15, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.6667, "x": -1.3, "y": -0.1}]}, "body_3": {"rotate": [{"angle": -2.16, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -2.48, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "angle": -2.16}], "translate": [{"y": -1.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "y": -1.38, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "y": -1.38, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 0.6667, "y": -1.2}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "y": 16.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 16.29, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "weapons": {"rotate": [{"angle": -3.33, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -6.66, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.6667, "angle": -3.33}]}}}, "skill": {"bones": {"hair_10": {"rotate": [{"angle": -7.52, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.53}]}, "hand_R3": {"rotate": [{"angle": -2.63, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "angle": 55.83, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "angle": -128.88}], "translate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "x": 1.96, "y": 4.31, "curve": 0.333, "c2": 0.33, "c3": 0.68, "c4": 0.71}, {"time": 0.2333, "x": -11.71, "y": 7.66, "curve": 0.382, "c2": 0.58, "c3": 0.731}, {"time": 0.3333, "x": -11.99, "y": -3.16}]}, "hair_19": {"rotate": [{"angle": -19.68, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -12.48}]}, "body_5": {"rotate": [{"curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2, "angle": -10.12, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "angle": 7}], "translate": [{"x": -0.95, "y": 0.05}]}, "leg_R3": {"rotate": [{"angle": 0.16}]}, "hair_3": {"rotate": [{"angle": -0.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -7.94}]}, "hair_4": {"rotate": [{"angle": -4.56, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -18.3}]}, "hair_9": {"rotate": [{"angle": -5.7, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.32}]}, "hair_13": {"rotate": [{"angle": 11.97, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -5.38}]}, "hair4": {"rotate": [{"angle": -1.66, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.38}]}, "head": {"rotate": [{"angle": 0.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 4.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 6.27, "y": 4.36}]}, "hair2": {"rotate": [{"angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -2.1}]}, "hand_L3": {"rotate": [{"angle": 5.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -1.01}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -7.64, "y": 7.24}]}, "body": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 11.62}], "translate": [{"curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.1333, "x": -1.85, "y": -4.39, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3333, "x": -5.02, "y": -1.05}]}, "leg_L3": {"rotate": [{"angle": 0.27}]}, "hair_18": {"rotate": [{"angle": -6.13, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.71}]}, "hand_R": {"rotate": [{"angle": -1.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.5}]}, "hair_14": {"rotate": [{"angle": 14.59, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 0.48}]}, "hand_L": {"rotate": [{"angle": 2.41, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 9.21}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": -2.68, "y": 1.71}]}, "hair_17": {"rotate": [{"angle": -1.91, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.09}]}, "hair3": {"rotate": [{"angle": -4.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 18.9}]}, "hair_7": {"rotate": [{"angle": -0.16, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 1.99}]}, "hair_12": {"rotate": [{"angle": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -8.35}]}, "hair_8": {"rotate": [{"angle": -1.72, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 3.52}]}, "hair_5": {"rotate": [{"angle": -7.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -19.18}]}, "hair_11": {"rotate": [{"angle": -0.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.09}]}, "body_4": {"rotate": [{"angle": 0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -3.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.02}], "translate": [{"x": -0.35, "y": -0.03}]}, "body_3": {"rotate": [{"angle": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -10.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 8.09}], "translate": [{"y": -0.06}]}, "body_2": {"rotate": [{"angle": -0.33}]}, "leg_L": {"rotate": [{"angle": -0.28}]}}}, "skill01": {"bones": {"hair_10": {"rotate": [{"angle": 3.53, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 9.78, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -26.2, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 3.53}]}, "hand_R3": {"rotate": [{"angle": -128.88, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -134.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -128.88}], "translate": [{"x": -11.99, "y": -3.16}]}, "hair_19": {"rotate": [{"angle": -12.48, "curve": 0.346, "c2": 0.38, "c3": 0.682, "c4": 0.72}, {"time": 0.1, "angle": -9.93, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2667, "angle": -7.46, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": -26.46, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": -12.48}]}, "body_5": {"rotate": [{"angle": 7, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 7.41, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 5.02, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 7}], "translate": [{"x": -0.95, "y": 0.05}]}, "leg_R3": {"rotate": [{"angle": 0.16}]}, "hair_3": {"rotate": [{"angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.2, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -7.94}]}, "hair_4": {"rotate": [{"angle": -18.3, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -19.5, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": 0.97, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": -18.3}]}, "hair_9": {"rotate": [{"angle": 8.32, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": 11.6, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": -24.38, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 8.32}]}, "hair_13": {"rotate": [{"angle": -5.38, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -8.96, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 30.36, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -5.38}]}, "hair4": {"rotate": [{"angle": 18.38, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 23.33, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -5.2, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 18.38}]}, "head": {"rotate": [{"angle": 4.29, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.29, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.29}], "translate": [{"x": 6.27, "y": 4.36}]}, "hair2": {"rotate": [{"angle": -2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.59, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.1}]}, "hand_L3": {"rotate": [{"angle": -1.01, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.73, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.01}], "translate": [{"x": -7.64, "y": 7.24}]}, "body": {"rotate": [{"angle": 11.62, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 7.91, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 11.62}], "translate": [{"x": -5.02, "y": -1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -5.02, "y": -3.86, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -5.02, "y": -1.05}]}, "leg_L3": {"rotate": [{"angle": 0.27}]}, "hair_18": {"rotate": [{"angle": 3.71, "curve": 0.355, "c2": 0.45, "c3": 0.691, "c4": 0.79}, {"time": 0.1, "angle": 5.57, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1667, "angle": 6.09, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": -12.91, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 3.71}]}, "hand_R": {"rotate": [{"angle": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.9, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.5}]}, "hair_14": {"rotate": [{"angle": 0.48, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -6.34, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 32.98, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 0.48}]}, "hand_L": {"rotate": [{"angle": 9.21, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.21}], "translate": [{"x": -2.68, "y": 1.71}]}, "hair_17": {"rotate": [{"angle": 2.09, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 2.46, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -3.87, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 2.09}]}, "hair3": {"rotate": [{"angle": 18.9, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": 20.57, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "angle": -7.96, "curve": 0.244, "c3": 0.704, "c4": 0.81}, {"time": 1.6667, "angle": 18.9}]}, "hair_7": {"rotate": [{"angle": 1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.41, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.99}]}, "hair_12": {"rotate": [{"angle": -8.35, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": -8.81, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 7.75, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": -8.35}]}, "hair_8": {"rotate": [{"angle": 3.52, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": 3.83, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": -7.79, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": 3.52}]}, "hair_5": {"rotate": [{"angle": -19.18, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": -22.74, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -2.27, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -19.18}]}, "hair_11": {"rotate": [{"angle": -4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 1.23, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.09}]}, "body_4": {"rotate": [{"angle": 8.02, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": 8.24, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 5.85, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 8.02}], "translate": [{"x": -0.35, "y": -0.03}]}, "body_3": {"rotate": [{"angle": 8.09, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": 8.16, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 5.77, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": 8.09}], "translate": [{"y": -0.06}]}, "body_2": {"rotate": [{"angle": -0.33}]}, "leg_L": {"rotate": [{"angle": -0.28}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.2, "y": 1.46, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}}}, "skill02": {"bones": {"hair_10": {"rotate": [{"angle": 3.53, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.0667, "angle": -23.71, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": -50.9, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -7.72, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -20.84, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.52}]}, "hand_R3": {"rotate": [{"angle": -128.88, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "angle": 73.34, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "angle": -30.26, "curve": "stepped"}, {"time": 0.3333, "angle": -30.26, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.63}], "translate": [{"x": -11.99, "y": -3.16, "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "x": 1.41, "y": 7.16, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.1, "x": -1.89, "y": -6.12, "curve": "stepped"}, {"time": 0.3333, "x": -1.89, "y": -6.12, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "hair_19": {"rotate": [{"angle": -12.48, "curve": 0.346, "c2": 0.38, "c3": 0.682, "c4": 0.72}, {"time": 0.0333, "angle": -35.87, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -76.02, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -10.17, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -24.19, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -19.68}]}, "body_5": {"rotate": [{"angle": 7, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.0667, "angle": 9.11, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -4.65, "curve": "stepped"}, {"time": 0.3333, "angle": -4.65, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.4667, "angle": -4.13, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": 0.95, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}], "translate": [{"x": -0.95, "y": 0.05}]}, "leg_R3": {"rotate": [{"angle": 0.16}]}, "hair_3": {"rotate": [{"angle": -7.94, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -48.92, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -10.24, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.33}]}, "hair_4": {"rotate": [{"angle": -18.3, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1333, "angle": -43.91, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -12.03, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -9.79, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.56}]}, "hair_9": {"rotate": [{"angle": 8.32, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.0333, "angle": -21.89, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -49.08, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -5.91, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -19.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -5.7}]}, "leg_L2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 16.76, "curve": "stepped"}, {"time": 0.4333, "x": 16.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 8.38, "y": 4.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7}]}, "hair_13": {"rotate": [{"angle": -5.38, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.0333, "angle": -4.22, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 50.32, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -13.86, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 13.92, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 11.97}]}, "hair4": {"rotate": [{"angle": 18.38, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.0333, "angle": -17.86, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 36.69, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -27.49, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 0.28, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.66}]}, "head": {"rotate": [{"angle": 4.29, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 0.79}], "translate": [{"x": 6.27, "y": 4.36, "curve": 0.25, "c3": 0.75}, {"time": 0.1}]}, "hair2": {"rotate": [{"angle": -2.1, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 26.07, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -16.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -3.05}]}, "hand_L3": {"rotate": [{"angle": -1.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 19.12, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 17.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 0.82, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.27}], "translate": [{"x": -7.64, "y": 7.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1}]}, "yinying": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 8.64, "curve": "stepped"}, {"time": 0.4333, "x": 8.64, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 1.189, "curve": "stepped"}, {"time": 0.4333, "y": 1.189, "curve": 0.25, "c3": 0.75}, {"time": 0.6667}]}, "body": {"rotate": [{"angle": 11.62, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -2.73, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -3.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6}], "translate": [{"x": -5.02, "y": -1.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": 18.7, "y": -10.84, "curve": "stepped"}, {"time": 0.3333, "x": 18.7, "y": -10.84, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "leg_L3": {"rotate": [{"angle": 0.27}]}, "hair_18": {"rotate": [{"angle": 3.71, "curve": 0.355, "c2": 0.45, "c3": 0.691, "c4": 0.79}, {"time": 0.1333, "angle": -62.48, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 3.38, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -10.64, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -6.13}]}, "hand_R": {"rotate": [{"angle": 3.5, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -29.25, "curve": "stepped"}, {"time": 0.3333, "angle": -29.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -1.29}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "x": -0.95, "y": -1.98, "curve": "stepped"}, {"time": 0.3333, "x": -0.95, "y": -1.98, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "hair_14": {"rotate": [{"angle": 0.48, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.0667, "angle": -1.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 52.94, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -11.24, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": 16.54, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 14.59}]}, "hand_L": {"rotate": [{"angle": 9.21, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 28.27, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 26.55, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -2.04, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.41}], "translate": [{"x": -2.68, "y": 1.71, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -1.22, "y": 4.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "x": -1.31, "y": 5.19, "curve": "stepped"}, {"time": 0.3333, "x": -1.31, "y": 5.19, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "hair_17": {"rotate": [{"angle": 2.09, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1, "angle": -67.49, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 7.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -6.42, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.91}]}, "weapons": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 1.47, "curve": "stepped"}, {"time": 0.3333, "angle": 1.47, "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}, "hair3": {"rotate": [{"angle": 18.9, "curve": 0.364, "c2": 0.64, "c3": 0.7}, {"time": 0.1333, "angle": 33.93, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -30.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.42}]}, "hair_7": {"rotate": [{"angle": 1.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -43.39, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": 9.95, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -8.07, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.16}]}, "hair_12": {"rotate": [{"angle": -8.35, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333, "angle": 29.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": -12.63, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -1.86, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.88}]}, "hair_8": {"rotate": [{"angle": 3.52, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1333, "angle": -44.95, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 10.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -19.72, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.72}]}, "hair_5": {"rotate": [{"angle": -19.18, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.0333, "angle": -23.99, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -47.15, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "angle": -15.27, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -13.03, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -7.8}]}, "hair_11": {"rotate": [{"angle": -4.09, "curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": 28.8, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -13.82, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -3.05, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -0.32}]}, "body_4": {"rotate": [{"angle": 8.02, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.0667, "angle": 9.57, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1, "angle": -4.33, "curve": "stepped"}, {"time": 0.3333, "angle": -4.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.4333, "angle": -10.88, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.31}], "translate": [{"x": -0.35, "y": -0.03}]}, "body_3": {"rotate": [{"angle": 8.09, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.1, "angle": -3.9, "curve": "stepped"}, {"time": 0.3333, "angle": -3.9, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.4, "angle": -8.23, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.6, "angle": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.75}], "translate": [{"y": -0.06}]}, "body_2": {"rotate": [{"angle": -0.33}]}, "leg_L": {"rotate": [{"angle": -0.28}]}}, "deform": {"default": {"body_2": {"body_2": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "offset": 26, "vertices": [0.55562, -6.32135, -6.13955, -1.68357, -0.65542, -11.83507, -11.77563, -1.54002, -11.19826, 3.80833, -4.86871, -4.45234, -5.22998, 3.91521, -2.99708, 5.95698, -0.64609, -1.93502, -2.02123, 0.27614, -1.68838, 1.14504, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21646, -2.79534, -2.78702, -0.30546, -2.63281, 0.9639, -0.55975, -1.33293, -1.41359, 0.30295, -1.13204, 0.89918, 0.37849, -0.58994, -0.51403, -0.4765, -0.66815, -0.21183, 0, 0, 0, 0, -0.35765, -6.02996, -5.9953, -0.76649, -5.70533, 1.97252, -0.06462, -10.51751, -10.37202, -1.85455, -10.09921, 2.86158, -2.43041, -3.67993, -4.05834, 1.71076, -2.89658, 3.33396, -0.29191, -11.59358, -11.46476, -1.85605, -11.06842, 3.39959], "curve": "stepped"}, {"time": 0.3333, "offset": 26, "vertices": [0.55562, -6.32135, -6.13955, -1.68357, -0.65542, -11.83507, -11.77563, -1.54002, -11.19826, 3.80833, -4.86871, -4.45234, -5.22998, 3.91521, -2.99708, 5.95698, -0.64609, -1.93502, -2.02123, 0.27614, -1.68838, 1.14504, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21646, -2.79534, -2.78702, -0.30546, -2.63281, 0.9639, -0.55975, -1.33293, -1.41359, 0.30295, -1.13204, 0.89918, 0.37849, -0.58994, -0.51403, -0.4765, -0.66815, -0.21183, 0, 0, 0, 0, -0.35765, -6.02996, -5.9953, -0.76649, -5.70533, 1.97252, -0.06462, -10.51751, -10.37202, -1.85455, -10.09921, 2.86158, -2.43041, -3.67993, -4.05834, 1.71076, -2.89658, 3.33396, -0.29191, -11.59358, -11.46476, -1.85605, -11.06842, 3.39959], "curve": 0.25, "c3": 0.75}, {"time": 0.6}]}}}}}}