{"skeleton": {"hash": "8/92eOcLRktvsDf9M+4He0Kcx+k", "spine": "3.8.99", "x": -38.94, "y": -25.58, "width": 71, "height": 130.53, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/男人类"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 28.9, "y": -1.87}, {"name": "body", "parent": "bone", "length": 9.64, "x": -2.77, "y": 33.69}, {"name": "body_2", "parent": "body", "length": 13.57, "rotation": 82.74, "x": 3.56, "y": -0.4}, {"name": "body_3", "parent": "body_2", "length": 11.11, "rotation": -5.78, "x": 13.57}, {"name": "head", "parent": "body_3", "length": 17.43, "rotation": 14.78, "x": 10.31, "y": -0.05, "transform": "noScale"}, {"name": "arm_L2", "parent": "body_3", "length": 22.92, "rotation": -153.64, "x": 12.23, "y": -9.5}, {"name": "arm_L", "parent": "arm_L2", "length": 15.99, "rotation": -16.15, "x": 22.54, "y": 0.09}, {"name": "arm_R2", "parent": "body_3", "length": 25.81, "rotation": 168.58, "x": 6.54, "y": 19.13}, {"name": "arm_R", "parent": "arm_R2", "length": 16.39, "rotation": 21.7, "x": 26.54, "y": 0.33}, {"name": "leg_R3", "parent": "bone", "length": 15.75, "rotation": -105.35, "x": -5.9, "y": 29.37}, {"name": "leg_R2", "parent": "leg_R3", "length": 9.77, "rotation": 16.13, "x": 16.25, "y": -0.23}, {"name": "leg_L3", "parent": "bone", "length": 16.76, "rotation": -87.05, "x": 7.28, "y": 29.62}, {"name": "leg_L2", "parent": "leg_L3", "length": 8.6, "rotation": 4.26, "x": 17.09, "y": 0.09}, {"name": "yinying", "parent": "bone", "rotation": 76.16, "x": -2.22, "y": 3.17}], "slots": [{"name": "yinying", "bone": "yinying", "attachment": "yinying"}, {"name": "arm_L2", "bone": "arm_L2", "attachment": "arm_L2"}, {"name": "arm_L", "bone": "arm_L", "attachment": "arm_L"}, {"name": "body_2", "bone": "body_2", "attachment": "body_2"}, {"name": "leg_L3", "bone": "leg_L3", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L2", "attachment": "leg_L"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "leg_R3", "bone": "leg_R3", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R2", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R2", "attachment": "leg_R"}, {"name": "arm_R2", "bone": "arm_R2", "attachment": "arm_R2"}, {"name": "arm_R", "bone": "arm_R", "attachment": "arm_R"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "close_eye_R", "bone": "head", "attachment": "close_eye_R"}, {"name": "close_eye_L", "bone": "head", "attachment": "close_eye_L"}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"x": 5.82, "y": -0.45, "rotation": 92.84, "width": 26, "height": 24}}, "arm_L2": {"arm_L2": {"x": 12.8, "y": 1.12, "rotation": 76.68, "width": 23, "height": 31}}, "arm_R": {"arm_R": {"x": 4.88, "y": -0.86, "rotation": 92.77, "width": 25, "height": 25}}, "arm_R2": {"arm_R2": {"x": 15.09, "y": -3.89, "rotation": 114.47, "width": 25, "height": 31}}, "body": {"body": {"type": "mesh", "uvs": [0.08636, 0.01245, 0.08636, 0.25341, 0.04358, 0.41579, 0, 0.60436, 0.0558, 0.92388, 0.19025, 1, 0.52025, 1, 0.64247, 0.88198, 0.83802, 0.81912, 1, 0.71436, 1, 0.4315, 0.94802, 0.32674, 0.97552, 0, 0.54163, 0, 0.3033, 0.3215, 0.53552, 0.34245, 0.79219, 0.35817, 0.52941, 0.1696, 0.50497, 0.64102, 0.2758, 0.70388, 0.79524, 0.54674], "triangles": [14, 17, 15, 16, 15, 17, 12, 16, 13, 13, 14, 0, 16, 17, 13, 13, 17, 14, 19, 18, 6, 15, 20, 18, 19, 14, 18, 18, 14, 15, 5, 4, 19, 7, 20, 8, 4, 3, 19, 20, 15, 16, 8, 20, 9, 9, 20, 10, 20, 11, 10, 3, 2, 19, 19, 2, 14, 14, 2, 1, 20, 16, 11, 16, 12, 11, 14, 1, 0, 7, 18, 20, 6, 18, 7, 5, 19, 6], "vertices": [2, 2, -13.06, 4.87, 0.024, 3, 3.13, 17.16, 0.976, 2, 2, -13.06, -0.19, 0.88359, 3, -1.89, 16.52, 0.11641, 2, 2, -14.6, -3.6, 0.76945, 3, -5.47, 17.61, 0.23055, 2, 2, -16.17, -7.56, 0.62902, 3, -9.6, 18.67, 0.37098, 1, 2, -14.16, -14.27, 1, 1, 2, -9.32, -15.86, 1, 1, 2, 2.56, -15.86, 1, 1, 2, 6.96, -13.39, 1, 1, 2, 14, -12.07, 1, 1, 2, 19.83, -9.87, 1, 1, 2, 19.83, -3.93, 1, 2, 2, 17.96, -1.73, 0.91326, 3, 0.5, -14.45, 0.08674, 1, 3, 7.43, -14.56, 1, 1, 3, 5.46, 0.93, 1, 2, 2, -5.25, -1.62, 0.59715, 3, -2.32, 8.59, 0.40285, 2, 2, 3.11, -2.06, 0.59044, 3, -1.7, 0.24, 0.40956, 2, 2, 12.35, -2.39, 0.60708, 3, -0.86, -8.97, 0.39292, 2, 2, 2.89, 1.57, 0.09021, 3, 1.87, 0.92, 0.90979, 2, 2, 2.01, -8.33, 0.01455, 3, -8.06, 0.54, 0.98545, 2, 2, -6.24, -9.65, 0.38973, 3, -10.41, 8.56, 0.61027, 2, 2, 12.46, -6.35, 0.63264, 3, -4.78, -9.58, 0.36736], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26, 2, 28, 28, 30, 30, 32, 32, 22], "width": 36, "height": 21}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.02155, 0.23688, 0.16918, 0.10373, 0.37471, 0.02557, 0.53102, 0, 0.78576, 0.02846, 0.96234, 0.19346, 1, 0.41636, 0.99997, 0.63346, 0.93339, 0.82451, 0.81181, 0.93162, 0.61497, 1, 0.44997, 1, 0.24734, 0.9403, 0.11129, 0.78399, 0.02734, 0.57267, 0, 0.39609, 0.51076, 0.19057, 0.57155, 0.36714, 0.60918, 0.5582, 0.60918, 0.80135], "triangles": [7, 18, 6, 17, 13, 14, 17, 18, 13, 8, 19, 18, 7, 8, 18, 9, 19, 8, 18, 19, 13, 13, 19, 12, 11, 12, 19, 10, 19, 9, 11, 19, 10, 16, 2, 3, 16, 3, 4, 17, 16, 4, 17, 4, 5, 17, 5, 6, 18, 17, 6, 1, 15, 0, 17, 14, 15, 15, 16, 17, 16, 1, 2, 16, 15, 1], "vertices": [2, 4, 4.7, 24.32, 0.59937, 3, 20.7, 23.72, 0.40063, 2, 4, 10.89, 20, 0.74475, 3, 26.43, 18.8, 0.25525, 2, 4, 15.55, 13.06, 0.87453, 3, 30.36, 11.42, 0.12547, 2, 4, 17.84, 7.49, 0.96231, 3, 32.07, 5.65, 0.03769, 2, 4, 18.97, -2.18, 0.97585, 3, 32.23, -4.08, 0.02415, 2, 4, 14.38, -10.14, 0.86537, 3, 26.85, -11.53, 0.13463, 2, 4, 6.45, -13.44, 0.60348, 3, 18.63, -14.02, 0.39652, 2, 4, -1.59, -15.3, 0.29626, 3, 10.45, -15.07, 0.70374, 2, 4, -9.23, -14.48, 0.07981, 3, 2.93, -13.47, 0.92019, 2, 4, -14.24, -10.9, 0.00837, 3, -1.69, -9.41, 0.99163, 2, 4, -18.46, -4.2, 1e-05, 3, -5.22, -2.31, 0.99999, 2, 4, -19.88, 1.91, 0.0056, 3, -6.01, 3.91, 0.9944, 2, 4, -19.4, 9.93, 0.04001, 3, -4.73, 11.83, 0.95999, 2, 4, -14.79, 16.3, 0.13953, 3, 0.51, 17.71, 0.86047, 2, 4, -7.68, 21.22, 0.29059, 3, 8.07, 21.89, 0.70941, 2, 4, -1.38, 23.75, 0.45334, 3, 14.59, 23.77, 0.54666, 2, 4, 10.61, 6.61, 0.9498, 3, 24.79, 5.5, 0.0502, 2, 4, 4.59, 2.84, 0.92456, 3, 18.43, 2.36, 0.07544, 2, 4, -2.16, -0.19, 0.00069, 3, 11.41, 0.03, 0.99931, 1, 3, 2.24, -1.14, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 38, "height": 38}}, "close_eye_L": {"close_eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [6.85, -14.15, 8.38, -0.79, 18.01, -1.16, 17.41, -13.21], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 11, "height": 9}}, "close_eye_R": {"close_eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [8.9, 2.77, 9.55, 18.14, 19.88, 18.35, 19.08, 2.53], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 8}}, "head": {"head": {"x": 23.23, "y": 6.12, "rotation": -91.74, "width": 54, "height": 54}}, "leg_L": {"leg_L": {"x": 8.34, "y": 0.27, "rotation": 82.79, "width": 21, "height": 7}}, "leg_L2": {"leg_L2": {"x": 2.75, "y": -0.04, "rotation": 82.79, "width": 21, "height": 16}}, "leg_L3": {"leg_L3": {"x": 9.3, "y": -0.2, "rotation": 87.05, "width": 21, "height": 23}}, "leg_R": {"leg_R": {"x": 9.42, "y": -0.64, "rotation": 89.23, "width": 20, "height": 7}}, "leg_R2": {"leg_R2": {"x": 3.43, "y": -0.06, "rotation": 89.23, "width": 21, "height": 17}}, "leg_R3": {"leg_R3": {"x": 11.1, "y": -0.62, "rotation": 105.35, "width": 21, "height": 18}}, "yinying": {"yinying": {"x": 0.44, "y": -0.25, "rotation": -76.68, "width": 54, "height": 54}}}}], "animations": {"idle": {"slots": {"close_eye_L": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "color": "ffffff00"}]}, "close_eye_R": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 1.6333, "color": "ffffff00"}]}}, "bones": {"body_2": {"rotate": [{"angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 0.61, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 0.61}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": -1.1, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": -1.1, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}], "scale": [{"x": 1.031, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 1.031, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": 1.031}]}, "body_3": {"rotate": [{"angle": 0.59, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": 0.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 0.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": 0.59, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.7333, "angle": 0.61, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.5667, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "angle": 0.59}], "translate": [{"x": 0.01, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 0.34, "y": 0.04, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "x": 0.01, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": 0.34, "y": 0.04, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "x": 0.01}]}, "head": {"rotate": [{"angle": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -1.96, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -1.96, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 1.3}]}, "arm_R2": {"rotate": [{"angle": -5.78, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -5.78, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -5.78}]}, "arm_R": {"rotate": [{"angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -2.47, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -2.47}]}, "arm_L2": {"rotate": [{"angle": 4.83, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.83, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 4.83}]}, "arm_L": {"rotate": [{"angle": 2.43, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 2.43, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 2.43}]}, "body": {"translate": [{"y": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "y": 1.26, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "y": 1.26}]}}, "deform": {"default": {"body": {"body": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "vertices": [0.15599, -1.24799, -1.21826, -0.31252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.33678, 0.1744, -1.54748, -0.19722, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.078, -0.39, -0.39673, 0.02806], "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "vertices": [0.15599, -1.24799, -1.21826, -0.31252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.33678, 0.1744, -1.54748, -0.19722, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.078, -0.39, -0.39673, 0.02806], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "body_2": {"body_2": [{"offset": 20, "vertices": [0.7645, 0.02317, 0.76294, -0.05397, 1.05676, 0.09088, 1.06054, -0.01605, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.05677, 0.09088, 1.06054, -0.01605, 0, 0, 0, 0, 0, 0, 0, 0, 0.76695, 0.76365, 0.82967, 0.6785, 1.68098, 0.6018, 1.73002, 0.4242, 1.9772, -0.24495], "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "offset": 20, "vertices": [0.7645, 0.02317, 0.76294, -0.05397, 1.05676, 0.09088, 1.06054, -0.01605, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.05677, 0.09088, 1.06054, -0.01605, 0, 0, 0, 0, 0, 0, 0, 0, 0.76695, 0.76365, 0.82967, 0.6785, 1.68098, 0.6018, 1.73002, 0.4242, 1.9772, -0.24495], "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "offset": 20, "vertices": [0.7645, 0.02317, 0.76294, -0.05397, 1.05676, 0.09088, 1.06054, -0.01605, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.05677, 0.09088, 1.06054, -0.01605, 0, 0, 0, 0, 0, 0, 0, 0, 0.76695, 0.76365, 0.82967, 0.6785, 1.68098, 0.6018, 1.73002, 0.4242, 1.9772, -0.24495]}]}}}}}}