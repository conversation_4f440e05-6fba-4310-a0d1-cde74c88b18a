{"skeleton": {"hash": "wYWxJgyO8+iWHOV4YP6oC2r44Y0", "spine": "3.8.99", "x": -32.66, "y": -24.43, "width": 91, "height": 128.36, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/女人类"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 26.62, "y": -2.09}, {"name": "body", "parent": "bone", "length": 10.1, "rotation": -2.95, "x": 2.05, "y": 35.25}, {"name": "body_2", "parent": "body", "length": 11.77, "rotation": 84.82, "x": 1.84, "y": 2.7}, {"name": "body_3", "parent": "body_2", "length": 11.45, "rotation": 25.58, "x": 11.77}, {"name": "head", "parent": "body_3", "length": 20.62, "rotation": -23.82, "x": 10.27, "y": -2.36}, {"name": "arm_L2", "parent": "body_3", "length": 23.38, "rotation": -175.13, "x": 11.95, "y": -8.97}, {"name": "arm_L", "parent": "arm_L2", "length": 16.82, "rotation": 10.55, "x": 24.05, "y": -0.01}, {"name": "weapons", "parent": "arm_L", "length": 22.17, "rotation": 25.51, "x": 17.37, "y": 0.39}, {"name": "arm_R2", "parent": "body_3", "length": 22.44, "rotation": 127.53, "x": 13.71, "y": 9.11}, {"name": "arm_R", "parent": "arm_R2", "length": 17.12, "rotation": 44.69, "x": 23.19, "y": 0.23}, {"name": "leg_R", "parent": "bone", "length": 15.86, "rotation": -112.72, "x": 0.14, "y": 36.71}, {"name": "leg_R3", "parent": "leg_R", "length": 16.83, "rotation": 9.41, "x": 16.71, "y": 0.22}, {"name": "leg_L", "parent": "bone", "length": 13.69, "rotation": -95.76, "x": 12.43, "y": 34.96}, {"name": "leg_L3", "parent": "leg_L", "length": 14.93, "rotation": 0.96, "x": 13.59, "y": -0.26}, {"name": "hair_3", "parent": "head", "length": 10.26, "rotation": 136.18, "x": 32.78, "y": 9.56}, {"name": "hair_5", "parent": "hair_3", "length": 16.58, "rotation": 30.64, "x": 10.41, "y": -0.5}, {"name": "hair_6", "parent": "hair_5", "length": 15.23, "rotation": 31.72, "x": 16.58}, {"name": "hair", "parent": "hair_6", "length": 10.54, "rotation": -1.8, "x": 14.42, "y": 1.07}, {"name": "hair_4", "parent": "head", "length": 13.4, "rotation": 175.06, "x": 13.1, "y": 29.25}, {"name": "hair_7", "parent": "hair_4", "length": 10.47, "rotation": 41.45, "x": 13.4}, {"name": "hair_8", "parent": "head", "length": 17.1, "rotation": -165.77, "x": 20.44, "y": -8.86}, {"name": "hair_9", "parent": "hair_8", "length": 11.37, "rotation": 4.76, "x": 17.1}, {"name": "hair_10", "parent": "head", "length": 10.25, "rotation": -153.64, "x": 26.6, "y": -10.08}, {"name": "hair_11", "parent": "hair_10", "length": 12.72, "rotation": -7.38, "x": 10.25}, {"name": "hair_13", "parent": "hair_11", "length": 9.12, "rotation": -12.6, "x": 13.73, "y": 0.48}], "slots": [{"name": "yinying", "bone": "root", "attachment": "yinying"}, {"name": "weapons", "bone": "weapons", "attachment": "weapons"}, {"name": "arm_L2", "bone": "arm_L2", "attachment": "arm_L2"}, {"name": "arm_L", "bone": "arm_L", "attachment": "arm_L"}, {"name": "body_2", "bone": "body_2", "attachment": "body_2"}, {"name": "leg_L3", "bone": "leg_L3", "attachment": "leg_L3"}, {"name": "leg_L2", "bone": "leg_L3", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L", "attachment": "leg_L"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "leg_R3", "bone": "leg_R3", "attachment": "leg_R3"}, {"name": "leg_R2", "bone": "leg_R3", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R", "attachment": "leg_R"}, {"name": "arm_R2", "bone": "arm_R2", "attachment": "arm_R2"}, {"name": "arm_R", "bone": "arm_R", "attachment": "arm_R"}, {"name": "hair_6", "bone": "hair_13", "attachment": "hair_6"}, {"name": "hair_5", "bone": "hair_8", "attachment": "hair_5"}, {"name": "hair_4", "bone": "hair_4", "attachment": "hair_4"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "close_eye_R", "bone": "head", "attachment": "close_eye_R"}, {"name": "close_eye_L", "bone": "head", "attachment": "close_eye_L"}, {"name": "hair", "bone": "hair", "attachment": "hair"}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"x": 4.62, "y": -1.73, "rotation": 57.14, "width": 25, "height": 29}}, "arm_L2": {"arm_L2": {"x": 12.48, "y": -0.07, "rotation": 67.69, "width": 24, "height": 32}}, "arm_R": {"arm_R": {"x": 7.6, "y": -1.21, "rotation": 80.33, "width": 25, "height": 26}}, "arm_R2": {"arm_R2": {"x": 13.11, "y": -2.47, "rotation": 125.02, "width": 28, "height": 31}}, "body": {"body": {"type": "mesh", "uvs": [0.19354, 0, 0.09766, 0.19011, 0, 0.40628, 0, 0.74037, 0.1319, 0.97128, 0.34421, 1, 0.52911, 0.99584, 0.5976, 0.82389, 0.8099, 0.73054, 0.99823, 0.59297, 1, 0.49471, 0.95372, 0.34732, 0.91948, 0, 0.71745, 0, 0.50514, 0.04763, 0.35105, 0.02306, 0.23393, 0.35287, 0.46811, 0.39287, 0.71065, 0.36487, 0.50435, 0.24087, 0.51271, 0.62887, 0.25254, 0.67916, 0.77436, 0.53917], "triangles": [20, 6, 5, 5, 4, 21, 5, 21, 20, 6, 20, 7, 4, 3, 21, 7, 22, 8, 7, 20, 22, 3, 2, 21, 8, 22, 9, 2, 16, 21, 21, 17, 20, 21, 16, 17, 20, 18, 22, 20, 17, 18, 9, 22, 10, 22, 11, 10, 22, 18, 11, 2, 1, 16, 17, 19, 18, 17, 16, 19, 11, 18, 12, 1, 0, 16, 18, 13, 12, 18, 19, 13, 13, 19, 14, 16, 15, 19, 16, 0, 15, 19, 15, 14], "vertices": [1, 3, 4.51, 12.06, 1, 2, 3, -0.26, 14.57, 0.24247, 2, -12.7, 3.75, 0.75753, 1, 2, -15.66, -1.38, 1, 2, 3, -13.25, 15.97, 0.03268, 2, -15.26, -9.05, 0.96732, 2, 3, -17.89, 10.91, 0.00309, 2, -10.64, -14.13, 0.99691, 1, 2, -3.61, -14.43, 1, 1, 2, 2.48, -14.02, 1, 1, 2, 4.53, -9.96, 1, 1, 2, 11.42, -7.45, 1, 2, 3, -5.23, -16.16, 0.00025, 2, 17.46, -3.97, 0.99975, 1, 2, 17.4, -1.71, 1, 2, 3, 0.15, -13.91, 0.09391, 2, 15.7, 1.6, 0.90609, 1, 3, 7.9, -11.66, 1, 1, 3, 6.96, -5.06, 1, 1, 3, 4.88, 1.72, 1, 1, 3, 4.72, 6.84, 1, 2, 3, -3.33, 9.59, 0.14432, 2, -8.01, 0.25, 0.85568, 2, 3, -3.15, 1.81, 0.05486, 2, -0.25, -0.28, 0.94514, 2, 3, -1.38, -6.02, 0.02674, 2, 7.71, 0.78, 0.97326, 2, 3, 0.48, 1.12, 0.504, 2, 0.77, 3.28, 0.496, 1, 2, 1.5, -5.62, 1, 2, 3, -10.67, 7.92, 0.07096, 2, -7.01, -7.22, 0.92904, 2, 3, -5.05, -8.67, 0.01836, 2, 10.02, -3.12, 0.98164], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30, 2, 32, 32, 34, 34, 36, 36, 22, 34, 40, 40, 14], "width": 35, "height": 25}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.00652, 0.08075, 0.20295, 0, 0.71478, 0, 0.86695, 0.20391, 0.93888, 0.30833, 1, 0.40204, 1, 0.54662, 0.95548, 0.66175, 0.92505, 0.70994, 0.92782, 0.80633, 0.90568, 0.91878, 0.80055, 0.97768, 0.60412, 1, 0.48792, 1, 0.36895, 0.96697, 0.26659, 0.89736, 0.16422, 0.7983, 0.18912, 0.72601, 0.09229, 0.59481, 0.01759, 0.43685, 0, 0.26281, 0.46025, 0.4904, 0.82545, 0.47433, 0.66498, 0.49575, 0.63455, 0.68852, 0.64008, 0.86791, 0.58752, 0.30565, 0.41875, 0.10485], "triangles": [24, 23, 22, 17, 18, 21, 24, 21, 23, 7, 22, 6, 18, 19, 21, 22, 5, 6, 22, 23, 26, 23, 21, 26, 26, 21, 27, 22, 4, 5, 22, 3, 4, 22, 26, 3, 20, 21, 19, 27, 21, 20, 27, 20, 1, 1, 20, 0, 26, 2, 3, 26, 27, 2, 27, 1, 2, 12, 25, 11, 12, 13, 25, 13, 14, 25, 10, 11, 9, 25, 14, 24, 8, 9, 24, 25, 9, 11, 16, 17, 15, 24, 14, 15, 22, 8, 24, 15, 17, 24, 24, 9, 25, 17, 21, 24, 8, 22, 7], "vertices": [2, 3, 23.93, 21.08, 0.02722, 4, 20.07, 13.77, 0.97278, 2, 3, 27.24, 15.6, 0.00578, 4, 20.69, 7.39, 0.99422, 2, 3, 29.41, 0.4, 0.00643, 4, 16.09, -7.25, 0.99357, 2, 3, 23.8, -5.01, 0.04149, 4, 8.69, -9.71, 0.95851, 2, 3, 20.9, -7.6, 0.12082, 4, 4.96, -10.8, 0.87918, 2, 3, 18.28, -9.83, 0.27581, 4, 1.64, -11.68, 0.72419, 2, 3, 13.85, -10.46, 0.48763, 4, -2.64, -10.34, 0.51237, 2, 3, 10.12, -9.65, 0.69696, 4, -5.64, -7.99, 0.30304, 2, 3, 8.52, -8.95, 0.86177, 4, -6.8, -6.67, 0.13823, 2, 3, 5.57, -9.46, 0.94764, 4, -9.67, -5.86, 0.05236, 2, 3, 2.03, -9.29, 0.99233, 4, -12.8, -4.18, 0.00767, 2, 3, -0.23, -6.43, 0.99995, 4, -13.59, -0.62, 5e-05, 2, 3, -1.75, -0.69, 0.99847, 4, -12.49, 5.21, 0.00153, 2, 3, -2.24, 2.76, 0.98585, 4, -11.44, 8.53, 0.01415, 2, 3, -1.73, 6.44, 0.94764, 4, -9.4, 11.63, 0.05236, 2, 3, -0.03, 9.78, 0.87335, 4, -6.42, 13.91, 0.12665, 2, 3, 2.58, 13.26, 0.77706, 4, -2.57, 15.92, 0.22294, 2, 3, 4.9, 12.83, 0.60531, 4, -0.65, 14.54, 0.39469, 2, 3, 8.52, 16.28, 0.40704, 4, 4.1, 16.09, 0.59296, 2, 3, 13.05, 19.19, 0.20405, 4, 9.44, 16.76, 0.79595, 2, 3, 18.31, 20.48, 0.08592, 4, 14.75, 15.65, 0.91408, 2, 3, 13.28, 5.81, 0.22009, 4, 3.88, 4.59, 0.77991, 2, 3, 15.32, -4.96, 0.27264, 4, 1.07, -6.01, 0.72736, 2, 3, 13.99, -0.29, 0.00317, 4, 1.88, -1.22, 0.99683, 1, 3, 7.94, -0.23, 1, 1, 3, 2.46, -1.18, 1, 1, 4, 8.2, -0.77, 1, 2, 3, 24.94, 8.74, 0.00044, 4, 15.65, 2.19, 0.99956], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 32, "height": 33}}, "close_eye_L": {"close_eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [7.44, -12.63, 7.09, -3.61, 15.43, -2.79, 16.43, -11.73], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 11, "height": 10}}, "close_eye_R": {"close_eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [5.7, 1.85, 3.17, 15.08, 13.8, 15.25, 15.08, 2.45], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 14, "height": 11}}, "hair": {"hair": {"type": "mesh", "uvs": [0.70344, 0, 0.43344, 0.0674, 0.22824, 0.18122, 0.08244, 0.32417, 0, 0.49093, 0, 0.68681, 0.12564, 0.81916, 0.38484, 0.8774, 0.38484, 1, 0.62244, 1, 0.85464, 0.91975, 0.98424, 0.80328, 0.83844, 0.68417, 0.71964, 0.58093, 0.73044, 0.44593, 0.93024, 0.34799, 1, 0.2024, 1, 0.05417, 1, 0, 0.81684, 0.08593, 0.62244, 0.18917, 0.46044, 0.31358, 0.39024, 0.46975, 0.37404, 0.63917, 0.47664, 0.73975, 0.66563, 0.8774], "triangles": [9, 25, 10, 25, 9, 7, 9, 8, 7, 10, 25, 11, 7, 24, 25, 25, 12, 11, 25, 24, 12, 7, 6, 24, 6, 23, 24, 6, 5, 23, 24, 13, 12, 24, 23, 13, 5, 4, 23, 23, 22, 13, 23, 4, 22, 13, 22, 14, 4, 3, 22, 22, 21, 14, 22, 3, 21, 14, 21, 15, 3, 2, 21, 21, 2, 20, 2, 1, 20, 21, 20, 15, 15, 20, 16, 20, 19, 16, 19, 17, 16, 19, 1, 0, 19, 20, 1, 17, 19, 18, 19, 0, 18], "vertices": [2, 15, 1.03, -6.04, 0.69223, 16, -10.89, 0.02, 0.30777, 2, 15, 8.42, -7.72, 0.3589, 16, -5.39, -5.19, 0.6411, 3, 15, 16.07, -6.54, 0.06747, 16, 1.79, -8.08, 0.93144, 17, -16.82, 0.9, 0.00109, 2, 16, 9.88, -9.08, 0.84044, 17, -10.47, -4.2, 0.15956, 2, 16, 18.59, -8.17, 0.50822, 17, -2.59, -8.01, 0.49178, 2, 16, 28, -4.83, 0.17597, 17, 7.18, -10.11, 0.82403, 3, 16, 33.31, 0.39, 0.00112, 17, 14.44, -8.47, 0.82031, 18, 0.32, -9.53, 0.17857, 2, 17, 18.71, -2.76, 0.48866, 18, 4.41, -3.69, 0.51134, 2, 17, 24.82, -4.08, 0.15533, 18, 10.56, -4.82, 0.84467, 2, 17, 26.07, 1.73, 0.00057, 18, 11.63, 1.02, 0.99943, 2, 17, 23.3, 8.27, 0.00047, 18, 8.65, 7.47, 0.99953, 3, 16, 25.36, 20.35, 0.00357, 17, 18.17, 12.69, 0.06461, 18, 3.39, 11.73, 0.93182, 4, 15, 20.78, 22.93, 0.00262, 16, 20.86, 14.88, 0.06247, 17, 11.46, 10.4, 0.25242, 18, -3.25, 9.24, 0.68249, 4, 15, 19.69, 16.98, 0.06528, 16, 16.89, 10.32, 0.26938, 17, 5.69, 8.61, 0.31044, 18, -8.96, 7.26, 0.3549, 4, 15, 15.07, 11.87, 0.31092, 16, 10.31, 8.27, 0.35069, 17, -0.98, 10.33, 0.24911, 18, -15.68, 8.77, 0.08928, 4, 15, 8.04, 11.23, 0.64026, 16, 3.93, 11.3, 0.29316, 17, -4.81, 16.26, 0.06131, 18, -19.7, 14.58, 0.00527, 3, 15, 1.94, 6.64, 0.91094, 16, -3.65, 10.46, 0.08625, 17, -11.7, 19.53, 0.00281, 2, 15, -2.9, 0.83, 0.99862, 16, -10.77, 7.93, 0.00138, 2, 15, -4.67, -1.29, 0.95809, 16, -13.37, 7.01, 0.04191, 2, 15, 1.66, -0.86, 0.99037, 16, -7.71, 4.16, 0.00963, 1, 15, 8.76, 0.08, 1, 1, 16, 6.21, -0.35, 1, 4, 15, 22.38, 7.35, 0.00096, 16, 14.3, 0.66, 0.9553, 17, -1.59, 1.76, 0.04295, 18, -16.02, 0.18, 0.00079, 1, 17, 6.77, -0.46, 1, 2, 17, 12.33, 0.96, 0.74378, 18, -2.09, -0.17, 0.25622, 1, 18, 5.67, 3.21, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 27, "height": 53}}, "hair_4": {"hair_4": {"type": "mesh", "uvs": [0.35787, 0, 0.26427, 0.17426, 0.05547, 0.38281, 0, 0.56902, 0.07707, 0.77757, 0.48027, 0.94143, 0.77546, 1, 1, 1, 1, 0.86695, 0.93386, 0.6584, 0.98426, 0.38653, 0.91946, 0.2264, 0.68186, 0.04764, 0.52347, 0, 0.51626, 0.18543, 0.48746, 0.39026, 0.52346, 0.67701, 0.66746, 0.87812], "triangles": [16, 15, 9, 4, 3, 16, 17, 16, 9, 17, 9, 8, 5, 4, 16, 17, 5, 16, 6, 17, 8, 5, 17, 6, 6, 8, 7, 14, 0, 13, 14, 13, 12, 1, 0, 14, 14, 12, 11, 15, 1, 14, 15, 14, 11, 15, 11, 10, 2, 1, 15, 9, 15, 10, 3, 15, 16, 15, 3, 2], "vertices": [1, 19, -2.99, -4.2, 1, 1, 19, 2.24, -4.58, 1, 2, 19, 8.78, -6.47, 0.99618, 20, -7.74, -1.79, 0.00382, 2, 19, 14.24, -6.22, 0.7023, 20, -3.49, -5.22, 0.2977, 2, 19, 19.94, -3.9, 0.07791, 20, 2.32, -7.26, 0.92209, 1, 20, 9.47, -4.41, 1, 1, 20, 13.16, -1.44, 1, 1, 20, 14.85, 1.48, 1, 2, 19, 19.77, 10.18, 8e-05, 20, 11.51, 3.41, 0.99992, 2, 19, 14.03, 8.02, 0.17174, 20, 5.78, 5.59, 0.82826, 2, 19, 6.15, 7.22, 0.88357, 20, -0.65, 10.2, 0.11643, 2, 19, 1.79, 5.35, 0.9919, 20, -5.16, 11.7, 0.0081, 1, 19, -2.59, 0.84, 1, 1, 19, -3.48, -1.76, 1, 1, 19, 1.81, -0.81, 1, 1, 19, 7.72, -0.07, 1, 2, 19, 15.77, 2.09, 0.00011, 20, 3.16, 0, 0.99989, 1, 20, 9.29, -1.06, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 17, "height": 31}}, "hair_5": {"hair_5": {"type": "mesh", "uvs": [0.1266, 0.08897, 0.32455, 0, 0.60275, 0, 0.77395, 0.05033, 0.8221, 0.22569, 0.8542, 0.38322, 0.94515, 0.54669, 1, 0.66856, 1, 0.79042, 0.83815, 0.92119, 0.6081, 1, 0.45295, 1, 0.469, 0.86472, 0.32455, 0.72503, 0.1052, 0.61506, 0, 0.45456, 0, 0.27325, 0.469, 0.14545, 0.46365, 0.2792, 0.47435, 0.4397, 0.5546, 0.55264, 0.6188, 0.72503, 0.67765, 0.85581], "triangles": [11, 12, 10, 10, 22, 9, 10, 12, 22, 9, 22, 8, 12, 21, 22, 12, 13, 21, 22, 21, 8, 21, 7, 8, 13, 20, 21, 21, 6, 7, 21, 20, 6, 13, 14, 20, 14, 19, 20, 20, 5, 6, 14, 15, 19, 20, 19, 5, 15, 18, 19, 15, 16, 18, 19, 18, 5, 18, 4, 5, 16, 0, 18, 18, 17, 4, 18, 0, 17, 17, 3, 4, 0, 1, 17, 17, 2, 3, 17, 1, 2], "vertices": [2, 21, 0.67, -6.2, 0.9884, 22, -16.89, -4.82, 0.0116, 1, 21, -1.96, -1.84, 1, 1, 21, -1.2, 3.67, 1, 2, 21, 1.06, 6.81, 0.99964, 22, -15.42, 8.12, 0.00036, 2, 21, 7.44, 6.9, 0.9418, 22, -9.05, 7.68, 0.0582, 2, 21, 13.15, 6.76, 0.66183, 22, -3.37, 7.07, 0.33817, 2, 21, 19.23, 7.76, 0.33072, 22, 2.77, 7.56, 0.66928, 2, 21, 23.72, 8.25, 0.05523, 22, 7.29, 7.67, 0.94477, 2, 21, 28.07, 7.65, 0.00187, 22, 11.57, 6.71, 0.99813, 1, 22, 15.46, 2.53, 1, 1, 22, 17.22, -2.58, 1, 1, 22, 16.55, -5.61, 1, 2, 21, 29.27, -3.24, 0.00916, 22, 11.86, -4.23, 0.99084, 2, 21, 23.89, -5.41, 0.12271, 22, 6.32, -5.96, 0.87729, 2, 21, 19.37, -9.22, 0.37276, 22, 1.5, -9.37, 0.62724, 2, 21, 13.36, -10.51, 0.68533, 22, -4.59, -10.16, 0.31466, 2, 21, 6.89, -9.62, 0.90512, 22, -10.96, -8.74, 0.09488, 1, 21, 3.62, 0.3, 1, 2, 21, 8.37, -0.46, 0.99982, 22, -8.73, 0.26, 0.00018, 2, 21, 14.13, -1.04, 0.96107, 22, -3.05, -0.79, 0.03893, 2, 21, 18.37, 0, 0.00026, 22, 1.27, -0.11, 0.99974, 1, 22, 7.61, -0.21, 1, 1, 22, 12.46, -0.09, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 22, "height": 38}}, "hair_6": {"hair_6": {"type": "mesh", "uvs": [0.04175, 0, 0.02169, 0.16876, 0.015, 0.36251, 0, 0.57651, 0.22231, 0.73557, 0.41625, 0.83678, 0.40288, 1, 0.69044, 1, 0.89106, 0.83967, 1, 0.61989, 0.84425, 0.44927, 0.71719, 0.24105, 0.64362, 0.08489, 0.46975, 0, 0.36943, 0.10803, 0.3895, 0.31335, 0.46975, 0.48976, 0.62356, 0.6893, 0.657, 0.83968], "triangles": [17, 10, 9, 5, 4, 17, 8, 17, 9, 18, 5, 17, 8, 18, 17, 6, 5, 18, 7, 18, 8, 6, 18, 7, 16, 15, 10, 3, 2, 16, 17, 16, 10, 4, 3, 16, 17, 4, 16, 14, 0, 13, 14, 13, 12, 1, 0, 14, 11, 15, 14, 11, 14, 12, 1, 14, 15, 2, 1, 15, 15, 11, 10, 2, 15, 16], "vertices": [2, 23, -3.51, -1.72, 0.9989, 24, -13.42, -3.48, 0.0011, 2, 23, 2.25, -4.16, 0.83034, 24, -7.4, -5.15, 0.16966, 2, 23, 8.95, -6.71, 0.50105, 24, -0.43, -6.82, 0.49895, 3, 23, 16.31, -9.64, 0.16882, 24, 7.25, -8.78, 0.75588, 25, -4.3, -10.45, 0.0753, 3, 23, 23.06, -8.31, 0.00405, 24, 13.77, -6.59, 0.63284, 25, 1.58, -6.89, 0.36311, 2, 24, 18.1, -4.38, 0.30355, 25, 5.33, -3.79, 0.69645, 2, 24, 23.94, -5.91, 0.04552, 25, 11.36, -4, 0.95448, 1, 25, 11.36, 0.6, 1, 2, 24, 19.86, 3.01, 0.12285, 25, 5.43, 3.81, 0.87715, 3, 23, 23.29, 4.85, 0.00284, 24, 12.3, 6.48, 0.4362, 25, -2.7, 5.55, 0.56096, 3, 23, 16.5, 4.66, 0.27397, 24, 5.6, 5.43, 0.4984, 25, -9.01, 3.06, 0.22763, 3, 23, 8.57, 5.39, 0.60731, 24, -2.36, 5.13, 0.37555, 25, -16.72, 1.03, 0.01715, 2, 23, 2.74, 6.26, 0.93781, 24, -8.26, 5.24, 0.06219, 1, 23, -1.17, 4.71, 1, 1, 23, 2.04, 1.84, 1, 1, 23, 9.29, -0.45, 1, 2, 23, 15.86, -1.48, 0.00018, 24, 5.75, -0.75, 0.99982, 2, 24, 13.5, 0.05, 0.3025, 25, -0.13, -0.47, 0.6975, 1, 25, 5.43, 0.06, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 18, "height": 39}}, "head": {"head": {"x": 20.33, "y": 7.32, "rotation": -83.63, "width": 55, "height": 50}}, "leg_L": {"leg_L": {"x": 8.61, "y": -1.23, "rotation": 95.76, "width": 22, "height": 21}}, "leg_L2": {"leg_L2": {"x": 15.43, "y": 0.83, "rotation": 94.8, "width": 22, "height": 10}}, "leg_L3": {"leg_L3": {"x": 6.96, "y": 0.12, "rotation": 94.8, "width": 22, "height": 25}}, "leg_R": {"leg_R": {"x": 12.3, "y": -1.68, "rotation": 112.72, "width": 23, "height": 16}}, "leg_R2": {"leg_R2": {"x": 15.95, "y": -1.41, "rotation": 103.31, "width": 23, "height": 10}}, "leg_R3": {"leg_R3": {"x": 6.84, "y": -2.02, "rotation": 103.31, "width": 26, "height": 26}}, "weapons": {"weapons": {"x": 12.64, "y": 1.98, "rotation": 31.63, "width": 34, "height": 26}}, "yinying": {"yinying": {"x": -1.79, "y": 2.57, "width": 54, "height": 54}}}}], "animations": {"idle": {"slots": {"close_eye_L": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.0333, "color": "ffffff00"}]}, "close_eye_R": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.0333, "color": "ffffff00"}]}}, "bones": {"body_2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -0.07, "y": 1.44, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "x": -0.07, "y": 1.44, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "body_3": {"rotate": [{"angle": 0.11, "curve": 0.349, "c2": 0.43, "c3": 0.684, "c4": 0.77}, {"time": 0.0667, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.9667, "angle": 1.83, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 0.11, "curve": 0.349, "c2": 0.43, "c3": 0.684, "c4": 0.77}, {"time": 1.7333, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 2.6333, "angle": 1.83, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.3333, "angle": 0.11}], "translate": [{"x": 0.06, "y": 0.01, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 2.23, "y": 0.32, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "x": 0.06, "y": 0.01, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": 2.23, "y": 0.32, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "x": 0.06, "y": 0.01}]}, "arm_R2": {"rotate": [{"angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.08}]}, "arm_R": {"rotate": [{"angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -1.08, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 4.52, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -1.08}]}, "arm_L2": {"rotate": [{"angle": 4.6}, {"time": 0.8333}, {"time": 1.6667, "angle": 4.6}, {"time": 2.5}, {"time": 3.3333, "angle": 4.6}]}, "arm_L": {"rotate": [{"angle": 4.6}, {"time": 0.8333}, {"time": 1.6667, "angle": 4.6}, {"time": 2.5}, {"time": 3.3333, "angle": 4.6}]}, "head": {"rotate": [{"angle": 0.21, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 2.27, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 0.21, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 2.27, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.3333, "angle": 0.21}]}, "hair_5": {"rotate": [{"angle": -4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -4.66}]}, "hair_6": {"rotate": [{"angle": -4.53, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": -4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": -4.53, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.7333, "angle": -4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "angle": -4.53}]}, "hair": {"rotate": [{"angle": -4.23, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "angle": -4.66, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": -4.23, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.8, "angle": -4.66, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.3333, "angle": -4.23}]}, "hair_8": {"rotate": [{"angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.02}]}, "hair_9": {"rotate": [{"angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 3.02, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 3.02}]}, "hair_4": {"rotate": [{"angle": -3.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.62, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -3.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8333, "angle": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.62, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -3.2}]}, "hair_7": {"rotate": [{"angle": -3.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "angle": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 5.62, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": -3.2, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8333, "angle": -4.52, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 5.62, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": -3.2}]}, "hair_11": {"rotate": [{"angle": -1.77, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -16.36, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -1.77, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.8667, "angle": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -16.36, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.3333, "angle": -1.77}]}, "hair_13": {"rotate": [{"angle": -1.77, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 0.2, "angle": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": -16.36, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": -1.77, "curve": 0.379, "c2": 0.6, "c3": 0.723}, {"time": 1.8667, "angle": 1.3, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": -16.36, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.3333, "angle": -1.77}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 1.48, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}}, "deform": {"default": {"body_2": {"body_2": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "offset": 84, "vertices": [0.98992, 0.14142, 0.94423, -0.32928, 1.2197, 0.30051, 1.22144, -0.29356, 0.05297, 0.51264, 0.28268, 0.43098, -0.44196, 0.44194], "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "offset": 84, "vertices": [0.98992, 0.14142, 0.94423, -0.32928, 1.2197, 0.30051, 1.22144, -0.29356, 0.05297, 0.51264, 0.28268, 0.43098, -0.44196, 0.44194], "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}}}}}}