{"skeleton": {"hash": "e0P9kF41jhzDmbCgsuP+RZP+Uzg", "spine": "3.8.99", "x": -36.95, "y": -20.76, "width": 62.67, "height": 98.59, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/男侏儒"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 21.29}, {"name": "body", "parent": "bone", "length": 8.74, "rotation": -0.75, "x": -2.15, "y": 15.37}, {"name": "body_2", "parent": "body", "length": 8.28, "rotation": 78.33, "x": 2.44, "y": -0.53}, {"name": "body_3", "parent": "body_2", "length": 8.11, "rotation": 17.27, "x": 8.71, "y": -0.05}, {"name": "head", "parent": "body_3", "length": 16.82, "rotation": 1.7, "x": 6.69, "y": -2.22}, {"name": "hair_2", "parent": "head", "length": 8.53, "rotation": 102.18, "x": 8.92, "y": -2.82}, {"name": "hair_3", "parent": "hair_2", "length": 7.58, "rotation": -24.95, "x": 8.53}, {"name": "hair", "parent": "head", "length": 5.14, "rotation": -137.01, "x": 9.24, "y": -7.34}, {"name": "hair2", "parent": "hair", "length": 5.64, "rotation": 57.05, "x": 5.14}, {"name": "nose", "parent": "head", "length": 5.89, "rotation": -135.21, "x": 14.32, "y": -2.36}, {"name": "hair_4", "parent": "head", "length": 8.59, "rotation": 1.15, "x": 18.51, "y": 18.26}, {"name": "hair_5", "parent": "hair_4", "length": 9.93, "rotation": 12.63, "x": 9.64, "y": 0.09}, {"name": "hair_6", "parent": "hair_5", "length": 9.1, "rotation": 5.92, "x": 10.04, "y": -0.04}, {"name": "hair_7", "parent": "head", "length": 9.7, "rotation": 24.88, "x": 16.57, "y": 20.57}, {"name": "hair_8", "parent": "hair_7", "length": 7.57, "rotation": 11.72, "x": 9.7}, {"name": "hair_9", "parent": "hair_8", "length": 10.49, "rotation": -15.74, "x": 7.57}, {"name": "hair_10", "parent": "head", "length": 8.98, "rotation": -16.13, "x": 16.17, "y": -11.22}, {"name": "hair_11", "parent": "hair_10", "length": 8.18, "rotation": 6.36, "x": 8.98}, {"name": "hair_12", "parent": "hair_11", "length": 9.96, "rotation": 22.08, "x": 8.18}, {"name": "ear_L", "parent": "head", "length": 7.4, "rotation": -90.3, "x": 13.64, "y": -13.71}, {"name": "ear_R", "parent": "head", "length": 9.55, "rotation": 82.07, "x": 15.9, "y": 17.05}, {"name": "arm_R2", "parent": "body_3", "length": 16.25, "rotation": 149.13, "x": 12.05, "y": 15.58}, {"name": "arm_R3", "parent": "arm_R2", "length": 10.47, "rotation": 34.86, "x": 16.25}, {"name": "arm_L2", "parent": "body_3", "length": 16.1, "rotation": -163.04, "x": 10.34, "y": -5.98}, {"name": "arm_L", "parent": "arm_L2", "length": 10.27, "rotation": -6.21, "x": 16.55, "y": 0.19}, {"name": "leg_R2", "parent": "bone", "length": 11.93, "rotation": -89.49, "x": -8.03, "y": 12.1}, {"name": "leg_L2", "parent": "bone", "length": 15.91, "rotation": -84.84, "x": 6.54, "y": 17.35}], "slots": [{"name": "yinying", "bone": "bone", "attachment": "yinying"}, {"name": "arm_L2", "bone": "arm_L2", "attachment": "arm_L2"}, {"name": "arm_L", "bone": "arm_L", "attachment": "arm_L"}, {"name": "body_2", "bone": "body_2", "attachment": "body_2"}, {"name": "leg_L2", "bone": "leg_L2", "attachment": "leg_L2"}, {"name": "leg_L", "bone": "leg_L2", "attachment": "leg_L"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "leg_R2", "bone": "leg_R2", "attachment": "leg_R2"}, {"name": "leg_R", "bone": "leg_R2", "attachment": "leg_R"}, {"name": "arm_R2", "bone": "arm_R2", "attachment": "arm_R2"}, {"name": "arm_R", "bone": "arm_R2", "attachment": "arm_R"}, {"name": "ear_L", "bone": "ear_L", "attachment": "ear_L"}, {"name": "hair_5", "bone": "hair_10", "attachment": "hair_5"}, {"name": "head", "bone": "head", "attachment": "head"}, {"name": "close_eye_R", "bone": "head", "color": "ffffff00", "attachment": "close_eye_R"}, {"name": "close_eye_L", "bone": "head", "color": "ffffff00", "attachment": "close_eye_L"}, {"name": "hair_4", "bone": "hair_7", "attachment": "hair_4"}, {"name": "hair_3", "bone": "hair_4", "attachment": "hair_3"}, {"name": "ear_R", "bone": "ear_R", "attachment": "ear_R"}, {"name": "hair_2", "bone": "hair_2", "attachment": "hair_2"}, {"name": "hair", "bone": "hair", "attachment": "hair"}, {"name": "nose", "bone": "nose", "attachment": "nose"}], "skins": [{"name": "default", "attachments": {"arm_L": {"arm_L": {"x": 2.57, "y": -0.08, "rotation": 74.41, "width": 21, "height": 21}}, "arm_L2": {"arm_L2": {"x": 9.34, "y": 1.04, "rotation": 68.2, "width": 20, "height": 24}}, "arm_R": {"arm_R": {"x": 19.83, "y": 1.95, "rotation": 116.02, "width": 22, "height": 23}}, "arm_R2": {"arm_R2": {"x": 9.73, "y": -1.31, "rotation": 116.02, "width": 21, "height": 24}}, "body": {"body": {"x": 0.89, "y": -2.03, "rotation": 0.75, "width": 32, "height": 19}}, "body_2": {"body_2": {"type": "mesh", "uvs": [0.2218, 0.02724, 0.10325, 0.13686, 0.02583, 0.2984, 0.01616, 0.5609, 0.09841, 0.78301, 0.32825, 0.99647, 0.58954, 1, 0.85083, 0.96763, 0.99115, 0.7109, 1, 0.39647, 0.95244, 0.20032, 0.74922, 0.01859, 0.59922, 0, 0.42744, 0, 0.53874, 0.11955, 0.58954, 0.28397, 0.63551, 0.5407, 0.61373, 0.69936, 0.59438, 0.84936], "triangles": [14, 13, 12, 11, 15, 14, 11, 14, 12, 15, 11, 10, 9, 16, 15, 9, 15, 10, 0, 15, 1, 8, 16, 9, 2, 16, 3, 15, 16, 1, 14, 0, 13, 15, 0, 14, 2, 1, 16, 17, 16, 8, 16, 4, 3, 16, 17, 5, 7, 17, 8, 18, 17, 7, 18, 5, 17, 5, 4, 16, 6, 5, 18, 6, 18, 7], "vertices": [2, 3, 20.46, 15.45, 0.2456, 4, 15.82, 11.31, 0.7544, 2, 3, 16.89, 18.43, 0.39005, 4, 13.29, 15.21, 0.60995, 2, 3, 12.27, 19.87, 0.54836, 4, 9.31, 17.96, 0.45164, 2, 3, 5.54, 18.69, 0.71845, 4, 2.54, 18.83, 0.28155, 2, 3, 0.45, 14.96, 0.87671, 4, -3.43, 16.78, 0.12329, 2, 3, -3.44, 6.81, 0.9647, 4, -9.57, 10.15, 0.0353, 2, 3, -1.79, -1.12, 0.97824, 4, -10.34, 2.09, 0.02176, 2, 3, 0.78, -8.85, 0.82433, 4, -10.19, -6.06, 0.17567, 2, 3, 8.23, -11.66, 0.51995, 4, -3.9, -10.95, 0.48005, 2, 3, 16.28, -10.17, 0.20969, 4, 4.22, -11.92, 0.79031, 2, 3, 20.94, -7.64, 0.03048, 4, 9.43, -10.88, 0.96952, 2, 3, 24.2, -0.47, 0.004, 4, 14.67, -5, 0.996, 2, 3, 23.67, 4.18, 0.03288, 4, 15.54, -0.41, 0.96712, 2, 3, 22.52, 9.38, 0.11991, 4, 15.99, 4.9, 0.88009, 2, 3, 20.23, 5.34, 0.02678, 4, 12.6, 1.72, 0.97322, 2, 3, 16.39, 2.88, 0.00509, 4, 8.21, 0.51, 0.99491, 1, 4, 1.44, -0.34, 1, 2, 3, 6.01, -0.17, 0.99748, 4, -2.62, 0.68, 0.00252, 1, 3, 2.07, -0.43, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 31, "height": 26}}, "close_eye_L": {"close_eye_L": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [9.53, -16.43, 12.26, -4.3, 22.19, -5.44, 20.94, -16.37], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 11, "height": 10}}, "close_eye_R": {"close_eye_R": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.22, -2.17, 12.41, 16.06, 24.94, 13.65, 23.36, -1.9], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 15, "height": 11}}, "ear_L": {"ear_L": {"x": 2.86, "y": 0.25, "rotation": -6.24, "width": 14, "height": 20}}, "ear_R": {"ear_R": {"x": 7.91, "y": -1.63, "rotation": -178.62, "width": 21, "height": 24}}, "hair": {"hair": {"type": "mesh", "uvs": [0.15608, 0.12264, 0.27255, 0.04407, 0.49902, 0, 0.68343, 0.1855, 0.92932, 0.35835, 0.97461, 0.54299, 0.81932, 0.77871, 0.65432, 0.95156, 0.4602, 0.98299, 0.24667, 0.84549, 0.10108, 0.64121, 0.02991, 0.33085, 0.02991, 0.18942, 0.28873, 0.29943, 0.52167, 0.52336, 0.78696, 0.51943], "triangles": [15, 3, 4, 14, 3, 15, 15, 4, 5, 6, 15, 5, 6, 7, 14, 6, 14, 15, 8, 9, 14, 7, 8, 14, 13, 1, 2, 0, 1, 13, 11, 12, 0, 11, 0, 13, 3, 13, 2, 14, 13, 3, 10, 11, 13, 10, 13, 14, 9, 10, 14], "vertices": [2, 8, -3.21, 1.13, 0.9851, 9, -3.59, 7.62, 0.0149, 2, 8, -2.41, 3.25, 0.90712, 9, -1.38, 8.11, 0.09288, 2, 8, 0.12, 6.22, 0.67883, 9, 2.49, 7.6, 0.32117, 2, 8, 4.19, 6.28, 0.35967, 9, 4.75, 4.21, 0.64033, 2, 8, 8.94, 7.15, 0.10432, 9, 8.06, 0.7, 0.89568, 2, 8, 11.2, 5.68, 0.0002, 9, 8.06, -2, 0.9998, 2, 8, 11.33, 1.46, 0.03236, 9, 4.59, -4.41, 0.96764, 2, 8, 10.77, -2.2, 0.17194, 9, 1.21, -5.92, 0.82806, 2, 8, 8.54, -4.68, 0.47493, 9, -2.07, -5.4, 0.52507, 2, 8, 4.53, -5.57, 0.7759, 9, -5, -2.52, 0.2241, 2, 8, 0.79, -5, 0.96966, 9, -6.56, 0.93, 0.03034, 1, 8, -2.95, -2.48, 1, 2, 8, -4.23, -0.97, 0.99908, 9, -5.91, 7.33, 0.00092, 2, 8, 0.12, 0.71, 0.99078, 9, -2.14, 4.6, 0.00922, 2, 8, 5.16, 0.89, 0.23603, 9, 0.76, 0.47, 0.76397, 1, 9, 5.1, -0.77, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 17, "height": 14}}, "hair_2": {"hair_2": {"type": "mesh", "uvs": [0.09022, 0.1536, 0.22641, 0.2256, 0.38164, 0.1136, 0.5906, 0.02964, 0.82679, 0.06697, 0.97155, 0.2403, 0.99059, 0.47763, 0.9144, 0.73896, 0.73536, 0.95229, 0.56393, 0.99229, 0.34108, 0.95496, 0.15822, 0.80029, 0.0668, 0.58696, 0.01537, 0.25897, 0.18679, 0.40296, 0.38298, 0.56563, 0.62488, 0.5603, 0.76203, 0.4083], "triangles": [14, 0, 1, 13, 0, 14, 15, 2, 16, 1, 2, 15, 14, 1, 15, 12, 13, 14, 11, 12, 14, 11, 14, 15, 10, 11, 15, 10, 15, 9, 17, 3, 4, 17, 4, 5, 17, 5, 6, 16, 3, 17, 2, 3, 16, 7, 17, 6, 16, 17, 7, 8, 16, 7, 9, 15, 16, 9, 16, 8], "vertices": [2, 6, 15.09, -8.48, 0.01036, 7, 9.52, -4.92, 0.98964, 2, 6, 12.73, -6.54, 0.10619, 7, 6.56, -4.16, 0.89381, 2, 6, 9.1, -7.08, 0.35362, 7, 3.5, -6.18, 0.64638, 2, 6, 4.54, -6.87, 0.66872, 7, -0.73, -7.91, 0.33128, 2, 6, 0.02, -4.74, 0.90622, 7, -5.72, -7.89, 0.09378, 2, 6, -2.02, -1.31, 0.9921, 7, -9.02, -5.64, 0.0079, 1, 6, -1.26, 2.19, 1, 2, 6, 1.51, 5.39, 0.9777, 7, -8.64, 1.93, 0.0223, 2, 6, 6.1, 7.22, 0.85774, 7, -5.25, 5.52, 0.14226, 2, 6, 9.71, 6.63, 0.56528, 7, -1.74, 6.5, 0.43472, 2, 6, 13.96, 4.6, 0.25432, 7, 2.98, 6.46, 0.74568, 2, 6, 16.85, 1.17, 0.04094, 7, 7.05, 4.57, 0.95906, 2, 6, 17.64, -2.48, 7e-05, 7, 9.3, 1.59, 0.99993, 2, 6, 17.08, -7.49, 2e-05, 7, 10.91, -3.18, 0.99998, 2, 6, 14.37, -4.29, 0.00207, 7, 7.1, -1.42, 0.99793, 1, 7, 2.74, 0.55, 1, 1, 6, 6.41, 0.9, 1, 2, 6, 2.95, -0.33, 0.99856, 7, -4.92, -2.66, 0.00144], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 21, "height": 15}}, "hair_3": {"hair_3": {"type": "mesh", "uvs": [0.15258, 0.02555, 0.03173, 0.27129, 0.01158, 0.48481, 0.17273, 0.67012, 0.34058, 0.83529, 0.64944, 0.99241, 0.78373, 0.98032, 0.93144, 0.80709, 1, 0.58955, 0.97173, 0.42035, 0.93144, 0.25115, 0.75015, 0.13029, 0.52858, 0.06584, 0.21301, 0, 0.34059, 0.21892, 0.44801, 0.42841, 0.52858, 0.60163, 0.63601, 0.75472], "triangles": [6, 5, 17, 5, 4, 17, 6, 17, 7, 4, 16, 17, 7, 17, 8, 17, 16, 8, 4, 3, 16, 3, 2, 15, 3, 15, 16, 15, 2, 1, 16, 9, 8, 16, 15, 9, 15, 10, 9, 1, 14, 15, 15, 11, 10, 11, 14, 12, 11, 15, 14, 1, 0, 14, 0, 13, 14, 14, 13, 12], "vertices": [2, 13, 13.33, 1.05, 0.88855, 12, 23.2, 2.38, 0.11145, 3, 13, 6.74, 7.13, 0.589, 12, 16.01, 7.75, 0.40503, 11, 23.57, 11.15, 0.00597, 3, 13, 0.23, 10.81, 0.2557, 12, 9.15, 10.74, 0.64621, 11, 16.23, 12.57, 0.09809, 3, 13, -7.09, 10.65, 0.03378, 12, 1.89, 9.82, 0.5885, 11, 9.35, 10.09, 0.37772, 2, 12, -4.75, 8.52, 0.29491, 11, 3.14, 7.37, 0.70509, 2, 12, -12.16, 4.35, 0.0537, 11, -3.17, 1.68, 0.9463, 2, 12, -12.74, 1.56, 0.01281, 11, -3.13, -1.18, 0.98719, 3, 13, -18.43, -1.52, 0.02034, 12, -8.14, -3.46, 0.23202, 11, 2.46, -5.06, 0.74765, 3, 13, -12.24, -6.18, 0.14035, 12, -1.5, -7.45, 0.442, 11, 9.81, -7.51, 0.41765, 3, 13, -6.67, -8.27, 0.40756, 12, 4.26, -8.95, 0.49531, 11, 15.76, -7.71, 0.09712, 3, 13, -0.98, -10.13, 0.71737, 12, 10.11, -10.21, 0.2793, 11, 21.74, -7.67, 0.00333, 2, 13, 4.5, -8.58, 0.93069, 12, 15.4, -8.11, 0.06931, 2, 13, 8.58, -5.41, 0.99681, 12, 19.13, -4.53, 0.00319, 2, 13, 13.58, -0.48, 0.99996, 12, 23.59, 0.88, 4e-05, 2, 13, 5.52, 0.5, 0.98909, 12, 15.48, 1.03, 0.01091, 2, 12, 7.82, 1.46, 0.99988, 11, 16.95, 3.22, 0.00012, 2, 12, 1.55, 1.98, 0.90247, 11, 10.72, 2.36, 0.09753, 2, 12, -4.26, 1.72, 0.00112, 11, 5.11, 0.84, 0.99888], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 21, "height": 35}}, "hair_4": {"hair_4": {"type": "mesh", "uvs": [0.06845, 0.04449, 0.01391, 0.27549, 0.01391, 0.51473, 0.15507, 0.73748, 0.4342, 0.90248, 0.72294, 0.99598, 0.89298, 0.99873, 0.96998, 0.81173, 1, 0.60273, 0.92828, 0.43773, 0.73578, 0.20674, 0.53686, 0.09674, 0.29945, 0.00049, 0.23207, 0.22874, 0.40532, 0.42399, 0.55611, 0.58349, 0.7454, 0.79798], "triangles": [5, 16, 6, 6, 16, 7, 5, 4, 16, 4, 15, 16, 7, 16, 8, 16, 15, 8, 4, 3, 15, 3, 14, 15, 3, 2, 14, 15, 9, 8, 9, 15, 10, 2, 13, 14, 15, 14, 10, 14, 11, 10, 2, 1, 13, 14, 13, 11, 1, 0, 13, 13, 12, 11, 13, 0, 12], "vertices": [2, 16, 12.51, 1.31, 0.97011, 15, 19.97, -2.13, 0.02989, 3, 16, 7.37, 5.45, 0.78394, 15, 16.14, 3.25, 0.2155, 14, 24.85, 6.46, 0.00055, 3, 16, 1.43, 8.54, 0.46479, 15, 11.26, 7.83, 0.49089, 14, 19.13, 9.95, 0.04432, 3, 16, -5.67, 8.4, 0.16014, 15, 4.39, 9.62, 0.54616, 14, 12.05, 10.31, 0.29371, 3, 16, -12.86, 4.58, 0.01418, 15, -3.56, 7.9, 0.35959, 14, 4.61, 7.01, 0.62623, 2, 15, -10.21, 4.63, 0.0842, 14, -1.24, 2.46, 0.9158, 2, 15, -13.06, 1.71, 0.00188, 14, -3.43, -0.98, 0.99812, 3, 16, -16.52, -8.01, 0.00034, 15, -10.5, -3.22, 0.05196, 14, 0.07, -5.29, 0.9477, 3, 16, -11.65, -11.34, 0.01356, 15, -6.73, -7.75, 0.19626, 14, 4.69, -8.96, 0.79017, 3, 16, -6.76, -11.94, 0.14569, 15, -2.18, -9.66, 0.37698, 14, 9.53, -9.9, 0.47733, 3, 16, 1.11, -10.81, 0.4138, 15, 5.7, -10.71, 0.39138, 14, 17.46, -9.33, 0.19482, 3, 16, 6.04, -7.99, 0.7327, 15, 11.21, -9.33, 0.24828, 14, 22.58, -6.86, 0.01902, 3, 16, 11.05, -4.18, 0.93391, 15, 17.07, -7.02, 0.06594, 14, 27.85, -3.4, 0.00015, 2, 16, 6.13, 0.2, 0.99906, 15, 13.52, -1.47, 0.00094, 1, 15, 6.68, -0.76, 1, 2, 16, -6.27, -2.13, 0.00039, 15, 0.95, -0.35, 0.99961, 1, 14, 3.21, -0.89, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 24, "height": 28}}, "hair_5": {"hair_5": {"type": "mesh", "uvs": [0.25383, 0.01158, 0.1034, 0.19074, 0.03097, 0.42474, 0.07554, 0.65142, 0.19811, 0.85617, 0.42654, 1, 0.71068, 0.92199, 0.9224, 0.78305, 1, 0.56002, 0.98925, 0.39183, 0.8444, 0.27483, 0.73854, 0.17977, 0.56025, 0.02255, 0.4154, 0, 0.46554, 0.26387, 0.48226, 0.47593, 0.52126, 0.71358, 0.4934, 0.85618], "triangles": [5, 17, 6, 5, 4, 17, 7, 6, 16, 6, 17, 16, 17, 4, 16, 4, 3, 16, 7, 16, 8, 3, 15, 16, 16, 15, 8, 2, 15, 3, 15, 9, 8, 15, 10, 9, 15, 2, 14, 14, 2, 1, 10, 14, 11, 10, 15, 14, 1, 0, 14, 0, 13, 14, 14, 12, 11, 14, 13, 12], "vertices": [3, 19, 13.25, 1.87, 0.97838, 18, 19.76, 6.72, 0.02138, 17, 27.87, 8.86, 0.00024, 3, 19, 8.85, 6.71, 0.81223, 18, 13.86, 9.55, 0.15659, 17, 21.69, 11.02, 0.03117, 3, 19, 2.26, 10.57, 0.51147, 18, 6.29, 10.64, 0.28124, 17, 14.06, 11.28, 0.20729, 3, 19, -4.91, 12.03, 0.20034, 18, -0.9, 9.3, 0.2726, 17, 7.06, 9.15, 0.52705, 3, 19, -11.94, 11.71, 0.03316, 18, -7.29, 6.36, 0.13739, 17, 1.03, 5.52, 0.82945, 3, 19, -17.85, 8.66, 0.00059, 18, -11.62, 1.31, 0.02446, 17, -2.71, 0.02, 0.97495, 2, 18, -8.79, -4.5, 0.12789, 17, 0.74, -5.45, 0.87211, 3, 19, -14.65, -3.44, 0.02152, 18, -4.1, -8.69, 0.39158, 17, 5.87, -9.09, 0.5869, 3, 19, -8.42, -7.29, 0.12006, 18, 3.12, -9.92, 0.61116, 17, 13.18, -9.51, 0.26878, 3, 19, -3.25, -8.81, 0.35056, 18, 8.48, -9.39, 0.59782, 17, 18.45, -8.39, 0.05162, 3, 19, 1.27, -7.14, 0.64911, 18, 12.04, -6.14, 0.34739, 17, 21.63, -4.77, 0.0035, 2, 19, 4.87, -6.02, 0.88391, 18, 14.96, -3.75, 0.11609, 2, 19, 10.84, -4.11, 0.98674, 18, 19.77, 0.27, 0.01326, 2, 19, 12.51, -1.46, 0.99999, 18, 20.32, 3.35, 1e-05, 2, 19, 4.18, 0.27, 0.99718, 18, 11.95, 1.82, 0.00282, 3, 19, -2.36, 2.13, 0.03691, 18, 5.19, 1.09, 0.9575, 17, 14.02, 1.66, 0.00559, 1, 17, 6.66, -0.42, 1, 1, 17, 2.06, -0.6, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 21, "height": 32}}, "head": {"head": {"x": 20.35, "y": 4.84, "rotation": -96.55, "width": 46, "height": 46}}, "leg_L": {"leg_L": {"x": 13.89, "y": -2.08, "rotation": 84.84, "width": 22, "height": 9}}, "leg_L2": {"leg_L2": {"x": 7.37, "y": -2, "rotation": 84.84, "width": 21, "height": 22}}, "leg_R": {"leg_R": {"x": 8.77, "y": -0.33, "rotation": 89.49, "width": 22, "height": 9}}, "leg_R2": {"leg_R2": {"x": 5.27, "y": -0.8, "rotation": 89.49, "width": 21, "height": 16}}, "nose": {"nose": {"x": 2.52, "y": 0.31, "rotation": 38.66, "width": 14, "height": 11}}, "yinying": {"yinying": {"x": -1.22, "y": 2.42, "scaleX": 0.8586, "scaleY": 0.8586, "width": 54, "height": 54}}}}], "animations": {"idle": {"slots": {"close_eye_L": {"color": [{"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "close_eye_R": {"color": [{"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}}, "bones": {"body_2": {"rotate": [{"angle": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -0.09}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 0.54, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "body_3": {"rotate": [{"angle": -0.08, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "angle": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "angle": 0.3, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "angle": -0.08, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.7333, "angle": -0.09, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "angle": 0.3, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "angle": -0.08}], "translate": [{"x": 0.05, "y": 0.01, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": 1.7, "y": 0.38, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 1.6667, "x": 0.05, "y": 0.01, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.7333, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "x": 1.7, "y": 0.38, "curve": 0.246, "c3": 0.718, "c4": 0.86}, {"time": 3.3333, "x": 0.05, "y": 0.01}]}, "head": {"rotate": [{"angle": 0.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "angle": 0.49, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": 3.8, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "angle": 0.49}], "translate": [{"x": 0.11, "y": -0.01, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 0.87, "y": -0.08, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": 0.11, "y": -0.01, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 1.8333, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": 0.87, "y": -0.08, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 3.3333, "x": 0.11, "y": -0.01}]}, "body": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "y": 0.75, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "arm_R2": {"rotate": [{"angle": -3.53, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.53, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.51, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -3.53}]}, "arm_R3": {"rotate": [{"angle": -3.53, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.51, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -3.53, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.51, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -3.53}]}, "arm_L2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "arm_L": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -4.5, "curve": 0.25, "c3": 0.75}, {"time": 3.3333}]}, "hair_2": {"rotate": [{"angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 2.74, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 2.74, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.1}]}, "hair_3": {"rotate": [{"angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -6.1, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": 5.93, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -6.1}]}, "hair": {"rotate": [{"angle": 9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -4.88, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 9.94}]}, "hair2": {"rotate": [{"angle": 9.94, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -24.75, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.94, "curve": 0.25, "c3": 0.75}, {"time": 2.5, "angle": -24.75, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 9.94}]}, "hair_4": {"rotate": [{"angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "angle": 8.46, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.6667, "angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.8, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "angle": 8.46, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 3.3333, "angle": 0.77}]}, "hair_5": {"rotate": [{"angle": 1.41, "curve": 0.36, "c2": 0.46, "c3": 0.698, "c4": 0.81}, {"time": 0.1333, "angle": 0.23, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 1.0333, "angle": 8.46, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.6667, "angle": 1.41, "curve": 0.36, "c2": 0.46, "c3": 0.698, "c4": 0.81}, {"time": 1.8, "angle": 0.23, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.8667, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "angle": 8.46, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 3.3333, "angle": 1.41}]}, "hair_6": {"rotate": [{"angle": 2.21, "curve": 0.352, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.1333, "angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 1.1, "angle": 8.46, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.6667, "angle": 2.21, "curve": 0.352, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 1.8, "angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.9333, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 8.46, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 3.3333, "angle": 2.21}]}, "hair_9": {"rotate": [{"angle": 3.51, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1, "angle": 2.21, "curve": 0.352, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 8.46, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 1.6667, "angle": 3.51, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 1.7667, "angle": 2.21, "curve": 0.352, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 1.9, "angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 8.46, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 3.3333, "angle": 3.51}]}, "hair_8": {"rotate": [{"angle": 2.61, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1, "angle": 1.41, "curve": 0.36, "c2": 0.46, "c3": 0.698, "c4": 0.81}, {"time": 0.2333, "angle": 0.23, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 8.46, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 1.6667, "angle": 2.61, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 1.7667, "angle": 1.41, "curve": 0.36, "c2": 0.46, "c3": 0.698, "c4": 0.81}, {"time": 1.9, "angle": 0.23, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 8.46, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 3.3333, "angle": 2.61}]}, "hair_7": {"rotate": [{"angle": 1.82, "curve": 0.349, "c2": 0.39, "c3": 0.685, "c4": 0.74}, {"time": 0.1, "angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 8.46, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 1.6667, "angle": 1.82, "curve": 0.349, "c2": 0.39, "c3": 0.685, "c4": 0.74}, {"time": 1.7667, "angle": 0.77, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 8.46, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 3.3333, "angle": 1.82}]}, "hair_10": {"rotate": [{"angle": 1.48, "curve": 0.349, "c2": 0.39, "c3": 0.685, "c4": 0.74}, {"time": 0.1, "angle": 0.63, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.0667, "angle": 6.87, "curve": 0.243, "c3": 0.691, "c4": 0.76}, {"time": 1.7667, "angle": 0.63, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 1.9, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "angle": 6.87, "curve": 0.243, "c3": 0.66, "c4": 0.64}, {"time": 3.3333, "angle": 1.48}]}, "hair_12": {"rotate": [{"angle": 2.85, "curve": 0.339, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.1, "angle": 1.8, "curve": 0.352, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 0.2333, "angle": 0.63, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 0.3667, "curve": 0.25, "c3": 0.75}, {"time": 1.2, "angle": 6.87, "curve": 0.243, "c3": 0.651, "c4": 0.61}, {"time": 1.7667, "angle": 1.8, "curve": 0.352, "c2": 0.4, "c3": 0.689, "c4": 0.75}, {"time": 1.9, "angle": 0.63, "curve": 0.37, "c2": 0.63, "c3": 0.708}, {"time": 2.0333, "curve": 0.25, "c3": 0.75}, {"time": 2.8667, "angle": 6.87, "curve": 0.247, "c3": 0.632, "c4": 0.53}, {"time": 3.3333, "angle": 2.85}]}, "hair_11": {"rotate": [{"angle": 2.12, "curve": 0.343, "c2": 0.37, "c3": 0.679, "c4": 0.71}, {"time": 0.1, "angle": 1.14, "curve": 0.36, "c2": 0.46, "c3": 0.698, "c4": 0.81}, {"time": 0.2333, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 1.1333, "angle": 6.87, "curve": 0.242, "c3": 0.669, "c4": 0.68}, {"time": 1.7667, "angle": 1.14, "curve": 0.36, "c2": 0.46, "c3": 0.698, "c4": 0.81}, {"time": 1.9, "angle": 0.19, "curve": 0.355, "c2": 0.65, "c3": 0.69}, {"time": 1.9667, "curve": 0.25, "c3": 0.75}, {"time": 2.8, "angle": 6.87, "curve": 0.244, "c3": 0.644, "c4": 0.58}, {"time": 3.3333, "angle": 2.12}]}}}}}