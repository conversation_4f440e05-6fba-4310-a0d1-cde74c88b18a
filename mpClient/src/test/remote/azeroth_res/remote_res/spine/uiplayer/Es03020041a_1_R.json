{"skeleton": {"hash": "1evy63s1gOhyaqlP07E0aBmIYAM", "spine": "3.8.99", "x": -320.37, "y": 65.21, "width": 561.63, "height": 170.83, "images": "../images/wuqi/", "audio": "C:/Users/<USER>/Desktop/"}, "bones": [{"name": "Es03020041a_1_R", "scaleX": 0.45, "scaleY": 0.45}, {"name": "zong", "parent": "Es03020041a_1_R", "rotation": 0.57}, {"name": "shen", "parent": "zong", "x": 90, "y": 398.96}, {"name": "shen2", "parent": "shen", "length": 156.43, "rotation": 84.36, "x": 2.98, "y": 21.18}, {"name": "shen3", "parent": "shen2", "length": 107.56, "rotation": 19.47, "x": 156.27, "y": 1.84}, {"name": "shen4", "parent": "shen3", "length": 62.7, "rotation": 39.58, "x": 107.56}, {"name": "bi_U_L", "parent": "shen4", "length": 226.58, "rotation": 96.64, "x": 130.74, "y": 68.12}, {"name": "bi_D_L", "parent": "bi_U_L", "length": 123.05, "rotation": 33.77, "x": 224.26, "y": -1.51}, {"name": "bi_D_L2", "parent": "bi_D_L", "length": 90.23, "rotation": 7.53, "x": 123.44, "y": 0.02}, {"name": "Es03020041_gold", "parent": "bi_D_L2", "length": 172.81, "rotation": 76.96, "x": 57.25, "y": 39.75, "transform": "noScale"}, {"name": "Es03020041_zxg", "parent": "Es03020041_gold", "x": 213.27, "y": -3.58}, {"name": "Es03020041_shadow_2", "parent": "Es03020041_gold", "length": 12.45, "rotation": 47.36, "x": 213.13, "y": -3.73}, {"name": "Es03020041_gold2", "parent": "Es03020041_gold", "length": 7.72, "rotation": -0.72, "x": 215.72, "y": -7.63}, {"name": "Es03020041_light_7", "parent": "Es03020041_gold2", "length": 21.19, "rotation": 69.75, "x": -3.28, "y": 22.06}, {"name": "Es03020041_light_8", "parent": "Es03020041_gold2", "length": 14.2, "rotation": 53.29, "x": 5.21, "y": 20.52}, {"name": "Es03020041_light_9", "parent": "Es03020041_gold2", "length": 27.19, "rotation": 25.66, "x": 14.09, "y": 15.88}, {"name": "Es03020041_light_10", "parent": "Es03020041_gold2", "length": 18.25, "rotation": -19.78, "x": 11.77, "y": -0.96}, {"name": "Es03020041_light_11", "parent": "Es03020041_gold2", "length": 11.37, "rotation": -66.37, "x": 6.96, "y": -19.16}, {"name": "Es03020041_light_12", "parent": "Es03020041_gold2", "length": 10.97, "rotation": -117.07, "x": -12.04, "y": -9.61}, {"name": "Es03020041_light_13", "parent": "Es03020041_gold2", "length": 18.64, "rotation": -155.22, "x": -26.79, "y": -5.83}, {"name": "Es03020041_light_14", "parent": "Es03020041_gold2", "length": 15.33, "rotation": 153.07, "x": -18.11, "y": 8.92}, {"name": "Es03020041_light_3", "parent": "Es03020041_gold2", "x": 50.41, "y": -32.11}, {"name": "Es03020041_light_5", "parent": "Es03020041_gold2", "x": -45.91, "y": -29.78}, {"name": "Es03020041_light_2", "parent": "Es03020041_gold2", "x": 24.22, "y": 51.7}, {"name": "Es03020041_light_4", "parent": "Es03020041_gold2", "x": -33.71, "y": 38.59}, {"name": "Es03020041_light_6", "parent": "Es03020041_gold2", "x": -22.9, "y": -64.64}, {"name": "Es03020041_gold3", "parent": "Es03020041_shadow_2", "length": 5.94, "rotation": -63.7, "x": -0.09, "color": "000337ff"}, {"name": "Es03020041_yan_01", "parent": "Es03020041_gold3", "length": 7.41, "rotation": 35.85, "x": -27.17, "y": 23.61, "color": "000337ff"}, {"name": "Es03020041_yan_2", "parent": "Es03020041_yan_01", "x": 6.08, "y": 10.37, "color": "000337ff"}, {"name": "Es03020041_yan_3", "parent": "Es03020041_yan_01", "x": 26.6, "y": 19.28, "color": "000337ff"}, {"name": "Es03020041_yan_4", "parent": "Es03020041_yan_01", "x": 54.24, "y": 8.98, "color": "000337ff"}, {"name": "Es03020041_gold4", "parent": "Es03020041_gold3", "length": 8.98, "rotation": 107.34, "x": -32.46, "y": -13.36, "color": "000337ff"}, {"name": "Es03020041_yan_02", "parent": "Es03020041_gold4", "x": 6.84, "y": 4.62, "color": "000337ff"}, {"name": "Es03020041_yan_5", "parent": "Es03020041_gold4", "x": 42.5, "y": 2.22, "color": "000337ff"}, {"name": "Es03020041_yan_6", "parent": "Es03020041_gold4", "x": 62.58, "y": -20.77, "color": "000337ff"}, {"name": "Es03020041_yan_03", "parent": "Es03020041_gold3", "length": 12.78, "rotation": 164.43, "x": 2.36, "y": -39.03, "color": "000337ff"}, {"name": "Es03020041_yan_8", "parent": "Es03020041_yan_03", "x": 7.32, "y": 5.15, "color": "000337ff"}, {"name": "Es03020041_yan_9", "parent": "Es03020041_yan_03", "x": 33.34, "y": 15.69, "color": "000337ff"}, {"name": "Es03020041_yan_10", "parent": "Es03020041_yan_03", "x": 53.22, "y": 0.72, "color": "000337ff"}, {"name": "Es03020041_yan_04", "parent": "Es03020041_gold3", "length": 9, "rotation": 24.37, "x": 8.77, "y": -31.78, "color": "000337ff"}, {"name": "Es03020041_yan_11", "parent": "Es03020041_yan_04", "x": 1.21, "y": -6.76, "color": "000337ff"}, {"name": "Es03020041_yan_12", "parent": "Es03020041_yan_04", "x": -22.02, "y": -17.2, "color": "000337ff"}, {"name": "Es03020041_yan_13", "parent": "Es03020041_yan_04", "x": -47.51, "y": -9.65, "color": "000337ff"}, {"name": "Es03020041_yan_06", "parent": "Es03020041_gold3", "length": 8.94, "rotation": 46.96, "x": 18.09, "y": 26.68, "color": "000337ff"}, {"name": "Es03020041_yan_14", "parent": "Es03020041_yan_06", "x": 7.32, "y": -8.73, "color": "000337ff"}, {"name": "Es03020041_yan_15", "parent": "Es03020041_yan_06", "x": 7.4, "y": -30.94, "color": "000337ff"}, {"name": "Es03020041_yan_16", "parent": "Es03020041_yan_06", "x": -5.7, "y": -50.55, "color": "000337ff"}, {"name": "Es03020041_yan_17", "parent": "Es03020041_yan_06", "x": -35.9, "y": -58.02, "color": "000337ff"}, {"name": "Es03020041_yan_18", "parent": "Es03020041_yan_06", "x": 11.98, "y": -14.21, "color": "000337ff"}, {"name": "Es03020041_yan_19", "parent": "Es03020041_yan_06", "x": 15.53, "y": -39.49, "color": "000337ff"}, {"name": "Es03020041_yan_20", "parent": "Es03020041_yan_06", "x": 5.07, "y": -64.63, "color": "000337ff"}, {"name": "Es03020041_gold5", "parent": "Es03020041_gold3", "length": 8.46, "rotation": -1.3, "x": 4.13, "y": 35.94, "color": "000337ff"}, {"name": "Es03020041_yan_05", "parent": "Es03020041_gold5", "x": 6.56, "y": 3.84, "color": "000337ff"}, {"name": "Es03020041_yan_7", "parent": "Es03020041_gold5", "x": 21.91, "y": 9.61, "color": "000337ff"}, {"name": "Es03020041_yan_21", "parent": "Es03020041_gold5", "x": 38.99, "y": 0.64, "color": "000337ff"}], "slots": [{"name": "Es03020041_gold", "bone": "Es03020041_gold", "attachment": "Es03020041_gold"}, {"name": "Es03020041_yan_04", "bone": "Es03020041_yan_13", "attachment": "Es03020041_yan_04"}, {"name": "Es03020041_yan_03", "bone": "Es03020041_yan_10", "attachment": "Es03020041_yan_03"}, {"name": "Es03020041_yan_02", "bone": "Es03020041_yan_6", "attachment": "Es03020041_yan_02"}, {"name": "Es03020041_yan_01", "bone": "Es03020041_yan_4", "attachment": "Es03020041_yan_01"}, {"name": "Es03020041_yan_05", "bone": "Es03020041_yan_21", "attachment": "Es03020041_yan_05"}, {"name": "Es03020041_yan_06", "bone": "Es03020041_yan_17", "attachment": "Es03020041_yan_06"}, {"name": "Es03020041_yan_6", "bone": "Es03020041_yan_20", "attachment": "Es03020041_yan_06"}, {"name": "Es03020041_shadow_2", "bone": "Es03020041_shadow_2", "attachment": "Es03020041_shadow_2"}, {"name": "Es03020041_shadow", "bone": "Es03020041_shadow_2", "attachment": "Es03020041_shadow"}, {"name": "Es03020041_zxg", "bone": "Es03020041_zxg", "attachment": "Es03020041_zxg"}, {"name": "Es03020041_light_14", "bone": "Es03020041_light_14", "attachment": "Es03020041_light_14"}, {"name": "Es03020041_light_13", "bone": "Es03020041_light_13", "attachment": "Es03020041_light_13"}, {"name": "Es03020041_light_12", "bone": "Es03020041_light_12", "attachment": "Es03020041_light_12"}, {"name": "Es03020041_light_11", "bone": "Es03020041_light_11", "attachment": "Es03020041_light_11"}, {"name": "Es03020041_light_10", "bone": "Es03020041_light_10", "attachment": "Es03020041_light_10"}, {"name": "Es03020041_light_9", "bone": "Es03020041_light_9", "attachment": "Es03020041_light_9"}, {"name": "Es03020041_light_8", "bone": "Es03020041_light_8", "attachment": "Es03020041_light_8"}, {"name": "Es03020041_light_7", "bone": "Es03020041_light_7", "attachment": "Es03020041_light_7"}, {"name": "Es03020041_light_6", "bone": "Es03020041_light_6", "attachment": "Es03020041_light_6"}, {"name": "Es03020041_light_5", "bone": "Es03020041_light_5", "attachment": "Es03020041_light_5"}, {"name": "Es03020041_light_4", "bone": "Es03020041_light_4", "attachment": "Es03020041_light_4"}, {"name": "Es03020041_light_3", "bone": "Es03020041_light_3", "attachment": "Es03020041_light_3"}, {"name": "Es03020041_light_2", "bone": "Es03020041_light_2", "attachment": "Es03020041_light_2"}, {"name": "Es03020041_light", "bone": "Es03020041_zxg", "attachment": "Es03020041_light"}], "skins": [{"name": "default", "attachments": {"Es03020041_gold": {"Es03020041_gold": {"x": 15.8, "y": -0.85, "rotation": -0.72, "width": 557, "height": 153}}, "Es03020041_light": {"Es03020041_light": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [11.85, -12.41, -12.14, -12.1, -11.84, 11.89, 12.15, 11.59], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 24, "height": 24}}, "Es03020041_light_2": {"Es03020041_light_2": {"x": -0.21, "y": -0.43, "width": 15, "height": 7}}, "Es03020041_light_3": {"Es03020041_light_3": {"x": 0.1, "y": -0.12, "width": 12, "height": 10}}, "Es03020041_light_4": {"Es03020041_light_4": {"x": -0.27, "y": 0.18, "width": 5, "height": 4}}, "Es03020041_light_5": {"Es03020041_light_5": {"x": -0.07, "y": 0.05, "width": 9, "height": 7}}, "Es03020041_light_6": {"Es03020041_light_6": {"type": "mesh", "uvs": [1, 0.90355, 1, 0.96287, 0.88968, 0.96986, 0.80364, 1, 0.54156, 1, 0.55612, 0.90355], "triangles": [2, 5, 0, 1, 2, 0, 4, 5, 2, 3, 4, 2], "vertices": [5.42, 6.08, 5.42, -1.1, 2.66, -1.95, 0.51, -5.59, -6.04, -5.59, -5.68, 6.08], "hull": 6, "edges": [10, 0, 10, 8, 6, 8, 6, 4, 0, 2, 4, 2], "width": 25, "height": 121}}, "Es03020041_light_7": {"Es03020041_light_7": {"x": 18.5, "y": -0.42, "rotation": -69.75, "width": 20, "height": 43}}, "Es03020041_light_8": {"Es03020041_light_8": {"x": 6.78, "y": -0.31, "rotation": -53.29, "width": 12, "height": 16}}, "Es03020041_light_9": {"Es03020041_light_9": {"x": 25.83, "y": -0.33, "rotation": -25.66, "width": 50, "height": 26}}, "Es03020041_light_10": {"Es03020041_light_10": {"x": 24.8, "y": 1.2, "rotation": 19.78, "width": 50, "height": 18}}, "Es03020041_light_11": {"Es03020041_light_11": {"x": 8.36, "y": 0.23, "rotation": 66.37, "width": 8, "height": 17}}, "Es03020041_light_12": {"Es03020041_light_12": {"x": 14.6, "y": 1.35, "rotation": 117.07, "width": 18, "height": 30}}, "Es03020041_light_13": {"Es03020041_light_13": {"x": 16.44, "y": 0.56, "rotation": 155.22, "width": 34, "height": 18}}, "Es03020041_light_14": {"Es03020041_light_14": {"x": 12.33, "y": 0.83, "rotation": -153.07, "width": 28, "height": 16}}, "Es03020041_shadow": {"Es03020041_shadow": {"x": -0.64, "y": 0.57, "rotation": -48.08, "width": 52, "height": 54}}, "Es03020041_shadow_2": {"Es03020041_shadow_2": {"x": 1.14, "y": 0.83, "rotation": -48.08, "width": 82, "height": 77}}, "Es03020041_yan_01": {"Es03020041_yan_01": {"type": "mesh", "uvs": [0, 0.80835, 0, 0.92435, 0, 1, 0.05576, 1, 0.07589, 0.91635, 0.12481, 0.80178, 0.21296, 0.68809, 0.31361, 0.58698, 0.42264, 0.5292, 0.56941, 0.50753, 0.7078, 0.51476, 0.82103, 0.52198, 0.94054, 0.52559, 1, 0.56531, 1, 0.42448, 0.94893, 0.32698, 0.86506, 0.25476, 0.76651, 0.18253, 0.68264, 0.13559, 0.59038, 0.09948, 0.5107, 0.06337, 0.3807, 0.03087, 0.27587, 0.04531, 0.20667, 0.08865, 0.16264, 0.18615, 0.13119, 0.30531, 0.09345, 0.46059, 0.05571, 0.56531, 0.03054, 0.67725, 0.03308, 0.90407, 0.05579, 0.79162, 0.10263, 0.67673, 0.15089, 0.59851, 0.23747, 0.43718, 0.37231, 0.34429, 0.51424, 0.34674, 0.60709, 0.33861, 0.70077, 0.34838, 0.80722, 0.38505, 0.85832, 0.41194, 0.93212, 0.46327], "triangles": [12, 14, 13, 12, 11, 40, 11, 39, 40, 12, 40, 14, 10, 38, 11, 11, 38, 39, 9, 36, 10, 36, 37, 10, 10, 37, 38, 40, 15, 14, 40, 39, 15, 39, 16, 15, 39, 38, 16, 37, 17, 38, 38, 17, 16, 36, 18, 37, 37, 18, 17, 36, 19, 18, 7, 6, 33, 6, 32, 33, 32, 26, 33, 7, 34, 8, 7, 33, 34, 8, 35, 9, 8, 34, 35, 9, 35, 36, 26, 25, 33, 25, 24, 33, 24, 23, 33, 34, 23, 22, 34, 33, 23, 34, 20, 35, 35, 19, 36, 35, 20, 19, 34, 21, 20, 34, 22, 21, 2, 29, 3, 3, 29, 4, 2, 1, 29, 1, 0, 29, 29, 30, 4, 4, 30, 5, 29, 0, 30, 0, 28, 30, 30, 31, 5, 6, 31, 32, 6, 5, 31, 30, 28, 31, 28, 27, 31, 31, 27, 32, 27, 26, 32], "vertices": [2, 28, -4.2, 0.47, 0.97993, 29, -24.72, -8.45, 0.02007, 1, 28, -5.64, -3.45, 1, 1, 28, -6.59, -6.01, 1, 1, 28, -3.34, -7.2, 1, 2, 28, -1.13, -4.81, 0.98222, 29, -21.65, -13.72, 0.01778, 2, 28, 3.14, -1.99, 0.83901, 29, -17.38, -10.9, 0.16099, 3, 28, 9.69, -0.04, 0.5598, 29, -10.84, -8.95, 0.43882, 30, -38.48, 1.35, 0.00138, 3, 28, 16.8, 1.22, 0.24666, 29, -3.72, -7.69, 0.71529, 30, -31.36, 2.61, 0.03805, 3, 28, 23.86, 0.84, 0.05653, 29, 3.34, -8.08, 0.74632, 30, -24.3, 2.23, 0.19715, 3, 28, 32.67, -1.58, 0.00241, 29, 12.15, -10.49, 0.51616, 30, -15.49, -0.19, 0.48143, 2, 29, 20.11, -13.7, 0.22345, 30, -7.53, -3.4, 0.77655, 2, 29, 26.61, -16.37, 0.04922, 30, -1.03, -6.07, 0.95078, 2, 29, 33.52, -19.06, 0.00155, 30, 5.87, -8.75, 0.99845, 1, 30, 8.84, -11.37, 1, 1, 30, 10.59, -6.61, 1, 2, 29, 36.47, -12.53, 6e-05, 30, 8.83, -2.22, 0.99994, 2, 29, 32.49, -8.29, 0.02811, 30, 4.85, 2.02, 0.97189, 2, 29, 27.66, -3.74, 0.11926, 30, 0.02, 6.57, 0.88074, 2, 29, 23.36, -0.36, 0.29327, 30, -4.28, 9.95, 0.70673, 2, 29, 18.45, 2.84, 0.50464, 30, -9.19, 13.15, 0.49536, 2, 29, 14.26, 5.77, 0.72205, 30, -13.38, 16.08, 0.27795, 3, 28, 27.62, 18.57, 0.00125, 29, 7.1, 9.65, 0.87683, 30, -20.54, 19.96, 0.12192, 3, 28, 21.34, 20.33, 0.00961, 29, 0.82, 11.41, 0.9623, 30, -26.82, 21.72, 0.02809, 3, 28, 16.78, 20.35, 0.03588, 29, -3.74, 11.43, 0.9608, 30, -31.38, 21.74, 0.00332, 3, 28, 13, 18, 0.10363, 29, -7.52, 9.08, 0.89629, 30, -35.16, 19.39, 8e-05, 2, 28, 9.69, 14.65, 0.2647, 29, -10.83, 5.73, 0.7353, 2, 28, 5.56, 10.21, 0.49097, 29, -14.96, 1.29, 0.50903, 2, 28, 2.06, 7.48, 0.73524, 29, -18.46, -1.43, 0.26476, 2, 28, -0.79, 4.24, 0.89914, 29, -21.31, -4.68, 0.10086, 1, 28, -3.47, -3.48, 1, 1, 28, -0.75, -0.17, 1, 2, 28, 3.41, 2.71, 0.85019, 29, -17.11, -6.2, 0.14981, 2, 28, 7.19, 4.32, 0.6232, 29, -13.33, -4.6, 0.3768, 2, 28, 14.23, 7.91, 0.19338, 29, -6.29, -1, 0.80662, 2, 29, 2.71, -0.75, 0.95685, 30, -24.93, 9.55, 0.04315, 2, 29, 10.94, -3.88, 0.6732, 30, -16.7, 6.43, 0.3268, 2, 29, 16.44, -5.59, 0.42087, 30, -11.2, 4.71, 0.57913, 2, 29, 21.77, -7.93, 0.17082, 30, -5.87, 2.37, 0.82918, 2, 29, 27.51, -11.45, 0.00229, 30, -0.13, -1.15, 0.99771, 1, 30, 2.51, -3.15, 1, 1, 30, 6.16, -6.46, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 62, "height": 36}}, "Es03020041_yan_02": {"Es03020041_yan_02": {"type": "mesh", "uvs": [0.67369, 0, 0.50366, 0.05627, 0.37267, 0.12087, 0.27233, 0.20881, 0.18313, 0.30212, 0.0605, 0.37928, 0, 0.5226, 0, 0.64104, 0.03129, 0.73974, 0.10934, 0.84203, 0.26542, 0.93355, 0.4215, 0.982, 0.58874, 0.98918, 0.48561, 0.89048, 0.41314, 0.8169, 0.35461, 0.72359, 0.32117, 0.63566, 0.30723, 0.56388, 0.38527, 0.64643, 0.4438, 0.6536, 0.42429, 0.56388, 0.4438, 0.4921, 0.46889, 0.39799, 0.53299, 0.2975, 0.63055, 0.23828, 0.73646, 0.17188, 0.85954, 0.1155, 0.97939, 0.07423, 1, 0.03475, 0.98218, 0, 0.85118, 0, 0.30234, 0.85582, 0.23166, 0.74619, 0.18989, 0.60678, 0.27342, 0.45785, 0.33009, 0.32167, 0.46181, 0.21617, 0.59032, 0.13137, 0.74132, 0.07345, 0.87305, 0.03828], "triangles": [36, 37, 24, 24, 37, 25, 25, 38, 26, 25, 37, 38, 2, 1, 37, 37, 0, 38, 37, 1, 0, 26, 39, 27, 26, 38, 39, 27, 39, 28, 38, 30, 39, 38, 0, 30, 39, 29, 28, 39, 30, 29, 18, 20, 19, 20, 18, 17, 7, 6, 33, 33, 17, 16, 17, 33, 34, 20, 17, 21, 33, 6, 34, 17, 34, 21, 6, 5, 34, 21, 34, 22, 5, 4, 34, 34, 35, 22, 34, 4, 35, 22, 35, 23, 4, 3, 35, 35, 36, 23, 35, 3, 36, 23, 36, 24, 3, 2, 36, 36, 2, 37, 11, 13, 12, 11, 10, 13, 13, 10, 31, 13, 31, 14, 10, 9, 31, 9, 32, 31, 14, 32, 15, 14, 31, 32, 9, 8, 32, 8, 33, 32, 32, 16, 15, 32, 33, 16, 8, 7, 33], "vertices": [2, 33, 24.15, -17.94, 0.11948, 34, 4.07, 5.05, 0.88052, 2, 33, 20.29, -9.83, 0.31507, 34, 0.2, 13.16, 0.68493, 2, 33, 15.76, -3.54, 0.58189, 34, -4.33, 19.45, 0.41811, 3, 32, 45.14, -1.04, 0.00465, 33, 9.48, 1.37, 0.80641, 34, -10.61, 24.36, 0.18894, 3, 32, 38.45, 3.36, 0.04085, 33, 2.8, 5.76, 0.90796, 34, -17.29, 28.75, 0.05119, 3, 32, 32.99, 9.29, 0.15463, 33, -2.66, 11.69, 0.84355, 34, -22.75, 34.68, 0.00181, 2, 32, 22.62, 12.44, 0.34653, 33, -13.03, 14.85, 0.65347, 2, 32, 13.98, 12.7, 0.57528, 33, -21.68, 15.1, 0.42472, 2, 32, 6.73, 11.44, 0.78142, 33, -28.92, 13.85, 0.21858, 2, 32, -0.84, 8, 0.9182, 33, -36.49, 10.41, 0.0818, 2, 32, -7.74, 0.87, 0.98658, 33, -43.39, 3.27, 0.01342, 1, 32, -11.49, -6.36, 1, 1, 32, -12.25, -14.2, 1, 2, 32, -4.9, -9.57, 0.99871, 33, -40.56, -7.17, 0.00129, 2, 32, 0.57, -6.33, 0.95712, 33, -35.09, -3.92, 0.04288, 2, 32, 7.46, -3.78, 0.82916, 33, -28.2, -1.38, 0.17084, 2, 32, 13.92, -2.4, 0.59555, 33, -21.73, 0, 0.40445, 2, 32, 19.18, -1.9, 0.36719, 33, -16.48, 0.5, 0.63281, 2, 32, 13.05, -5.39, 0.22474, 33, -22.61, -2.98, 0.77526, 2, 32, 12.44, -8.12, 0.18851, 33, -23.21, -5.72, 0.81149, 2, 32, 19.01, -7.4, 0.16528, 33, -16.64, -5, 0.83472, 3, 32, 24.23, -8.48, 0.11085, 33, -11.43, -6.07, 0.8828, 34, -31.52, 16.92, 0.00636, 3, 32, 31.06, -9.86, 0.04864, 33, -4.6, -7.45, 0.86831, 34, -24.69, 15.53, 0.08306, 3, 32, 38.3, -13.09, 0.00848, 33, 2.64, -10.68, 0.73296, 34, -17.44, 12.3, 0.25856, 2, 33, 6.83, -15.4, 0.4726, 34, -13.26, 7.59, 0.5274, 2, 33, 11.52, -20.52, 0.21925, 34, -8.56, 2.47, 0.78075, 2, 33, 15.47, -26.42, 0.06142, 34, -4.62, -3.43, 0.93858, 2, 33, 18.31, -32.14, 0.00329, 34, -1.78, -9.15, 0.99671, 1, 34, 1.07, -10.21, 1, 1, 34, 3.63, -9.45, 1, 2, 33, 23.91, -26.28, 0.01713, 34, 3.82, -3.29, 0.98287, 1, 32, -2.12, -1.04, 1, 2, 32, 5.98, 2.04, 0.87839, 33, -29.67, 4.45, 0.12161, 2, 32, 16.21, 3.7, 0.51108, 33, -19.44, 6.11, 0.48892, 2, 32, 26.96, -0.55, 0.13168, 33, -8.69, 1.86, 0.86832, 2, 33, 1.17, -1.1, 0.97963, 34, -18.92, 21.89, 0.02037, 2, 33, 8.68, -7.52, 0.67035, 34, -11.41, 15.47, 0.32965, 2, 33, 14.69, -13.74, 0.30523, 34, -5.4, 9.25, 0.69477, 2, 33, 18.7, -20.96, 0.0374, 34, -1.39, 2.03, 0.9626, 1, 34, 0.99, -4.24, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60], "width": 47, "height": 73}}, "Es03020041_yan_03": {"Es03020041_yan_03": {"type": "mesh", "uvs": [0.08161, 0, 0.01941, 0.11471, 0.00757, 0.33105, 0.01053, 0.51805, 0.04014, 0.65738, 0.09345, 0.79305, 0.20599, 0.92505, 0.41033, 1, 0.62949, 1, 0.78349, 0.98005, 0.97895, 0.95071, 1, 0.78571, 0.81903, 0.77471, 0.65022, 0.71605, 0.51695, 0.62438, 0.40441, 0.51071, 0.31853, 0.36405, 0.29187, 0.19171, 0.28003, 0.03405, 0.21191, 0, 0.14336, 0.16887, 0.16874, 0.32916, 0.20682, 0.49887, 0.24744, 0.63401, 0.35913, 0.7503, 0.47844, 0.84773, 0.7069, 0.87601, 0.8922, 0.88544, 0.80843, 0.8823, 0.59013, 0.86973], "triangles": [4, 3, 22, 3, 21, 22, 3, 2, 21, 22, 21, 16, 21, 17, 16, 2, 20, 21, 2, 1, 20, 17, 20, 18, 18, 20, 19, 20, 17, 21, 1, 0, 20, 20, 0, 19, 8, 7, 29, 7, 25, 29, 6, 24, 7, 7, 24, 25, 6, 23, 24, 6, 5, 23, 29, 25, 13, 25, 14, 13, 25, 24, 14, 5, 4, 23, 24, 15, 14, 24, 23, 15, 4, 22, 23, 23, 22, 15, 22, 16, 15, 8, 26, 9, 8, 29, 26, 9, 27, 10, 9, 28, 27, 9, 26, 28, 10, 27, 11, 28, 12, 27, 27, 12, 11, 28, 26, 12, 29, 13, 26, 26, 13, 12], "vertices": [2, 37, 28.44, -20.59, 7e-05, 38, 8.57, -5.62, 0.99993, 2, 37, 28.71, -14.79, 0.03965, 38, 8.84, 0.18, 0.96035, 2, 37, 24.53, -6.7, 0.17277, 38, 4.66, 8.27, 0.82723, 2, 37, 20.33, -0.07, 0.38037, 38, 0.46, 14.91, 0.61963, 2, 37, 15.98, 4.14, 0.61353, 38, -3.89, 19.11, 0.38647, 3, 36, 36.68, 18.12, 5e-05, 37, 10.66, 7.58, 0.80345, 38, -9.21, 22.55, 0.1965, 3, 36, 28.81, 19.83, 0.03575, 37, 2.78, 9.29, 0.89341, 38, -17.09, 24.26, 0.07084, 3, 36, 18.09, 17.02, 0.20316, 37, -7.94, 6.48, 0.7866, 38, -27.81, 21.45, 0.01024, 2, 36, 8.34, 11.12, 0.48853, 37, -17.69, 0.58, 0.51147, 2, 36, 1.92, 6.25, 0.78617, 37, -24.1, -4.29, 0.21383, 2, 36, -6.13, -0.07, 0.95209, 37, -32.16, -10.61, 0.04791, 2, 36, -3.48, -6.56, 0.96952, 37, -29.5, -17.1, 0.03048, 2, 36, 4.81, -2.08, 0.79057, 37, -21.21, -12.62, 0.20943, 3, 36, 13.59, 0.36, 0.50207, 37, -12.43, -10.18, 0.48999, 38, -32.3, 4.79, 0.00794, 3, 36, 21.52, 0.65, 0.20296, 37, -4.51, -9.89, 0.71236, 38, -24.38, 5.08, 0.08468, 3, 36, 29, -0.4, 0.04857, 37, 2.97, -10.94, 0.64454, 38, -16.9, 4.03, 0.30689, 3, 36, 36.01, -3.36, 0.00374, 37, 9.98, -13.89, 0.37364, 38, -9.89, 1.08, 0.62262, 2, 37, 14.92, -19.37, 0.12079, 38, -4.95, -4.4, 0.87921, 2, 37, 18.88, -24.71, 0.00966, 38, -1, -9.74, 0.99034, 1, 38, 2.78, -9.13, 1, 1, 38, 2.15, -1.22, 1, 2, 37, 17.4, -11.11, 0.1351, 38, -2.47, 3.86, 0.8649, 2, 37, 12.02, -6.04, 0.48808, 38, -7.85, 8.93, 0.51192, 2, 37, 7.27, -2.28, 0.77074, 38, -12.6, 12.69, 0.22926, 3, 36, 25.8, 9.43, 0.00768, 37, -0.23, -1.11, 0.98536, 38, -20.1, 13.86, 0.00696, 2, 36, 18.37, 9.71, 0.17278, 37, -7.65, -0.83, 0.82722, 2, 36, 7.59, 4.58, 0.68311, 37, -18.43, -5.96, 0.31689, 1, 36, -0.85, -0.08, 1, 2, 36, 2.94, 2.07, 0.91071, 37, -23.08, -8.47, 0.08929, 2, 36, 12.92, 7.5, 0.4002, 37, -13.1, -3.04, 0.5998], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 52, "height": 42}}, "Es03020041_yan_04": {"Es03020041_yan_04": {"type": "mesh", "uvs": [0, 0.23272, 0.08481, 0.38006, 0.17841, 0.51005, 0.32094, 0.56205, 0.43155, 0.49706, 0.54643, 0.49706, 0.68683, 0.47106, 0.80808, 0.36272, 0.89742, 0.21972, 0.93997, 0.05939, 0.95912, 0, 1, 0, 1, 0.11572, 0.98677, 0.28906, 0.94848, 0.45372, 0.90168, 0.58805, 0.83786, 0.70939, 0.74426, 0.81339, 0.65704, 0.88705, 0.56344, 0.96939, 0.46346, 0.98239, 0.36774, 0.96072, 0.24648, 0.88272, 0.14225, 0.78305, 0.05077, 0.65305, 0, 0.50139, 0, 0.38439, 0.07694, 0.51876, 0.14649, 0.63265, 0.25831, 0.7132, 0.39058, 0.7632, 0.49802, 0.75765, 0.61802, 0.70209, 0.70983, 0.61013, 0.81074, 0.54625, 0.8871, 0.43236, 0.93237, 0.32828, 0.95555, 0.19495], "triangles": [34, 35, 15, 15, 35, 14, 34, 7, 35, 35, 36, 14, 14, 36, 13, 36, 35, 8, 35, 7, 8, 36, 37, 13, 36, 8, 37, 13, 37, 12, 8, 9, 37, 37, 9, 12, 9, 10, 12, 10, 11, 12, 21, 30, 20, 20, 31, 19, 20, 30, 31, 19, 32, 18, 19, 31, 32, 21, 22, 30, 18, 32, 17, 22, 29, 30, 32, 33, 17, 16, 33, 34, 16, 17, 33, 29, 3, 30, 30, 4, 31, 30, 3, 4, 31, 5, 32, 31, 4, 5, 16, 34, 15, 32, 6, 33, 32, 5, 6, 33, 6, 34, 6, 7, 34, 22, 23, 29, 23, 28, 29, 23, 24, 28, 28, 2, 29, 29, 2, 3, 24, 27, 28, 24, 25, 27, 28, 27, 2, 27, 1, 2, 1, 27, 26, 27, 25, 26, 26, 0, 1], "vertices": [1, 42, -3.85, 9.1, 1, 1, 42, 0.15, 4.46, 1, 2, 42, 4.71, 0.21, 0.89679, 41, -20.78, 7.76, 0.10321, 2, 42, 12.24, -2.37, 0.45213, 41, -13.24, 5.18, 0.54787, 2, 42, 18.52, -1.56, 0.1638, 41, -6.96, 5.99, 0.8362, 3, 42, 24.76, -2.52, 0.02029, 41, -0.72, 5.03, 0.97468, 40, -23.95, -5.41, 0.00503, 2, 41, 7.02, 4.55, 0.79784, 40, -16.21, -5.89, 0.20216, 2, 41, 14.06, 6.43, 0.40582, 40, -9.18, -4.01, 0.59418, 2, 41, 19.5, 9.5, 0.0827, 40, -3.73, -0.94, 0.9173, 1, 40, -0.76, 2.98, 1, 1, 40, 0.52, 4.4, 1, 1, 40, 2.75, 4.06, 1, 1, 40, 2.27, 0.97, 1, 2, 41, 24.07, 6.9, 0.04833, 40, 0.84, -3.54, 0.95167, 2, 41, 21.32, 2.82, 0.21469, 40, -1.92, -7.62, 0.78531, 2, 41, 18.22, -0.37, 0.39868, 40, -5.01, -10.81, 0.60132, 2, 41, 14.25, -3.07, 0.59537, 40, -8.98, -13.51, 0.40463, 2, 41, 8.74, -5.07, 0.8158, 40, -14.49, -15.51, 0.1842, 3, 42, 29.18, -13.86, 0.00034, 41, 3.69, -6.3, 0.95238, 40, -19.54, -16.74, 0.04728, 3, 42, 23.75, -15.27, 0.02861, 41, -1.73, -7.72, 0.97021, 40, -24.96, -18.16, 0.00118, 2, 42, 18.26, -14.78, 0.12673, 41, -7.22, -7.23, 0.87327, 2, 42, 13.15, -13.4, 0.28829, 41, -12.33, -5.85, 0.71171, 2, 42, 6.88, -10.31, 0.5822, 41, -18.61, -2.75, 0.4178, 2, 42, 1.62, -6.78, 0.85429, 41, -23.86, 0.78, 0.14571, 2, 42, -2.82, -2.54, 0.99498, 41, -28.3, 5.01, 0.00502, 1, 42, -4.96, 1.93, 1, 1, 42, -4.48, 5.05, 1, 1, 42, -0.85, 0.82, 1, 2, 42, 2.47, -2.8, 0.91294, 41, -23.02, 4.76, 0.08706, 2, 42, 8.21, -5.88, 0.6176, 41, -17.27, 1.67, 0.3824, 2, 42, 15.2, -8.32, 0.26697, 41, -10.28, -0.77, 0.73303, 2, 42, 21.06, -9.07, 0.07416, 41, -4.42, -1.52, 0.92584, 2, 41, 2.33, -1.04, 0.9673, 40, -20.9, -11.48, 0.0327, 2, 41, 7.7, 0.65, 0.79813, 40, -15.53, -9.79, 0.20187, 2, 41, 13.45, 1.51, 0.53867, 40, -9.78, -8.93, 0.46133, 2, 41, 18.07, 3.91, 0.28542, 40, -5.17, -6.53, 0.71458, 2, 41, 20.96, 6.31, 0.12234, 40, -2.28, -4.13, 0.87766, 2, 41, 22.76, 9.67, 0.01243, 40, -0.47, -0.77, 0.98757], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 55, "height": 27}}, "Es03020041_yan_05": {"Es03020041_yan_05": {"type": "mesh", "uvs": [0.0237, 0.38786, 0.1176, 0.32553, 0.26033, 0.23753, 0.40119, 0.29986, 0.5195, 0.45386, 0.6228, 0.62986, 0.70458, 0.80586, 0.77783, 0.97086, 0.8999, 0.99652, 1, 0.99652, 1, 0.87552, 0.91868, 0.75086, 0.85858, 0.53453, 0.79661, 0.35486, 0.76093, 0.14953, 0.67078, 0.04319, 0.54683, 0, 0.43227, 0, 0.32334, 0, 0.19563, 0, 0.10924, 0.03586, 0.02473, 0.15686, 0, 0.28886, 0.07413, 0.23847, 0.16171, 0.1679, 0.25486, 0.13805, 0.348, 0.13805, 0.44949, 0.16248, 0.54263, 0.22219, 0.62883, 0.33619, 0.67749, 0.42847, 0.72197, 0.53704, 0.77758, 0.66733, 0.82763, 0.7759, 0.88322, 0.88852], "triangles": [0, 23, 1, 0, 22, 23, 1, 24, 2, 1, 23, 24, 22, 21, 23, 23, 20, 24, 23, 21, 20, 24, 25, 2, 24, 19, 25, 24, 20, 19, 25, 19, 18, 5, 29, 30, 5, 4, 29, 4, 3, 28, 4, 28, 29, 28, 3, 27, 13, 30, 14, 30, 29, 14, 29, 15, 14, 29, 28, 15, 2, 26, 3, 3, 26, 27, 2, 25, 26, 28, 16, 15, 28, 27, 16, 26, 17, 27, 27, 17, 16, 25, 18, 26, 26, 18, 17, 8, 10, 9, 7, 34, 8, 8, 34, 10, 7, 33, 34, 7, 6, 33, 34, 11, 10, 34, 33, 11, 6, 32, 33, 6, 31, 32, 6, 5, 31, 11, 33, 12, 33, 32, 12, 32, 31, 12, 5, 30, 31, 31, 13, 12, 31, 30, 13], "vertices": [2, 53, -17.1, -9.9, 0.00407, 52, -1.75, -4.12, 0.99593, 2, 53, -13.8, -7.53, 0.13968, 52, 1.56, -1.75, 0.86032, 3, 54, -25.81, 4.91, 0.00068, 53, -8.74, -4.06, 0.43777, 52, 6.62, 1.72, 0.56155, 3, 54, -19.91, 5.34, 0.05277, 53, -2.83, -3.63, 0.71493, 52, 12.52, 2.15, 0.2323, 3, 54, -14.33, 3.66, 0.22862, 53, 2.75, -5.31, 0.7368, 52, 18.1, 0.47, 0.03458, 3, 54, -9.2, 1.36, 0.51681, 53, 7.88, -7.61, 0.48318, 52, 23.23, -1.84, 1e-05, 2, 54, -4.91, -1.2, 0.79713, 53, 12.16, -10.17, 0.20287, 2, 54, -1.03, -3.64, 0.95461, 53, 16.05, -12.61, 0.04539, 2, 54, 3.91, -2.7, 0.99908, 53, 20.99, -11.67, 0.00092, 1, 54, 7.84, -1.51, 1, 2, 54, 7.1, 0.92, 0.99806, 53, 24.18, -8.04, 0.00194, 2, 54, 3.15, 2.46, 0.93872, 53, 20.23, -6.51, 0.06128, 2, 54, -0.53, 6.09, 0.77917, 53, 16.55, -2.88, 0.22083, 2, 54, -4.06, 8.95, 0.54511, 53, 13.02, -0.01, 0.45489, 2, 54, -6.72, 12.65, 0.32329, 53, 10.36, 3.69, 0.67671, 3, 54, -10.9, 13.71, 0.15726, 53, 6.18, 4.75, 0.84262, 52, 21.53, 10.52, 0.00013, 3, 54, -16.03, 13.1, 0.05991, 53, 1.05, 4.13, 0.91027, 52, 16.4, 9.91, 0.02981, 3, 54, -20.52, 11.73, 0.00775, 53, -3.44, 2.77, 0.83793, 52, 11.91, 8.54, 0.15432, 2, 53, -7.72, 1.47, 0.5885, 52, 7.63, 7.24, 0.4115, 2, 53, -12.73, -0.06, 0.30377, 52, 2.63, 5.72, 0.69623, 2, 53, -15.9, -1.81, 0.09495, 52, -0.54, 3.97, 0.90505, 2, 53, -18.47, -5.25, 0.01892, 52, -3.12, 0.53, 0.98108, 1, 52, -3.28, -2.42, 1, 1, 52, -0.68, -0.52, 1, 2, 53, -13.03, -3.84, 0.11052, 52, 2.32, 1.94, 0.88948, 2, 53, -9.56, -2.12, 0.394, 52, 5.79, 3.65, 0.606, 2, 53, -5.91, -1.01, 0.71336, 52, 9.45, 4.76, 0.28664, 2, 53, -1.78, -0.29, 0.95317, 52, 13.58, 5.48, 0.04683, 2, 54, -14.84, 8.59, 0.05693, 53, 2.24, -0.38, 0.94307, 2, 54, -10.76, 7.33, 0.2526, 53, 6.32, -1.64, 0.7474, 2, 54, -8.28, 6.05, 0.42199, 53, 8.79, -2.91, 0.57801, 2, 54, -5.88, 4.4, 0.62104, 53, 11.2, -4.56, 0.37896, 2, 54, -2.9, 2.45, 0.85029, 53, 14.18, -6.52, 0.14971, 2, 54, -0.27, 0.87, 0.98236, 53, 16.81, -8.1, 0.01764, 1, 54, 2.6, -0.73, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 41, "height": 21}}, "Es03020041_yan_06": {"Es03020041_yan_06": {"type": "mesh", "uvs": [0.27655, 0.76585, 0.14094, 0.80901, 0, 0.79013, 0.00217, 0.87645, 0.09678, 0.98164, 0.25132, 1, 0.42163, 1, 0.58248, 0.97085, 0.67078, 0.86296, 0.67404, 0.82247, 0.7021, 0.76247, 0.7477, 0.68897, 0.74069, 0.61179, 0.71964, 0.51279, 0.6986, 0.43479, 0.67755, 0.35979, 0.64423, 0.28779, 0.60038, 0.19479, 0.53899, 0.11979, 0.48463, 0.04629, 0.4057, 0, 0.32678, 0, 0.28819, 0.02229, 0.3794, 0.06879, 0.44604, 0.13029, 0.49966, 0.20931, 0.53649, 0.28731, 0.55772, 0.3626, 0.53317, 0.4466, 0.50511, 0.5216, 0.47178, 0.5891, 0.43671, 0.6311, 0.34726, 0.6641, 0.31043, 0.7196, 0.1669, 0.90197, 0.34081, 0.87718, 0.48332, 0.78628, 0.58718, 0.6582, 0.60892, 0.52362, 0.61616, 0.39968, 0.59614, 0.2999, 0.56832, 0.22258, 0.51964, 0.15884, 0.4501, 0.06963, 0.38057, 0.03395], "triangles": [44, 21, 20, 43, 44, 20, 44, 22, 21, 43, 23, 44, 23, 22, 44, 20, 19, 43, 43, 19, 18, 24, 23, 43, 24, 43, 18, 42, 24, 18, 42, 18, 17, 25, 24, 42, 41, 25, 42, 17, 41, 42, 26, 25, 41, 41, 17, 16, 40, 26, 41, 16, 40, 41, 27, 26, 40, 15, 39, 40, 15, 40, 16, 27, 40, 39, 39, 15, 14, 28, 27, 39, 38, 28, 39, 38, 39, 14, 38, 14, 13, 29, 28, 38, 38, 30, 29, 37, 30, 38, 38, 13, 12, 37, 38, 12, 37, 12, 11, 10, 37, 11, 37, 31, 30, 36, 37, 10, 36, 31, 37, 32, 31, 36, 33, 32, 36, 9, 36, 10, 8, 36, 9, 7, 36, 8, 3, 2, 1, 36, 0, 33, 35, 0, 36, 34, 1, 0, 34, 0, 35, 3, 1, 34, 6, 35, 36, 4, 3, 34, 5, 34, 35, 4, 34, 5, 7, 6, 36, 5, 35, 6], "vertices": [2, 47, 7.38, 6.24, 0.78256, 46, -22.81, -1.23, 0.21744, 2, 47, -1.85, 8.02, 0.92432, 46, -32.05, 0.55, 0.07568, 2, 47, -8.93, 14.01, 0.99909, 46, -39.13, 6.54, 0.00091, 1, 47, -12.22, 8.33, 1, 2, 47, -11.12, -1.7, 0.99398, 46, -41.32, -9.16, 0.00602, 2, 47, -3.27, -8.11, 0.92022, 46, -33.47, -15.58, 0.07978, 2, 47, 6.19, -13.87, 0.77213, 46, -24.01, -21.33, 0.22787, 2, 47, 16.27, -17.41, 0.56522, 46, -13.93, -24.88, 0.43478, 2, 47, 25.44, -13.39, 0.40295, 46, -4.76, -20.86, 0.59705, 2, 47, 27.22, -10.87, 0.27022, 46, -2.98, -18.34, 0.72978, 3, 47, 31.15, -7.93, 0.16705, 46, 0.95, -15.39, 0.83269, 45, -12.16, -35.01, 0.00025, 3, 47, 36.58, -4.7, 0.07301, 46, 6.39, -12.16, 0.9185, 45, -6.72, -31.78, 0.00849, 3, 47, 39.24, 0.55, 0.0205, 46, 9.05, -6.91, 0.91065, 45, -4.06, -26.53, 0.06885, 3, 47, 41.99, 7.69, 0.00326, 46, 11.79, 0.22, 0.78236, 45, -1.32, -19.39, 0.21438, 2, 46, 13.7, 6, 0.54959, 45, 0.6, -13.62, 0.45041, 3, 46, 15.5, 11.58, 0.28899, 45, 2.39, -8.04, 0.71087, 44, 2.48, -30.24, 0.00014, 3, 46, 16.49, 17.38, 0.10143, 45, 3.39, -2.24, 0.84508, 44, 3.47, -24.44, 0.05349, 3, 46, 17.73, 24.9, 0.01236, 45, 4.63, 5.28, 0.75544, 44, 4.71, -16.93, 0.2322, 2, 45, 4.18, 12.22, 0.47241, 44, 4.27, -9.98, 0.52759, 2, 45, 4.07, 18.83, 0.19242, 44, 4.15, -3.37, 0.80758, 2, 45, 1.52, 24.51, 0.0378, 44, 1.6, 2.3, 0.9622, 1, 44, -2.78, 4.97, 1, 2, 45, -5.89, 27.03, 0.00421, 44, -5.8, 4.82, 0.99579, 2, 45, -2.66, 20.93, 0.10131, 44, -2.58, -1.28, 0.89869, 2, 45, -1.39, 14.68, 0.34705, 44, -1.31, -7.52, 0.65295, 3, 46, 11.57, 27.36, 0.00093, 45, -1.54, 7.74, 0.6694, 44, -1.45, -14.46, 0.32967, 3, 46, 10.53, 21.05, 0.07576, 45, -2.58, 1.43, 0.8308, 44, -2.49, -20.77, 0.09345, 3, 46, 8.73, 15.45, 0.28425, 45, -4.37, -4.17, 0.7099, 44, -4.29, -26.38, 0.00585, 3, 47, 34.25, 18.29, 0.0003, 46, 4.05, 10.82, 0.58094, 45, -9.06, -8.79, 0.41876, 3, 47, 29.73, 14.37, 0.02032, 46, -0.47, 6.9, 0.81766, 45, -13.58, -12.71, 0.16202, 3, 47, 25.21, 11.11, 0.08718, 46, -4.99, 3.65, 0.87563, 45, -18.09, -15.97, 0.03718, 3, 47, 21.6, 9.57, 0.22612, 46, -8.59, 2.11, 0.77211, 45, -21.7, -17.51, 0.00177, 2, 47, 15.33, 10.45, 0.39767, 46, -14.87, 2.99, 0.60233, 2, 47, 11.09, 8.09, 0.58938, 46, -19.1, 0.63, 0.41062, 1, 47, -4.08, 1.11, 1, 2, 47, 6.55, -3.16, 0.83664, 46, -23.64, -10.63, 0.16336, 2, 47, 18.06, -2.08, 0.42681, 46, -12.14, -9.54, 0.57319, 2, 47, 28.89, 2.73, 0.06495, 46, -1.31, -4.74, 0.93505, 2, 46, 5.21, 3.26, 0.84576, 45, -7.89, -16.35, 0.15424, 2, 46, 10.51, 11.06, 0.38814, 45, -2.59, -8.55, 0.61186, 2, 46, 13.35, 18.22, 0.02811, 45, 0.24, -1.4, 0.97189, 2, 45, 1.75, 4.56, 0.87574, 44, 1.83, -17.65, 0.12426, 2, 45, 1.56, 10.34, 0.57709, 44, 1.65, -11.86, 0.42291, 2, 45, 1.23, 18.49, 0.10601, 44, 1.32, -3.72, 0.89399, 1, 44, -1.14, 0.95, 1], "hull": 34, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 0, 66], "width": 65, "height": 76}}, "Es03020041_yan_6": {"Es03020041_yan_06": {"type": "mesh", "uvs": [0.54909, 0.04061, 0.64879, 0.08987, 0.74183, 0.1505, 0.89758, 0.3056, 0.95961, 0.40034, 1, 0.51781, 1, 0.59929, 0.95296, 0.73571, 0.91308, 0.83453, 0.85327, 0.91032, 0.75358, 1, 0.67618, 0.87675, 0.68403, 0.79488, 0.72797, 0.72777, 0.75464, 0.64725, 0.75307, 0.54659, 0.70522, 0.41053, 0.66538, 0.31446, 0.61813, 0.23801, 0.56706, 0.16593, 0.50449, 0.09604, 0.43553, 0.03488, 0.3768, 0, 0.44497, 0, 0.81937, 0.23008, 0.48845, 0.04465, 0.5306, 0.07255, 0.61506, 0.12243, 0.68275, 0.19942, 0.74429, 0.27837, 0.79506, 0.3697, 0.82737, 0.45522, 0.85045, 0.57042, 0.87352, 0.66779, 0.84275, 0.73956, 0.79814, 0.83956, 0.77352, 0.90798], "triangles": [19, 27, 28, 2, 27, 1, 19, 20, 27, 27, 20, 26, 1, 27, 0, 21, 25, 20, 20, 25, 26, 27, 26, 0, 26, 25, 0, 0, 25, 23, 25, 21, 23, 21, 22, 23, 15, 16, 31, 31, 4, 5, 16, 30, 31, 31, 30, 4, 16, 17, 30, 30, 3, 4, 17, 29, 30, 3, 29, 24, 3, 30, 29, 17, 18, 29, 18, 28, 29, 29, 28, 24, 18, 19, 28, 28, 2, 24, 3, 24, 2, 28, 27, 2, 10, 36, 9, 10, 11, 36, 36, 35, 9, 9, 35, 8, 36, 11, 35, 11, 12, 35, 12, 13, 35, 35, 34, 8, 35, 13, 34, 8, 34, 7, 13, 14, 34, 34, 33, 7, 34, 14, 33, 7, 33, 6, 14, 32, 33, 33, 32, 6, 14, 15, 32, 32, 5, 6, 15, 31, 32, 32, 31, 5], "vertices": [2, 49, -0.25, 25.57, 0.11053, 48, 3.29, 0.29, 0.88947, 2, 49, 3.34, 19.01, 0.3298, 48, 6.88, -6.27, 0.6702, 3, 50, 16.56, 37.07, 0.01713, 49, 6.11, 11.93, 0.70459, 48, 9.65, -13.35, 0.27828, 3, 50, 19.08, 21.73, 0.08968, 49, 8.63, -3.41, 0.80481, 48, 12.17, -28.69, 0.10551, 3, 50, 18.78, 13.49, 0.32484, 49, 8.33, -11.65, 0.67484, 48, 11.87, -36.93, 0.00032, 2, 50, 16.38, 4.49, 0.56967, 49, 5.93, -20.64, 0.43033, 2, 50, 13.16, -0.79, 0.80375, 49, 2.71, -25.93, 0.19625, 2, 50, 5.16, -8.06, 0.93156, 49, -5.29, -33.2, 0.06844, 2, 50, -0.96, -13.13, 0.99747, 49, -11.41, -38.27, 0.00253, 1, 50, -7.27, -16.03, 1, 1, 50, -16.35, -18.48, 1, 1, 50, -15.78, -7.86, 1, 1, 50, -12.11, -2.81, 1, 2, 50, -7.02, 0.06, 0.98428, 49, -17.47, -25.08, 0.01572, 2, 50, -2.35, 4.38, 0.8574, 49, -12.81, -20.76, 0.1426, 2, 50, 1.54, 10.97, 0.56282, 49, -8.92, -14.17, 0.43718, 3, 50, 4.25, 21.42, 0.24531, 49, -6.2, -3.72, 0.73013, 48, -2.66, -29, 0.02456, 3, 50, 5.84, 29.01, 0.03886, 49, -4.62, 3.87, 0.8259, 48, -1.07, -21.41, 0.13524, 3, 50, 6.24, 35.56, 0.0001, 49, -4.22, 10.43, 0.64007, 48, -0.67, -14.85, 0.35983, 2, 49, -4.21, 16.83, 0.34085, 48, -0.66, -8.45, 0.65915, 2, 49, -4.92, 23.48, 0.11821, 48, -1.37, -1.8, 0.88179, 2, 49, -6.33, 29.78, 0.00946, 48, -2.79, 4.5, 0.99054, 1, 48, -4.67, 8.75, 1, 2, 49, -4.43, 31.73, 0.00755, 48, -0.88, 6.45, 0.99245, 3, 50, 17.72, 29.28, 0.02284, 49, 7.27, 4.14, 0.83647, 48, 10.81, -21.14, 0.14068, 1, 48, -0.23, 2.08, 1, 2, 49, -2.54, 24.13, 0.02603, 48, 1, -1.15, 0.97397, 2, 49, 0.18, 18.03, 0.30039, 48, 3.72, -7.25, 0.69961, 2, 49, 0.89, 10.75, 0.66395, 48, 4.44, -14.53, 0.33605, 2, 49, 1.19, 3.54, 0.93138, 48, 4.74, -21.74, 0.06862, 2, 50, 10.86, 21.04, 0.08469, 49, 0.4, -4.1, 0.91531, 2, 50, 9.27, 14.39, 0.33622, 49, -1.18, -10.75, 0.66378, 2, 50, 6, 6.13, 0.73817, 49, -4.46, -19.01, 0.26184, 2, 50, 3.43, -0.97, 0.96667, 49, -7.02, -26.11, 0.03333, 1, 50, -1.11, -4.59, 1, 1, 50, -7.54, -9.57, 1, 1, 50, -11.61, -13.18, 1], "hull": 24, "edges": [44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 48, 48, 4, 4, 2, 2, 0, 44, 46, 0, 46, 4, 6], "width": 65, "height": 76}}, "Es03020041_zxg": {"Es03020041_zxg": {"x": -0.5, "y": -0.25, "rotation": -0.72, "width": 43, "height": 42}}}}], "animations": {"animation": {"slots": {"Es03020041_light_7": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "Es03020041_light_8": {"color": [{"time": 0.4333, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff"}]}, "Es03020041_light_9": {"color": [{"color": "ffffff66"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff66"}]}, "Es03020041_light_10": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}]}, "Es03020041_light_11": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00"}]}, "Es03020041_light_12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}]}, "Es03020041_light_13": {"color": [{"time": 0.2333, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00"}, {"time": 1.4, "color": "ffffffff"}]}, "Es03020041_light_14": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00"}]}, "Es03020041_yan_01": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "Es03020041_yan_02": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "Es03020041_yan_03": {"color": [{"time": 0.3, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.4667, "color": "ffffffff"}]}, "Es03020041_yan_04": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}, "Es03020041_yan_05": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}]}, "Es03020041_yan_06": {"color": [{"time": 0.2, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff"}]}, "Es03020041_yan_6": {"color": [{"color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}}, "bones": {"Es03020041_zxg": {"rotate": [{}, {"time": 0.4333, "angle": 90}, {"time": 0.8333, "angle": 180}, {"time": 1.2667, "angle": -90}, {"time": 1.6667}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.176, "y": 1.176, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "Es03020041_shadow_2": {"rotate": [{"angle": -47.36}, {"time": 0.4333, "angle": -137.36}, {"time": 0.8333, "angle": 132.64}, {"time": 1.2667, "angle": 42.64}, {"time": 1.6667, "angle": -47.36}]}, "Es03020041_light_7": {"translate": [{"x": -5.61, "y": -15.21, "curve": 0.25, "c3": 0}, {"time": 1.1667, "x": 12.31, "y": 33.37, "curve": "stepped"}, {"time": 1.6667}], "scale": [{"x": 0.04, "curve": 0.25, "c3": 0}, {"time": 1.4}]}, "Es03020041_light_8": {"translate": [{"x": 3.93, "y": 5.26, "curve": 0.107, "c2": 0.29, "c3": 0.107}, {"time": 0.9333, "x": 23.09, "y": 30.96, "curve": "stepped"}, {"time": 1.4333, "x": -11.17, "y": -14.98, "curve": 0.481, "c3": 0.769, "c4": 0.38}, {"time": 1.6667, "x": 3.93, "y": 5.26}], "scale": [{"x": 0.342, "curve": 0.121, "c2": 0.25, "c3": 0.083}, {"time": 1.1667, "curve": "stepped"}, {"time": 1.4333, "x": 0.016, "curve": 0.456, "c3": 0.759, "c4": 0.38}, {"time": 1.6667, "x": 0.342}]}, "Es03020041_light_9": {"translate": [{"x": 23.1, "y": 11.09, "curve": 0.22, "c2": 0.6, "c3": 0.535}, {"time": 0.3, "x": 24.01, "y": 11.55, "curve": "stepped"}, {"time": 0.8, "x": -9.24, "y": -5.54, "curve": 0.416, "c3": 0.214, "c4": 0.66}, {"time": 1.6667, "x": 23.1, "y": 11.09}], "scale": [{"x": 0.924, "curve": 0.159, "c2": 0.55, "c3": 0.442}, {"time": 0.5667, "curve": "stepped"}, {"time": 0.8, "x": 0.029, "curve": 0.518, "c3": 0.409, "c4": 0.56}, {"time": 1.6667, "x": 0.924}]}, "Es03020041_light_10": {"translate": [{"x": 23.26, "y": -8.36, "curve": 0.321, "c2": 0.66, "c3": 0.654}, {"time": 0.0333, "x": 23.37, "y": -8.4, "curve": "stepped"}, {"time": 0.5333, "x": -12.24, "y": 4.4, "curve": 0.25, "c3": 0}, {"time": 1.6667, "x": 23.26, "y": -8.36}], "scale": [{"x": 0.983, "curve": 0.25, "c2": 0.62, "c3": 0.574}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "x": 0.153, "curve": 0.25, "c3": 0}, {"time": 1.6667, "x": 0.983}]}, "Es03020041_light_11": {"translate": [{"x": 11.38, "y": -26.02, "curve": "stepped"}, {"time": 0.2333, "x": -9.9, "y": 22.62, "curve": 0.25, "c3": 0.042}, {"time": 1.4, "x": 11.38, "y": -26.02}], "scale": [{"curve": "stepped"}, {"time": 0.2333, "x": 0.523, "curve": 0.25, "c3": 0.042}, {"time": 1.6333}]}, "Es03020041_light_12": {"translate": [{"x": -12.17, "y": -23.81, "curve": 0.321, "c2": 0.66, "c3": 0.654}, {"time": 0.0333, "x": -12.23, "y": -23.92, "curve": "stepped"}, {"time": 0.5333, "x": 7.32, "y": 14.33, "curve": 0.265, "c3": 0.015, "c4": 0.95}, {"time": 1.6667, "x": -12.17, "y": -23.81}], "scale": [{"x": 0.981, "curve": 0.25, "c2": 0.62, "c3": 0.574}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5333, "x": 0.062, "curve": 0.366, "c3": 0.14, "c4": 0.72}, {"time": 1.6667, "x": 0.981}]}, "Es03020041_light_13": {"translate": [{"x": -8.15, "y": -3.76, "curve": 0.09, "c2": 0.43, "c3": 0.251}, {"time": 0.7333, "x": -17.87, "y": -8.25, "curve": "stepped"}, {"time": 1.2333, "x": 22.44, "y": 10.36, "curve": 0.576, "c3": 0.724, "c4": 0.44}, {"time": 1.6667, "x": -8.15, "y": -3.76}], "scale": [{"x": 0.694, "curve": 0.087, "c2": 0.38, "c3": 0.197}, {"time": 0.9667, "curve": "stepped"}, {"time": 1.2333, "x": 0.055, "curve": 0.551, "c3": 0.761, "c4": 0.42}, {"time": 1.6667, "x": 0.694}]}, "Es03020041_light_14": {"translate": [{"x": -35.86, "y": 18.21, "curve": "stepped"}, {"time": 0.4667, "x": 10.08, "y": -5.12, "curve": 0.25, "c3": 0}, {"time": 1.6333, "x": -35.86, "y": 18.21}], "scale": [{"x": 0.992, "curve": 0.271, "c2": 0.63, "c3": 0.6}, {"time": 0.2, "curve": "stepped"}, {"time": 0.4667, "x": 0.442, "curve": 0.333, "c3": 0.095, "c4": 0.78}, {"time": 1.6667, "x": 0.992}]}, "Es03020041_light_3": {"scale": [{"x": 0.273, "y": 0.273}, {"time": 0.0333, "x": 0.206, "y": 0.206}, {"time": 0.3, "x": 0.802, "y": 0.802}, {"time": 0.5667, "x": 0.206, "y": 0.206}, {"time": 0.8667, "x": 0.802, "y": 0.802}, {"time": 1.1333, "x": 0.206, "y": 0.206}, {"time": 1.4, "x": 0.802, "y": 0.802}, {"time": 1.6667, "x": 0.273, "y": 0.273}]}, "Es03020041_light_5": {"scale": [{"x": 0.653, "y": 0.653}, {"time": 0.2, "x": 0.206, "y": 0.206}, {"time": 0.5, "x": 0.802, "y": 0.802}, {"time": 0.7667, "x": 0.206, "y": 0.206}, {"time": 1.0333, "x": 0.802, "y": 0.802}, {"time": 1.3333, "x": 0.206, "y": 0.206}, {"time": 1.6, "x": 0.802, "y": 0.802}, {"time": 1.6667, "x": 0.653, "y": 0.653}]}, "Es03020041_light_2": {"scale": [{"x": 0.206, "y": 0.206}, {"time": 0.2667, "x": 0.802, "y": 0.802}, {"time": 0.5667, "x": 0.206, "y": 0.206}, {"time": 0.8333, "x": 0.802, "y": 0.802}, {"time": 1.1, "x": 0.206, "y": 0.206}, {"time": 1.4, "x": 0.802, "y": 0.802}, {"time": 1.6667, "x": 0.206, "y": 0.206}]}, "Es03020041_light_4": {"scale": [{"x": 0.728, "y": 0.728}, {"time": 0.2333, "x": 0.206, "y": 0.206}, {"time": 0.5, "x": 0.802, "y": 0.802}, {"time": 0.8, "x": 0.206, "y": 0.206}, {"time": 1.0667, "x": 0.802, "y": 0.802}, {"time": 1.3333, "x": 0.206, "y": 0.206}, {"time": 1.6333, "x": 0.802, "y": 0.802}, {"time": 1.6667, "x": 0.728, "y": 0.728}]}, "Es03020041_light_6": {"scale": [{"x": 0.728, "y": 0.728}, {"time": 0.0333, "x": 0.802, "y": 0.802}, {"time": 0.3, "x": 0.206, "y": 0.206}, {"time": 0.6, "x": 0.802, "y": 0.802}, {"time": 0.8667, "x": 0.206, "y": 0.206}, {"time": 1.1333, "x": 0.802, "y": 0.802}, {"time": 1.4333, "x": 0.206, "y": 0.206}, {"time": 1.6667, "x": 0.728, "y": 0.728}]}, "Es03020041_yan_2": {"translate": [{"x": -10.67, "y": -19.22, "curve": "stepped"}, {"time": 0.3333, "x": -10.67, "y": -19.22, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.15, "y": 4.68, "curve": "stepped"}, {"time": 1.6667, "x": -10.67, "y": -19.22}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "Es03020041_yan_3": {"translate": [{"x": -20.24, "y": -24.83, "curve": "stepped"}, {"time": 0.1667, "x": -20.24, "y": -24.83, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.15, "y": 4.68, "curve": "stepped"}, {"time": 1.6667, "x": -20.24, "y": -24.83}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "Es03020041_yan_4": {"translate": [{"x": -37.99, "y": -13.72, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.15, "y": 4.68, "curve": "stepped"}, {"time": 1.6667, "x": -37.99, "y": -13.72}], "scale": [{"x": 0, "y": 0, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "Es03020041_yan_02": {"rotate": [{"angle": -1.2, "curve": "stepped"}, {"time": 0.3333, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "angle": -1.2}], "translate": [{"x": -20.95, "y": -13.08, "curve": "stepped"}, {"time": 0.3333, "x": -20.95, "y": -13.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.43, "y": 15.54, "curve": "stepped"}, {"time": 1.6667, "x": -20.95, "y": -13.08}], "scale": [{"x": 0.072, "y": 0.072, "curve": "stepped"}, {"time": 0.3333, "x": 0.072, "y": 0.072, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.072, "y": 0.072}]}, "Es03020041_yan_5": {"rotate": [{"angle": -1.2, "curve": "stepped"}, {"time": 0.1667, "angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "angle": -1.2}], "translate": [{"x": -20.95, "y": -13.08, "curve": "stepped"}, {"time": 0.1667, "x": -20.95, "y": -13.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.43, "y": 15.54, "curve": "stepped"}, {"time": 1.6667, "x": -20.95, "y": -13.08}], "scale": [{"x": 0.072, "y": 0.072, "curve": "stepped"}, {"time": 0.1667, "x": 0.072, "y": 0.072, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.072, "y": 0.072}]}, "Es03020041_yan_6": {"rotate": [{"angle": -1.2, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "angle": -1.2}], "translate": [{"x": -20.95, "y": -13.08, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 9.43, "y": 15.54, "curve": "stepped"}, {"time": 1.6667, "x": -20.95, "y": -13.08}], "scale": [{"x": 0.072, "y": 0.072, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.072, "y": 0.072}]}, "Es03020041_yan_8": {"translate": [{"x": -15.33, "y": -26.15, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "x": 10.14, "y": 10.08, "curve": "stepped"}, {"time": 1.3, "x": -15.77, "y": -26.78, "curve": "stepped"}, {"time": 1.6333, "x": -15.77, "y": -26.78, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.6667, "x": -15.33, "y": -26.15}], "scale": [{"x": -0.551, "y": -0.551, "curve": 0.271, "c2": 0.09, "c3": 0.753}, {"time": 0.6333, "curve": "stepped"}, {"time": 1.3, "x": -0.578, "y": -0.578, "curve": "stepped"}, {"time": 1.6333, "x": -0.578, "y": -0.578, "curve": 0.318, "c3": 0.652, "c4": 0.34}, {"time": 1.6667, "x": -0.551, "y": -0.551}]}, "Es03020041_yan_9": {"translate": [{"x": -11.28, "y": -20.38, "curve": 0.331, "c2": 0.32, "c3": 0.758}, {"time": 0.6333, "x": 10.14, "y": 10.08, "curve": "stepped"}, {"time": 1.3, "x": -15.77, "y": -26.78, "curve": "stepped"}, {"time": 1.4667, "x": -15.77, "y": -26.78, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "x": -11.28, "y": -20.38}], "scale": [{"x": -0.304, "y": -0.304, "curve": 0.331, "c2": 0.32, "c3": 0.758}, {"time": 0.6333, "curve": "stepped"}, {"time": 1.3, "x": -0.578, "y": -0.578, "curve": "stepped"}, {"time": 1.4667, "x": -0.578, "y": -0.578, "curve": 0.277, "c3": 0.621, "c4": 0.4}, {"time": 1.6667, "x": -0.304, "y": -0.304}]}, "Es03020041_yan_10": {"translate": [{"x": -7.34, "y": -14.79, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "x": 10.14, "y": 10.08, "curve": "stepped"}, {"time": 1.3, "x": -15.77, "y": -26.78, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "x": -7.34, "y": -14.79}], "scale": [{"x": -0.065, "y": -0.065, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 0.6333, "curve": "stepped"}, {"time": 1.3, "x": -0.578, "y": -0.578, "curve": 0.261, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "x": -0.065, "y": -0.065}]}, "Es03020041_yan_11": {"translate": [{"x": -8.22, "y": -12.3, "curve": "stepped"}, {"time": 0.5, "x": 10.44, "y": 22.83, "curve": "stepped"}, {"time": 0.8333, "x": 10.44, "y": 22.83, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -8.22, "y": -12.3}], "scale": [{"curve": "stepped"}, {"time": 0.5, "x": 0.467, "y": 0.467, "curve": "stepped"}, {"time": 0.8333, "x": 0.467, "y": 0.467, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "Es03020041_yan_12": {"translate": [{"x": -8.22, "y": -12.3, "curve": "stepped"}, {"time": 0.5, "x": 23.6, "y": 28.45, "curve": "stepped"}, {"time": 0.6667, "x": 23.6, "y": 28.45, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -8.22, "y": -12.3}], "scale": [{"curve": "stepped"}, {"time": 0.5, "x": 0.467, "y": 0.467, "curve": "stepped"}, {"time": 0.6667, "x": 0.467, "y": 0.467, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "Es03020041_yan_13": {"translate": [{"x": -8.22, "y": -12.3, "curve": "stepped"}, {"time": 0.5, "x": 38.68, "y": 22.42, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": -8.22, "y": -12.3}], "scale": [{"curve": "stepped"}, {"time": 0.5, "x": 0.467, "y": 0.467, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "Es03020041_yan_14": {"translate": [{"x": -3.3, "y": 2.32, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "x": 8.3, "y": -14.38, "curve": "stepped"}, {"time": 1.2, "x": -13.02, "y": 16.31, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.6667, "x": -3.3, "y": 2.32}], "scale": [{"x": 0.482, "y": 0.481, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": "stepped"}, {"time": 1.2, "x": 0.048, "y": 0.046, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.6667, "x": 0.482, "y": 0.481}]}, "Es03020041_yan_15": {"translate": [{"x": -9.41, "y": 20.91, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "x": 8.3, "y": -14.38, "curve": "stepped"}, {"time": 1.2, "x": -12.06, "y": 26.18, "curve": "stepped"}, {"time": 1.5333, "x": -12.06, "y": 26.18, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "x": -9.41, "y": 20.91}], "scale": [{"x": 0.172, "y": 0.17, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.5333, "curve": "stepped"}, {"time": 1.2, "x": 0.048, "y": 0.046, "curve": "stepped"}, {"time": 1.5333, "x": 0.048, "y": 0.046, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 1.6667, "x": 0.172, "y": 0.17}]}, "Es03020041_yan_16": {"translate": [{"x": -4.24, "y": 20.59, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "x": 8.3, "y": -14.38, "curve": "stepped"}, {"time": 1.2, "x": -10.06, "y": 36.83, "curve": "stepped"}, {"time": 1.3667, "x": -10.06, "y": 36.83, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "x": -4.24, "y": 20.59}], "scale": [{"x": 0.35, "y": 0.349, "curve": 0.356, "c2": 0.42, "c3": 0.756}, {"time": 0.5333, "curve": "stepped"}, {"time": 1.2, "x": 0.048, "y": 0.046, "curve": "stepped"}, {"time": 1.3667, "x": 0.048, "y": 0.046, "curve": 0.262, "c3": 0.618, "c4": 0.44}, {"time": 1.6667, "x": 0.35, "y": 0.349}]}, "Es03020041_yan_17": {"translate": [{"x": 11.21, "y": 14.3, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "x": 8.3, "y": -14.38, "curve": "stepped"}, {"time": 1.2, "x": 13.66, "y": 38.33, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.6667, "x": 11.21, "y": 14.3}], "scale": [{"x": 0.482, "y": 0.481, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "curve": "stepped"}, {"time": 1.2, "x": 0.048, "y": 0.046, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1.6667, "x": 0.482, "y": 0.481}]}, "Es03020041_yan_18": {"translate": [{"x": -13.02, "y": 16.31, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.3, "y": -14.38, "curve": "stepped"}, {"time": 1.6667, "x": -13.02, "y": 16.31}], "scale": [{"x": 0.048, "y": 0.046, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.048, "y": 0.046}]}, "Es03020041_yan_19": {"translate": [{"x": -20, "y": 32.1, "curve": "stepped"}, {"time": 0.1667, "x": -20, "y": 32.1, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.3, "y": -14.38, "curve": "stepped"}, {"time": 1.6667, "x": -20, "y": 32.1}], "scale": [{"x": 0.048, "y": 0.046, "curve": "stepped"}, {"time": 0.1667, "x": 0.048, "y": 0.046, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.048, "y": 0.046}]}, "Es03020041_yan_20": {"translate": [{"x": -16.34, "y": 51.75, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 8.3, "y": -14.38, "curve": "stepped"}, {"time": 1.6667, "x": -13.74, "y": 44.15}], "scale": [{"x": 0.048, "y": 0.046, "curve": 0.25, "c3": 0.75}, {"time": 1, "curve": "stepped"}, {"time": 1.6667, "x": 0.048, "y": 0.046}]}, "Es03020041_yan_05": {"translate": [{"x": 12.35, "y": 7.2, "curve": "stepped"}, {"time": 0.5, "x": -13.95, "y": -12.39, "curve": "stepped"}, {"time": 0.8333, "x": -13.95, "y": -12.39, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 12.35, "y": 7.2}], "scale": [{"curve": "stepped"}, {"time": 0.5, "x": 0.027, "y": 0.027, "curve": "stepped"}, {"time": 0.8333, "x": 0.027, "y": 0.027, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "Es03020041_yan_7": {"translate": [{"x": 12.35, "y": 7.2, "curve": "stepped"}, {"time": 0.5, "x": -13.95, "y": -12.39, "curve": "stepped"}, {"time": 0.6667, "x": -13.95, "y": -12.39, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "x": 12.35, "y": 7.2}], "scale": [{"curve": "stepped"}, {"time": 0.5, "x": 0.027, "y": 0.027, "curve": "stepped"}, {"time": 0.6667, "x": 0.027, "y": 0.027, "curve": 0.25, "c3": 0.75}, {"time": 1.5}]}, "Es03020041_yan_21": {"translate": [{"x": 12.35, "y": 7.2, "curve": "stepped"}, {"time": 0.5, "x": -13.95, "y": -12.39, "curve": 0.25, "c3": 0.119, "c4": 1.04}, {"time": 1.5, "x": 12.35, "y": 7.2}], "scale": [{"curve": "stepped"}, {"time": 0.5, "x": 0.027, "y": 0.027, "curve": 0.25, "c3": 0.119, "c4": 1.04}, {"time": 1.5}]}, "shen": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 0.55, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}], "translate": [{"x": -0.03, "y": -3.3, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -6.94, "y": 0.07, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -0.03, "y": -3.3}]}, "shen4": {"translate": [{"x": 3.12, "y": 0.01, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 4.93, "y": 0.02, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1.6667, "x": 3.12, "y": 0.01}]}, "shen3": {"translate": [{"x": 3.18, "y": -0.67, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "x": 8.64, "y": -1.81, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1.6667, "x": 3.18, "y": -0.67}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "y": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "shen2": {"translate": [{"x": -0.75, "y": 0.56, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -5.75, "y": 4.29, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1.6667, "x": -0.75, "y": 0.56}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.01, "curve": 0.25, "c3": 0.75}, {"time": 1.6667}]}, "bi_U_L": {"rotate": [{"angle": -0.29, "curve": 0.373, "c2": 0.49, "c3": 0.751}, {"time": 0.4333, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 1.2667, "angle": 1.01, "curve": 0.251, "c3": 0.623, "c4": 0.49}, {"time": 1.6667, "angle": -0.29}]}, "bi_D_L": {"rotate": [{"angle": 0.93, "curve": 0.272, "c2": 0.12, "c3": 0.649, "c4": 0.6}, {"time": 0.4333, "angle": -0.73, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.7667, "angle": -1.74, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "angle": 1.01, "curve": 0.31, "c3": 0.645, "c4": 0.35}, {"time": 1.6667, "angle": 0.93}]}}}}}