[1, ["61NXJOyyZNU6ZGh0FJkMv2", "46o7YGu6lLj5YOxrz+/uqX@6c48a", "2enw6dg1JAa5xhWIDgaiWv", "7dTS9VmSNMGZ7bp+SaMzo7@c484d", "2enw6dg1JAa5xhWIDgaiWv@80081", "1dYX7BD/5GBpWb5pESWm/g@f9941", "7dTS9VmSNMGZ7bp+SaMzo7@00b27", "7dTS9VmSNMGZ7bp+SaMzo7@6bf39", "7dTS9VmSNMGZ7bp+SaMzo7@99161", "2enw6dg1JAa5xhWIDgaiWv@a0708", "2enw6dg1JAa5xhWIDgaiWv@4ddd4", "b7yA5VwgxLqpPr9XP5C2+t", "2enw6dg1JAa5xhWIDgaiWv@f6c2e", "7dTS9VmSNMGZ7bp+SaMzo7@4d0d2", "ecY0sTuzBDeIhktZfhZqkM@f9941", "0d2Rss5lBIELZgdjqjQQeK", "2enw6dg1JAa5xhWIDgaiWv@46320", "7dTS9VmSNMGZ7bp+SaMzo7@d6880", "1dYX7BD/5GBpWb5pESWm/g@6c48a", "ecY0sTuzBDeIhktZfhZqkM@6c48a"], ["node", "_spriteFrame", "targetInfo", "_font", "_textureSource", "target", "source", "_target", "root", "_atlas", "asset", "listBoss", "value", "data", "_parent", "_userDefinedFont"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_prefab", "_components", "_parent", "_children", "_lpos"], -1, 4, 9, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame", "_color", "_atlas"], 1, 1, 4, 6, 5, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_cacheMode", "_isUnderline", "_underlineHeight", "_outlineWidth", "node", "__prefab", "_font", "_color"], -8, 1, 4, 6, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "clickEvents"], 2, 1, 4, 5, 1, 9], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_lpos"], 2, 1, 2, 12, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["6ed15gRRYFOZ421TrCdyZTo", ["node", "__prefab", "listBoss"], 3, 1, 4, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.ClickEvent", [], 3], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingTop", "_spacingY", "node", "__prefab"], -1, 1, 4], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["2b852xrmS9HTZ4ufS6ZJDUB", ["node", "__prefab"], 3, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_isSystemFontUsed", "node", "__prefab", "_font", "_userDefinedFont"], -1, 1, 4, 6, 6], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["96925NpxVhGX5GHR06EhbFH", ["node", "__prefab", "tmpNode", "pageChangeEvent"], 3, 1, 4, 1, 4]], [[8, 0, 2], [12, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [15, 0, 2], [1, 0, 6, 5, 4, 8, 2], [26, 0, 1, 1], [2, 0, 2, 3, 4, 2], [18, 0, 1, 2, 2], [5, 0, 1, 2, 3, 1], [14, 0, 1, 2, 3, 4, 2], [17, 0, 1, 2, 3], [20, 0, 1, 2, 2], [1, 0, 6, 5, 4, 2], [1, 0, 6, 7, 5, 4, 8, 2], [1, 0, 3, 6, 5, 4, 8, 3], [10, 0, 1, 2, 3, 4, 4], [2, 0, 2, 3, 4, 6, 2], [4, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 5, 6, 11, 12, 14, 13, 8], [1, 1, 2, 6, 4, 3], [1, 0, 6, 7, 5, 4, 2], [13, 0, 1, 2, 3, 4, 5, 4], [2, 1, 0, 2, 3, 4, 3], [2, 2, 3, 4, 1], [4, 1, 2, 3, 4, 1], [16, 0, 1, 2, 2], [24, 1], [3, 0, 1, 2, 3, 4, 5, 6, 10, 11, 12, 13, 9], [6, 0, 2], [1, 0, 7, 5, 4, 2], [1, 0, 7, 5, 4, 8, 2], [7, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 1], [9, 0, 1, 2, 1], [11, 0, 1, 2, 3, 4, 5, 3], [2, 1, 0, 2, 3, 5, 4, 3], [4, 1, 2, 1], [4, 0, 1, 2, 5, 2], [19, 0, 1, 2, 3], [21, 0, 1, 2, 2], [22, 0, 1, 1], [23, 0, 1, 2, 1], [25, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 3, 4, 5, 8, 9, 7, 6, 11, 12, 14, 13, 11], [3, 0, 1, 2, 3, 4, 5, 7, 6, 11, 12, 13, 9], [3, 0, 1, 2, 3, 4, 5, 6, 11, 12, 13, 8], [27, 0, 1, 1], [28, 0, 1, 1], [29, 0, 1, 2, 3, 4, 5, 6, 7, 5], [30, 0, 1, 2, 3, 4, 5, 4], [31, 0, 1, 2, 3, 1]], [[[[28, "XiYouBossDlgUI"], [29, "XiYouBossDlgUI", [-18, -19, -20, -21, -22, -23], [[2, -14, [0, "fd9xMOlmZLnJnAoBBH+Y2g"], [5, 750, 1334]], [33, -16, [0, "2as+cHMZNJbbOYfqZTekzf"], -15], [15, 45, 750, 1334, -17, [0, "f5L3gaC8JM7L8NvHzSdy9U"]]], [34, "c46/YsCPVOJYA4mWEpNYRx", null, -13, 0, [[9, ["handle"], -4, [3, ["2dQ2wXCkFJzY5oPQvWqcNX"]], -3, [3, ["09JETm349J/q+uedF63ohs"]]], [9, ["txt_xhlabel"], -6, [3, ["f4kuyi9iVKF7T1eYAhBqGz"]], -5, [3, ["ceTv9iSLFGzJ0PBS37Ry58"]]], [9, ["icon_btn"], -8, [3, ["f4kuyi9iVKF7T1eYAhBqGz"]], -7, [3, ["78rquV0gBDjo8pdU7jR4Ud"]]], [9, ["img_icon"], -10, [3, ["f4kuyi9iVKF7T1eYAhBqGz"]], -9, [3, ["61JiN7oXNNPrJggtE71sdb"]]], [9, ["item_num"], -12, [3, ["f4kuyi9iVKF7T1eYAhBqGz"]], -11, [3, ["2eTrIr2/hNlLiCJSAJFt+D"]]]], [-1, -2]]], [30, "item", [-27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40], [[2, -24, [0, "584aCshFNFHqB0thT/Z2RJ"], [5, 653, 127]], [6, 0, -25, [0, "64tC/p4ARNEZtUUpQF6tqP"], 24], [36, -26, [0, "06UUgi6etEHYuOEPWIEzQx"]]], [1, "21VZSh33pDH7TDkdxqMuos", null, null, null, 1, 0], [1, -9.31, -73.5, 0]], [19, 0, {}, 2, [21, "90tZ6Q1OhAL4rJ1kNTaJFT", null, null, -46, [25, "5a9my3sgdE4pQWVe6IQ8l2", 1, [[10, "MoneyConsume", ["_name"], -41], [7, ["_lpos"], -42, [1, 218.658, 25.414, 0]], [7, ["_lrot"], -43, [3, 0, 0, 0, 1]], [7, ["_euler"], -44, [1, 0, 0, 0]], [38, 10002, ["consumType"], [3, ["f4kuyi9iVKF7T1eYAhBqGz"]]], [10, true, ["_active"], -45], [11, ["_lpos"], [3, ["1f1P/Fad9I3J64KBRIZlzx"]], [1, -40.375, 0, 0]], [11, ["_contentSize"], [3, ["d9hquB1jhA4Yd48Dt4PuY5"]], [5, 122.75, 30]]]], 21]], [12, "bgClose", 1, [[2, -47, [0, "4agUZQFs5H7qM6C5bffIhW"], [5, 750, 1334]], [35, 1, 0, -48, [0, "60HOHZ+3tCF5kY2ZJsbqu3"], [4, 3019898880], 0], [24, -50, [0, "631saGBcFD65l1Ud9iPcLK"], [4, 4292269782], -49], [15, 45, 39, 39, -51, [0, "3106bU34VGa4XMc/XGGx1H"]]], [1, "03X+FRS0NPArUVirdxU4Nl", null, null, null, 1, 0]], [31, "listBoss", 1, [-56], [[[8, -52, [0, "44svZzb3pIjJ3hreRk0l+Q"], [5, 682, 994], [0, 0.5, 1]], -53, -54, [22, 1, 0, -55, [0, "29qhl6VABExqqyzXg711MI"], 25]], 4, 1, 1, 4], [1, "baPg1g3axITJiVpOAl+/gs", null, null, null, 1, 0], [1, 0.176, 414.808, 0]], [20, "view", 5, [-61], [[8, -57, [0, "9bnJk7uNZBnZ55STVVv03e"], [5, 682, 994], [0, 0.5, 1]], [40, -58, [0, "142muNut1N25EXzsQBD9SE"]], [41, -59, [0, "f95Fv1BKdNhK/cstbVL+8e"], [4, 16777215]], [15, 45, 682, 800, -60, [0, "262pvESiJGx42V7eejitaG"]]], [1, "5b4glVtLdBqb52Rh+s0vc5", null, null, null, 1, 0]], [13, "btnHead", 2, [-64, -65, -66], [[32, -62, [0, "689Fms5/hIl6PRcnIUIKGi"]], [37, 3, -63, [0, "25EM5kIVFGg5yIR3FDMQUG"], [[26]]]], [1, "506+nidgBAApYLp3G7k9AG", null, null, null, 1, 0], [1, -260.81, 0.779, 0]], [13, "btnGo", 2, [-71], [[2, -67, [0, "78rE7jqQ5GNIWOw+SJbk2t"], [5, 131, 51]], [16, 2, -68, [0, "8eYLo4OjVKtY/d0bspBw48"], 14, 15], [17, 3, -70, [0, "1aIPgYM3JMvYrscagFdrPE"], [4, 4292269782], -69]], [1, "bbfMVjhe1JhZwfl0J7kWcl", null, null, null, 1, 0], [1, 84.89800000000002, -24.142000000000053, 0]], [13, "btnChuansong", 2, [-76], [[2, -72, [0, "c9RNzowh5J9qQ71l/CuqPV"], [5, 131, 51]], [16, 0, -73, [0, "33AZEstntAI4U+CycEGX/k"], 17, 18], [17, 3, -75, [0, "e3ZEk9sElCsIDKqpwfd58X"], [4, 4292269782], -74]], [1, "d3vuRX6UBLwKC9vpo1TFvi", null, null, null, 1, 0], [1, 231.159, -24.142000000000053, 0]], [20, "content", 6, [2], [[8, -77, [0, "eeScPVfMpMFqGwDbIn/LfH"], [5, 653, 137], [0, 0.5, 1]], [42, 1, 2, 10, 10, -78, [0, "ffrQSsV05Ji7PGDYid4GOp"]]], [1, "05cM6kn3ZDCq/p3+DeS0lt", null, null, null, 1, 0]], [4, "txtCdt", 2, [[2, -79, [0, "91PUVQ8o9GWJ/iCzDDuITX"], [5, 57.94599914550781, 26.68]], [18, "已刷新", 18, 18, 18, false, false, true, -80, [0, "1esh1QjXVAwLpYWZ5up1Xj"], [4, 4279627566], 12], [5, -81, [0, "e9NAR7qttLYKrXA3MVKBjF"]], [46, -82, [0, "6ciWUj8HBOIp1w2Ms2F2nR"]]], [1, "0ep0MgUtFI+Kgfhd8eStLF", null, null, null, 1, 0], [1, -16.173, 40.267, 0]], [3, ["90tZ6Q1OhAL4rJ1kNTaJFT"]], [14, "btnUpdate", false, 2, [[2, -83, [0, "26TyX6ZQtNJYPJl9V/zXol"], [5, 40, 38]], [16, 2, -84, [0, "793+wIZItCy4v2/ijUD8n5"], 22, 23], [17, 3, -86, [0, "8b1z1TP2xB2KLDmoqbVQf0"], [4, 4292269782], -85]], [1, "3bQDi0CE1E+6o4G4YAociL", null, null, null, 1, 0], [1, 72.556, 40, 0]], [19, 0, {}, 1, [21, "48QgggtnpApZxwpAfGYRjg", null, null, -94, [25, "27+hKg+51DWJGiOEtYSwss", 1, [[10, "MyScrollBar", ["_name"], -87], [7, ["_lpos"], -88, [1, 329.521, -82.135, 0]], [7, ["_lrot"], -89, [3, 0, 0, 0, 1]], [7, ["_euler"], -90, [1, 0, 0, 0]], [11, ["_contentSize"], [3, ["ae8XjF0uhCJI6F8w5MXn7m"]], [5, 6, 994]], [39, ["scrollView"], -92, -91], [10, false, ["autoHide"], -93], [11, ["_lpos"], [3, ["09JETm349J/q+uedF63ohs"]], [1, 0, -98.989, 0]]]], 26]], [4, "btnClose", 1, [[2, -95, [0, "beR4XxtudJQqWyc7irHx7o"], [5, 58, 57]], [6, 2, -96, [0, "daD8oA495MMZR4GOz/0vqt"], 27], [24, -98, [0, "a4ULpRpHdBNZVrWN9FQ57S"], [4, 4292269782], -97]], [1, "c1U73OVd5Pn4WIq6eXVqQb", null, null, null, 1, 0], [1, 346.801, 433.422, 0]], [4, "bg", 1, [[2, -99, [0, "d6QQGxRhxN0bLBddeWDM7j"], [5, 706, 1210]], [6, 2, -100, [0, "03h/JUSc9Nm6W4KauWmE1h"], 1], [47, -101, [0, "e3knOOIGpDFaOTYJ7uj9ur"]]], [1, "82G6Qmsx5LD4yFiUgz7ba6", null, null, null, 1, 0], [1, 0.08899999999999997, 10, 0]], [4, "txt_diao<PERSON>o", 7, [[2, -102, [0, "5bG16PJK1KRJF47MYQpLnL"], [5, 91.93399047851562, 34.24]], [43, "查看掉落", 22, 22, 24, false, false, true, 0, 1, true, -103, [0, "862ZRjIuJIurubeNExsyvW"], [4, 4283957233], 4], [5, -104, [0, "25rL6tG3lHcaKFHK6nVvCc"]]], [1, "def4CEY4lOjalDIDQ6zXQW", null, null, null, 1, 0], [1, 0, -39.358, 0]], [4, "txt_tuijian", 2, [[8, -105, [0, "94NRu2n2dCDbB+bFyL0tfG"], [5, 84.99998474121094, 35.5], [0, 1, 0.5]], [44, "推荐等级：", 18, 18, 25, false, false, 1, true, -106, [0, "85770ztq5K7oKVB1CTSbls"], 8], [5, -107, [0, "a3aiZOQQlKfIae5aaak2tt"]]], [1, "7dwDGQ/39GgpYjGts6efNh", null, null, null, 1, 0], [1, -119.83, -21.235, 0]], [4, "txt_needlv", 2, [[8, -108, [0, "045aTSjWNPn6sxcCG6HkjP"], [5, 10.803985595703125, 26.68], [0, 0, 0.5]], [18, "1", 18, 18, 18, false, false, true, -109, [0, "964P6XLZ1BrKuUQ1QFIwVt"], [4, 4279627566], 9], [5, -110, [0, "ce73kXPkBPuqH1wZAlJ/SA"]]], [1, "a9NWOkZ2ZPArdVUmLD0PQU", null, null, null, 1, 0], [1, -122.469, -20.304, 0]], [4, "txtLeader", 2, [[2, -111, [0, "bdkz3e5OtAebR0qhJyGpB2"], [5, 113.9119873046875, 35.5]], [18, "精英豺狼人", 22, 22, 25, false, false, true, -112, [0, "2czGTDdwZOdrbwjFV6YihJ"], [4, 4283957233], 10], [5, -113, [0, "1er4OSHEBEHpWuaojUPB81"]]], [1, "faS9aNXwtJjr0ux9SRanm7", null, null, null, 1, 0], [1, -149.877, 38.353, 0]], [4, "mapName", 2, [[2, -114, [0, "f6lldYEGFN45neG9QpP9OH"], [5, 75.98199462890625, 26.68]], [45, "暮色森林", 18, 18, 18, false, false, true, -115, [0, "d3Hc4zaZlMgq/HAjdAY9V7"], 11], [5, -116, [0, "93/2qcMblPUKeTmJZN+1dz"]]], [1, "e6foy7UwpEhKOLjOQx/gB+", null, null, null, 1, 0], [1, -134.897, 6.704, 0]], [12, "Label", 8, [[2, -117, [0, "6fvgv/chZBA6AjCDNcKWpI"], [5, 54, 37.5]], [27, "前往", 24, 24, 25, false, false, true, 3, -118, [0, "fb+uM77gtHu438kyK+C07M"], 13], [5, -119, [0, "c5N2CYHCJJDoleCX3LYcG6"]]], [1, "e7J5zYkYBJqanzvZikRUK2", null, null, null, 1, 0]], [12, "Label", 9, [[2, -120, [0, "57YVSbL51EjIaI0Nl/EasV"], [5, 53.97599792480469, 37.5]], [27, "传送", 24, 24, 25, false, false, true, 3, -121, [0, "faihu7v0ZAIYjBotccgh9n"], 16], [5, -122, [0, "ae0HSrybRLSIsfEEsDGdUZ"]]], [1, "2fG4YH6wxCU7go1ENI2Nsv", null, null, null, 1, 0]], [3, ["48QgggtnpApZxwpAfGYRjg"]], [4, "img_head", 7, [[2, -123, [0, "86jAnSgJ9E2pcTQghkAV9/"], [5, 80, 80]], [23, -124, [0, "5al1mpUrhPu7iUiKn8BlcP"], 2]], [1, "48SI2PcutPipO0+lxh6tGr", null, null, null, 1, 0], [1, 0, 14.331999999999994, 0]], [4, "btn_reward", 7, [[2, -125, [0, "adjuuMSFdC9YxItHzlbnIT"], [5, 34, 26]], [22, 1, 2, -126, [0, "08ZRie8lZBjLNQVeQ3c0zx"], 3]], [1, "99ZBLNcCpJ3YRVuCSPAmcX", null, null, null, 1, 0], [1, 31.132, -10.962, 0]], [4, "Sprite", 2, [[2, -127, [0, "2bUgSsDlREXJ5yIpN5TOgD"], [5, 29, 23]], [6, 2, -128, [0, "6aSZjspFBImZPZQYQt7KDx"], 5]], [1, "18CJbnLklC4r0EXVCi4+S+", null, null, null, 1, 0], [1, -192.916, 6.185, 0]], [4, "Sprite-001", 2, [[2, -129, [0, "1aJ0i9Sq1PxZTCbKJPy4gy"], [5, 97, 58]], [6, 2, -130, [0, "4cpDN66o9D+49W3BkOMrII"], 6]], [1, "c8ry4IC2NNAZAqkz7acsbl", null, null, null, 1, 0], [1, -16.724, 34.573, 0]], [14, "Sprite-002", false, 2, [[2, -131, [0, "f2F/7SLMNPQolbkCPT8+rX"], [5, 29, 23]], [6, 2, -132, [0, "8dOeLsyEZFTLn0EVgcV5Rl"], 7]], [1, "7eeKsFaqJNqZZhgJKRqNX9", null, null, null, 1, 0], [1, 204.726, 31.805, 0]], [14, "txt_show", false, 2, [[2, -133, [0, "dbX56l0BpNObT/atsXPTUD"], [5, 60.08393859863281, 22.68]], [48, 18, "<color=#ffffff>10/</color><color=#00ff00>100</color>", 18, false, -134, [0, "a5cSQllDRHSax3gAUCFkFw"], 19, 20]], [1, "c94YzuXZNIFZX0dTui9JRH", null, null, null, 1, 0], [1, 255.221, 28.284, 0]], [4, "Sprite", 1, [[2, -135, [0, "d3SZLtGBBOEJvbUgEHXcu2"], [5, 166, 44]], [23, -136, [0, "fe+Q7YYTFDA5aJH+Ze4F6A"], 28]], [1, "adES9krfVP6qmeVYPcJGac", null, null, null, 1, 0], [1, 9.556, 470.971, 0]], [49, 0.23, 0.75, false, 5, [0, "eaJJiCeLlAGKTxGpaDVy1U"], 10], [50, 5, [0, "70X3AOt09AgoQlowOSAkPB"], 2, [26]], [3, ["2dQ2wXCkFJzY5oPQvWqcNX"]]], 0, [0, -1, 14, 0, -2, 3, 0, 5, 14, 0, 6, 14, 0, 5, 3, 0, 6, 3, 0, 5, 3, 0, 6, 3, 0, 5, 3, 0, 6, 3, 0, 5, 3, 0, 6, 3, 0, 8, 1, 0, 0, 1, 0, 11, 33, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 16, 0, -3, 5, 0, -4, 14, 0, -5, 15, 0, -6, 31, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 27, 0, -3, 28, 0, -4, 29, 0, -5, 18, 0, -6, 19, 0, -7, 20, 0, -8, 21, 0, -9, 11, 0, -10, 8, 0, -11, 9, 0, -12, 30, 0, -13, 3, 0, -14, 13, 0, 2, 12, 0, 2, 12, 0, 2, 12, 0, 2, 12, 0, 2, 12, 0, 8, 3, 0, 0, 4, 0, 0, 4, 0, 7, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -2, 32, 0, -3, 33, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 10, 0, 0, 7, 0, 0, 7, 0, -1, 25, 0, -2, 26, 0, -3, 17, 0, 0, 8, 0, 0, 8, 0, 7, 8, 0, 0, 8, 0, -1, 22, 0, 0, 9, 0, 0, 9, 0, 7, 9, 0, 0, 9, 0, -1, 23, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 13, 0, 0, 13, 0, 7, 13, 0, 0, 13, 0, 2, 24, 0, 2, 24, 0, 2, 24, 0, 2, 24, 0, 12, 32, 0, 2, 34, 0, 2, 34, 0, 8, 14, 0, 0, 15, 0, 0, 15, 0, 7, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 13, 1, 2, 14, 10, 136], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 3, 1, 1, 1, 3, 3, 3, 3, 3, 3, 1, 9, 3, 1, 9, 3, 15, 10, 1, 9, 1, 1, 10, 1, 1], [4, 5, 6, 7, 0, 3, 8, 3, 0, 0, 0, 0, 0, 0, 9, 2, 0, 10, 2, 0, 0, 11, 12, 2, 13, 14, 15, 16, 17]], [[{"name": "xiyoujingying_bg_big", "rect": {"x": 0, "y": 0, "width": 706, "height": 1210}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 706, "height": 1210}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-353, -605, 0, 353, -605, 0, -353, 605, 0, 353, 605, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1210, 706, 1210, 0, 0, 706, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -353, "y": -605, "z": 0}, "maxPos": {"x": 353, "y": 605, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [18]], [[{"name": "xiyoujingying_touxiang_01", "rect": {"x": 748, "y": 2, "width": 80, "height": 80}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 80, "height": 80}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "xiyoujingying_itembg", "rect": {"x": 2, "y": 2, "width": 682, "height": 124}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 682, "height": 124}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "xiyoujingying_icon_02", "rect": {"x": 748, "y": 85, "width": 34, "height": 26}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 34, "height": 26}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "xiyou<PERSON><PERSON><PERSON>_shuxin_bg", "rect": {"x": 687, "y": 2, "width": 97, "height": 58}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 97, "height": 58}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "xiyoujingying_icon_01", "rect": {"x": 687, "y": 102, "width": 29, "height": 23}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 29, "height": 23}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "xiyoujingying_biaoti_01", "rect": {"x": 831, "y": 2, "width": 166, "height": 44}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 166, "height": 44}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [1]], [[{"name": "pub_bg2", "rect": {"x": 0, "y": 0, "width": 418, "height": 306}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 418, "height": 306}, "rotated": false, "capInsets": [0, 152, 0, 116], "vertices": {"rawPosition": [-209, -153, 0, 209, -153, 0, -209, 153, 0, 209, 153, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 306, 418, 306, 0, 0, 418, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -209, "y": -153, "z": 0}, "maxPos": {"x": 209, "y": 153, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [19]]]]