[1, ["4fS7IiG/dIALymBg6P5Ror@6c48a", "61NXJOyyZNU6ZGh0FJkMv2", "59DIuZ4bpO6Lm0t9QRJPP7@6c48a", "94FGSZedBCE46Kdw4aaHkw@f9941", "59DIuZ4bpO6Lm0t9QRJPP7@f9941", "2enw6dg1JAa5xhWIDgaiWv@46320", "2enw6dg1JAa5xhWIDgaiWv@81fd7", "fdx7mUEbFH9K5L18qmMZk1@d22bc", "fdx7mUEbFH9K5L18qmMZk1@26d25", "2enw6dg1JAa5xhWIDgaiWv@71bc8", "fdx7mUEbFH9K5L18qmMZk1@5712f", "fdx7mUEbFH9K5L18qmMZk1@09058", "1eUO2ZVahI3rjY1B9HHC3d", "fdx7mUEbFH9K5L18qmMZk1@c8aba", "2enw6dg1JAa5xhWIDgaiWv@4ddd4", "fdx7mUEbFH9K5L18qmMZk1@a6e32", "2enw6dg1JAa5xhWIDgaiWv@b2b8c", "fdx7mUEbFH9K5L18qmMZk1@43c4b", "fdx7mUEbFH9K5L18qmMZk1", "fdx7mUEbFH9K5L18qmMZk1@bff88", "2enw6dg1JAa5xhWIDgaiWv@9503f", "fdx7mUEbFH9K5L18qmMZk1@6d055", "16mojGSc1Nvrb/YTBQYkJg@f9941", "2enw6dg1JAa5xhWIDgaiWv@3abb2"], ["node", "_spriteFrame", "_textureSource", "_font", "targetInfo", "_target", "_parent", "root", "target", "source", "btnAddTime", "hangupTimeLimit", "bagSpine", "bagBtn", "txtNum", "listBoss", "_content", "_checkMark", "data", "asset", "_atlas"], ["cc.SpriteFrame", ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_enableOutline", "_enableWrapText", "_cacheMode", "_outlineWidth", "_isUnderline", "node", "__prefab", "_font", "_color"], -7, 1, 4, 6, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos"], 0, 4, 9, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_color", "_atlas"], 0, 1, 4, 6, 5, 6], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_target", "clickEvents", "_normalColor"], 2, 1, 4, 1, 9, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_lpos", "_children"], 2, 1, 12, 4, 5, 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_alignMode", "node", "__prefab"], -1, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "_paddingLeft", "_paddingTop", "_spacingY", "_isAlign", "node", "__prefab"], -4, 1, 4], "cc.SpriteAtlas", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["152d7fd8yxC4o9NN8M4lTIu", ["node", "__prefab", "listBoss", "txtNum", "bagBtn", "bagSpine", "hangupTimeLimit", "btnAddTime"], 3, 1, 4, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["f51falwV8dEWKPE+viecXlo", ["node", "__prefab"], 3, 1, 4], ["<PERSON><PERSON>", ["bounceDuration", "brake", "elastic", "horizontal", "node", "__prefab", "_content"], -1, 1, 4, 1], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Toggle", ["node", "__prefab", "_normalColor", "_target", "_checkMark"], 3, 1, 4, 5, 1, 1], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["2b852xrmS9HTZ4ufS6ZJDUB", ["node", "__prefab"], 3, 1, 4], ["79625nJPs5FErtP6kcjZWHi", ["node", "__prefab"], 3, 1, 4], ["96925NpxVhGX5GHR06EhbFH", ["_virtual", "frameByFrameRenderNum", "selectedMode", "node", "__prefab", "tmpNode", "pageChangeEvent"], 0, 1, 4, 1, 4]], [[12, 0, 2], [15, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [2, 0, 5, 4, 3, 7, 2], [25, 0, 1, 1], [3, 3, 4, 5, 1], [5, 0, 1, 2, 3, 1], [8, 0, 1, 2, 3], [3, 0, 3, 4, 5, 2], [4, 0, 1, 2, 4, 2], [6, 0, 1, 2, 3, 4, 2], [18, 0, 2], [2, 0, 6, 4, 3, 7, 2], [2, 0, 5, 6, 4, 3, 7, 2], [2, 0, 5, 4, 3, 2], [1, 0, 1, 2, 3, 6, 4, 7, 5, 8, 10, 11, 12, 10], [27, 0, 1, 2, 3], [28, 0, 1, 2, 2], [2, 0, 5, 6, 4, 3, 2], [7, 0, 1, 2, 4, 5, 4], [24, 0, 1, 1], [1, 0, 1, 2, 3, 4, 5, 10, 11, 13, 12, 7], [1, 0, 1, 2, 3, 6, 4, 7, 5, 10, 11, 12, 9], [11, 0, 2], [2, 0, 6, 4, 3, 2], [2, 1, 2, 5, 3, 3], [6, 0, 1, 5, 2, 3, 4, 2], [5, 0, 1, 1], [7, 0, 1, 2, 3, 4, 5, 5], [13, 0, 1, 2, 3, 4, 5, 6, 7, 1], [14, 0, 1, 2, 3, 4, 5, 3], [16, 0, 1, 2, 3, 4, 5, 4], [17, 0, 1, 2, 3, 4, 2], [3, 1, 0, 3, 4, 5, 3], [3, 0, 3, 4, 6, 5, 2], [3, 3, 4, 5, 7, 1], [3, 0, 2, 3, 4, 5, 3], [3, 0, 3, 4, 2], [19, 0, 1, 1], [4, 1, 2, 3, 1], [4, 0, 1, 2, 4, 5, 3, 2], [4, 0, 1, 2, 5, 3, 2], [4, 1, 2, 1], [8, 1], [20, 0, 1, 2, 3, 4, 5, 6, 5], [21, 0, 1, 1], [22, 0, 1, 2, 1], [23, 0, 1, 2, 3, 4, 1], [1, 0, 1, 2, 3, 6, 4, 9, 7, 5, 10, 11, 13, 12, 10], [1, 0, 1, 2, 3, 4, 7, 5, 10, 11, 12, 8], [1, 0, 1, 2, 3, 6, 4, 5, 10, 11, 13, 12, 8], [1, 0, 1, 2, 3, 6, 4, 5, 10, 11, 12, 8], [1, 0, 1, 2, 3, 4, 5, 10, 11, 7], [1, 0, 1, 2, 3, 6, 4, 5, 8, 10, 11, 9], [9, 0, 1, 2, 7, 8, 4], [9, 0, 1, 3, 4, 2, 5, 6, 7, 8, 8], [26, 0, 1, 2, 2], [29, 0, 1, 1], [30, 0, 1, 1], [31, 0, 1, 2, 3, 4, 5, 6, 4]], [[[{"name": "tuogaun_bg", "rect": {"x": 0, "y": 0, "width": 706, "height": 1171}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 706, "height": 1171}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-353, -585.5, 0, 353, -585.5, 0, -353, 585.5, 0, 353, 585.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1171, 706, 1171, 0, 0, 706, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -353, "y": -585.5, "z": 0}, "maxPos": {"x": 353, "y": 585.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [2]], [[[23, "TuoGuanGuaJiUI"], [24, "TuoGuanGuaJiUI", [-14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24], [[2, -5, [0, "a95eAbSRlBGo0VhzlO9UeX"], [5, 750, 1334]], [19, 45, 100, 100, -6, [0, "d8bY7GeKlDm78+atdhhFs5"]], [29, -13, [0, "c1grg6YuBMRZbCxCLRiX+w"], -12, -11, -10, -9, -8, -7]], [30, "c46/YsCPVOJYA4mWEpNYRx", null, -4, 0, [[32, ["redNode"], -3, [11, ["8fN27e90VKwrOm0UkkXWci"]], -2, [11, ["75lOaODvdAG7/FKF89g08B"]]]], [-1]]], [12, "item", [-30, -31, -32, -33, -34, -35, -36, -37, -38, -39], [[2, -25, [0, "817ZB0DJ5DMIsYdXPauZTS"], [5, 319, 134]], [8, 2, -26, [0, "55CPnalLBMx7ogbV/o3Jz/"], 29], [38, -27, [0, "95z/coy4VBj6twHn1K7ucY"]], [39, -29, [0, "83ALGOV7hNlYvCTTHoPULg"], -28]], [1, "58Usnr/XFNt5vY/JJAhEw9", null, null, null, 1, 0], [1, -162.5, -74, 0]], [12, "btnAddTime", [-43, -44, -45, -46], [[2, -40, [0, "426ZPFsvBLQ52Za2PV3mRf"], [5, 157, 60]], [5, -41, [0, "b6L0s1rTFLnLlM8gVuUnuI"], 15], [9, 3, -42, [0, "84fhKMoahGr6pPbYM+Rpmb"], [[7, "152d7fd8yxC4o9NN8M4lTIu", "onOpenAddTimeDlg", 1]]]], [1, "82mWmF7VhEXZ7gbQAu9jta", null, null, null, 1, 0], [1, -103.5, -4.859000000000009, 0]], [13, "bottomNode", 1, [-48, -49, -50, -51, -52, -53], [[27, -47, [0, "a6dbmqC/hD4KCOM6+iFcTf"]]], [1, "f3tTJEzndEG62Bq3R1XuXl", null, null, null, 1, 0], [1, 0, -353, 0]], [12, "btnGo", [-58], [[2, -54, [0, "8ek0g3SblMFpQkvPRFPLb+"], [5, 157, 60]], [8, 2, -55, [0, "67RNBvBwlPbqiyqheloNeK"], 17], [40, 3, -57, [0, "54YQjsf6hHRp97fC6INXl0"], [[7, "152d7fd8yxC4o9NN8M4lTIu", "onGoClick", 1]], [4, 4292269782], -56]], [1, "40iX3K5RpH8Kygs1UH6pSz", null, null, null, 1, 0], [1, 103.5, -4.8589999999999804, 0]], [26, "listBoss", 1, [-64], [[[6, -59, [0, "47X5/ixMNBTpJ4d7cVJ8Bm"], [5, 674, 694.872], [0, 0.5, 1]], [44, 0.23, 0.75, false, false, -61, [0, "d7Q0F2KQpGorqvR17gqLv8"], -60], -62, [33, 1, 0, -63, [0, "ceUiepguRL/YhGs5Mvecm9"], 30]], 4, 4, 1, 4], [1, "97G96q/qVBlZ1q7Oi0Po/a", null, null, null, 1, 0], [1, 0, 340.174, 0]], [18, "view", 6, [-69], [[6, -65, [0, "52jP5F9kRML7s+wK8maT9q"], [5, 674, 694.872], [0, 0.5, 1]], [45, -66, [0, "733oiNwTRGd5HKh1mtj79F"]], [46, -67, [0, "38cZmKwbhEfYbuSABG3otJ"], [4, 16777215]], [28, 45, 760, 300, 0, -68, [0, "58gan007xA/pLMNE7+e2lU"]]], [1, "c2XhKqcllLs7Zxvf//I0r1", null, null, null, 1, 0]], [13, "Toggle", 2, [-74, -75], [[2, -70, [0, "61OYeNaiFHgIChyeVxqz1T"], [5, 45, 35]], [47, -73, [0, "e5xQrFrItKa5c/r9PoGnZg"], [4, 4292269782], -72, -71]], [1, "11Yw8aoadEEqk7BeLbVwxM", null, null, null, 1, 0], [1, 120.786, -42.272, 0]], [14, "bgClose", 1, [[2, -76, [0, "855Clw4CZKErl89Oqk+SxM"], [5, 750, 1334]], [34, 0, -77, [0, "58ZaIRknJDE7qI4YaH/4Ma"], [4, 3019898880], 0], [19, 45, 39, 39, -78, [0, "9aOFJPt3FMfaGGgsrl3hMe"]], [20, -79, [0, "0ddlHU4ypHlIohMQ6Z1d64"]]], [1, "a3ddQ+WspDLK50cpQDEnX6", null, null, null, 1, 0]], [3, "btnClose", 1, [[2, -80, [0, "70Jf0ATJBAPKBC6LgcrY2b"], [5, 58, 57]], [8, 2, -81, [0, "a31HN1ifNE7JlcnWcLazwk"], 2], [41, 3, -83, [0, "fdfHI2E/xJeYidB9UCT/+Y"], [4, 4292269782], -82]], [1, "b4UXeJ/tRFjI0FSdkjUggS", null, null, null, 1, 0], [1, 346.749, 410.36, 0]], [3, "allChange", 4, [[2, -84, [0, "b4SyiCuCVPsa1OPHTM3adX"], [5, 84.01998901367188, 35.5]], [48, "一键勾选", 20, 20, 25, false, false, true, 1, true, -85, [0, "98wxS9jqZEPK+gH/KdzpwX"], [4, 4279627566], 9], [4, -86, [0, "67ut9BepVApZJwXlMwt1gL"]], [9, 3, -87, [0, "ac0F2V261JA7oK1uGL9a0O"], [[7, "152d7fd8yxC4o9NN8M4lTIu", "onAllChangeClick", 1]]]], [1, "dePJSRKj5LHa4HvA/5d7Op", null, null, null, 1, 0], [1, 2.723000000000013, -32.40499999999997, 0]], [10, "bagBtn", 4, [[[2, -88, [0, "02yF5WFMtG15k8Pl4247QX"], [5, 85, 85]], [9, 3, -89, [0, "41uFSo/DlCfaSnuW+yrVkR"], [[7, "152d7fd8yxC4o9NN8M4lTIu", "openGuajiBag", 1]]], -90], 4, 4, 1], [1, "5cKFrRO5VG+4IPT7eKWoLc", null, null, null, 1, 0], [1, 265.977, -131.311, 0]], [13, "Layout", 4, [3, 5], [[2, -91, [0, "f1kYjdv/dPLqh+KKmKgvDg"], [5, 364, 100]], [54, 1, 1, 50, -92, [0, "61ZtoQSWZMqpIUQwBkgMNo"]]], [1, "95arUGnL5F+79DbO5rG+QN", null, null, null, 1, 0], [1, 0, -121.299, 0]], [25, 0, {}, 3, [31, "c46/YsCPVOJYA4mWEpNYRx", null, null, -99, [56, "90KMFFmJVCXa/CTAjdujta", 1, [[16, "RedDot", ["_name"], -93], [17, ["_lpos"], -94, [1, 76.46699999999998, 26.39900000000003, 0]], [17, ["_lrot"], -95, [3, 0, 0, 0, 1]], [17, ["_euler"], -96, [1, 0, 0, 0]], [16, 1, ["checkType"], -97], [16, 112, ["redPointIdList", "0"], -98]]], 14]], [18, "content", 7, [2], [[6, -100, [0, "93Wao+FB9HPaSLucJ5vl18"], [5, 660, 141], [0, 0.5, 1]], [55, 1, 3, 8, 7, 10, 9, true, -101, [0, "8efyhNniZOe5BPnS0MrzjR"]]], [1, "54J6qmd7xJEKL8kQpztYQY", null, null, null, 1, 0]], [3, "txtCdt", 2, [[2, -102, [0, "d4RPI2yDJGdpsGVrGPCp3+"], [5, 57.94599914550781, 29.2]], [21, "已刷新", 18, 18, 20, false, true, -103, [0, "06aISeQkhNGbAwiEQwLwYa"], [4, 4279627566], 25], [4, -104, [0, "21NsG+ATFBzKxjdcu8t8FB"]], [57, -105, [0, "f6vMkdr4pDFrZJ1n4Mnegx"]]], [1, "18wauGG+ZHv7XUJ3xfqgTQ", null, null, null, 1, 0], [1, 108.90899999999999, 43.44399999999996, 0]], [3, "tuogaun_bg", 1, [[2, -106, [0, "ackPkctCBBxqAVRD+Fln4g"], [5, 706, 1171]], [5, -107, [0, "5fRbSNboBIoLLX8GGyVwro"], 1], [20, -108, [0, "2f5Hepe3lBeZB+m/JpYW2C"]]], [1, "efKaBirfdLmp7Q9TLl9Poe", null, null, null, 1, 0], [1, 0, 13.829, 0]], [3, "btnHelp", 1, [[2, -109, [0, "8b+wcKpTpBFpDB5upPUBdB"], [5, 52, 52]], [5, -110, [0, "abkSvnFWtNBYCAj1QYXAUI"], 3], [9, 3, -111, [0, "f23iUAX5dDT7iafgjQrL0N"], [[7, "152d7fd8yxC4o9NN8M4lTIu", "onHelpClick", 1]]]], [1, "e5bsd8RGxLMYIgws5IutA3", null, null, null, 1, 0], [1, -309.3, 374.161, 0]], [3, "tequanBtn", 4, [[2, -112, [0, "70+FKdl+lK85Cjpbg3Gonz"], [5, 99, 83]], [5, -113, [0, "72zzMciHZO4LOI7ROwEaYM"], 10], [9, 3, -114, [0, "c06mYjdmNOSq4wgBdH78zQ"], [[7, "152d7fd8yxC4o9NN8M4lTIu", "onTeQuanClick", 1]]]], [1, "63C/f0ceZAQJFBoPFANKeJ", null, null, null, 1, 0], [1, -262.819, -128.311, 0]], [10, "txtNum", 4, [[[6, -115, [0, "aeRU1Pk4ZKNLlrPTIo3V1u"], [5, 4, 29.2], [0, 0, 0.5]], -116, [4, -117, [0, "d3rgtArsZNt6ZnTN093Jnm"]]], 4, 1, 4], [1, "17CuUHz19M8LPvsxs0HyAh", null, null, null, 1, 0], [1, -325.391, -34.561, 0]], [10, "hangupTimeLimit", 4, [[[2, -118, [0, "bahUgW2fRDrYGYC0I0YmGC"], [5, 6, 31.2]], -119, [4, -120, [0, "91dUyIBQNMm5VXPJ419nsF"]]], 4, 1, 4], [1, "f6/6Mue7BMN5jacjlb77mH", null, null, null, 1, 0], [1, 0, -70.446, 0]], [3, "Label", 3, [[2, -121, [0, "4fKq9msPFLXI0sDhBc18fY"], [5, 46, 33.72]], [15, "增加", 20, 20, 22, false, false, 1, true, 3, -122, [0, "a6h/FLQ0BC5b4QH7JvBEBS"], 12], [4, -123, [0, "cbMF895nFBdImxseHs0lRj"]]], [1, "26DrJ47BpB9bDhl5BJWDvt", null, null, null, 1, 0], [1, 15.170999999999992, 12.000000000000028, 0]], [3, "Label-001", 3, [[2, -124, [0, "bc4dzAgSZFNancnp5yogwF"], [5, 86, 33.72]], [15, "时长上限", 20, 20, 22, false, false, 1, true, 3, -125, [0, "b3jg5hO0JPZ4+4quHelvdR"], 13], [4, -126, [0, "94uMUC3ENKPZ5q+Wo9jviq"]]], [1, "19Q1/WN4BBqpdr6ZF5/LHK", null, null, null, 1, 0], [1, 0, -12.481999999999971, 0]], [11, ["c46/YsCPVOJYA4mWEpNYRx"]], [14, "Label", 5, [[2, -127, [0, "99fVyj3YlEEIGxTe0vN8SN"], [5, 102, 37.5]], [15, "开始托管", 24, 24, 25, false, false, 1, true, 3, -128, [0, "7cEJLmD4tL24LT0ahnuGUg"], 16], [4, -129, [0, "4awj9YlLtJo5k+7dnnacYS"]]], [1, "4cbzcjwO5AiLcaeZVUa8Ui", null, null, null, 1, 0]], [3, "btn_reward", 2, [[2, -130, [0, "de0zRP0ulHx5pu+ZeTIiH0"], [5, 34, 26]], [35, -131, [0, "7bywFEqtpB4bZGoKAqQKs0"], 20, 21], [42, -132, [0, "16AJ0Q4RJJVpHKWX8UYBPc"]]], [1, "34ZQAcHgBCl4p2w7n1zIxM", null, null, null, 1, 0], [1, -81.79400000000001, -9.274000000000001, 0]], [3, "Label", 2, [[6, -133, [0, "91iTLE0YpErYWgRL2uk2Rt"], [5, 84.99998474121094, 29.2], [0, 1, 0.5]], [49, "推荐等级：", 18, 18, 20, false, 1, true, -134, [0, "37/jji8bdOYJ0OWQ399Lgi"], 24], [4, -135, [0, "3an1vINfZKUqYS6iyutdad"]]], [1, "ccZdZf79NA44fBjDD3SutA", null, null, null, 1, 0], [1, -65.886, -44.068, 0]], [3, "txtLeader", 2, [[2, -136, [0, "044stbHC9Llorg4CIvlJrw"], [5, 113.9119873046875, 35.5]], [50, "精英豺狼人", 22, 22, 25, false, false, true, -137, [0, "60os7GyTZNrZwT38M+V0r9"], [4, 4283957233], 26], [4, -138, [0, "e2WtQ7ve9K4IcYr57J13mP"]]], [1, "1cvzz5v61GEr2mZcX+OIUq", null, null, null, 1, 0], [1, 5, 36.56, 0]], [3, "txt_lv", 2, [[6, -139, [0, "beoOJuGAZFa5DBJfBw67ZE"], [5, 10.803985595703125, 29.2], [0, 0, 0.5]], [21, "1", 18, 18, 20, false, true, -140, [0, "1anXnjjV5OGKScHTDDRORF"], [4, 4279627566], 27], [4, -141, [0, "12/sFtPDZH8I/mbnoD22Y0"]]], [1, "f2TS+tJiBC5rNNXm2xKOny", null, null, null, 1, 0], [1, -68.731, -44.068, 0]], [3, "mapName", 2, [[6, -142, [0, "64DYz4PAhIeYjsZaZdj1oy"], [5, 75.98199462890625, 29.2], [0, 0, 0.5]], [51, "暮色森林", 18, 18, 20, false, false, true, -143, [0, "bch7AZ2WFG+oAbDmtXumBv"], 28], [4, -144, [0, "c5S10YJfdHo5Z7iNfmisqa"]]], [1, "b16Osw7INDz6K4UGvh2DAE", null, null, null, 1, 0], [1, -18.667, 4.149, 0]], [3, "tuoguan_icon12", 1, [[2, -145, [0, "a4ilQx5ZpKZri9Qz43QMxh"], [5, 372.96000000000004, 18]], [8, 0, -146, [0, "09K+fPLGBGebRklH+Wr+51"], 4]], [1, "c2Px7aEK1Gz6aK7gaC060l", null, null, null, 1, 0], [1, -0.9710000000000005, 361.633, 0]], [3, "tuoguan_frame_03", 1, [[2, -147, [0, "a5ebaKS1tEdp+UyARKjwbJ"], [5, 699, 44]], [5, -148, [0, "a3f18U0uxKkIvv0Iea/bAh"], 5]], [1, "f9UzklcFBBiZDM1dQUpboP", null, null, null, 1, 0], [1, 0, -547, 0]], [3, "pub_wenhao1", 1, [[2, -149, [0, "7e1XSSW8hANZZ5p/7drjnr"], [5, 29, 29]], [5, -150, [0, "cdXiUGqXNAcq3TBs+rJAEN"], 6]], [1, "55u2EFlRVDvLrjYADn+sAG", null, null, null, 1, 0], [1, -224.663, -549, 0]], [3, "lbHint", 1, [[2, -151, [0, "07j5YxgxNP2baRJUAsiiWk"], [5, 453.97991943359375, 35.5]], [22, "托管挂机支持离线，离开游戏后依然会正常挂机杀怪", 20, 20, 25, false, false, 1, true, -152, [0, "d5ry0Vs01NmozTX5dUqoRf"], 7]], [1, "72dBFgMLxL1YqfDRX2ropq", null, null, null, 1, 0], [1, 19.958000000000027, -549, 0]], [3, "OutlineLabel", 1, [[2, -153, [0, "2dvGTe22lEBo6KpSdxs87Q"], [5, 196, 35.5]], [22, "稀有精英击杀设置", 24, 24, 25, false, false, 1, true, -154, [0, "9e2b4p0TpEFbtiKPhq3fq3"], 8]], [1, "fbVxUedN9FYJqj8b3q0/2v", null, null, null, 1, 0], [1, -3, 364.028, 0]], [3, "tuoguan_icon_03", 3, [[2, -155, [0, "42ntlw6gpPfpG34uPzjGdU"], [5, 32, 26]], [5, -156, [0, "5atFx5vINPjIDwnoBeFc+u"], 11]], [1, "84EAXfMcBE0JwknRVhvjYI", null, null, null, 1, 0], [1, -24.437000000000012, 12.918000000000006, 0]], [3, "tuoguan_icon9", 2, [[2, -157, [0, "a7gLS84rBNaa6OwtG+Irug"], [5, 97, 58]], [5, -158, [0, "77aKjEp7lHA4AmJj4aP5cL"], 18]], [1, "d8rGTkXQRJvpATiojufPek", null, null, null, 1, 0], [1, 108.909, 38.526, 0]], [3, "img_head", 2, [[2, -159, [0, "d9Ms328oxLP64FIoiiQTvm"], [5, 60, 60]], [8, 2, -160, [0, "18udWL5D1JlIqQF9l6XA8E"], 19]], [1, "7f0EDtssZOWKI/0ebPrOXn", null, null, null, 1, 0], [1, -104.274, 13.159, 0]], [3, "tuoguan_icon7", 2, [[2, -161, [0, "1aqEhZK7VNwIg4rox9sekx"], [5, 29, 23]], [5, -162, [0, "30XDJtlpRLs5gKggarbG6V"], 22]], [1, "eciLV/9v9E0r/oFqouWok9", null, null, null, 1, 0], [1, -37.927, 4.2940000000000005, 0]], [14, "Sprite", 8, [[2, -163, [0, "54ysNMestHc4NwWBLJesN2"], [5, 22, 22]], [36, 2, false, -164, [0, "46NUCkhk9O9q/MndkiLZqh"], 23]], [1, "ccnG6iC1pLh7JQZQ31LRT1", null, null, null, 1, 0]], [10, "Checkmark", 8, [[[2, -165, [0, "72tLHRutZDoqhI1XLHbUVd"], [5, 36, 29]], -166], 4, 1], [1, "275pF5E99CEKRT6WCq3f+S", null, null, null, 1, 0], [1, 4.639, 4.783, 0]], [58, 12, [0, "e2MCFNbcdISYYcA72bCwxH"]], [52, "", 18, 18, 20, false, true, 20, [0, "8bLbKZdBtLxKa2zDPQvUR2"]], [53, "", 20, 20, 20, false, false, true, 3, 21, [0, "ff4IB7tzxPHb7mQXVg8/Pd"]], [11, ["8fN27e90VKwrOm0UkkXWci"]], [37, 2, 41, [0, "acIjU6L3FEmY8l8U2wIanO"]], [59, false, 4, 1, 6, [0, "dabdSDDMJLkYeOwyDWroeF"], 2, [43]]], 0, [0, -1, 14, 0, 8, 14, 0, 9, 14, 0, 7, 1, 0, 0, 1, 0, 0, 1, 0, 10, 3, 0, 11, 44, 0, 12, 42, 0, 13, 12, 0, 14, 43, 0, 15, 47, 0, 0, 1, 0, -1, 9, 0, -2, 17, 0, -3, 10, 0, -4, 18, 0, -5, 31, 0, -6, 32, 0, -7, 33, 0, -8, 34, 0, -9, 35, 0, -10, 4, 0, -11, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 5, 2, 0, 0, 2, 0, -1, 37, 0, -2, 38, 0, -3, 26, 0, -4, 39, 0, -5, 8, 0, -6, 27, 0, -7, 16, 0, -8, 28, 0, -9, 29, 0, -10, 30, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 36, 0, -2, 22, 0, -3, 23, 0, -4, 14, 0, 0, 4, 0, -1, 11, 0, -2, 19, 0, -3, 12, 0, -4, 20, 0, -5, 21, 0, -6, 13, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, -1, 25, 0, 0, 6, 0, 16, 15, 0, 0, 6, 0, -3, 47, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, 0, 8, 0, 17, 46, 0, 5, 8, 0, 0, 8, 0, -1, 40, 0, -2, 41, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 5, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -3, 42, 0, 0, 13, 0, 0, 13, 0, 4, 24, 0, 4, 24, 0, 4, 24, 0, 4, 24, 0, 4, 45, 0, 4, 45, 0, 7, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, -2, 43, 0, 0, 20, 0, 0, 21, 0, -2, 44, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, -2, 46, 0, 18, 1, 2, 6, 15, 3, 6, 13, 5, 6, 13, 166], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 44, 46], [1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 1, 1, 3, 3, 19, 1, 3, 1, 1, 1, 1, 20, 1, 1, 3, 3, 3, 3, 3, 1, 1, 3, 3, 1], [3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 10, 11, 1, 1, 12, 13, 1, 14, 15, 16, 17, 18, 19, 20, 1, 1, 1, 1, 1, 21, 22, 1, 1, 23]], [[{"name": "tuo<PERSON><PERSON><PERSON><PERSON>", "spriteFrames": ["tuoguan_btn_01", "fdx7mUEbFH9K5L18qmMZk1@c8aba", "tuoguan_frame_03", "fdx7mUEbFH9K5L18qmMZk1@26d25", "tuoguan_icon1", "fdx7mUEbFH9K5L18qmMZk1@7ead9", "tuoguan_icon10", "fdx7mUEbFH9K5L18qmMZk1@5712f", "tuoguan_icon11", "fdx7mUEbFH9K5L18qmMZk1@89d61", "tuoguan_icon12", "fdx7mUEbFH9K5L18qmMZk1@d22bc", "tuoguan_icon2", "fdx7mUEbFH9K5L18qmMZk1@6d055", "tuoguan_icon3", "fdx7mUEbFH9K5L18qmMZk1@767ce", "tuoguan_icon4", "fdx7mUEbFH9K5L18qmMZk1@9f59a", "tuoguan_icon5", "fdx7mUEbFH9K5L18qmMZk1@46edc", "tuoguan_icon6", "fdx7mUEbFH9K5L18qmMZk1@60961", "tuoguan_icon7", "fdx7mUEbFH9K5L18qmMZk1@bff88", "tuoguan_icon8", "fdx7mUEbFH9K5L18qmMZk1@43c4b", "tuoguan_icon9", "fdx7mUEbFH9K5L18qmMZk1@a6e32", "tuoguan_icon_03", "fdx7mUEbFH9K5L18qmMZk1@09058", "tuoguanbeibao_icon-01", "fdx7mUEbFH9K5L18qmMZk1@ad951"]}], [10], 0, [], [], []], [[{"name": "tuoguan_icon_03", "rect": {"x": 688, "y": 151, "width": 32, "height": 26}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 26}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_frame_03", "rect": {"x": 2, "y": 2, "width": 699, "height": 44}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 699, "height": 44}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon8", "rect": {"x": 439, "y": 191, "width": 34, "height": 26}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 34, "height": 26}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon5", "rect": {"x": 499, "y": 128, "width": 98, "height": 97}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 98, "height": 97}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon10", "rect": {"x": 646, "y": 49, "width": 99, "height": 83}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 99, "height": 83}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon6", "rect": {"x": 439, "y": 220, "width": 28, "height": 29}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 28, "height": 29}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon2", "rect": {"x": 2, "y": 49, "width": 319, "height": 134}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 319, "height": 134}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon3", "rect": {"x": 324, "y": 49, "width": 319, "height": 76}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 319, "height": 76}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon1", "rect": {"x": 704, "y": 2, "width": 25, "height": 26}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 25, "height": 26}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon11", "rect": {"x": 181, "y": 207, "width": 129, "height": 25}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 129, "height": 25}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon4", "rect": {"x": 2, "y": 207, "width": 176, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 176, "height": 40}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon9", "rect": {"x": 339, "y": 191, "width": 97, "height": 58}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 97, "height": 58}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguanbeibao_icon-01", "rect": {"x": 600, "y": 151, "width": 85, "height": 85}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 85, "height": 85}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon7", "rect": {"x": 313, "y": 207, "width": 29, "height": 23}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 29, "height": 23}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_btn_01", "rect": {"x": 339, "y": 128, "width": 157, "height": 60}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 157, "height": 60}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]], [[{"name": "tuoguan_icon12", "rect": {"x": 2, "y": 186, "width": 334, "height": 18}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 334, "height": 18}, "rotated": false, "capInsets": [103, 0, 102, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [2], [0]]]]