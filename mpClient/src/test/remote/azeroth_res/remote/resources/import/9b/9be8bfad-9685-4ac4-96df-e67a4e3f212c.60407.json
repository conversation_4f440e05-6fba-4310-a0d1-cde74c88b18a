[1, ["61NXJOyyZNU6ZGh0FJkMv2", "ecY0sTuzBDeIhktZfhZqkM@f9941", "fdx7mUEbFH9K5L18qmMZk1@9f59a", "94FGSZedBCE46Kdw4aaHkw@f9941", "65nnJ3SlJOoYRwNWpKye8V@f9941", "2enw6dg1JAa5xhWIDgaiWv@46320", "2enw6dg1JAa5xhWIDgaiWv@50f42", "fdx7mUEbFH9K5L18qmMZk1@bff88", "fdx7mUEbFH9K5L18qmMZk1@7ead9", "2enw6dg1JAa5xhWIDgaiWv@97f2a", "2enw6dg1JAa5xhWIDgaiWv@c4250", "0d2Rss5lBIELZgdjqjQQeK", "2enw6dg1JAa5xhWIDgaiWv@f9880", "8fOvUyIEhI+YYSThw1sq2H"], ["node", "_font", "_spriteFrame", "targetInfo", "root", "_target", "_parent", "_userDefinedFont", "asset", "tmpPrefab", "target", "source", "boss<PERSON>um", "monsterNum", "expRate", "exp", "moneyRate", "money", "btnQuXiaoAI", "timer<PERSON><PERSON>p", "mapName", "listReward", "listDongTai", "_content", "value", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos"], 0, 4, 9, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_enableOutline", "_enableWrapText", "_cacheMode", "_outlineWidth", "node", "__prefab", "_font", "_color"], -6, 1, 4, 6, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_alignMode", "_left", "_right", "_bottom", "node", "__prefab"], -4, 1, 4], ["cc.Node", ["_name", "_components", "_prefab", "_lpos", "_parent", "_children"], 2, 12, 4, 5, 1, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target", "clickEvents"], 2, 1, 4, 5, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingLeft", "_paddingTop", "_spacingX", "_spacingY", "_affectedByScale", "node", "__prefab"], -4, 1, 4], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "elastic", "node", "__prefab", "_content"], -1, 1, 4, 1], ["96925NpxVhGX5GHR06EhbFH", ["frameByFrameRenderNum", "templateType", "_virtual", "node", "__prefab", "pageChangeEvent", "tmpNode"], 0, 1, 4, 4, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["2b852xrmS9HTZ4ufS6ZJDUB", ["node", "__prefab"], 3, 1, 4], ["cc.RichText", ["_lineHeight", "_string", "_fontSize", "_maxWidth", "_isSystemFontUsed", "node", "__prefab", "_font", "_userDefinedFont"], -2, 1, 4, 6, 6], ["f51falwV8dEWKPE+viecXlo", ["adaptiveSize", "node", "__prefab"], 2, 1, 4], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.TargetInfo", ["localID"], 2], ["2c57eCRQBBMzZskhVkd164Q", ["node", "__prefab", "listDongTai", "listReward", "mapName", "timer<PERSON><PERSON>p", "btnQuXiaoAI", "money", "moneyRate", "exp", "expRate", "monsterNum", "boss<PERSON>um"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.TargetOverrideInfo", ["propertyPath", "source", "sourceInfo", "target", "targetInfo"], 2, 1, 4, 1, 4]], [[12, 0, 2], [13, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [17, 0, 1, 1], [5, 0, 1, 2, 3, 1], [0, 0, 5, 6, 4, 3, 7, 2], [0, 0, 5, 4, 3, 7, 2], [4, 0, 4, 1, 2, 3, 2], [1, 0, 1, 2, 3, 6, 4, 5, 9, 10, 8], [0, 0, 5, 4, 3, 2], [2, 1, 0, 2, 3, 4, 3], [1, 0, 1, 2, 3, 6, 4, 5, 9, 10, 12, 11, 8], [28, 0, 2], [4, 0, 4, 5, 1, 2, 3, 2], [2, 0, 2, 3, 4, 2], [2, 2, 3, 4, 1], [1, 0, 1, 2, 3, 6, 4, 7, 5, 9, 10, 11, 9], [25, 0, 1, 2, 2], [0, 0, 5, 6, 4, 3, 2], [6, 0, 1, 2, 3, 4, 2], [7, 1], [3, 0, 1, 2, 7, 8, 4], [21, 0, 1, 1], [22, 0, 1, 2, 1], [11, 0, 2], [0, 0, 6, 4, 3, 2], [0, 0, 6, 4, 3, 7, 2], [0, 1, 2, 5, 3, 3], [4, 0, 1, 2, 3, 2], [2, 0, 2, 3, 5, 4, 2], [2, 1, 0, 2, 3, 5, 4, 3], [6, 1, 2, 5, 3, 4, 1], [7, 0, 1, 2, 3], [3, 0, 4, 5, 1, 2, 7, 8, 6], [3, 0, 6, 1, 2, 3, 7, 8, 6], [3, 0, 1, 2, 3, 7, 8, 5], [14, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 3, 4, 5, 3], [16, 0, 1, 1], [1, 0, 1, 2, 3, 4, 5, 9, 10, 11, 7], [1, 0, 1, 2, 3, 6, 4, 5, 9, 10, 11, 8], [1, 0, 1, 2, 3, 6, 4, 7, 5, 8, 9, 10, 11, 10], [1, 0, 1, 2, 3, 6, 4, 7, 5, 9, 10, 12, 11, 9], [18, 0, 1, 1], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 6], [20, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [8, 0, 1, 2, 3, 4, 5, 7, 8, 7], [9, 0, 1, 3, 2, 4, 5, 6, 5], [9, 0, 1, 2, 4, 5, 6, 4], [10, 0, 3, 4, 6, 5, 2], [10, 1, 2, 0, 3, 4, 5, 4], [23, 0, 1, 2, 2], [24, 0, 1, 2, 3], [26, 0, 1, 2, 2], [27, 0, 1, 2, 2], [29, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [30, 0, 1, 2, 3, 4, 2]], [[24, "TuoGuanInfoUI"], [25, "TuoGuanInfoUI", [-19, -20, -21, -22], [[2, -5, [0, "a5+I8ZmetN67+oXESIPd+y"], [5, 750, 1334]], [56, -17, [0, "8dOxTb6GNA7ZvoX/pyAIFS"], -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6], [21, 45, 100, 100, -18, [0, "34gqkmRdJF9pXbT/DEOus4"]]], [37, "c46/YsCPVOJYA4mWEpNYRx", null, -4, 0, [[57, ["handle"], -3, [12, ["2dQ2wXCkFJzY5oPQvWqcNX"]], -2, [12, ["09JETm349J/q+uedF63ohs"]]]], [-1]]], [5, "center", 1, [-24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35], [[2, -23, [0, "f26tllGF9Hj4ndsbF2C/0L"], [5, 0, 0]]], [1, "f9nHih6OdAW4oKgjAo+0q2", null, null, null, 1, 0], [1, 16.54, 23.823, 0]], [5, "tong<PERSON>i<PERSON><PERSON>nt", 2, [-37, -38, -39, -40, -41, -42], [[4, -36, [0, "75WzkgJdBFv5lw/NG0oHZw"], [5, 560, 74.5], [0, 0.5, 1]]], [1, "f3U3bu8+FCi53Vpej7wlwp", null, null, null, 1, 0], [1, -20.414, 23.651, 0]], [26, "Text_1", [-47], [[4, -43, [0, "f4fuqulsxH9J4FpnyMs3Q3"], [5, 510, 31.5], [0, 0.5, 1]], [44, 25, "", 18, 510, false, -44, [0, "59kirgUnhKtq4UwxFy1J2e"], 10, 11], [3, -45, [0, "2787vZhEFKhaWHrx+yjVNV"]], [45, true, -46, [0, "57R3tv6rJFPpfpYoasDUZv"]]], [1, "124XFxzIlBz4GclWSuMgdN", null, null, null, 1, 0], [1, 14.309, -10, 0]], [9, "bg1", 1, [[2, -48, [0, "f9678W/HVHh6922/FrD7jk"], [5, 750, 1334]], [29, 0, -49, [0, "239dZXIAdHd4FtV5JKZ8sx"], [4, 3019898880], 0], [31, -51, [0, "b9emesDgRBt4wMUti6xRim"], [[32, "2c57eCRQBBMzZskhVkd164Q", "onClickCloseBtn", 1]], [4, 4292269782], -50], [21, 45, 750, 1334, -52, [0, "57ORTBsKpOq5fbRvI2qo5D"]]], [1, "c92ON7jUtH6pY3UdvBJz8G", null, null, null, 1, 0]], [13, "listDongTai", 2, [-58], [[[4, -53, [0, "11yM+iW/1N47of3q6iUrbL"], [5, 580, 193], [0, 0.5, 1]], [48, 0.23, 0.75, false, false, -55, [0, "12GHRB6X9F9Z7nJSLgqzup"], -54], -56, [10, 1, 0, -57, [0, "dfXeouH/JIN41GtVPdbxYn"], 12]], 4, 4, 1, 4], [1, "36HG+6G2BMIZf5/bjbrDnt", null, null, null, 1, 0], [1, -17, 331.57, 0]], [5, "view", 6, [-63], [[4, -59, [0, "a6ANtIfC5EhbUR6rM/fKnL"], [5, 560, 193], [0, 0.5, 1]], [22, -60, [0, "33U5aRVBdBdonLQ76nbeys"]], [23, -61, [0, "f33WFyW+1Mnovw+VGS+zX8"], [4, 16777215]], [33, 45, 6.585999999999995, 13.414000000000003, 318, 230, -62, [0, "b4RfcRUNBByZbg0rhatJrY"]]], [1, "34Q7FhS3xIq4sIRfxucaGK", null, null, null, 1, 0], [1, -3.4139999999999873, 0, 0]], [13, "rewardList", 2, [-68], [[[4, -64, [0, "62FfpX0KxPW5kN1n6oA4zm"], [5, 582.118, 271.6], [0, 0.5, 1]], -65, -66, [10, 1, 0, -67, [0, "26qvboDXJOArnkeYbi9fY2"], 19]], 4, 1, 1, 4], [1, "c6qfOFYctAd7BIs7m3CUOC", null, null, null, 1, 0], [1, -19.027, -110.158, 0]], [18, "view", 8, [-73], [[4, -69, [0, "f55HvlJoBF85zrp/S0rPva"], [5, 582.118, 271.6], [0, 0.5, 1]], [22, -70, [0, "betgvXkERNXL9/hu5qnql6"]], [23, -71, [0, "2fOtOF0J5BCI7ZgJhty78N"], [4, 16777215]], [35, 45, 240, 250, 0, -72, [0, "6bCuaCNZVKlpjFCVQ10N8Q"]]], [1, "29H/AiQkpEBJtUbwsC0FBe", null, null, null, 1, 0]], [13, "btnQuXiaoAI", 2, [-77], [[[2, -74, [0, "becUQk4X5KQqWUCWGAjOAi"], [5, 157, 60]], [30, 1, 0, -75, [0, "6cT6q8aLBLKrrlI70G0aes"], [4, 4292269782], 26], -76], 4, 4, 1], [1, "6139qEmJ9DsLjJ3cLORY+w", null, null, null, 1, 0], [1, -9.971, -478.32400000000007, 0]], [6, "btnClose", 1, [[2, -78, [0, "89+WpwLoxHhKnknj2khEI4"], [5, 58, 57]], [10, 1, 2, -79, [0, "1dbuRRkeRIrrFMKz8H2WXk"], 2], [19, 3, -81, [0, "38cEeawv1JkJwq7w3+E/jR"], [4, 4292269782], -80]], [1, "2d36qw83RBhoRxEw+PCk/t", null, null, null, 1, 0], [1, 286.615, 491.666, 0]], [28, "timeLable", [[[4, -82, [0, "07+JG6x+9D3oYNA7tYxZ2y"], [5, 97.27993774414062, 29.2], [0, 0, 0.5]], [39, "00:00:00", 20, 20, 20, false, true, -83, [0, "ccsmLm4fBIjZeD2loQNEmo"], 7], [3, -84, [0, "52Y8E+hkVDrYRy6xjnVC+T"]], -85], 4, 4, 4, 1], [1, "f3shmsJbVKEa5oHiYY3nKW", null, null, null, 1, 0], [1, 16, 1.0109999999999673, 0]], [18, "content", 7, [4], [[4, -86, [0, "eflLg5RNZDLL6fd7W22VGf"], [5, 560, 41.5], [0, 0.5, 1]], [46, 1, 2, 2, 10, 7, 5, true, -87, [0, "5bLOONqIhH5a2Nad1ZQaQq"]]], [1, "4cS24AKudIRKT8U9JoYo8D", null, null, null, 1, 0]], [9, "content", 9, [[4, -88, [0, "c8bm1mI/JA9aCWrsv9E+73"], [5, 582.118, 36], [0, 0.5, 1]], [47, 1, 3, 27, 36, 20, 14.9, -89, [0, "8eS9guvItOzYR9C/zu4HAK"]], [34, 41, 37.5, 110, 80, 0, -90, [0, "bcW/k9dUFDx6tNfxTLFSOB"]]], [1, "74URZgKUVLP5ZT5Jipj0hP", null, null, null, 1, 0]], [27, 0, {}, 2, [36, "48QgggtnpApZxwpAfGYRjg", null, null, -96, [52, "ffDw/4M2xAiI89yw3DBAz4", 1, [[53, "MyScrollBar", ["_name"], -91], [17, ["_lpos"], -92, [1, 251.956, -263.781, 0]], [17, ["_lrot"], -93, [3, 0, 0, 0, 1]], [17, ["_euler"], -94, [1, 0, 0, 0]], [54, ["scrollView"], [12, ["2dQ2wXCkFJzY5oPQvWqcNX"]], -95], [55, ["_contentSize"], [12, ["ae8XjF0uhCJI6F8w5MXn7m"]], [5, 6, 234.982]]]], 20]], [6, "bg", 1, [[2, -97, [0, "f9G+c9cdBOX40DLHhs+oWp"], [5, 610, 1015]], [10, 1, 0, -98, [0, "d4Hi8W8MdCc41zOlMxA8tJ"], 1], [38, -99, [0, "85Cd/FnW5M7YNfw/2uF3Po"]]], [1, "4dIM+Wp1FK872kzJt2gHkh", null, null, null, 1, 0], [1, 0, -12, 0]], [5, "pub_frame_bottom_9", 2, [-102], [[2, -100, [0, "94XU8JDIpPIpBNJedBjinH"], [5, 520, 46]], [10, 1, 0, -101, [0, "22Nab/gmlIY6JPZZJbaaPF"], 4]], [1, "31i6+A+L1N47xjDuxMaaVT", null, null, null, 1, 0], [1, -26.381, 427.52, 0]], [5, "tuoguan_icon7", 2, [-105], [[2, -103, [0, "b5WYrPtKdIUIP+0lxxiwI3"], [5, 29, 23]], [15, -104, [0, "a9LhoVhnFA7pKrPbWDBwRF"], 6]], [1, "86x+BUzM5FqpxOb42Z7dBp", null, null, null, 1, 0], [1, -263.447, 356.186, 0]], [7, "mapName", 18, [[[4, -106, [0, "b3+FJL8dpLvbdBnLXutAxf"], [5, 4, 29.2], [0, 0, 0.5]], -107, [3, -108, [0, "a3TCK2F29NOL+hj3p853Jy"]]], 4, 1, 4], [1, "a6hkGkrtlMf4fcgBsis40D", null, null, null, 1, 0], [1, 18.115, -0.145, 0]], [5, "tuoguan_icon1", 2, [12], [[2, -109, [0, "12pNWl2b5MKrYGNF47+nhI"], [5, 25, 26]], [15, -110, [0, "ceDHDXrKhIUItE7P2QPB1B"], 8]], [1, "11Mnq++PxO0roxgBT+dw21", null, null, null, 1, 0], [1, 141.932, 357.918, 0]], [5, "node1", 3, [-112, -113], [[2, -111, [0, "60gBMdi3lMaoPjgMjE/iPZ"], [5, 150, 30]]], [1, "87G/vNRdxGZLWMa/WmDlTe", null, null, null, 1, 0], [1, -183.29, -21, 0]], [6, "Label", 21, [[2, -114, [0, "0aKfbf2p9BKYCfWT0uGDIW"], [5, 85.01799011230469, 29.2]], [11, "金币获取：", 18, 18, 20, false, false, true, -115, [0, "87fQ5MBtdJBYSpT707bBh8"], [4, 4283957233], 13], [3, -116, [0, "c0JUfNvWBHnLF28IovuFBa"]]], [1, "fcuaSuiSNE4qtjGvVlQF+t", null, null, null, 1, 0], [1, -28.05, 1, 0]], [7, "money", 21, [[[4, -117, [0, "27rQksSyhD/Y0BjRhrQi/P"], [5, 16.491989135742188, 29.2], [0, 0, 0.5]], -118, [3, -119, [0, "444HwFP4pCxaXFS95TYug/"]]], 4, 1, 4], [1, "9egi9AbB1EYISmpLs/2ImW", null, null, null, 1, 0], [1, 11.84, 1, 0]], [5, "node2", 3, [-121, -122], [[2, -120, [0, "389dOI0ElLcZsypFB8o5fh"], [5, 150, 30]]], [1, "e25tzjAvtAKKgxerqixbE/", null, null, null, 1, 0], [1, -183.29, -54, 0]], [6, "Label", 24, [[2, -123, [0, "05CHSe+kpHNqBh+FKjooV5"], [5, 84.9639892578125, 29.2]], [11, "金币效率：", 18, 18, 20, false, false, true, -124, [0, "b7PfiSrnBMQZkyl78ZW18Z"], [4, 4283957233], 14], [3, -125, [0, "63idnxa6JMNY0jPVPHqHFA"]]], [1, "efAxEsJl1NzYfEoBBNLNLb", null, null, null, 1, 0], [1, -28.05, 1, 0]], [7, "moneyRate", 24, [[[4, -126, [0, "23NGcn0CBC+pHu4uOL71uX"], [5, 16.491989135742188, 29.2], [0, 0, 0.5]], -127, [3, -128, [0, "38mOcZo4ZJiI1kBHkTUyUz"]]], 4, 1, 4], [1, "0b/HGgK5pNUqFU4XS0Fu5Q", null, null, null, 1, 0], [1, 11.84, 1, 0]], [5, "node3", 3, [-130, -131], [[2, -129, [0, "e2z+mfK6VC0L3ZIwDFJp3N"], [5, 150, 30]]], [1, "f1mpREaRZKSbLjDrQvc6mm", null, null, null, 1, 0], [1, 16.734, -20.275, 0]], [6, "Label", 27, [[2, -132, [0, "85ZuUaiyhMi7qKA0R9ELXe"], [5, 85, 29.2]], [11, "经验获取：", 18, 18, 20, false, false, true, -133, [0, "a2gWOku6ZGwIGXeLDUbsjl"], [4, 4294943815], 15], [3, -134, [0, "e0Fh7VUxJACo00leXbQTJ0"]]], [1, "caqn94lBhEOZ8SDKfzh+ES", null, null, null, 1, 0], [1, -28.05, 1, 0]], [7, "exp", 27, [[[4, -135, [0, "10FrVpJ3JKvJKK0CsabLiw"], [5, 16.491989135742188, 29.2], [0, 0, 0.5]], -136, [3, -137, [0, "6bk2oFqj1KPY/IBEAjazm9"]]], 4, 1, 4], [1, "dbvUNdto9LAaieWSJvb0wj", null, null, null, 1, 0], [1, 11.84, 1, 0]], [5, "node4", 3, [-139, -140], [[2, -138, [0, "00u+k7/AFIaJChv96MnQ+y"], [5, 150, 30]]], [1, "e4qqPzRD9MFJDpCC7Bq3cA", null, null, null, 1, 0], [1, 16.734, -50.388, 0]], [6, "Label", 30, [[2, -141, [0, "b5FFiJElRCV6wEwUmpfeZL"], [5, 84.94599914550781, 29.2]], [11, "经验效率：", 18, 18, 20, false, false, true, -142, [0, "f9JpVYaOdCf41S39BFZsry"], [4, 4294943815], 16], [3, -143, [0, "f7YHvHWc1Ocp76XOpURUHv"]]], [1, "c6KhN887ZLtqSAH2l7Nu20", null, null, null, 1, 0], [1, -28.05, 1, 0]], [7, "expRate", 30, [[[4, -144, [0, "55jPj7epdEIZAlQS3KsDB0"], [5, 16.491989135742188, 29.2], [0, 0, 0.5]], -145, [3, -146, [0, "29kJSu5vhDhobY4dKkr8tD"]]], 4, 1, 4], [1, "282nvyZd1PkJCxPNn3HMpJ", null, null, null, 1, 0], [1, 11.84, 1, 0]], [5, "node5", 3, [-148, -149], [[2, -147, [0, "1dcCDoxatMTJoqJVlxADeF"], [5, 150, 30]]], [1, "0c0XhSAchFuoqNqlPohPPV", null, null, null, 1, 0], [1, 200.997, -22.426, 0]], [6, "Label", 33, [[2, -150, [0, "45CfF/2WdOeZ580UPf0bhP"], [5, 84.92799377441406, 29.2]], [40, "普通怪物：", 18, 18, 20, false, false, true, -151, [0, "542hxyoZhHnKCskYxMUZQ+"], 17], [3, -152, [0, "c2Swx3LWhLw61QQe49aC91"]]], [1, "89CuB8WtpB3qs/Vx2giG7Z", null, null, null, 1, 0], [1, -27.05, 3, 0]], [7, "monsterNum", 33, [[[4, -153, [0, "d3QZRGV+hMao173La9r+5v"], [5, 16.491989135742188, 29.2], [0, 0, 0.5]], -154, [3, -155, [0, "19RvAYqBZMOZU49jujTjz7"]]], 4, 1, 4], [1, "26e76vLk9BsqJ/v3eur9ul", null, null, null, 1, 0], [1, 11.84, 1, 0]], [5, "node6", 3, [-157, -158], [[2, -156, [0, "b5U1S5i6lNway8NVt3zoKp"], [5, 150, 30]]], [1, "48VNV1UDRLjIKYQVqp3EKI", null, null, null, 1, 0], [1, 201.997, -52.727, 0]], [6, "Label", 36, [[2, -159, [0, "769bLggPZKVZTqhauhyEEZ"], [5, 84.92799377441406, 29.2]], [11, "稀有怪物：", 18, 18, 20, false, false, true, -160, [0, "2dxzsePQVMc6wXb+CC+fZR"], [4, 4278223103], 18], [3, -161, [0, "84277OIpZGHZ1DM0CPe8bp"]]], [1, "260ZU/IqFPf7IYmCxi1/jX", null, null, null, 1, 0], [1, -27.05, 2, 0]], [7, "boss<PERSON>um", 36, [[[4, -162, [0, "c0SjgAYnRL9bUUyy/UXlgM"], [5, 16.491989135742188, 29.2], [0, 0, 0.5]], -163, [3, -164, [0, "fehCTaTQZJAoeUyimwGg8f"]]], 4, 1, 4], [1, "20TLj2TYFOUqLLGOgKucZ5", null, null, null, 1, 0], [1, 11.84, 1, 0]], [12, ["48QgggtnpApZxwpAfGYRjg"]], [5, "biaotiBg", 2, [-167], [[2, -165, [0, "f9q06L5ClHj6wzlvjyh6ba"], [5, 176, 40]], [14, 2, -166, [0, "bbW6H/qKhFvKEQ4ybJAayG"], 22]], [1, "afmoBTEt1H+YzUNfqPeRtr", null, null, null, 1, 0], [1, -17.59699999999998, 56.321000000000026, 0]], [9, "Label", 40, [[2, -168, [0, "48u/Coz6lIaLxcZ1RkeV8o"], [5, 100, 35.5]], [16, "统计信息", 24, 24, 25, false, false, 1, true, -169, [0, "d3Ui4hCu1MErN8/pQX2tqs"], 21], [3, -170, [0, "34nc+/TChDJIbBesHAKGrl"]]], [1, "31E0cVP21NxqFLcEafsYis", null, null, null, 1, 0]], [5, "biaotiBg1", 2, [-173], [[2, -171, [0, "06jH/RBvpJTreVT0ZGlS0C"], [5, 176, 40]], [14, 2, -172, [0, "afwSZsDe9GAK4u0GgZafFI"], 24]], [1, "2dnwh9bhRFFJ9QaVJhugjD", null, null, null, 1, 0], [1, -17.597, -113.001, 0]], [9, "Label", 42, [[2, -174, [0, "67K+jCgvRB07FSbh5jaM8X"], [5, 100, 35.5]], [16, "获取奖励", 24, 24, 25, false, false, 1, true, -175, [0, "d30L2IslxD3L1NVyEYyr+0"], 23], [3, -176, [0, "6dTOYOuipOLJOgn/hj+X6D"]]], [1, "6avtdn0b9Ij78Kq5PNdqYL", null, null, null, 1, 0]], [9, "Label", 10, [[2, -177, [0, "dbyqeyCCdGBLeliwcIrCR7"], [5, 102, 37.5]], [41, "取消托管", 24, 24, 25, false, false, 1, true, 3, -178, [0, "e6O8xlThVFZYATT2/oA+jJ"], 25], [3, -179, [0, "6d+qsOoGFL7JD6ysN6sF4M"]]], [1, "e90tUthfRPt5kHaAaZD1w7", null, null, null, 1, 0]], [6, "label", 2, [[2, -180, [0, "a0G+0IlGRCkL7ImNakbN/u"], [5, 345.9639892578125, 29.2]], [42, "退出游戏后，将继续托管，获得在线同等收益", 18, 18, 20, false, false, 1, true, -181, [0, "1f89UT5OZBx5KLdWBvL31J"], [4, 4279627566], 27], [3, -182, [0, "ad0JvykG9O1bf8x/e80bqZ"]]], [1, "cbDw7gUm9HCqLEFVt3vejb", null, null, null, 1, 0], [1, -14.133, -409.951, 0]], [6, "OutlineLabel", 17, [[2, -183, [0, "2f3LUnRGxAoIf2u/4QRTkd"], [5, 100, 35.5]], [16, "托管信息", 24, 24, 25, false, false, 1, true, -184, [0, "b9oT1wzm9C/p8nOHeMNPga"], 3]], [1, "9bjqtHmPxMQIQC7yb/cDxi", null, null, null, 1, 0], [1, 9, 0, 0]], [6, "pub_bg2", 2, [[2, -185, [0, "d9Px7+Wg9FU6iEly56/iaH"], [5, 580, 123]], [14, 0, -186, [0, "17lmz1l41MGajKil3EmNP4"], 5]], [1, "5aplLf889Ev5aDUZYKDFrD", null, null, null, 1, 0], [1, -17.968, -5, 0]], [6, "pic", 4, [[2, -187, [0, "4d3W7WGrtJ64lgtKvqpbEz"], [5, 18, 20]], [15, -188, [0, "741c0Z0D1Mqbd7GkAHeQ9q"], 9]], [1, "81S1VXujBBppf1vgeoOxAl", null, null, null, 1, 0], [1, -268.441, -12.343, 0]], [8, "", 20, 20, 20, false, false, true, 19, [0, "a4uy1ZTBhMVLqr8ImlRmqf"]], [43, 12, [0, "268L7do1pLd5OjAfdmJCh1"]], [50, 6, 6, [0, "19RpKOC4tKSZecTSg2K/dF"], 4, [20]], [8, "0", 18, 18, 20, false, false, true, 23, [0, "e9nxDgHvZCBbDH9YbWz/h0"]], [8, "0", 18, 18, 20, false, false, true, 26, [0, "fa9Limy6ZE1pUWoSL/Ud89"]], [8, "0", 18, 18, 20, false, false, true, 29, [0, "cczl8G1lRCpJE0kaSWysZ5"]], [8, "0", 18, 18, 20, false, false, true, 32, [0, "39FlRcOOVOJJf6+8lWrqLn"]], [8, "0", 18, 18, 20, false, false, true, 35, [0, "84WcfpWuVO26q6/gvRd37S"]], [8, "0", 18, 18, 20, false, false, true, 38, [0, "e0NFLtk+FPKKWpgBMQL2ea"]], [49, 0.23, 0.75, false, 8, [0, "0aoaLH1bxDY7o4Gy37BFXY"], 14], [51, 2, false, 5, 8, [0, "fd8ePWed1OnbxO3nDAI/mm"], [20]], [19, 3, 10, [0, "19E0l6mt1LKINiFC5MmX5l"], [4, 4292269782], 10]], 0, [0, -1, 15, 0, 10, 15, 0, 11, 15, 0, 4, 1, 0, 0, 1, 0, 12, 57, 0, 13, 56, 0, 14, 55, 0, 15, 54, 0, 16, 53, 0, 17, 52, 0, 18, 60, 0, 19, 50, 0, 20, 49, 0, 21, 59, 0, 22, 51, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 16, 0, -3, 11, 0, -4, 2, 0, 0, 2, 0, -1, 17, 0, -2, 47, 0, -3, 18, 0, -4, 20, 0, -5, 6, 0, -6, 3, 0, -7, 8, 0, -8, 15, 0, -9, 40, 0, -10, 42, 0, -11, 10, 0, -12, 45, 0, 0, 3, 0, -1, 21, 0, -2, 24, 0, -3, 27, 0, -4, 30, 0, -5, 33, 0, -6, 36, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 48, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 23, 13, 0, 0, 6, 0, -3, 51, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 13, 0, 0, 8, 0, -2, 58, 0, -3, 59, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 14, 0, 0, 10, 0, 0, 10, 0, -3, 60, 0, -1, 44, 0, 0, 11, 0, 0, 11, 0, 5, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -4, 50, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 3, 39, 0, 3, 39, 0, 3, 39, 0, 3, 39, 0, 24, 58, 0, 4, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 46, 0, 0, 18, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, -2, 49, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, -2, 23, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, -2, 52, 0, 0, 23, 0, 0, 24, 0, -1, 25, 0, -2, 26, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, -2, 53, 0, 0, 26, 0, 0, 27, 0, -1, 28, 0, -2, 29, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, -2, 54, 0, 0, 29, 0, 0, 30, 0, -1, 31, 0, -2, 32, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, -2, 55, 0, 0, 32, 0, 0, 33, 0, -1, 34, 0, -2, 35, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, -2, 56, 0, 0, 35, 0, 0, 36, 0, -1, 37, 0, -2, 38, 0, 0, 37, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, -2, 57, 0, 0, 38, 0, 0, 40, 0, 0, 40, 0, -1, 41, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, -1, 43, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 25, 1, 4, 6, 13, 12, 6, 20, 188], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 52, 53, 54, 55, 56, 57, 59], [2, 2, 2, 1, 2, 2, 2, 1, 2, 2, 1, 7, 2, 1, 1, 1, 1, 1, 1, 2, 8, 1, 2, 1, 2, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 9], [3, 4, 5, 0, 6, 1, 7, 0, 8, 9, 0, 0, 1, 0, 0, 0, 0, 0, 0, 10, 11, 0, 2, 0, 2, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 13]]