[1, ["97cQVX9CNAQKF6Nmv2prqC@6c48a", "61NXJOyyZNU6ZGh0FJkMv2", "d8ikyXL1JGQ6jVrMuXESDm", "f5Tdyt2EFAu6vZpGi2AkO6@84008", "f5Tdyt2EFAu6vZpGi2AkO6@37969", "f5Tdyt2EFAu6vZpGi2AkO6@a9079", "94FGSZedBCE46Kdw4aaHkw@f9941", "eftwBq4pBOl7PxUnAuilSO@f9941", "2enw6dg1JAa5xhWIDgaiWv@46320", "f5Tdyt2EFAu6vZpGi2AkO6@bbe12", "f5Tdyt2EFAu6vZpGi2AkO6@469b4", "f5Tdyt2EFAu6vZpGi2AkO6@8bd80", "f5Tdyt2EFAu6vZpGi2AkO6@b6737", "f5Tdyt2EFAu6vZpGi2AkO6@316b7", "f5Tdyt2EFAu6vZpGi2AkO6@30338", "f5Tdyt2EFAu6vZpGi2AkO6@e860c", "f5Tdyt2EFAu6vZpGi2AkO6@d3039", "delgeop2NFDoM6INUk2qDe@f9941", "16mojGSc1Nvrb/YTBQYkJg@f9941", "f5Tdyt2EFAu6vZpGi2AkO6@ecfcb", "0d2Rss5lBIELZgdjqjQQeK", "62Hrci6TNDrq5pYKIyRrhH", "f5Tdyt2EFAu6vZpGi2AkO6@449ff", "f5Tdyt2EFAu6vZpGi2AkO6", "f5Tdyt2EFAu6vZpGi2AkO6@d7174", "2enw6dg1JAa5xhWIDgaiWv@b2b8c", "2enw6dg1JAa5xhWIDgaiWv", "f5Tdyt2EFAu6vZpGi2AkO6@bc190", "2enw6dg1JAa5xhWIDgaiWv@6e447", "2enw6dg1JAa5xhWIDgaiWv@ede1c", "2enw6dg1JAa5xhWIDgaiWv@2daac", "16mojGSc1Nvrb/YTBQYkJg@6c48a", "94FGSZedBCE46Kdw4aaHkw@6c48a", "delgeop2NFDoM6INUk2qDe@6c48a", "eftwBq4pBOl7PxUnAuilSO@6c48a"], ["node", "_textureSource", "_spriteFrame", "_font", "root", "targetInfo", "asset", "target", "source", "data", "_parent", "_atlas", "_target", "value", "memberlist", "txt_renshu", "txt_lv", "txtGuildName", "handle", "icon", "mountsSpineComp", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "wqLComp", "wqRComp", "<PERSON>n<PERSON><PERSON><PERSON>", "footComp", "legComp", "handLComp", "handRComp", "bodyCComp", "bodyBComp", "bodyAComp", "shoulderLComp", "shoulderRComp", "head<PERSON><PERSON><PERSON>", "head<PERSON><PERSON><PERSON>", "bodyNode"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame", "_atlas", "_color"], 0, 1, 4, 6, 6, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_overflow", "_cacheMode", "_underlineHeight", "node", "__prefab", "_font", "_color"], -7, 1, 4, 6, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos"], 0, 4, 12, 1, 2, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo", "sourceInfo"], 2, 1, 1, 4, 4], ["cc.<PERSON><PERSON>", ["node", "__prefab", "clickEvents", "_normalColor", "_target"], 3, 1, 4, 9, 5, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], "cc.SpriteAtlas", ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 1], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.Mask", ["node", "__prefab"], 3, 1, 4], ["cc.Graphics", ["node", "__prefab", "_fillColor"], 3, 1, 4, 5], ["cc.Layout", ["_resizeMode", "_layoutType", "_paddingTop", "_spacingY", "node", "__prefab"], -1, 1, 4], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["cab5fRvbX9BYLwMiSMYhgVL", ["click2Top", "node", "__prefab"], 2, 1, 4], ["<PERSON><PERSON>", ["bounceDuration", "brake", "horizontal", "node", "__prefab", "_content"], 0, 1, 4, 1], ["96925NpxVhGX5GHR06EhbFH", ["node", "__prefab", "tmpNode", "pageChangeEvent"], 3, 1, 4, 1, 4], ["16a7fBdCAFCa6HqTtOg+yvF", ["direction", "node", "__prefab", "handle"], 2, 1, 4, 1], ["f8eb7p3AvlNIqdosAbz3+gZ", ["node", "__prefab", "icon"], 3, 1, 4, 1], ["526e1FCQoxNU6UONVMPBSRV", ["node", "__prefab", "bodyNode", "head<PERSON><PERSON><PERSON>", "head<PERSON><PERSON><PERSON>", "shoulderRComp", "shoulderLComp", "bodyAComp", "bodyBComp", "bodyCComp", "handRComp", "handLComp", "legComp", "footComp", "<PERSON>n<PERSON><PERSON><PERSON>", "wqRComp", "wqLComp", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "mountsSpineComp"], 3, 1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["79625nJPs5FErtP6kcjZWHi", ["node", "__prefab"], 3, 1, 4]], [[11, 0, 2], [14, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [1, 0, 5, 3, 4, 7, 2], [5, 0, 1, 1], [2, 3, 4, 5, 1], [4, 0, 5, 4, 3, 2], [34, 0, 1, 1], [16, 0, 2], [26, 0, 1, 1], [19, 0, 1, 2, 2], [1, 0, 5, 6, 3, 4, 7, 2], [5, 0, 1, 2, 3, 1], [3, 0, 1, 2, 3, 4, 5, 8, 6, 10, 11, 12, 9], [4, 1, 2, 5, 3, 3], [15, 0, 1, 2, 3, 4, 5, 4], [17, 0, 1, 2, 2], [18, 0, 1, 2, 3], [20, 0, 1, 2, 2], [10, 0, 2], [4, 0, 5, 4, 3, 7, 2], [6, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 7, 4, 5, 8, 6, 10, 11, 12, 10], [1, 0, 1, 5, 3, 4, 7, 3], [1, 0, 6, 3, 4, 2], [12, 0, 1, 2, 3, 4, 4], [2, 0, 3, 4, 5, 2], [2, 1, 0, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 4, 5, 6, 10, 11, 12, 8], [3, 0, 1, 2, 3, 4, 5, 6, 10, 11, 8], [4, 0, 6, 4, 3, 2], [4, 0, 1, 5, 6, 4, 3, 7, 3], [4, 0, 4, 3, 2], [1, 0, 1, 6, 3, 4, 7, 3], [1, 0, 6, 3, 4, 7, 2], [1, 0, 5, 3, 4, 2], [1, 0, 1, 5, 6, 3, 4, 7, 3], [1, 0, 1, 5, 6, 3, 4, 3], [1, 0, 1, 5, 3, 4, 3], [1, 0, 1, 2, 5, 3, 4, 7, 4], [1, 0, 5, 6, 3, 4, 2], [13, 0, 1, 2, 3, 4, 5, 3], [6, 0, 1, 4, 2, 3, 2], [2, 0, 3, 4, 7, 5, 2], [2, 0, 3, 4, 5, 6, 2], [2, 3, 4, 5, 6, 1], [2, 1, 0, 3, 4, 5, 3], [2, 0, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 1], [7, 0, 1, 1], [8, 0, 1, 2, 3], [8, 1], [21, 0, 1, 2, 2], [22, 0, 1, 2, 3], [23, 0, 1, 1], [24, 0, 1, 2, 1], [25, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 3, 7, 4, 5, 6, 10, 11, 12, 9], [3, 0, 1, 2, 3, 4, 5, 6, 10, 11, 13, 12, 8], [3, 0, 1, 2, 3, 4, 5, 9, 6, 10, 11, 9], [27, 0, 1, 1], [28, 0, 1, 2, 2], [29, 0, 1, 2, 3, 4, 5, 4], [30, 0, 1, 2, 3, 1], [31, 0, 1, 2, 3, 2], [32, 0, 1, 2, 1], [33, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1]], [[[[19, "GuildOtherInfoUI"], [30, "GuildOtherInfoUI", [-18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28], [[[2, -15, [0, "73IBqyKcRJa7t2s8jswn3k"], [5, 750, 1334]], -16, [25, 45, 750, 1334, -17, [0, "f5WEgrmaxERL3sP6LDsvcp"]]], 4, 1, 4], [41, "c46/YsCPVOJYA4mWEpNYRx", null, -14, 0, [[42, ["handle"], -7, [8, ["2dQ2wXCkFJzY5oPQvWqcNX"]], -6, [8, ["09JETm349J/q+uedF63ohs"]]], [21, ["userModel3"], -9, -8, [8, ["d51ZN24RRESpuoHZqYMWmi"]]], [21, ["userModel2"], -11, -10, [8, ["d51ZN24RRESpuoHZqYMWmi"]]], [21, ["userModel1"], -13, -12, [8, ["d51ZN24RRESpuoHZqYMWmi"]]]], [-1, -2, -3, -4, -5]]], [33, "item", 512, [-31, -32, -33, -34, -35, -36, -37, -38, -39], [[2, -29, [0, "a7cQtZJUxB2LBJzpJVxvQJ"], [5, 682, 124]], [26, 2, -30, [0, "c5J+WwF3lOpqUqh6LYwgLL"], 45]], [1, "d3pn++bNRDBLrjG40JZqZR", null, null, null, 1, 0], [1, 0, -72, 0]], [34, "zhezhao", [-42, -43, -44, -45, -46, -47, -48, -49], [[2, -40, [0, "32PeyRYcdFbom707Cp0pSh"], [5, 718, 44]], [5, -41, [0, "24U1tpueBNkIdnAsWm7+v+"], 25]], [1, "d94YtnO8NEmIii7cF0CG+Y", null, null, null, 1, 0], [1, 0, -161.868, 0]], [11, "dizuo_2", 3, [-52, -53, -54, -55], [[2, -50, [0, "fflQCEnHtKKY/QF2SQQ+MW"], [5, 205, 100]], [5, -51, [0, "f0/HQA7L1NvJGdBibxoW6N"], 19]], [1, "c2EE/dWCFMpJbGPd04clxd", null, null, null, 1, 0], [1, 0, 78.536, 0]], [11, "bottom", 1, [-57, -58, -59, -60, -61], [[4, -56, [0, "19R7ZGokBG54COmk1cWPEK"]]], [1, "47LhtlOYZD17Bh661FP15Q", null, null, null, 1, 0], [1, 0, 74.585, 0]], [35, "bg1", 1, [[2, -62, [0, "bcPTLnhVdDy76ZP3xBsY4e"], [5, 750, 1334]], [43, 0, -63, [0, "386peeUKVBFIEMfRDPGfTk"], [4, 3019898880], 0], [48, -65, [0, "1fwOYsMZlJ/6Y1MqHAaXVu"], [[50, "cab5fRvbX9BYLwMiSMYhgVL", "onClickCloseBtn", 1]], [4, 4292269782], -64], [25, 45, 39, 39, -66, [0, "1cp19jOdBIM7/GgdBoaBjv"]]], [1, "44XUsfacZAr7uyh6d+dh07", null, null, null, 1, 0]], [11, "dizuo_1", 3, [-69, -70, -71], [[2, -67, [0, "efU0XPWMhNQ4rrNSVOIYGm"], [5, 162, 80]], [5, -68, [0, "34Gdz5nYpDqoq/bUWmzP8r"], 13]], [1, "ccUO8gwZRHypPBCCcGrQtt", null, null, null, 1, 0], [1, -209.522, 84.523, 0]], [11, "dizuo_3", 3, [-74, -75, -76], [[2, -72, [0, "cdR0O8N4tLKoH0FLY9EQGe"], [5, 162, 80]], [5, -73, [0, "d77h7FotpDQLHDWZeXhU/B"], 24]], [1, "1fLj12aZNL6Iv/xzj9aYgz", null, null, null, 1, 0], [1, 214.283, 85.713, 0]], [11, "bg3", 1, [-79, 3], [[2, -77, [0, "16gB0bDltPWKIdDvyPBWVV"], [5, 720, 371]], [5, -78, [0, "dePyd+DvFNPbDgsNK+M8+M"], 26]], [1, "b2Z3ggGgBMkr0p+HmKascD", null, null, null, 1, 0], [1, 0, 318.392, 0]], [8, ["c46/YsCPVOJYA4mWEpNYRx"]], [8, ["c46/YsCPVOJYA4mWEpNYRx"]], [14, 0, {}, 1, [15, "48QgggtnpApZxwpAfGYRjg", null, null, -87, [16, "edgkuI1R1FAL8N8xflkMc8", 1, [[17, "MyScrollBar", ["_name"], -80], [10, ["_lpos"], -81, [1, 355.985, -238.047, 0]], [10, ["_lrot"], -82, [3, 0, 0, 0, 1]], [10, ["_euler"], -83, [1, 0, 0, 0]], [18, ["_contentSize"], [8, ["ae8XjF0uhCJI6F8w5MXn7m"]], [5, 6, 630]], [52, ["scrollView"], -85, -84], [17, false, ["autoHide"], -86]]], 33]], [31, "ScrollView", 512, 1, [-91], [[[12, -88, [0, "46O5bqOZxD9JwgxnglDb4m"], [5, 720, 630], [0, 0.5, 1]], -89, -90], 4, 1, 1], [1, "b4Q+0zTXRH06RkZ+C1DXKb", null, null, null, 1, 0], [1, 0, 80.11300000000006, 0]], [36, "view", 512, 13, [-95], [[12, -92, [0, "9445nWe4tEr4z0BP071cdT"], [5, 700, 630], [0, 0.5, 1]], [54, -93, [0, "c0EBnFmLVHCZDrgojBJi8c"]], [55, -94, [0, "b6pIBUO4lPxY+Jk/Kdb7+c"], [4, 16777215]]], [1, "58+qJypF1Pt6UraNSYRqgC", null, null, null, 1, 0], [1, -6.087999999999999, 0, 0]], [37, "content", 512, 14, [2], [[12, -96, [0, "c7IrX5+ohP17I1npnZhka5"], [5, 700, 134], [0, 0.5, 1]], [56, 1, 2, 10, 10, -97, [0, "98bcJxVMVFmYdCYjt3Bt8o"]]], [1, "1fgU+qIDtL7ZSvLkU6Odd0", null, null, null, 1, 0]], [3, "txtName", 2, [[2, -98, [0, "c0YLCpQd1HyYiGNE4LbfAd"], [5, 150, 29.2]], [57, "玩家名称", 18, 18, 20, 2, false, false, true, -99, [0, "79ZNhWz75OtZoEK3xiqzfd"], 37], [9, -100, [0, "a5Qz2b5W9FFaOElN83fQRL"]], [49, -101, [0, "3a1d8R7qBHGamL6achRH0O"]]], [1, "a4ZZ9dpmZK/qa7PGbnMdW9", null, null, null, 1, 0], [1, -157.626, 2, 0]], [38, "bg2", 512, 1, [[2, -102, [0, "b1k+nPcm1LFbDhv8PQA4eK"], [5, 750, 1147]], [26, 0, -103, [0, "b38xlppbBJtIrGF4wn1BaW"], 1], [60, -104, [0, "08X1mph+FMyrUrqDbtUrno"]]], [1, "a2uwIz9xZBvoouJRuV0jtN", null, null, null, 1, 0]], [3, "Label", 3, [[2, -105, [0, "8aBKB3rQZPMrHoGTfOqrjc"], [5, 59.02198791503906, 35.5]], [13, "等级：", 22, 22, 25, false, false, 1, true, -106, [0, "ddRw16ihFN0rMnx5QH9vs6"], 7], [9, -107, [0, "e2v/gXAv9N2qLYDlYwhebq"]]], [1, "8fjaE1Qj9H1ZMW2SHMJBe3", null, null, null, 1, 0], [1, -270.262, 0, 0]], [3, "Label1", 3, [[2, -108, [0, "e92lcp01VFY6Y18vBF2DDN"], [5, 59, 35.5]], [13, "成员：", 22, 22, 25, false, false, 1, true, -109, [0, "5dZMvqgfZBe7y1wN6V7QVI"], 8], [9, -110, [0, "5eda7eE75MPZFKq+qtpp5z"]]], [1, "c0QQFnLiRKGY9itZ6+dBu6", null, null, null, 1, 0], [1, -78.738, 0, 0]], [14, 0, {}, 7, [15, "c46/YsCPVOJYA4mWEpNYRx", null, null, -111, [16, "61vgU7PqVL4Y0usfiYAN4I", 1, [[17, "userModel2", ["_name"], 10], [10, ["_lpos"], 10, [1, -3, 14.457, 0]], [10, ["_lrot"], 10, [3, 0, 0, 0, 1]], [10, ["_euler"], 10, [1, 0, 0, 0]], [10, ["_lscale"], 10, [1, 0.5, 0.5, 1]]]], 9]], [11, "aignBg", 7, [-114], [[2, -112, [0, "0a9aU3LJtGxY8cWIz3bMYM"], [5, 39, 124]], [5, -113, [0, "dfJTZ4acRAFr9W72VAHYm+"], 12]], [1, "a4sNsO9t5IyoR7PoJAF4BV", null, null, null, 1, 0], [1, 58.45099999999999, 147.046, 0]], [3, "Label", 21, [[2, -115, [0, "9eSGv5S5JJ1KrNQ3RSEB6s"], [5, 40, 110.5]], [22, "战力第一", 22, 22, 25, 3, false, false, 1, true, -116, [0, "2fHPht3NdEprTgXfOc0zF5"], 11], [9, -117, [0, "276o/950lFGJrjvmvJJ6Td"]]], [1, "34+cmuAfpPfZjxMcqYSpwz", null, null, null, 1, 0], [1, 0, -6.493000000000052, 0]], [14, 0, {}, 4, [15, "c46/YsCPVOJYA4mWEpNYRx", null, null, -118, [16, "aer3/q14xD3oWQapXbyADW", 1, [[53, "userModel1", ["_name"], [8, ["c46/YsCPVOJYA4mWEpNYRx"]]], [18, ["_lpos"], [8, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, -3, 14.457, 0]], [18, ["_lrot"], [8, ["c46/YsCPVOJYA4mWEpNYRx"]], [3, 0, 0, 0, 1]], [18, ["_euler"], [8, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0, 0, 0]], [18, ["_lscale"], [8, ["c46/YsCPVOJYA4mWEpNYRx"]], [1, 0.5, 0.5, 1]]]], 14]], [11, "aignBg", 4, [-121], [[2, -119, [0, "c7/MlQUUBP46c1KvDj1Bms"], [5, 39, 124]], [5, -120, [0, "01n7e1HwdAOIHfU/Ov/Qwx"], 18]], [1, "40hOGZh05Oh6MOUmvnMEvY", null, null, null, 1, 0], [1, 59.522999999999996, 182.665, 0]], [3, "Label", 24, [[2, -122, [0, "56vmU3LrlNGo/AtngPvFgh"], [5, 40, 60.49999999999999]], [22, "会长", 22, 22, 25, 3, false, false, 1, true, -123, [0, "5d/xRdP1dILbfIfk/ZNH+c"], 17], [9, -124, [0, "71ayFYrZBMFLFFZgfYFo2l"]]], [1, "60f4LMhOxNIYzLzZDziAri", null, null, null, 1, 0], [1, 0, -6.493000000000052, 0]], [14, 0, {}, 8, [15, "c46/YsCPVOJYA4mWEpNYRx", null, null, -125, [16, "8fcNBxC7lClY+lXKAc6nTq", 1, [[17, "userModel3", ["_name"], 11], [10, ["_lpos"], 11, [1, -3, 14.457, 0]], [10, ["_lrot"], 11, [3, 0, 0, 0, 1]], [10, ["_euler"], 11, [1, 0, 0, 0]], [10, ["_lscale"], 11, [1, 0.5, 0.5, 1]]]], 20]], [11, "aignBg", 8, [-128], [[2, -126, [0, "069l/V5/VE3IGmtBmcAqpr"], [5, 39, 124]], [5, -127, [0, "5fu/fUlXtGELQmBq8CfJ3P"], 23]], [1, "82qLs1+HRB6oEXL7dPOlSz", null, null, null, 1, 0], [1, 63.285, 142.855, 0]], [3, "Label", 27, [[2, -129, [0, "dehsHr93dAcLlqcEa6ys/p"], [5, 40, 110.5]], [22, "贡献第一", 22, 22, 25, 3, false, false, 1, true, -130, [0, "08EqJckEBHjI6iKSPVkRis"], 22], [9, -131, [0, "0epWQhO4ZK179Qghz/owfK"]]], [1, "4e8b6XKL1MbJBZ7ZdAzsfK", null, null, null, 1, 0], [1, 0, -6.493000000000052, 0]], [3, "OutlineLabel", 5, [[2, -132, [0, "2eL0kVGbJPhJdfXtWHCMjf"], [5, 92.13198852539062, 35.5]], [13, "角色名称", 22, 22, 25, false, false, 1, true, -133, [0, "5aafwBu/ZE1LpZX7kHbQG9"], 29], [9, -134, [0, "36DP5TL1ZIb5j79Jzh//2j"]]], [1, "f1pW11X51Lj75pNEC5dxqE", null, null, null, 1, 0], [1, -188.613, 24.386, 0]], [3, "OutlineLabel-001", 5, [[2, -135, [0, "dc8tp1VTtId6nZJicAMZub"], [5, 48.02198791503906, 35.5]], [13, "等级", 22, 22, 25, false, false, 1, true, -136, [0, "2c1egDXHpN+KK/b39HlZQN"], 30], [9, -137, [0, "9crVHgsaRO1bl1bOVcCKog"]]], [1, "41/s8zWJlKSq2wOv2a2JZ1", null, null, null, 1, 0], [1, 102.966, 24.386, 0]], [3, "OutlineLabel-003", 5, [[2, -138, [0, "81NXRIbrFOQ6J8rSbOGVsM"], [5, 48.02198791503906, 35.5]], [13, "战力", 22, 22, 25, false, false, 1, true, -139, [0, "afOxJb/PFLPZj5XJbzLHc/"], 31], [9, -140, [0, "fdeX5m3xxOOLDdlvOAOuVb"]]], [1, "93xQI7B05DnpUv80tdTC1S", null, null, null, 1, 0], [1, 226.698, 24.386, 0]], [3, "OutlineLabel-004", 5, [[2, -141, [0, "0aaItWKUpERplMcaHPCFt3"], [5, 48.04399108886719, 35.5]], [13, "职业", 22, 22, 25, false, false, 1, true, -142, [0, "2aki32OZpFhawNLrAGzTOR"], 32], [9, -143, [0, "96brd2p2RE7opiMbwzUlBx"]]], [1, "caJP25/fhCubacpZtj6Nqi", null, null, null, 1, 0], [1, -53.687, 24.386, 0]], [8, ["48QgggtnpApZxwpAfGYRjg"]], [8, ["c46/YsCPVOJYA4mWEpNYRx"]], [23, "txtLv", 512, 2, [[2, -144, [0, "684duXP1pCo4xiE3WfwVfo"], [5, 39.94596862792969, 29.2]], [58, "200", 18, 18, 20, false, false, true, -145, [0, "d9YORMZ71DLLqPsXpmaeSu"], [4, 4284279425], 38], [9, -146, [0, "0eXwdpe+JNZJAUWtKGmxSD"]]], [1, "4aRyiDA/5IPaezzCEwNK/Y", null, null, null, 1, 0], [1, 105.051, 2, 0]], [23, "txtZhanli", 512, 2, [[12, -147, [0, "02ZOEWcDVCaYE5Hvm1TLZZ"], [5, 16.491989135742188, 29.2], [0, 0, 0.5]], [28, "0", 18, 18, 20, false, false, true, -148, [0, "54mtfdGLVDv5vUV7i6n7KZ"], 40], [9, -149, [0, "57KC4Nl45AuJW2Si6eo8Ry"]]], [1, "9aOjHtuatBPp9FC0/cgunZ", null, null, null, 1, 0], [1, 223.119, 2, 0]], [39, "txtJob", 512, false, 2, [[2, -150, [0, "fbGcx/KsdHbpWttopJrnrV"], [5, 16.491989135742188, 29.2]], [28, "0", 18, 18, 20, false, false, true, -151, [0, "82BQQgAN1FdoN5LlEd8iB3"], 41], [9, -152, [0, "437tDw69pOqJPErBm0yRDm"]]], [1, "3ejwG8pl1GOIvLUUNTG5ts", null, null, null, 1, 0], [1, -51.819, 2, 0]], [20, "lbl_lv", 1, [[[12, -153, [0, "df73fGxNpKgqDU2PjHHghL"], [5, 19.267990112304688, 35.5], [0, 0, 0.5]], -154, [9, -155, [0, "10NSgiZNtAJZsQeC635Wu4"]]], 4, 1, 4], [1, "0cVP0qt51NV5MZHSgsOep2", null, null, null, 1, 0], [1, -241.691, 156.52400000000011, 0]], [20, "lbl_people", 1, [[[12, -156, [0, "2a/ZP1oa9PMLCJad5PFixE"], [5, 50.3759765625, 35.5], [0, 0, 0.5]], -157, [9, -158, [0, "adXExkm6pPkZ3p68AezE0Q"]]], 4, 1, 4], [1, "40xEI5JIdKmpmtgzeGXwK8", null, null, null, 1, 0], [1, -44.46500000000003, 156.52400000000011, 0]], [20, "guildName", 1, [[[2, -159, [0, "6ezmzkPhxCapLL6vKnYIgA"], [5, 40.10798645019531, 29.2]], -160, [9, -161, [0, "80qYlyPv1HkYFm4XvtDiUQ"]]], 4, 1, 4], [1, "admxxpyOxCQa+9V5SE9nZG", null, null, null, 1, 0], [1, 0, 518.193, 0]], [61, true, 1, [0, "2aFGDyp+pEaJ7/P1mlsZCG"]], [3, "btnClose", 1, [[2, -162, [0, "dbFyttuhFLeb4eDTxlzUG+"], [5, 58, 57]], [5, -163, [0, "81TYKeu5dIbIrl7GNsUYcZ"], 2]], [1, "dck7UNPflDb6XUcKtKuxxx", null, null, null, 1, 0], [1, 343.404, 570.468, 0]], [3, "biaotibg", 9, [[2, -164, [0, "a9PsZ8dylN8rOpyGCXOblZ"], [5, 374, 32]], [5, -165, [0, "31elf1bXdPD78hdVMlx2II"], 3]], [1, "c4RywlHbJIYaJsaikg0Io8", null, null, null, 1, 0], [1, 0, 201.297, 0]], [3, "icon_1", 3, [[2, -166, [0, "06jHVG6qdMOZZlb8fMuzpp"], [5, 36, 36]], [5, -167, [0, "b2UPk/tVxDWaFj1XIZ9Hlt"], 4]], [1, "76ng3y/sRG/6VHNKAT16XK", null, null, null, 1, 0], [1, -320.55, 0, 0]], [3, "icon_2", 3, [[2, -168, [0, "bcCmhJrb5Et5JuIr8BomFW"], [5, 36, 36]], [5, -169, [0, "61KiiEy95Bw7rE6+PTtIIn"], 5]], [1, "e91nmhfhhOMJeFEEd9/upp", null, null, null, 1, 0], [1, -154.227, 0, 0]], [3, "icon_3", 3, [[2, -170, [0, "f6+H0fUPZJCq3r4PAiOB11"], [5, 22, 22]], [5, -171, [0, "bfZVhzl4pKh6UyxIK1ne1+"], 6]], [1, "30aZuqe5NPH4c7YrgUAmTv", null, null, null, 1, 0], [1, -128.019, 0, 0]], [3, "icon_zhanli", 7, [[2, -172, [0, "16CEO5SYNJeqkgJlncG9Lb"], [5, 45, 54]], [5, -173, [0, "798APQENJNOLlSm5AigyVi"], 10]], [1, "65CWnBV1FFII1YLE7OKiXq", null, null, null, 1, 0], [1, -69.047, 184.522, 0]], [3, "icon_guang", 4, [[2, -174, [0, "5dFJFGsgtPC7jWkzLyXgDo"], [5, 226, 131]], [5, -175, [0, "0d78xAvY5Ma6stjdf9Fob8"], 15]], [1, "326b46gPNFPJrOdAw8kBag", null, null, null, 1, 0], [1, 0, 48.809, 0]], [3, "icon_huizhang", 4, [[2, -176, [0, "005nXDcFZCvahA6izRLGGk"], [5, 45, 54]], [5, -177, [0, "64qQEWVwtOkI7YrrrZI1X6"], 16]], [1, "45gvm1hF5MKob6v4aAFrQX", null, null, null, 1, 0], [1, -69.047, 223.807, 0]], [3, "icon_gongxian", 8, [[2, -178, [0, "2dWWuB9YhHnY53ykyNIqjL"], [5, 45, 54]], [5, -179, [0, "5dMkqgcqBP1bN9dG/L2U7y"], 21]], [1, "6bQ69IxVVMHbGD2gd7F2TB", null, null, null, 1, 0], [1, -47.619, 178.57, 0]], [3, "Sprite", 1, [[2, -180, [0, "3diE4L+c5HvK3OGhoNTu8E"], [5, 720, 630]], [27, 1, 0, false, -181, [0, "acmGE0KCJKSbw9DFem3PIh"], 27]], [1, "9fvR9UTB5I0KiC9iklsBAt", null, null, null, 1, 0], [1, 0, -235.129, 0]], [3, "lblbg", 5, [[2, -182, [0, "88w9hbpDRJwpAkPANsbKyO"], [5, 728, 45]], [5, -183, [0, "4dBc4MFp5JlLblp2xOHU9U"], 28]], [1, "19lvV14dVJEKwWQ8oNgcN5", null, null, null, 1, 0], [1, 0, 27.717, 0]], [14, 0, {}, 2, [15, "c46/YsCPVOJYA4mWEpNYRx", null, null, -184, [16, "8cAnzWvShO/6yS4lODLCaZ", 1, [[17, "UIPlayerHead", ["_name"], 34], [10, ["_lpos"], 34, [1, -278.431, 0, 0]], [10, ["_lrot"], 34, [3, 0, 0, 0, 1]], [10, ["_euler"], 34, [1, 0, 0, 0]]]], 34]], [3, "rank", 2, [[2, -185, [0, "bf08tMoupNOoOGONXwI/qV"], [5, 38, 38]], [44, 0, -186, [0, "acl78ZYGRFTIXWfjb/eJb3"], 35, 36]], [1, "28fCjHgWlHA4A/ER8E9zIs", null, null, null, 1, 0], [1, -307.146, 28.789, 0]], [3, "pic", 2, [[2, -187, [0, "23Sp2dnMxIg5ClcAQeHt4Y"], [5, 33, 31]], [5, -188, [0, "e4RhjC53dD9rU1XOyUbv3y"], 39]], [1, "efcGoiD5NAUaQLKvqMhsIh", null, null, null, 1, 0], [1, 202.821, 0, 0]], [3, "jobPic", 2, [[2, -189, [0, "b78hjjQxJKOYlWI714ND86"], [5, 60, 60]], [45, -190, [0, "7ez1yidzxMoI8gGED90Rgo"], 42, 43]], [1, "dcrZhENDJI/o22UfzmWXAF", null, null, null, 1, 0], [1, -49.004, -1.067, 0]], [3, "btn_info", 2, [[2, -191, [0, "ecjJFsAnBNI67CyUWGAJyS"], [5, 42, 40]], [5, -192, [0, "0aWAs4Oc9FSIgglnh1vfic"], 44]], [1, "dbDdF0mCZB2ZUQkUD2mJhu", null, null, null, 1, 0], [1, -260.323, -21.446000000000026, 0]], [8, ["2dQ2wXCkFJzY5oPQvWqcNX"]], [62, 0.23, 0.75, false, 13, [0, "abRHLt+nZDV4b4NR/4Pex1"], 15], [63, 13, [0, "36XFL6eQVAdLZYE+0ddr8c"], 2, [51]], [29, "0", 22, 22, 25, false, false, true, 38, [0, "8aqci8xVRF/bEo+r1B16x9"]], [29, "1/50", 22, 22, 25, false, false, true, 39, [0, "98RL9kRDdO34sTRO5Zd3T0"]], [59, "名称", 18, 18, 20, false, false, 0, true, 40, [0, "2a7IQPAUxDoqlfex5Di1vP"]]], 0, [0, -1, 53, 0, -2, 12, 0, -3, 26, 0, -4, 23, 0, -5, 20, 0, 7, 12, 0, 8, 12, 0, 7, 26, 0, 8, 41, 0, 7, 20, 0, 8, 41, 0, 7, 23, 0, 8, 41, 0, 4, 1, 0, 0, 1, 0, -2, 41, 0, 0, 1, 0, -1, 6, 0, -2, 17, 0, -3, 42, 0, -4, 9, 0, -5, 51, 0, -6, 5, 0, -7, 12, 0, -8, 38, 0, -9, 39, 0, -10, 40, 0, -11, 13, 0, 0, 2, 0, 0, 2, 0, -1, 53, 0, -2, 54, 0, -3, 16, 0, -4, 35, 0, -5, 55, 0, -6, 36, 0, -7, 37, 0, -8, 56, 0, -9, 57, 0, 0, 3, 0, 0, 3, 0, -1, 44, 0, -2, 45, 0, -3, 46, 0, -4, 18, 0, -5, 19, 0, -6, 7, 0, -7, 4, 0, -8, 8, 0, 0, 4, 0, 0, 4, 0, -1, 23, 0, -2, 48, 0, -3, 49, 0, -4, 24, 0, 0, 5, 0, -1, 52, 0, -2, 29, 0, -3, 30, 0, -4, 31, 0, -5, 32, 0, 0, 6, 0, 0, 6, 0, 12, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -1, 20, 0, -2, 47, 0, -3, 21, 0, 0, 8, 0, 0, 8, 0, -1, 26, 0, -2, 50, 0, -3, 27, 0, 0, 9, 0, 0, 9, 0, -1, 43, 0, 5, 33, 0, 5, 33, 0, 5, 33, 0, 5, 33, 0, 13, 59, 0, 5, 58, 0, 5, 58, 0, 4, 12, 0, 0, 13, 0, -2, 59, 0, -3, 60, 0, -1, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 4, 20, 0, 0, 21, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 4, 23, 0, 0, 24, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 4, 26, 0, 0, 27, 0, 0, 27, 0, -1, 28, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 32, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, -2, 61, 0, 0, 38, 0, 0, 39, 0, -2, 62, 0, 0, 39, 0, 0, 40, 0, -2, 63, 0, 0, 40, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 4, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 9, 1, 2, 10, 15, 3, 10, 9, 41, 14, 60, 41, 15, 62, 41, 16, 61, 41, 17, 63, 192], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 62, 63], [2, 2, 2, 2, 2, 2, 2, 3, 3, 6, 2, 3, 2, 2, 6, 2, 2, 3, 2, 2, 6, 2, 3, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 6, 6, 2, 11, 3, 3, 2, 3, 3, 2, 11, 2, 2, 3, 3, 3], [6, 7, 8, 9, 4, 4, 10, 1, 1, 2, 11, 1, 3, 5, 2, 12, 13, 1, 3, 14, 2, 15, 1, 3, 5, 16, 17, 18, 19, 1, 1, 1, 1, 20, 21, 22, 23, 1, 1, 24, 1, 1, 25, 26, 27, 28, 1, 1, 1]], [[[19, "MyScrollBar"], [24, "MyScrollBar", [-6], [[2, -2, [0, "ae8XjF0uhCJI6F8w5MXn7m"], [5, 6, 200]], [27, 1, 0, false, -3, [0, "50ybzMZVlK5Jxr6WAghGGD"], 1], [64, 1, -5, [0, "2dQ2wXCkFJzY5oPQvWqcNX"], -4]], [1, "48QgggtnpApZxwpAfGYRjg", null, null, null, -1, 0]], [3, "bar", 1, [[12, -7, [0, "0cIvL3kQtDpbtwMErjwTyE"], [5, 17, 193], [0, 0.5, 0]], [46, 1, 0, -8, [0, "d9ANShN19DaY8NCq/N+OjY"], 0]], [1, "09JETm349J/q+uedF63ohs", null, null, null, 1, 0], [1, 0, -98.989, 0]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 18, 2, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 9, 1, 8], [0, 0], [2, 2], [29, 30]], [[{"name": "pub_frame_xiao_6", "rect": {"x": 0, "y": 0, "width": 418, "height": 306}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 418, "height": 306}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-209, -153, 0, 209, -153, 0, -209, 153, 0, 209, 153, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 306, 418, 306, 0, 0, 418, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -209, "y": -153, "z": 0}, "maxPos": {"x": 209, "y": 153, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [31]], [[[19, "UIPlayerHead"], [32, "UIPlayerHead", [[[4, -2, [0, "6dV2Ske+5MYbBtsQ9bfHmo"]], -3, [65, -5, [0, "54BQwDKY9FlJPSQ6/WOXeX"], -4]], 4, 1, 4], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [47, 2, false, 1, [0, "e3lZ7YCexMoYPzNGCZ0et6"]]], 0, [0, 4, 1, 0, 0, 1, 0, -2, 2, 0, 19, 2, 0, 0, 1, 0, 9, 1, 5], [], [], []], [[{"name": "default_sprite", "rect": {"x": 0, "y": 0, "width": 2, "height": 2}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2, "height": 2}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1, -1, 0, 1, -1, 0, -1, 1, 0, 1, 1, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2, 2, 2, 0, 0, 2, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -1, "y": -1, "z": 0}, "maxPos": {"x": 1, "y": 1, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [32]], [[[19, "UserModelUI"], [24, "UserModelUI", [-22, -23], [[2, -2, [0, "d16u+jlqNIPq62s6S8P6LM"], [5, 500, 500]], [66, -21, [0, "d51ZN24RRESpuoHZqYMWmi"], -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [40, "bodyNode", 1, [-25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40], [[4, -24, [0, "53VHLjFdlIqpRTV77tl+y4"]]], [1, "d1Uq/MsWBIP6MbVdom+lVF", null, null, null, 1, 0]], [6, "mounts", 1, [[[4, -41, [0, "82CQAP+C5Mzr166Ct0HUdC"]], -42], 4, 1], [1, "3buC4ItP9D8ZT/Z3w+/LkP", null, null, null, 1, 0]], [6, "head_b", 2, [[[4, -43, [0, "16aKme4P1K0Y/9WSVylPWW"]], -44], 4, 1], [1, "dcmDy19K5DsK12WoleFYAZ", null, null, null, 1, 0]], [6, "forearm_l", 2, [[[4, -45, [0, "44Hzs1/eBEBrNEr6Ke/uZm"]], -46], 4, 1], [1, "edb979qLpCnq7S74HlNWwH", null, null, null, 1, 0]], [6, "body_c", 2, [[[4, -47, [0, "883fPFcJZMobMkXHBGZLSX"]], -48], 4, 1], [1, "373H4IMnNOmIh3UylaP1pH", null, null, null, 1, 0]], [6, "hand_l", 2, [[[4, -49, [0, "77J/ZZwk9AwK9xB8oDBIr4"]], -50], 4, 1], [1, "0duOU11qlBeJ2atv+0tOzo", null, null, null, 1, 0]], [6, "wq_l", 2, [[[4, -51, [0, "6cEPVXMgFBboDr/xSchZUm"]], -52], 4, 1], [1, "c1Uz522nNCqotkag/Wvyer", null, null, null, 1, 0]], [6, "body_b", 2, [[[4, -53, [0, "17c/w58VlM37yPCyHUG9gd"]], -54], 4, 1], [1, "cfTc30RgRK0agdygfME8dw", null, null, null, 1, 0]], [6, "leg", 2, [[[4, -55, [0, "8f8d48e/lD17WOASkNcqYk"]], -56], 4, 1], [1, "045ZaBsFhHkpr+MY+WmAtv", null, null, null, 1, 0]], [6, "foot", 2, [[[4, -57, [0, "096ZId9XhMur4CGYZxRj5r"]], -58], 4, 1], [1, "4fpf6895lBm6GGG1pVFH5X", null, null, null, 1, 0]], [6, "shoulder_l", 2, [[[4, -59, [0, "c1+/kqNdFGtaYE89CsdYh4"]], -60], 4, 1], [1, "24wFzjZuBLPY1qsORZxtzC", null, null, null, 1, 0]], [6, "head_a", 2, [[[4, -61, [0, "db+hRPh/1Eu4drin8Sz9jf"]], -62], 4, 1], [1, "5fP0LZnItJsK9mBGSaxkfI", null, null, null, 1, 0]], [6, "forearm_r", 2, [[[4, -63, [0, "76UcAD4ZdEtKNbMVKMr8Nh"]], -64], 4, 1], [1, "9f7+BPcFZEkZztF7xGwP2O", null, null, null, 1, 0]], [6, "body_a", 2, [[[4, -65, [0, "1dHkzg8TBNHJPl498a7df5"]], -66], 4, 1], [1, "bc0Jit5EhPBaoKwmDJQpbB", null, null, null, 1, 0]], [6, "wq_r", 2, [[[4, -67, [0, "09O7bh99hKxpLGTVL9bPkg"]], -68], 4, 1], [1, "1atu6tAZNJr4S6rT5U1RLe", null, null, null, 1, 0]], [6, "hand_r", 2, [[[4, -69, [0, "96P78HfdhBzaz7cYn962yh"]], -70], 4, 1], [1, "c1vDYCk+NBtY5+3/mYuek2", null, null, null, 1, 0]], [6, "shoulder_r", 2, [[[4, -71, [0, "19ZcXBJxZC1JLSnSikV1Zs"]], -72], 4, 1], [1, "efIabgMspB9qkdPQOA5CAA", null, null, null, 1, 0]], [6, "dun", 2, [[[4, -73, [0, "09XrXqeSdN4IQrfcLxS38N"]], -74], 4, 1], [1, "6f98W+MVpG3K6ECA5u8zCc", null, null, null, 1, 0]], [7, 3, [0, "131ZoUGzxJ4LjlnPMeVCg3"]], [7, 4, [0, "1ejxWDBA9AMKU/+mLe2/Ru"]], [7, 5, [0, "10SA5+aeRBgLXypg5xAfco"]], [7, 6, [0, "3a1ZDm0MNKI5g41S2+V/Nz"]], [7, 7, [0, "51pbxcGfpASIgZp9CjLPh2"]], [7, 8, [0, "23phfAL6tBeJ8EQ2YxXE6Q"]], [7, 9, [0, "f5aqOum/NG/6rPlFdpc3jK"]], [7, 10, [0, "e0CROpcf5Jk4IjvvSa/vl2"]], [7, 11, [0, "51YkPr1p1K0KliOZynEdN7"]], [7, 12, [0, "e3EW0PPxZGxIWVEGR/MsPg"]], [7, 13, [0, "9dn4Fb55FAVYed9/CogroS"]], [7, 14, [0, "8cU12PMW9CrZ7PHsK2ZxHx"]], [7, 15, [0, "f11dOVDaVE6qUUYMuEEFyf"]], [7, 16, [0, "e3R3P6QNVMOJbfUtFynJ+M"]], [7, 17, [0, "23UdJotXFHUJP0bfuxnPTD"]], [7, 18, [0, "4bipnXbOFAHoEvBJ9vyWYS"]], [7, 19, [0, "7a7AzlN7ZEybid6F0B7xts"]]], 0, [0, 4, 1, 0, 0, 1, 0, 20, 20, 0, 21, 22, 0, 22, 31, 0, 23, 25, 0, 24, 33, 0, 25, 36, 0, 26, 28, 0, 27, 27, 0, 28, 24, 0, 29, 34, 0, 30, 23, 0, 31, 26, 0, 32, 32, 0, 33, 29, 0, 34, 35, 0, 35, 21, 0, 36, 30, 0, 37, 2, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, -8, 11, 0, -9, 12, 0, -10, 13, 0, -11, 14, 0, -12, 15, 0, -13, 16, 0, -14, 17, 0, -15, 18, 0, -16, 19, 0, 0, 3, 0, -2, 20, 0, 0, 4, 0, -2, 21, 0, 0, 5, 0, -2, 22, 0, 0, 6, 0, -2, 23, 0, 0, 7, 0, -2, 24, 0, 0, 8, 0, -2, 25, 0, 0, 9, 0, -2, 26, 0, 0, 10, 0, -2, 27, 0, 0, 11, 0, -2, 28, 0, 0, 12, 0, -2, 29, 0, 0, 13, 0, -2, 30, 0, 0, 14, 0, -2, 31, 0, 0, 15, 0, -2, 32, 0, 0, 16, 0, -2, 33, 0, 0, 17, 0, -2, 34, 0, 0, 18, 0, -2, 35, 0, 0, 19, 0, -2, 36, 0, 9, 1, 74], [], [], []], [[{"name": "ch<PERSON><PERSON>_gonghui_jineng_bg_04", "rect": {"x": 0, "y": 0, "width": 720, "height": 371}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 720, "height": 371}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-360, -185.5, 0, 360, -185.5, 0, -360, 185.5, 0, 360, 185.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 371, 720, 371, 0, 0, 720, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -360, "y": -185.5, "z": 0}, "maxPos": {"x": 360, "y": 185.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [33]], [[{"name": "pub_frame_da_3", "rect": {"x": 0, "y": 0, "width": 750, "height": 607}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 750, "height": 607}, "rotated": false, "capInsets": [0, 305, 0, 276], "vertices": {"rawPosition": [-375, -303.5, 0, 375, -303.5, 0, -375, 303.5, 0, 375, 303.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 607, 750, 607, 0, 0, 750, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -375, "y": -303.5, "z": 0}, "maxPos": {"x": 375, "y": 303.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [34]], [[{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "spriteFrames": ["cheng<PERSON>_gonghui_bg_01", "f5Tdyt2EFAu6vZpGi2AkO6@29f85", "chengzhen_gonghui_btn_01", "f5Tdyt2EFAu6vZpGi2AkO6@65078", "cheng<PERSON>_gonghui_btn_huang", "f5Tdyt2EFAu6vZpGi2AkO6@c4bbf", "cheng<PERSON>_gonghui_btn_hui", "f5Tdyt2EFAu6vZpGi2AkO6@57a3d", "chengzhen_gonghui_btn_info", "f5Tdyt2EFAu6vZpGi2AkO6@bc190", "cheng<PERSON>_gonghui_frame", "f5Tdyt2EFAu6vZpGi2AkO6@d6b9b", "ch<PERSON><PERSON>_gonghui_frame_01", "f5Tdyt2EFAu6vZpGi2AkO6@60875", "ch<PERSON><PERSON>_gonghui_frame_02", "f5Tdyt2EFAu6vZpGi2AkO6@992c2", "ch<PERSON><PERSON>_gonghui_frame_biaoti", "f5Tdyt2EFAu6vZpGi2AkO6@bbe12", "cheng<PERSON>_gonghui_frame_mingcheng", "f5Tdyt2EFAu6vZpGi2AkO6@84008", "cheng<PERSON>_gonghui_frame_zhezhao", "f5Tdyt2EFAu6vZpGi2AkO6@d3039", "ch<PERSON><PERSON>_gonghui_icon_04", "f5Tdyt2EFAu6vZpGi2AkO6@76df4", "ch<PERSON><PERSON>_gonghui_icon_05", "f5Tdyt2EFAu6vZpGi2AkO6@51acc", "chengzhen_gonghui_icon_06", "f5Tdyt2EFAu6vZpGi2AkO6@d1bac", "ch<PERSON><PERSON>_gonghui_icon_07", "f5Tdyt2EFAu6vZpGi2AkO6@d7174", "chengzhen_gonghui_icon_08", "f5Tdyt2EFAu6vZpGi2AkO6@c9b6e", "ch<PERSON><PERSON>_gonghui_icon_09", "f5Tdyt2EFAu6vZpGi2AkO6@469b4", "cheng<PERSON>_gonghui_icon_1", "f5Tdyt2EFAu6vZpGi2AkO6@449ff", "ch<PERSON><PERSON>_gonghui_icon_10", "f5Tdyt2EFAu6vZpGi2AkO6@37969", "ch<PERSON><PERSON>_gonghui_icon_11", "f5Tdyt2EFAu6vZpGi2AkO6@55909", "cheng<PERSON>_gonghui_icon_2", "f5Tdyt2EFAu6vZpGi2AkO6@61b16", "ch<PERSON><PERSON>_gonghui_icon_3", "f5Tdyt2EFAu6vZpGi2AkO6@5c250", "cheng<PERSON>_gonghui_icon_dizuo_01", "f5Tdyt2EFAu6vZpGi2AkO6@a9079", "cheng<PERSON>_gonghui_icon_dizuo_02", "f5Tdyt2EFAu6vZpGi2AkO6@30338", "cheng<PERSON>_gonghui_icon_gongxian", "f5Tdyt2EFAu6vZpGi2AkO6@e860c", "chengzhen_gonghui_icon_guang", "f5Tdyt2EFAu6vZpGi2AkO6@b6737", "cheng<PERSON>_gonghui_icon_huizhang", "f5Tdyt2EFAu6vZpGi2AkO6@316b7", "cheng<PERSON>_gonghui_icon_renshu", "f5Tdyt2EFAu6vZpGi2AkO6@dfeec", "cheng<PERSON>_gonghui_icon_xia<PERSON>ui", "f5Tdyt2EFAu6vZpGi2AkO6@619d5", "cheng<PERSON>_gonghui_icon_zhan<PERSON>", "f5Tdyt2EFAu6vZpGi2AkO6@8bd80", "cheng<PERSON>_gonghui_icon_zijin", "f5Tdyt2EFAu6vZpGi2AkO6@36ebe", "ch<PERSON><PERSON>_gonghui_jineng_bg_01", "f5Tdyt2EFAu6vZpGi2AkO6@b050d", "ch<PERSON><PERSON>_gonghui_jineng_bg_02", "f5Tdyt2EFAu6vZpGi2AkO6@2e459", "ch<PERSON><PERSON>_gonghui_jineng_bg_03", "f5Tdyt2EFAu6vZpGi2AkO6@2b35e", "ch<PERSON><PERSON>_gonghui_juanxian_dengji_01", "f5Tdyt2EFAu6vZpGi2AkO6@e7a67", "ch<PERSON><PERSON>_gonghui_juanxian_dengji_02", "f5Tdyt2EFAu6vZpGi2AkO6@06794", "ch<PERSON><PERSON>_gonghui_juanxian_dengji_03", "f5Tdyt2EFAu6vZpGi2AkO6@66df1", "ch<PERSON><PERSON>_gonghui_juanxian_jinbi_bg", "f5Tdyt2EFAu6vZpGi2AkO6@a8c75", "ch<PERSON><PERSON>_gonghui_juanxian_zuanshi_bg", "f5Tdyt2EFAu6vZpGi2AkO6@a5cdb", "chengzhen_gonghui_line", "f5Tdyt2EFAu6vZpGi2AkO6@925a2", "ch<PERSON><PERSON>_gonghui_mask", "f5Tdyt2EFAu6vZpGi2AkO6@1e36a", "ch<PERSON><PERSON>_gonghui_qizhi_01", "f5Tdyt2EFAu6vZpGi2AkO6@612ab", "ch<PERSON><PERSON>_gonghui_sheng<PERSON>_banner", "f5Tdyt2EFAu6vZpGi2AkO6@af0c3", "cheng<PERSON>_gonghui_sousuo", "f5Tdyt2EFAu6vZpGi2AkO6@056b3", "<PERSON><PERSON><PERSON>_gonghui_xiang<PERSON>_liebiao_bg", "f5Tdyt2EFAu6vZpGi2AkO6@ecfcb", "fb_tx", "f5Tdyt2EFAu6vZpGi2AkO6@56e1a", "gonghuifliter", "f5Tdyt2EFAu6vZpGi2AkO6@736e7"]}], [9], 0, [], [], []], [[{"name": "cheng<PERSON>_gonghui_sousuo", "rect": {"x": 398, "y": 1695, "width": 45, "height": 45}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 45, "height": 45}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_juanxian_dengji_02", "rect": {"x": 403, "y": 1793, "width": 33, "height": 33}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 33, "height": 33}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_mask", "rect": {"x": 203, "y": 702, "width": 190, "height": 225}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 190, "height": 225}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_bg_01", "rect": {"x": 2, "y": 2, "width": 664, "height": 205}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 664, "height": 205}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_jineng_bg_03", "rect": {"x": 210, "y": 2, "width": 265, "height": 253}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 265, "height": 253}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_jineng_bg_02", "rect": {"x": 210, "y": 270, "width": 206, "height": 234}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 206, "height": 234}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_dizuo_02", "rect": {"x": 203, "y": 930, "width": 205, "height": 100}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 205, "height": 100}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_huizhang", "rect": {"x": 405, "y": 1647, "width": 45, "height": 54}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 45, "height": 54}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_zijin", "rect": {"x": 438, "y": 731, "width": 25, "height": 23}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 25, "height": 23}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_icon_10", "rect": {"x": 328, "y": 1793, "width": 36, "height": 36}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 36, "height": 36}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_1", "rect": {"x": 305, "y": 1752, "width": 38, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 38, "height": 38}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_icon_09", "rect": {"x": 168, "y": 1046, "width": 22, "height": 22}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 22, "height": 22}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_icon_05", "rect": {"x": 239, "y": 1755, "width": 66, "height": 63}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 66, "height": 63}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_icon_11", "rect": {"x": 329, "y": 1686, "width": 66, "height": 63}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 66, "height": 63}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "fb_tx", "rect": {"x": 314, "y": 1595, "width": 88, "height": 88}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 88, "height": 88}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_icon_3", "rect": {"x": 387, "y": 1752, "width": 38, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 38, "height": 38}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_frame_01", "rect": {"x": 419, "y": 270, "width": 166, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 166, "height": 38}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_qizhi_01", "rect": {"x": 359, "y": 1033, "width": 104, "height": 107}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 104, "height": 107}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_xia<PERSON>ui", "rect": {"x": 405, "y": 1590, "width": 54, "height": 51}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 54, "height": 51}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_2", "rect": {"x": 346, "y": 1752, "width": 38, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 38, "height": 38}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "chengzhen_gonghui_btn_01", "rect": {"x": 359, "y": 1308, "width": 91, "height": 91}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 91, "height": 91}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_juanxian_dengji_03", "rect": {"x": 428, "y": 1743, "width": 33, "height": 33}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 33, "height": 33}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "gonghuifliter", "rect": {"x": 203, "y": 1033, "width": 153, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 153, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_icon_04", "rect": {"x": 396, "y": 829, "width": 66, "height": 63}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 66, "height": 63}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_frame_mingcheng", "rect": {"x": 396, "y": 702, "width": 39, "height": 124}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 39, "height": 124}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_zhan<PERSON>", "rect": {"x": 272, "y": 1701, "width": 45, "height": 54}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 45, "height": 54}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "chengzhen_gonghui_line", "rect": {"x": 2, "y": 1400, "width": 460, "height": 5}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 460, "height": 5}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_frame_02", "rect": {"x": 314, "y": 1420, "width": 172, "height": 45}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 172, "height": 45}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_juanxian_zuanshi_bg", "rect": {"x": 10, "y": 1420, "width": 259, "height": 332}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 259, "height": 332}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_juanxian_jinbi_bg", "rect": {"x": 97, "y": 1080, "width": 259, "height": 337}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 259, "height": 337}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_dizuo_01", "rect": {"x": 359, "y": 1143, "width": 162, "height": 80}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 162, "height": 80}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_sheng<PERSON>_banner", "rect": {"x": 97, "y": 669, "width": 408, "height": 68}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 408, "height": 68}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_jineng_bg_01", "rect": {"x": 210, "y": 507, "width": 192, "height": 227}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 192, "height": 227}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "chengzhen_gonghui_icon_guang", "rect": {"x": 10, "y": 1755, "width": 226, "height": 131}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 226, "height": 131}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_frame_biaoti", "rect": {"x": 168, "y": 669, "width": 374, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 374, "height": 32}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "chengzhen_gonghui_btn_info", "rect": {"x": 419, "y": 439, "width": 42, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 42, "height": 40}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "chengzhen_gonghui_icon_08", "rect": {"x": 50, "y": 1390, "width": 27, "height": 27}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 27, "height": 27}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "chengzhen_gonghui_icon_06", "rect": {"x": 287, "y": 1824, "width": 39, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 39, "height": 38}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_frame_zhezhao", "rect": {"x": 50, "y": 669, "width": 718, "height": 44}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 718, "height": 44}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_frame", "rect": {"x": 272, "y": 1420, "width": 278, "height": 39}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 278, "height": 39}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_icon_07", "rect": {"x": 328, "y": 1832, "width": 33, "height": 31}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 33, "height": 31}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_renshu", "rect": {"x": 438, "y": 702, "width": 25, "height": 26}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 25, "height": 26}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "ch<PERSON><PERSON>_gonghui_juanxian_dengji_01", "rect": {"x": 367, "y": 1793, "width": 33, "height": 33}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 33, "height": 33}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "cheng<PERSON>_gonghui_icon_gongxian", "rect": {"x": 239, "y": 1824, "width": 45, "height": 54}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 45, "height": 54}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]], [[{"name": "<PERSON><PERSON><PERSON>_gonghui_xiang<PERSON>_liebiao_bg", "rect": {"x": 2, "y": 669, "width": 728, "height": 45}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 728, "height": 45}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [1], [0]]]]