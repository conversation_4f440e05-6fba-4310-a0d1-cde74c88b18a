[1, ["61NXJOyyZNU6ZGh0FJkMv2", "2enw6dg1JAa5xhWIDgaiWv@faa93", "deszC1g25M9pKP5WcWcvFS@6c48a", "94FGSZedBCE46Kdw4aaHkw@f9941", "65nnJ3SlJOoYRwNWpKye8V@f9941", "2enw6dg1JAa5xhWIDgaiWv@a0708", "24DvfvAblJorxYgmviXoSm", "19pVGyOTNGDpj8v7amXbW/@f9941", "8fOvUyIEhI+YYSThw1sq2H", "2enw6dg1JAa5xhWIDgaiWv@50f42", "b4Sp8Wg5tGnoyF1UfTFhM+@f9941", "9bcMMoWKFFdKr8mEvbYxar@91927", "9bcMMoWKFFdKr8mEvbYxar@44640", "19pVGyOTNGDpj8v7amXbW/@6c48a", "2enw6dg1JAa5xhWIDgaiWv@5c9aa", "2enw6dg1JAa5xhWIDgaiWv@bbfb5", "2enw6dg1JAa5xhWIDgaiWv@0e5a4", "2enw6dg1JAa5xhWIDgaiWv@d3732", "65nnJ3SlJOoYRwNWpKye8V@6c48a", "b4Sp8Wg5tGnoyF1UfTFhM+@6c48a"], ["node", "_spriteFrame", "targetInfo", "_font", "_textureSource", "root", "target", "source", "_target", "data", "asset", "price", "icon", "editBox", "btnMin", "btnMax", "btnReduce", "btnAdd", "_backgroundImage"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_prefab", "_components", "_parent", "_lpos", "_children"], -1, 4, 12, 1, 5, 2], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_enableOutline", "_overflow", "_horizontalAlign", "node", "__prefab", "_font", "_color"], -6, 1, 4, 6, 5], ["cc.Node", ["_name", "_components", "_prefab", "_parent", "_children", "_lpos"], 2, 9, 4, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo"], 2, 1, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.Layout", ["_resizeMode", "_layoutType", "_affectedByScale", "_isAlign", "node", "__prefab"], -1, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["96e1ftejFNLpLRv7xkHWTM+", ["node", "__prefab"], 3, 1, 4], ["06927HtDK9NpZs1itvSz4RY", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["f88f4TK4AFCyK6Y1RiKlBBd", ["node", "__prefab", "btnBuy"], 3, 1, 4, 1], ["aca95a1mutKK7YfLD7G/yhk", ["node", "__prefab", "btnAdd", "btnReduce", "btnMax", "btnMin", "editBox"], 3, 1, 4, 1, 1, 1, 1, 1], ["cc.EditBox", ["_string", "_inputMode", "_maxLength", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>"], 0, 1, 4, 1, 1]], [[8, 0, 2], [11, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 1], [4, 0, 2, 3, 4, 2], [14, 0, 2], [21, 0, 1, 2, 2], [1, 0, 6, 5, 4, 7, 2], [6, 0, 1, 2, 3, 4, 2], [20, 0, 1, 2, 3], [3, 0, 3, 1, 2, 5, 2], [3, 0, 3, 1, 2, 2], [3, 0, 3, 4, 1, 2, 5, 2], [5, 0, 1, 2, 3, 1], [17, 0, 1, 1], [7, 0, 2], [1, 1, 2, 6, 4, 3], [1, 0, 3, 6, 5, 4, 7, 3], [9, 0, 1, 2, 3, 4, 4], [12, 0, 1, 2, 3, 4, 5, 4], [13, 0, 1, 2, 3, 2], [4, 1, 0, 2, 3, 4, 3], [2, 0, 1, 2, 3, 4, 5, 6, 9, 10, 11, 8], [19, 0, 1, 2, 2], [22, 0, 1, 2, 3], [1, 0, 8, 5, 4, 2], [1, 0, 6, 8, 5, 4, 7, 2], [3, 0, 4, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5, 3], [4, 0, 2, 3, 5, 4, 2], [4, 0, 2, 3, 2], [6, 1, 2, 3, 4, 1], [15, 0, 1, 2, 3, 4, 5, 5], [16, 0, 1, 1], [2, 0, 1, 2, 3, 4, 5, 9, 10, 12, 11, 7], [2, 0, 1, 2, 3, 4, 5, 6, 9, 10, 8], [2, 0, 1, 2, 3, 7, 4, 5, 6, 9, 10, 9], [2, 0, 8, 1, 2, 3, 7, 4, 9, 10, 12, 8], [18, 0, 1, 1], [23, 0, 1, 2, 1], [24, 0, 1, 2, 3, 4, 5, 6, 1], [25, 0, 1, 2, 3, 4, 5, 6, 4]], [[[[14, "SpringBuyCountDlg"], [24, "SpringBuyCountDlg", [-11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21], [[[2, -8, [0, "68dlPzUqpM4bwcv1lbeSva"], [5, 750, 1334]], [17, 45, 750, 1334, -9, [0, "22mQPOs79LALBADNwlmM5E"]], -10], 4, 4, 1], [27, "c46/YsCPVOJYA4mWEpNYRx", null, -7, 0, [[19, ["step"], -4, -3, [4, ["a2VQdXye9E+LYprmfhlwqn"]]], [19, ["cell"], -6, -5, [4, ["1f5RzKwa5EaIr2SaOeKvoI"]]]], [-1, -2]]], [11, "btnBuy", 1, [-26], [[2, -22, [0, "9bc2XvHMBMQ4BSe8a3Ilth"], [5, 131, 51]], [3, 2, -23, [0, "32EUQWjh1GU5NDhnbnd6Ka"], 3], [7, 3, -25, [0, "9515MnW2VEGJKqgbAGyjTe"], [4, 4292269782], -24]], [1, "be2vLVj71I3b4QbNX12BBA", null, null, null, 1, 0], [1, -1.558, -193.016, 0]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [11, "box", 1, [-29, -30], [[2, -27, [0, "8eoMYUMNRARZXuXHBV8NGd"], [5, 77.38397216796875, 50]], [31, 1, 1, true, true, -28, [0, "66oJyiT2dC4Y2UN1qK5zSE"]]], [1, "3cEdtS+11L5ZXecKHEOSRz", null, null, null, 1, 0], [1, 0, -133.78499999999997, 0]], [9, "btnClose", 1, [[2, -31, [0, "8fqLLCuldAZIUqBaWSmNcw"], [5, 63, 98]], [3, 0, -32, [0, "0c2sYCxqVCJoAls1Rrh5Um"], 11], [30, -34, [0, "47cGcNYk9K67ofQJlnN7M3"], [4, 4292269782], -33]], [1, "24tDZZCYRC/ICxy8Iew5P5", null, null, null, 1, 0], [1, 255.61599999999999, 256.977, 0]], [10, "bgClose", 1, [[2, -35, [0, "f9iP0d6+ZJrKC4Fzl7ek5u"], [5, 750, 1334]], [28, 0, -36, [0, "78MWgtf1FI4ZhuwmOI1e0N"], [4, 3019898880], 0], [17, 45, 39, 39, -37, [0, "2d4u2E+/tHxZTpbUlTuCFU"]]], [1, "83Pey7NP1BnLpPnRhDvLVR", null, null, null, 1, 0]], [10, "bg", 1, [[2, -38, [0, "48sxSG4GFBTqgDntSqyv58"], [5, 530, 604]], [3, 0, -39, [0, "aa5AHc3iNP+pSaPotrtTCX"], 1], [32, -40, [0, "b2eWnu7u1Nzr/pnINqqzjH"]]], [1, "1eVNiDPcdOpIeJzV2YdChI", null, null, null, 1, 0]], [10, "Label", 2, [[2, -41, [0, "17hhg8LJpNdb9fsJSZMQUS"], [5, 48.97999572753906, 29.2]], [21, "购 买", 20, 20, 20, false, false, true, -42, [0, "deGR/Zkn9MFqbEjB5bK48V"], 2], [13, -43, [0, "3fXyVAiU5LxqIhBH0e5tcd"]]], [1, "33c8KJAtxJrp3fkBKJ9Y63", null, null, null, 1, 0]], [6, "icon", 4, [[[2, -44, [0, "1d76+UYYVC7L3wu7rnigx4"], [5, 31, 32]], -45, [37, -46, [0, "91kRISFYJMFoerOy4iQQyT"]]], 4, 1, 4], [1, "451Hm0+yFGboNlvamMVNeG", null, null, null, 1, 0], [1, -23.191986083984375, 0, 0]], [6, "price", 4, [[[12, -47, [0, "ea+7HPWN9CaqKRXR0dToyg"], [5, 46.38397216796875, 35.5], [0, 0, 0.5]], -48, [13, -49, [0, "16A1v3u/pIhZED5znfcar8"]]], 4, 1, 4], [1, "42iVbfBKhLfIbhl6ODtFAG", null, null, null, 1, 0], [1, -7.691986083984375, 0, 0]], [15, 0, {}, 1, [18, "c46/YsCPVOJYA4mWEpNYRx", null, null, -56, [22, "f8kCCDG3NFwJKtxE9vVJ5V", 1, [[8, "NumberStepper", ["_name"], -50], [5, ["_lpos"], -51, [1, 27, -61.201, 0]], [5, ["_lrot"], -52, [3, 0, 0, 0, 1]], [5, ["_euler"], -53, [1, 0, 0, 0]], [23, 1, ["showMax"], [4, ["a2VQdXye9E+LYprmfhlwqn"]]], [8, 3, ["_transition"], -54], [8, null, ["_normalSprite"], -55], [23, 3, ["_transition"], [4, ["416R/m4XhCyKRjgF1a1Yhq"]]]]], 4]], [4, ["c46/YsCPVOJYA4mWEpNYRx"]], [9, "OutlineLabel-001", 1, [[2, -57, [0, "80Kc9JUjtK1Z8QGc2fCYNW"], [5, 60, 31.5]], [33, "数量：", 24, 24, 25, false, false, -58, [0, "55kM0gJ8dB+JvOM00UcefK"], [4, 4280495954], 5], [13, -59, [0, "faD6MoEqZIqJeOZA9nBTpf"]]], [1, "d3VQYO/u1PJpV12vSBia9Z", null, null, null, 1, 0], [1, -132.422, -62.092, 0]], [15, 0, {}, 1, [18, "c46/YsCPVOJYA4mWEpNYRx", null, null, -60, [22, "ediLhCj7JADZxdg+z446lm", 1, [[8, "ItemCell", ["_name"], 3], [5, ["_lpos"], 3, [1, -1.069, 108.7, 0]], [5, ["_lrot"], 3, [3, 0, 0, 0, 1]], [5, ["_euler"], 3, [1, 0, 0, 0]], [5, ["_lscale"], 3, [1, 1.3, 1.3, 1]], [8, true, ["_active"], 3]]], 7]], [11, "pub_frame_bottom_9", 1, [-63], [[2, -61, [0, "cbCydhoWVAobOwyzzWZ/lr"], [5, 500, 46]], [20, 1, 0, -62, [0, "b3SLb+7wdCJqfSZ5IIHEOy"], 9]], [1, "cfbUAZPdZLoIzhjb0JmMrL", null, null, null, 1, 0], [1, 0, 258.474, 0]], [9, "baokubg3", 1, [[2, -64, [0, "81pQup3NdPwb3iKdbkG2wq"], [5, 494, 235]], [3, 0, -65, [0, "02UvwxOVlNJoTjy05KDW40"], 6]], [1, "29WcA0WJVC/Y7bt/XxnrO4", null, null, null, 1, 0], [1, -3, 109.551, 0]], [10, "OutlineLabel", 15, [[2, -66, [0, "14CH8yUcRKeqsG1rRivq17"], [5, 91.97799682617188, 35.5]], [21, "购买物品", 22, 22, 25, false, false, true, -67, [0, "1bpdKrgSROx7fFSeh2OMiC"], 8]], [1, "a0r0EXdXVJnZ+aTwQ9bU3L", null, null, null, 1, 0]], [9, "spBg", 1, [[2, -68, [0, "5fn7ZI9nVPzL19nxrzALJR"], [5, 582, 630]], [3, 0, -69, [0, "4fNFn2mf1KnL0TKdNlYxSB"], 10]], [1, "4edtlalkNDF5hSiXIxyaW3", null, null, null, 1, 0], [1, -4.7, 7.4, 0]], [38, 1, [0, "5bHQaFIqVHOZwpTUm4F7Vo"], 2], [29, 2, 9, [0, "5d6Kc20EZLdbgGpQ9HbBQI"]], [34, "100", 24, 24, 25, false, false, true, 10, [0, "9686yUVaFJRoA9QpiIKkUL"]], [4, ["faT5g4UP5BH6Gzuxj26szK"]]], 0, [0, -1, 14, 0, -2, 11, 0, 6, 11, 0, 7, 19, 0, 6, 14, 0, 7, 19, 0, 5, 1, 0, 0, 1, 0, 0, 1, 0, -3, 19, 0, -1, 6, 0, -2, 7, 0, -3, 2, 0, -4, 4, 0, -5, 11, 0, -6, 13, 0, -7, 16, 0, -8, 14, 0, -9, 15, 0, -10, 18, 0, -11, 5, 0, 0, 2, 0, 0, 2, 0, 8, 2, 0, 0, 2, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, -1, 9, 0, -2, 10, 0, 0, 5, 0, 0, 5, 0, 8, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -2, 20, 0, 0, 9, 0, 0, 10, 0, -2, 21, 0, 0, 10, 0, 2, 12, 0, 2, 12, 0, 2, 12, 0, 2, 12, 0, 2, 22, 0, 2, 22, 0, 5, 11, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 5, 14, 0, 0, 15, 0, 0, 15, 0, -1, 17, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 9, 1, 19, 11, 21, 19, 12, 20, 69], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 21], [1, 1, 3, 1, 10, 3, 1, 10, 3, 1, 1, 1, 1, 3], [3, 4, 0, 5, 6, 0, 7, 8, 0, 9, 10, 11, 12, 0]], [[{"name": "baokubg3", "rect": {"x": 0, "y": 0, "width": 494, "height": 319}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 494, "height": 319}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-247, -159.5, 0, 247, -159.5, 0, -247, 159.5, 0, 247, 159.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 319, 494, 319, 0, 0, 494, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -247, "y": -159.5, "z": 0}, "maxPos": {"x": 247, "y": 159.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [13]], [[[14, "NumberStepper"], [26, "NumberStepper", [-9, -10, -11, -12, -13], [[2, -2, [0, "8cowohbWVKBqCuMF6BRi4j"], [5, 150, 40]], [39, -8, [0, "a2VQdXye9E+LYprmfhlwqn"], -7, -6, -5, -4, -3]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [25, "EditBox", 1, [-17, -18], [[[2, -14, [0, "71A8OicrZA1bEcuVlUeKOI"], [5, 82, 29]], [20, 1, 0, -15, [0, "5dZr7zpoVKbae46gbueioO"], 0], -16], 4, 4, 1], [1, "03Or/UD8NNnoUL2vrCBLus", null, null, null, 1, 0], [1, 0.453, 0, 0]], [6, "btnAdd", 1, [[[2, -19, [0, "afw6Ug8NRAUrCmwARHqFif"], [5, 42, 40]], [3, 2, -20, [0, "e3KLRMu4pNTqeSJGp8lma+"], 1], -21], 4, 4, 1], [1, "55QvrDF9pHVLgy7vjZX3OY", null, null, null, 1, 0], [1, 58.261, 0, 0]], [16, "btnMax", false, 1, [[[2, -22, [0, "e7dkOeN/lPIKNeH1B16o9o"], [5, 42, 40]], [3, 2, -23, [0, "16F6gvWl5IM4+nPf05z4xF"], 2], -24], 4, 4, 1], [1, "fbgCm8JPhBFaKrRmbhkbb6", null, null, null, 1, 0], [1, 102.674, 0, 0]], [6, "btnReduce", 1, [[[2, -25, [0, "2eKAaGBcpPBJNvlV0kVWeP"], [5, 42, 40]], [3, 2, -26, [0, "edG8wSww9EmoVedNYpWruz"], 3], -27], 4, 4, 1], [1, "8aVE3nh+xDurYrYiEBZPAR", null, null, null, 1, 0], [1, -55.152, 0, 0]], [6, "btnMin", 1, [[[2, -28, [0, "705H7dA2VF85fdZppJWtaj"], [5, 42, 40]], [3, 2, -29, [0, "8euVkoOzFGxI163QbLpFiA"], 4], -30], 4, 4, 1], [1, "afSi9s9bxIgZG7qPeqfCzo", null, null, null, 1, 0], [1, -100.545, 0, 0]], [6, "TEXT_LABEL", 2, [[[12, -31, [0, "a9M6wL/xJFS43K83duLFfN"], [5, 80, 29], [0, 0, 1]], -32], 4, 1], [1, "e2BJHzecRIb70ofcj+cSPB", null, null, null, 1, 0], [1, -39, 14.5, 0]], [16, "PLACEHOLDER_LABEL", false, 2, [[[12, -33, [0, "a6fA9/vu1Ncp2Q9lyy89qF"], [5, 80, 29], [0, 0, 1]], -34], 4, 1], [1, "ecKC1yyCtMjqH1VvRHMq3I", null, null, null, 1, 0], [1, -39, 14.5, 0]], [35, "1", 18, 18, 20, 1, false, false, true, 7, [0, "a0U2KKTqRKlY7eR9i7OEN8"]], [36, "", 0, 20, 20, 29, 1, false, 8, [0, "b3qtCW1ZJL+pxY585JRH4R"], [4, 4290493371]], [40, "1", 2, 8, 2, [0, "f0EofCfZ1IzISFQi0DuX3+"], 9, 10], [7, 3, 3, [0, "416R/m4XhCyKRjgF1a1Yhq"], [4, 4292269782], 3], [7, 3, 4, [0, "faT5g4UP5BH6Gzuxj26szK"], [4, 4292269782], 4], [7, 3, 5, [0, "47yvvSW3RDT5zapaRYhk3g"], [4, 4292269782], 5], [7, 3, 6, [0, "cb73HMINFNkb2sm3aayyYi"], [4, 4292269782], 6]], 0, [0, 5, 1, 0, 0, 1, 0, 13, 11, 0, 14, 15, 0, 15, 13, 0, 16, 14, 0, 17, 12, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 6, 0, 0, 2, 0, 0, 2, 0, -3, 11, 0, -1, 7, 0, -2, 8, 0, 0, 3, 0, 0, 3, 0, -3, 12, 0, 0, 4, 0, 0, 4, 0, -3, 13, 0, 0, 5, 0, 0, 5, 0, -3, 14, 0, 0, 6, 0, 0, 6, 0, -3, 15, 0, 0, 7, 0, -2, 9, 0, 0, 8, 0, -2, 10, 0, 9, 1, 34], [0, 0, 0, 0, 0, 9, 11], [1, 1, 1, 1, 1, 3, 18], [1, 14, 15, 16, 17, 0, 1]], [[{"name": "pub_popBg1", "rect": {"x": 0, "y": 0, "width": 447, "height": 604}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 447, "height": 604}, "rotated": false, "capInsets": [100, 186, 132, 318], "vertices": {"rawPosition": [-223.5, -302, 0, 223.5, -302, 0, -223.5, 302, 0, 223.5, 302, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 604, 447, 604, 0, 0, 447, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -223.5, "y": -302, "z": 0}, "maxPos": {"x": 223.5, "y": 302, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [18]], [[{"name": "xinchun_icon_010", "rect": {"x": 1035, "y": 493, "width": 31, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 31, "height": 32}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [2]], [[{"name": "xinchun_btn_03", "rect": {"x": 530, "y": 954, "width": 63, "height": 98}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 63, "height": 98}, "rotated": true, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [], "indexes": [], "uv": [], "nuv": [], "minPos": {"x": 0, "y": 0, "z": 0}, "maxPos": {"x": 0, "y": 0, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [2]], [[{"name": "xinchun_frame_09", "rect": {"x": 0, "y": 0, "width": 679, "height": 893}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 679, "height": 893}, "rotated": false, "capInsets": [100, 200, 100, 100], "vertices": {"rawPosition": [-339.5, -446.5, 0, 339.5, -446.5, 0, -339.5, 446.5, 0, 339.5, 446.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 893, 679, 893, 0, 0, 679, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -339.5, "y": -446.5, "z": 0}, "maxPos": {"x": 339.5, "y": 446.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [4], [19]]]]