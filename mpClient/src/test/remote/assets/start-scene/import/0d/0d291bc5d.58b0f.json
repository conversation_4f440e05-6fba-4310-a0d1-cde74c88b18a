[1, ["50l5UEQFFHCYtSvnEVn/mW", "13nT+PXfpHNKOYZAGVBpZk@6c48a", "2fg0AtTZlIuLiGhzrTzhXr@6c48a", "30A25BxZNL4IXP7nGnXiyp@6c48a", "574Xb48JpFLIPal5VBhB+Q@6c48a", "99zsborI9EQpKxhoIDEO4Q@6c48a", "30A25BxZNL4IXP7nGnXiyp@f9941", "2fg0AtTZlIuLiGhzrTzhXr@f9941", "c8Dar5IPVAB43oZ61yPZ4b@f9941", "13nT+PXfpHNKOYZAGVBpZk@f9941", "574Xb48JpFLIPal5VBhB+Q@f9941", "99zsborI9EQpKxhoIDEO4Q@f9941", "c8Dar5IPVAB43oZ61yPZ4b@6c48a"], ["node", "_textureSource", "_spriteFrame", "_cameraComponent", "modalRich", "modalNode", "loadingNode", "labBanhao", "btnLoginNode", "audioSource", "_target", "scene", "_parent", "_defaultClip"], ["cc.ImageAsset", "cc.Texture2D", "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_parent", "_components", "_children", "_lpos"], 1, 1, 9, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_spriteFrame"], 0, 1, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_fontFamily", "_enableOutline", "_lineHeight", "_enableWrapText", "_cacheMode", "_isBold", "_outlineWidth", "node", "_color"], -7, 1, 5], ["cc.Node", ["_name", "_id", "_active", "_components", "_lpos", "_parent", "_children"], 0, 12, 5, 1, 2], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "_top", "node"], -2, 1], ["cc.UITransform", ["node", "_contentSize", "_anchorPoint"], 3, 1, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "clickEvents", "_normalColor", "_target"], 2, 1, 9, 5, 1], ["RenderQueueDesc", ["stages", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sortMode"], 0], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_lpos"], 2, 1, 2, 5], ["682c6os+ZRPUIK9PZbIsUT6", ["node"], 3, 1], ["cc.<PERSON>", ["node", "_cameraComponent"], 3, 1, 1], ["1a49e47nSJPJYJmtYGnsLRx", ["node", "audioSource", "btnLoginNode", "labBanhao", "loadingNode", "modalNode", "modalRich"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "node"], 0, 1], ["cc.RichText", ["_lineHeight", "_string", "_horizontalAlign", "_verticalAlign", "_fontSize", "_maxWidth", "_fontFamily", "node"], -4, 1], ["96e1ftejFNLpLRv7xkHWTM+", ["node"], 3, 1], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Scene", ["_name", "autoReleaseAssets", "_children", "_prefab", "_globals"], 1, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyColorHDR", "_groundAlbedoHDR"], 3, 5, 5], ["cc.ShadowsInfo", ["_shadowColor", "_size"], 3, 5, 5], ["cc.SkyboxInfo", [], 3], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", ["_depth"], 2], ["cc.SkinInfo", [], 3], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.Camera", ["_projection", "_priority", "_fov", "_fovAxis", "_orthoHeight", "_near", "_far", "_visibility", "node", "_color"], -5, 1, 5], ["ef5ecZ//yBDdZrR2hWnJlKA", ["node"], 3, 1], ["cc.AudioSource", ["_loop", "_playOnAwake", "node"], 1, 1], ["cc.PhysicsMaterial", ["_name", "_friction", "_rollingFriction", "_spinningFriction", "_restitution"], -2], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", ["_flows"], 3, 9], ["ShadowFlow", ["_name", "_stages"], 2, 9], ["ShadowStage", ["_name"], 2], ["ForwardFlow", ["_name", "_priority", "_stages"], 1, 9], ["ForwardStage", ["_name", "renderQueues"], 2, 9]], [[8, 0, 1, 1], [3, 0, 2, 3, 2], [3, 0, 2, 4, 3, 5, 2], [7, 0, 2, 3, 5, 4], [4, 1, 3, 4, 2], [20, 0, 1], [3, 0, 1, 2, 4, 3, 3], [16, 0, 1], [9, 0, 1, 2], [5, 0, 1, 2, 3, 5, 6, 7, 4, 9, 10, 10], [11, 0, 2], [6, 0, 1, 6, 3, 4, 3], [6, 0, 5, 3, 4, 2], [6, 0, 2, 5, 3, 4, 3], [3, 0, 1, 2, 4, 3, 5, 3], [3, 0, 2, 3, 5, 2], [12, 0, 1, 2, 3, 2], [8, 0, 1, 2, 1], [13, 0, 1], [14, 0, 1, 1], [7, 0, 4, 1, 2, 3, 5, 6], [7, 0, 1, 5, 3], [15, 0, 1, 2, 3, 4, 5, 6, 1], [4, 2, 0, 3, 4, 3], [4, 0, 1, 3, 4, 3], [4, 2, 0, 1, 3, 4, 4], [9, 0, 1, 2, 3, 4, 2], [17, 0, 1, 2, 3], [18, 0, 1, 2, 3, 4], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8], [5, 0, 1, 2, 3, 5, 6, 8, 4, 10, 11, 9], [5, 0, 1, 2, 3, 4, 10, 6], [5, 0, 1, 2, 3, 5, 8, 7, 4, 10, 9], [21, 0, 1, 2, 3, 2], [22, 0, 1, 2, 3, 4, 3], [23, 0, 1, 2, 3, 4, 5, 7], [24, 0, 1, 2, 3, 4, 5, 6, 7, 1], [25, 0, 1, 1], [26, 0, 1, 1], [27, 1], [28, 1], [29, 0, 2], [30, 1], [31, 1], [32, 1], [33, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [34, 0, 1], [35, 0, 1, 2, 3], [36, 0, 1, 2, 3, 4, 6], [37, 0, 1], [38, 0, 1, 2], [39, 0, 2], [40, 0, 1, 2, 3], [41, 0, 1, 2], [10, 0, 2], [10, 1, 2, 0, 4]], [[[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["13nT+PXfpHNKOYZAGVBpZk"]}], [1], 0, [], [], []], [[{"name": "btnIcon3", "rect": {"x": 0, "y": 0, "width": 131, "height": 59}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 131, "height": 59}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-65.5, -29.5, 0, 65.5, -29.5, 0, -65.5, 29.5, 0, 65.5, 29.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 59, 131, 59, 0, 0, 131, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -65.5, "y": -29.5, "z": 0}, "maxPos": {"x": 65.5, "y": 29.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [1], [1]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["2fg0AtTZlIuLiGhzrTzhXr"]}], [1], 0, [], [], []], [[{"name": "btnIcon1", "rect": {"x": 0, "y": 0, "width": 157, "height": 60}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 157, "height": 60}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-78.5, -30, 0, 78.5, -30, 0, -78.5, 30, 0, 78.5, 30, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 60, 157, 60, 0, 0, 157, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -78.5, "y": -30, "z": 0}, "maxPos": {"x": 78.5, "y": 30, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [1], [2]], [[{"fmt": "1", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["30A25BxZNL4IXP7nGnXiyp"]}], [1], 0, [], [], []], [[{"name": "loginBg_default2", "rect": {"x": 0, "y": 0, "width": 750, "height": 1334}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 750, "height": 1334}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-375, -667, 0, 375, -667, 0, -375, 667, 0, 375, 667, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 1334, 750, 1334, 0, 0, 750, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -375, "y": -667, "z": 0}, "maxPos": {"x": 375, "y": 667, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [1], [3]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["574Xb48JpFLIPal5VBhB+Q"]}], [1], 0, [], [], []], [[{"name": "btnIcon2", "rect": {"x": 0, "y": 0, "width": 131, "height": 59}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 131, "height": 59}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-65.5, -29.5, 0, 65.5, -29.5, 0, -65.5, 29.5, 0, 65.5, 29.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 59, 131, 59, 0, 0, 131, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -65.5, "y": -29.5, "z": 0}, "maxPos": {"x": 65.5, "y": 29.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [1], [4]], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["99zsborI9EQpKxhoIDEO4Q"]}], [1], 0, [], [], []], [[{"name": "loading_icon", "rect": {"x": 0, "y": 0, "width": 82, "height": 82}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 82, "height": 82}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-41, -41, 0, 41, -41, 0, -41, 41, 0, 41, 41, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 82, 82, 82, 0, 0, 82, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -41, "y": -41, "z": 0}, "maxPos": {"x": 41, "y": 41, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [1], [5]], [[[10, "preloading"], [11, "<PERSON><PERSON>", "beI88Z2HpFELqR4T5EMHpg", [-14, -15, -16, -17, -18, -19], [[[0, -1, [5, 750, 1334]], [18, -2], [19, -4, -3], [20, 45, 5.684341886080802e-14, 5.684341886080802e-14, 1334, 750, -5], -6, [22, -13, -12, -11, -10, -9, -8, -7]], 4, 4, 4, 4, 1, 4], [1, 375, 667, 0]], [6, "modal", false, 1, [-23, -24, -25, -26], [[0, -20, [5, 750, 1334]], [3, 45, 100, 100, -21], [7, -22]]], [14, "btnLogin", false, 1, [-31], [[0, -27, [5, 157, 60]], [23, 1, 2, -28, 1], [26, 3, -30, [[27, "1a49e47nSJPJYJmtYGnsLRx", "loginHWQuickGame", 1]], [4, 4292269782], -29]], [1, 0, -237, 0]], [6, "loadingView", false, 1, [-35], [[0, -32, [5, 750, 1334]], [3, 45, 100, 100, -33], [7, -34]]], [2, "Layout", 2, [-38, -39], [[0, -36, [5, 342, 100]], [28, 1, 1, 80, -37]], [1, 0, -107.61199999999997, 0]], [2, "btn1", 5, [-43], [[0, -40, [5, 131, 59]], [4, false, -41, 3], [8, 3, -42]], [1, -105.5, 0, 0]], [2, "btn0", 5, [-47], [[0, -44, [5, 131, 59]], [4, false, -45, 4], [8, 3, -46]], [1, 105.5, 0, 0]], [12, "rich", 2, [[[0, -48, [5, 380, 35.28]], [29, 28, "", 1, 1, 18, 380, "黑体", -49], -50, [5, -51]], 4, 4, 1, 4], [1, 0, 20.307000000000016, 0]], [1, "bg", 1, [[0, -52, [5, 750, 1334]], [24, 0, false, -53, 0], [3, 45, 750, 1334, -54]]], [13, "txtBanHao", false, 1, [[[17, -55, [5, 4, 32.980000000000004], [0, 0.5, 0]], -56, [21, 4, 60, -57]], 4, 1, 4], [1, 0, -607, 0]], [1, "Label", 6, [[0, -58, [5, 78, 36.24]], [5, -59], [9, "不同意", 24, 24, "黑体", 24, false, 1, true, 3, -60]]], [1, "Label", 7, [[0, -61, [5, 54, 36.24]], [5, -62], [9, "同意", 24, 24, "黑体", 24, false, 1, true, 3, -63]]], [1, "Sprite", 4, [[0, -64, [5, 82, 82]], [4, false, -65, 5], [33, true, -66, [6], 7]]], [1, "txt", 3, [[0, -67, [5, 101.875, 41.8]], [30, "开始游戏", 24, 24, "黑体", 30, false, true, true, -68, [4, 4288413694]]]], [1, "bg", 2, [[0, -69, [5, 447, 317.67299999999994]], [25, 1, 0, false, -70, 2]]], [15, "title", 2, [[0, -71, [5, 64, 54.4]], [31, "提 示", 24, 24, "黑体", true, -72]], [1, 0, 129.32000000000005, 0]], [34, "preloading", true, [1], [35, null, null, "9e28af1d-9c32-4161-b33b-0334257263be", null, null, null], [36, [37, [2, 0, 0, 0, 0.520833125], [2, 0, 0, 0, 0]], [38, [4, 4283190348], [0, 512, 512]], [39], [40], [41, 4], [42], [43], [44]]], [16, "Camera", 1, [-73], [1, 0, 0, 1000]], [45, 0, 1073741824, 15.913155631986243, 1, 667, 0, 2000, 1073741824, 18, [4, 4278190080]], [32, "", 18, 18, "黑体", 23, true, 1, true, 10], [46, 8], [47, true, false, 1]], 0, [0, 0, 1, 0, 0, 1, 0, 3, 19, 0, 0, 1, 0, 0, 1, 0, -5, 22, 0, 4, 21, 0, 5, 2, 0, 6, 4, 0, 7, 20, 0, 8, 3, 0, 9, 22, 0, 0, 1, 0, -1, 18, 0, -2, 9, 0, -3, 3, 0, -4, 10, 0, -5, 2, 0, -6, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 15, 0, -2, 5, 0, -3, 16, 0, -4, 8, 0, 0, 3, 0, 0, 3, 0, 10, 3, 0, 0, 3, 0, -1, 14, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, -2, 7, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 11, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 12, 0, 0, 8, 0, 0, 8, 0, -3, 21, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 20, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, -1, 19, 0, 11, 17, 1, 12, 17, 73], [0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 2, 2, 2, 2, -1, 13], [6, 7, 8, 9, 10, 11, 0, 0]], [[[48, "default-physics-material", 0.8, 0.1, 0.1, 0.1]], 0, 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"base": "2,2,2,2,0,0", "mipmaps": ["c8Dar5IPVAB43oZ61yPZ4b"]}], [1], 0, [], [], []], [[{"name": "bg1", "rect": {"x": 0, "y": 0, "width": 447, "height": 604}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 447, "height": 604}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-223.5, -302, 0, 223.5, -302, 0, -223.5, 302, 0, 223.5, 302, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 604, 447, 604, 0, 0, 447, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -223.5, "y": -302, "z": 0}, "maxPos": {"x": 223.5, "y": 302, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [2], 0, [0], [1], [12]], [[[49, [[50, "ShadowFlow", [[51, "ShadowStage"]]], [52, "ForwardFlow", 1, [[53, "ForwardStage", [[54, ["default"]], [55, true, 1, ["default"]]]]]]]]], 0, 0, [], [], []]]]