package net;

import com.teamdev.jxbrowser.net.Network;
import com.teamdev.jxbrowser.net.callback.BeforeUrlRequestCallback;
import env.Global;

import java.util.ArrayList;
import java.util.List;

public class MyNetworkDelegate {
    static String myHost = "https://husong.vip/yxmxt/";
    Network network;

    public String append(String fileName) {
        return MyNetworkDelegate.myHost + fileName;
    }

    public MyNetworkDelegate(Network network) {
        this.network = network;
    }

    public void doDelegate() {
        network.set(BeforeUrlRequestCallback.class, params -> {
            String url = params.urlRequest().url();
            if (!url.startsWith(myHost)) {
//            System.out.println("MyNetworkDelegate url :" + url);

//                if (url.contains("/resources/")) {
//                    int start = url.indexOf("/resources/");
//                    String target = myHost + url.substring(start + 11);
//                    return BeforeUrlRequestCallback.Response.redirect(target);
//                }

                if (url.contains("/native/")) {
                    int protocolEnd = url.indexOf("://") + 3;
                    String withoutProtocol = url.substring(protocolEnd);
                    int start = withoutProtocol.indexOf("/");
                    String target = myHost + "1/" + withoutProtocol.substring(start + 1);
                    return BeforeUrlRequestCallback.Response.redirect(target);
                }
            }
            //return BeforeUrlRequestCallback.Response.redirect(target);
            return BeforeUrlRequestCallback.Response.proceed();
        });

    }


}
