<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小游戏 - wx0c747e2a0ec57737</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #gameCanvas {
            border: 1px solid #333;
            background: #000;
        }

        #loading {
            color: white;
            font-family: Arial, sans-serif;
            text-align: center;
        }
    </style>
    <script type="module" src="/src/main.ts"></script>
</head>

<body>
    <canvas id="gameCanvas" style="display: none;"></canvas>

    <script>
        // 模拟GameGlobal对象
        window.GameGlobal = {
            manager: null,
            scene: null,
            requestAnimationFrame: function (callback) {
                requestAnimationFrame(callback);
            },
            __isAdapterInjected: true
        };

        // 定义 __devtoolssubcontext 来跳过适配器注入
        window.__devtoolssubcontext = true;
        // 模拟canvas相关API
        const canvas = document.getElementById('gameCanvas');
        window.canvas = canvas;

        // 创建Touch对象 - 按照微信小游戏格式
        function createTouch(identifier, pageX, pageY, clientX, clientY) {
            return {
                identifier: identifier,
                clientX: clientX,
                clientY: clientY,
                pageX: pageX,
                pageY: pageY
            };
        }

        // 触摸事件转换函数 - 转换为微信小游戏格式
        function convertTouchEvent(e, eventType) {
            const rect = canvas.getBoundingClientRect();
            let touches = [];
            const changedTouches = [];

            // 处理触摸点
            if (e.touches) {
                for (let i = 0; i < e.touches.length; i++) {
                    const touch = e.touches[i];
                    const touchObj = createTouch(
                        touch.identifier || 0,
                        touch.pageX,
                        touch.pageY,
                        touch.clientX,
                        touch.clientY
                    );

                    // 对于touchend和touchcancel，touches数组应该为空
                    if (eventType !== 'touchend' && eventType !== 'touchcancel') {
                        touches.push(touchObj);
                    }
                }
            }

            if (e.changedTouches) {
                for (let i = 0; i < e.changedTouches.length; i++) {
                    const touch = e.changedTouches[i];
                    changedTouches.push(createTouch(
                        touch.identifier || 0,
                        touch.pageX,
                        touch.pageY,
                        touch.clientX,
                        touch.clientY
                    ));
                }
            }

            // 如果没有触摸事件，处理鼠标事件（模拟触摸）
            if (!e.touches && e.type.startsWith('mouse')) {
                const mouseTouch = createTouch(
                    0,
                    e.pageX || e.clientX,
                    e.pageY || e.clientY,
                    e.clientX,
                    e.clientY
                );

                // 对于touchend和touchcancel，touches数组应该为空
                if (eventType !== 'touchend' && eventType !== 'touchcancel') {
                    touches.push(mouseTouch);
                }
                changedTouches.push(mouseTouch);
            }

            // 创建真正的Event对象，而不是普通对象
            const touchEvent = new CustomEvent(eventType, {
                bubbles: true,
                cancelable: true
            });

            // 添加触摸相关属性
            touchEvent.touches = touches;
            touchEvent.changedTouches = changedTouches;
            touchEvent.targetTouches = Array.prototype.slice.call(touches); // 重要：游戏代码需要这个
            touchEvent.timeStamp = e.timeStamp || Date.now();

            return touchEvent;
        }

        // 添加触摸事件监听器到canvas
        let isMouseDown = false;

        // 触摸事件
        canvas.addEventListener('touchstart', function(e) {
            e.preventDefault();
            const wxEvent = convertTouchEvent(e, 'touchstart');
            console.log('[wx] touchstart事件触发', wxEvent);

            // 调用所有注册的回调
            if (window.wx && window.wx._touchStartCallbacks) {
                window.wx._touchStartCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] touchstart回调执行错误:', err);
                    }
                });
            }
        }, { passive: false });

        canvas.addEventListener('touchmove', function(e) {
            e.preventDefault();
            const wxEvent = convertTouchEvent(e, 'touchmove');
            console.log('[wx] touchmove事件触发', wxEvent);

            if (window.wx && window.wx._touchMoveCallbacks) {
                window.wx._touchMoveCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] touchmove回调执行错误:', err);
                    }
                });
            }
        }, { passive: false });

        canvas.addEventListener('touchend', function(e) {
            e.preventDefault();
            const wxEvent = convertTouchEvent(e, 'touchend');
            console.log('[wx] touchend事件触发', wxEvent);

            if (window.wx && window.wx._touchEndCallbacks) {
                window.wx._touchEndCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] touchend回调执行错误:', err);
                    }
                });
            }
        }, { passive: false });

        canvas.addEventListener('touchcancel', function(e) {
            e.preventDefault();
            const wxEvent = convertTouchEvent(e, 'touchcancel');
            console.log('[wx] touchcancel事件触发', wxEvent);

            if (window.wx && window.wx._touchCancelCallbacks) {
                window.wx._touchCancelCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] touchcancel回调执行错误:', err);
                    }
                });
            }
        }, { passive: false });

        // 鼠标事件（模拟触摸）
        canvas.addEventListener('mousedown', function(e) {
            isMouseDown = true;
            const wxEvent = convertTouchEvent(e, 'touchstart');
            console.log('[wx] mousedown转touchstart事件触发', wxEvent);

            if (window.wx && window.wx._touchStartCallbacks) {
                window.wx._touchStartCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] mousedown转touchstart回调执行错误:', err);
                    }
                });
            }
        });

        canvas.addEventListener('mousemove', function(e) {
            if (!isMouseDown) return;
            const wxEvent = convertTouchEvent(e, 'touchmove');
            console.log('[wx] mousemove转touchmove事件触发', wxEvent);

            if (window.wx && window.wx._touchMoveCallbacks) {
                window.wx._touchMoveCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] mousemove转touchmove回调执行错误:', err);
                    }
                });
            }
        });

        canvas.addEventListener('mouseup', function(e) {
            if (!isMouseDown) return;
            isMouseDown = false;
            const wxEvent = convertTouchEvent(e, 'touchend');
            console.log('[wx] mouseup转touchend事件触发', wxEvent);

            if (window.wx && window.wx._touchEndCallbacks) {
                window.wx._touchEndCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] mouseup转touchend回调执行错误:', err);
                    }
                });
            }
        });

        canvas.addEventListener('mouseleave', function(e) {
            if (!isMouseDown) return;
            isMouseDown = false;
            const wxEvent = convertTouchEvent(e, 'touchcancel');
            console.log('[wx] mouseleave转touchcancel事件触发', wxEvent);

            if (window.wx && window.wx._touchCancelCallbacks) {
                window.wx._touchCancelCallbacks.forEach(callback => {
                    try {
                        callback(wxEvent);
                    } catch (err) {
                        console.error('[wx] mouseleave转touchcancel回调执行错误:', err);
                    }
                });
            }
        });

        // 虚拟文件系统模块系统
        const modules = new Map();           // 存储模块: moduleId -> { factory, loaded }
        const moduleCache = new Map();       // 模块缓存: absolutePath -> exports
        window.modules = modules;
        window.moduleCache = moduleCache;
        let loadedModules = 0;
        let totalModules = 0;
        let currentModulePath = '/';         // 当前执行模块的路径
        window.moduleExports = {};

        // 解析相对路径为绝对路径
        function resolvePath(currentPath, requestPath) {
            // 如果请求路径不以 ./ 或 ../ 开头，直接返回（当作绝对路径）
            if (!requestPath.startsWith('./') && !requestPath.startsWith('../')) {
                return requestPath;
            }

            // 获取当前模块的目录路径
            const currentDir = currentPath.includes('/')
                ? currentPath.substring(0, currentPath.lastIndexOf('/') + 1)
                : '/';

            // 处理相对路径
            let resolved = currentDir + requestPath;

            // 标准化路径：处理 ../ 和 ./
            const parts = resolved.split('/');
            const stack = [];

            for (const part of parts) {
                if (part === '' || part === '.') {
                    continue;
                } else if (part === '..') {
                    stack.pop();
                } else {
                    stack.push(part);
                }
            }

            return stack.join('/');
        }

        // 全局define函数
        window.define = function (id, factory) {
            // console.log(`[Define] 注册模块: ${id}`);
            const moduleData = { factory, loaded: false };

            // 存储原始ID
            modules.set(id, moduleData);

            // 如果ID以.js结尾，同时存储不带.js的版本
            if (id.endsWith('.js')) {
                const withoutJs = id.slice(0, -3);
                modules.set(withoutJs, moduleData);
                // console.log(`[Define] 同时注册: ${withoutJs}`);
            }
            // 如果ID不以.js结尾，同时存储带.js的版本
            else {
                const withJs = id + '.js';
                modules.set(withJs, moduleData);
                // console.log(`[Define] 同时注册: ${withJs}`);
            }

            totalModules++;
            // updateProgress();
        };

        // 全局require函数
        window.require = function (requestPath) {
            // 解析绝对路径
            let absolutePath = resolvePath(currentModulePath, requestPath);
            if (absolutePath.startsWith("//")) {
                absolutePath = absolutePath.substring(2);
            }
            // 检查缓存
            if (moduleCache.has(absolutePath)) {
                return moduleCache.get(absolutePath);
            }

            // 查找模块定义
            const moduleDef = modules.get(absolutePath);
            if (!moduleDef) {
                console.warn(`模块未找到: ${requestPath}`);
                console.log(`当前模块路径: ${currentModulePath}`);
                console.log(`解析后路径: ${absolutePath}`);
                console.log(`可用模块:`, Array.from(modules.keys()).slice(0, 5));
                return {};
            }

            if (absolutePath !== requestPath) {
                // console.log(`[Require] 路径解析: "${requestPath}" -> "${absolutePath}"`);
            } else {
                // console.log(`[Require] 加载模块: ${absolutePath}`);
            }

            // 保存当前路径，设置新的执行上下文
            const previousPath = currentModulePath;
            currentModulePath = absolutePath;

            const module = { id: absolutePath, exports: {} };
            // 使用Proxy hack exports，自动注册到全局
            const exports = new Proxy(module.exports, {
                set(target, prop, value) {
                    target[prop] = value;
                    // 自动注册到全局 moduleExports
                    window.moduleExports[absolutePath] = target;
                    return true;
                },
                get(target, prop) {
                    return target[prop];
                }
            });

            // 同时更新 module.exports 的引用
            module.exports = exports;

            try {
                moduleDef.factory.call(exports, window.require, module, exports);
                moduleCache.set(absolutePath, module.exports);
                moduleDef.loaded = true;
                loadedModules++;
                // updateProgress();
                return module.exports;
            } catch (error) {
                console.error(`模块执行失败: ${absolutePath}`, error);
                return {};
            } finally {
                // 恢复之前的路径上下文
                currentModulePath = previousPath;
            }
        };

        // 更新加载进度
        // function updateProgress() {
        //     if (totalModules > 0) {
        //         const progress = Math.round((loadedModules / totalModules) * 100);
        //         document.getElementById('progress').textContent = progress + '%';

        //         if (progress >= 100) {
        //             setTimeout(() => {
        //                 document.getElementById('loading').style.display = 'none';
        //                 document.getElementById('gameCanvas').style.display = 'block';
        //                 console.log('🎮 游戏加载完成！');
        //             }, 500);
        //         }
        //     }
        // }

        // 加载游戏脚本
        function loadGameScript() {
            // const script = document.createElement('script');
            // script.src = url;
            // script.onload = function () {
            //     console.log('游戏脚本加载完成');
            // };
            // script.onerror = function () {
            //     console.error('游戏脚本加载失败');
            //     document.getElementById('loading').innerHTML = '<p style="color: red;">游戏加载失败</p>';
            // };
            // document.head.appendChild(script);
            window.loadGameScript = null;
            LOAD_GAME_SCRIPT("game.js", "utf8")
        }

        // 启动游戏
        console.log('🎮 启动微信小游戏环境...');
        window.loadGameScript = loadGameScript
    </script>
</body>

</html>

<script>
    window.wx = {
        env: {
            USER_DATA_PATH: "src/test/remote/1",
        },
        showShareMenu: function () { },
        onShareAppMessage: function () { },
        onAddToFavorites: function () { },
        onShareTimeline: function () { },
        shareAppMessage: function () { },
        createGameClubButton: function () { },
        setKeepScreenOn: function () { },
        setPreferredFramesPerSecond: function () { },
        setStorage: function (options) {
            const { key, data, success, fail } = options
            localStorage.setItem(key, data)
            success && success()
        },
        setStorageSync: function (k, v) {
            localStorage.setItem(k, v)
        },
        getStorage: function (options) {
            const { key, success, fail } = options
            const data = localStorage.getItem(key)
            success && success({ data })
        },
        getStorageSync: function (k) {
            return localStorage.getItem(k)
        },
        getSystemInfo: function (options) {
            const { success } = options
            if (success) {
                success(this.getSystemInfoSync())
            }
        },
        getSystemInfoSync: function () {
            return {
                "batteryLevel": 100,
                "benchmarkLevel": -1,
                "brand": "devtools",
                "memorySize": 2048,
                "model": "iPad Pro 10.5-inch",
                "system": "iOS 10.0.1",
                "platform": "devtools",
                "pixelRatio": 2,
                safeArea: {
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    width: 192,
                    height: 303
                },
                "screenWidth": 192,
                "screenHeight": 303,
                "screenTop": 0,
                "windowWidth": 192,
                "windowHeight": 303,
                "statusBarHeight": 20,
                "version": "8.0.5",
                "language": "zh_CN",
                "SDKVersion": "2.27.3",
                "enableDebug": false,
                "fontSizeScaleFactor": 1,
                "fontSizeSetting": 26,
                "mode": "default",
                "host": { "appId": "wx59d2f86ab224f8d4", "env": "devtools", "version": "1.06.2405010" },
                "bluetoothEnabled": true,
                "locationEnabled": true,
                "wifiEnabled": true,
                "locationReducedAccuracy": true,
                "albumAuthorized": true,
                "bluetoothAuthorized": true,
                "cameraAuthorized": true,
                "locationAuthorized": true,
                "microphoneAuthorized": true,
                "notificationAuthorized": true,
                "notificationAlertAuthorized": true,
                "notificationBadgeAuthorized": true,
                "notificationSoundAuthorized": true,
                "phoneCalendarAuthorized": true,
                "deviceOrientation": "portrait",
                "devicePixelRatio": 2
            };
        },

        onShow: function (callback) {
            // callback({ query: {} })
        },

        onHide: function (callback) {
            console.log('[wx] 注册onHide回调');
        },

        request: function (options) {
            const {
                method = 'GET',
                url,
                data,
                timeout = 10000,
                success,
                fail,
                header = {}
            } = options;
            console.log('[wx] 网络请求:', url, method);
            const xhr = new XMLHttpRequest();
            xhr.timeout = timeout;
            xhr.open(method.toUpperCase(), url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            Object.keys(header).forEach(key => {
                xhr.setRequestHeader(key, header[key]);
            });
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        let responseData;
                        try {
                            responseData = JSON.parse(xhr.responseText);
                        } catch (e) {
                            responseData = xhr.responseText;
                        }

                        success && success({
                            data: responseData,
                            statusCode: xhr.status,
                            header: xhr.getAllResponseHeaders()
                        });
                    } else {
                        fail && fail({
                            errMsg: `request:fail ${xhr.status} ${xhr.statusText}`,
                            statusCode: xhr.status
                        });
                    }
                }
            };
            xhr.ontimeout = function () {
                fail && fail({
                    errMsg: 'request:fail timeout',
                    statusCode: 0
                });
            };
            xhr.onerror = function () {
                fail && fail({
                    errMsg: 'request:fail network error',
                    statusCode: 0
                });
            };
            try {
                if (method.toUpperCase() === 'POST' && data) {
                    xhr.send(JSON.stringify(data));
                } else {
                    xhr.send();
                }
            } catch (error) {
                fail && fail({
                    errMsg: `request:fail ${error.message}`,
                    statusCode: 0
                });
            }
        },

        getStorageSync: function (key) {
            return localStorage.getItem(key);
        },

        setStorageSync: function (key, value) {
            localStorage.setItem(key, value);
        },

        showToast: function (options) {
            console.log('[wx] 显示toast:', options.title);
        },

        getLaunchOptionsSync: function () {
            return { query: {} }
        },

        createImage: function () {
            return new Image()
        },
        createCanvas: function () {
            return window.canvas
        },
        // 触摸事件回调存储
        _touchStartCallbacks: [],
        _touchMoveCallbacks: [],
        _touchEndCallbacks: [],
        _touchCancelCallbacks: [],

        onTouchStart: function (callback) {
            console.log('[wx] 注册onTouchStart回调');
            if (typeof callback === 'function') {
                this._touchStartCallbacks.push(callback);
            }
        },
        onTouchMove: function (callback) {
            console.log('[wx] 注册onTouchMove回调');
            if (typeof callback === 'function') {
                this._touchMoveCallbacks.push(callback);
            }
        },
        onTouchEnd: function (callback) {
            console.log('[wx] 注册onTouchEnd回调');
            if (typeof callback === 'function') {
                this._touchEndCallbacks.push(callback);
            }
        },
        onTouchCancel: function (callback) {
            console.log('[wx] 注册onTouchCancel回调');
            if (typeof callback === 'function') {
                this._touchCancelCallbacks.push(callback);
            }
        },

        onDeviceOrientationChange: function () {
        },

        getFileSystemManager: function () {
            return {
                readFileSync: function (filePath, encoding = 'utf8') {
                    return READ_FILE_SYNC(filePath, encoding)
                },
                mkdirSync: function (filePath, recursive = true) {
                    return MAKE_DIR_SYNC(filePath, recursive)
                },
                writeFileSync: function (filePath, data, encoding = 'utf8') {
                    return WRITE_FILE_SYNC(filePath, data, encoding)
                },
                readFile: function (options) {
                    const { filePath, encoding, success, fail } = options
                    return new Promise((resolve, reject) => {
                        const data = READ_FILE_SYNC(filePath, encoding)
                        success && success({ data })
                    })
                },
                access: function (options) {
                    const { path, success, fail } = options
                    const is = ACCESS_FILE(path)
                    if (is) {
                        success && success()
                    }
                    else {
                        fail && fail()
                    }
                },
                copyFile: function (options) {
                    const { srcPath, destPath, success, fail } = options
                    const is = COPY_FILE(srcPath, destPath)
                    if (is) {
                        success && success()
                    }
                    else {
                        fail && fail("复制失败")
                    }
                }
            }
        },

        loadSubpackage: function (args) {
            console.log("加载分包....")
            const { name, success, fail } = args
            const gameJson = READ_FILE_SYNC("game.json", "utf8")
            const { subPackages } = JSON.parse(gameJson)
            const sub = subPackages.find(item => item.name == name)
            if (!sub) return
            const url = sub.root.substring(1)
            LOAD_GAME_SCRIPT(`${url}game.js`, "utf8")
            success()
            // const script = document.createElement('script');
            // script.src = `subpackages${sub.root}/game.js`;
            // script.onload = function () {
            //     console.log('分包脚本加载完成', name);
            //     success()
            // };
            // script.onerror = function () {
            //     console.error('分包脚本加载失败', name);
            //     fail()
            // };
            // document.head.appendChild(script);
        },

        downloadFile: function (options) {
            const { url, success, fail } = options
            const filePath = DOWNLOAD_FILE(url)
            if (filePath != "") {
                success && success({ tempFilePath: filePath, statusCode: 200 })
            }
            else {
                fail && fail({ errMsg: "下载失败" })
            }
        },


    };
</script>