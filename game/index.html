<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小游戏 - wx0c747e2a0ec57737</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #myCanvas {
            border: 1px solid #333;
            background: #000;
            width: 100%;
            height: 100%;
        }

        #loading {
            color: white;
            font-family: Arial, sans-serif;
            text-align: center;
        }
    </style>
    <script type="module" src="/src/main.ts"></script>
</head>

<body>
    <canvas id="myCanvas"></canvas>
    <!-- <div id="app"> </div> -->
    <script>
        const orgPostMessage = postMessage;
        const orgClearInterval = clearInterval;
        const orgSetInterval = setInterval;
        const orgSetTimeout = setTimeout;
        const orgClearTimeout = clearTimeout;
        window.postMessage = null
        Object.defineProperty(window, 'clearInterval', {
            value: orgClearInterval,
            writable: false,
            configurable: false
        });
        Object.defineProperty(window, 'setInterval', {
            value: orgSetInterval,
            writable: false,
            configurable: false
        });
        Object.defineProperty(window, 'setTimeout', {
            value: orgSetTimeout,
            writable: false,
            configurable: false
        });
        Object.defineProperty(window, 'clearTimeout', {
            value: orgClearTimeout,
            writable: false,
            configurable: false
        });
        const orgrequestAnimationFrame = requestAnimationFrame;
        window.__devtoolssubcontext = undefined
        // 模拟GameGlobal对象
        window.GameGlobal = window;
        var orgWebAssembly = window.WebAssembly;
        const orgInstantiate = orgWebAssembly.instantiate;
        orgWebAssembly.instantiate = function (path, importObject) {
            let buffer = path;
            if (typeof path == 'string') {
                buffer = wx.getFileSystemManager().readFileSync(path, "");
            }
            return orgInstantiate(buffer, importObject);
        }
        window.WXWebAssembly = orgWebAssembly


        const orgDefineProperty = Object.defineProperty;
        Object.defineProperty = function (obj, prop, descriptor) {
            // 检查属性是否已经存在，如果存在且不可配置则直接返回
            if (typeof obj.hasOwnProperty === 'function' && obj.hasOwnProperty(prop)) {
                const existingDescriptor = Object.getOwnPropertyDescriptor(obj, prop);
                if (existingDescriptor && !existingDescriptor.configurable) {
                    return;
                }
            }

            try {
                orgDefineProperty(obj, prop, descriptor);
            } catch (err) {
                console.log(`[wx] defineProperty 失败:`, prop, err.message);
            }
        }

        // 模拟canvas相关API
        const canvas = document.getElementById('myCanvas');
        window.canvas = canvas;
        window.screencanvas = canvas;

        // 虚拟文件系统模块系统
        const modules = new Map();           // 存储模块: moduleId -> { factory, loaded }
        const moduleCache = new Map();       // 模块缓存: absolutePath -> exports
        window.modules = modules;
        window.moduleCache = moduleCache;
        let loadedModules = 0;
        let totalModules = 0;
        let currentModulePath = '/';         // 当前执行模块的路径
        window.moduleExports = {};

        // 解析相对路径为绝对路径
        function resolvePath(currentPath, requestPath) {
            // 如果请求路径不以 ./ 或 ../ 开头，直接返回（当作绝对路径）
            if (!requestPath.startsWith('./') && !requestPath.startsWith('../')) {
                return requestPath;
            }

            // 获取当前模块的目录路径
            const currentDir = currentPath.includes('/')
                ? currentPath.substring(0, currentPath.lastIndexOf('/') + 1)
                : '/';

            // 处理相对路径
            let resolved = currentDir + requestPath;

            // 标准化路径：处理 ../ 和 ./
            const parts = resolved.split('/');
            const stack = [];

            for (const part of parts) {
                if (part === '' || part === '.') {
                    continue;
                } else if (part === '..') {
                    stack.pop();
                } else {
                    stack.push(part);
                }
            }

            return stack.join('/');
        }

        // 全局define函数
        window.define = function (id, factory) {
            // console.log(`[Define] 注册模块: ${id}`);
            const moduleData = { factory, loaded: false };

            // 存储原始ID
            modules.set(id, moduleData);

            // 如果ID以.js结尾，同时存储不带.js的版本
            if (id.endsWith('.js')) {
                const withoutJs = id.slice(0, -3);
                modules.set(withoutJs, moduleData);
                // console.log(`[Define] 同时注册: ${withoutJs}`);
            }
            // 如果ID不以.js结尾，同时存储带.js的版本
            else {
                const withJs = id + '.js';
                modules.set(withJs, moduleData);
                // console.log(`[Define] 同时注册: ${withJs}`);
            }

            totalModules++;
            // updateProgress();
        };

        // 全局require函数
        window.require = function (requestPath) {
            // 解析绝对路径
            let absolutePath = resolvePath(currentModulePath, requestPath);
            if (absolutePath.startsWith("//")) {
                absolutePath = absolutePath.substring(2);
            }
            // 检查缓存
            if (moduleCache.has(absolutePath)) {
                return moduleCache.get(absolutePath);
            }

            // 查找模块定义
            const moduleDef = modules.get(absolutePath);
            if (!moduleDef) {
                console.warn(`模块未找到: ${requestPath}`);
                console.log(`当前模块路径: ${currentModulePath}`);
                console.log(`解析后路径: ${absolutePath}`);
                console.log(`可用模块:`, Array.from(modules.keys()).slice(0, 5));
                return {};
            }

            if (absolutePath !== requestPath) {
                // console.log(`[Require] 路径解析: "${requestPath}" -> "${absolutePath}"`);
            } else {
                // console.log(`[Require] 加载模块: ${absolutePath}`);
            }

            // 保存当前路径，设置新的执行上下文
            const previousPath = currentModulePath;
            currentModulePath = absolutePath;

            const module = { id: absolutePath, exports: {} };
            // 使用Proxy hack exports，自动注册到全局
            const exports = new Proxy(module.exports, {
                set(target, prop, value) {
                    target[prop] = value;
                    // 自动注册到全局 moduleExports
                    window.moduleExports[absolutePath] = target;
                    return true;
                },
                get(target, prop) {
                    return target[prop];
                }
            });

            // 同时更新 module.exports 的引用
            module.exports = exports;

            try {
                moduleDef.factory.call(exports, window.require, module, exports);
                moduleCache.set(absolutePath, module.exports);
                moduleDef.loaded = true;
                loadedModules++;
                // updateProgress();
                return module.exports;
            } catch (error) {
                console.error(`模块执行失败: ${absolutePath}`, error);
                return {};
            } finally {
                // 恢复之前的路径上下文
                currentModulePath = previousPath;
            }
        };

        // 加载游戏脚本
        function loadGameScript() {
            // const script = document.createElement('script');
            // script.src = url;
            // script.onload = function () {
            //     console.log('游戏脚本加载完成');
            // };
            // script.onerror = function () {
            //     console.error('游戏脚本加载失败');
            //     document.getElementById('loading').innerHTML = '<p style="color: red;">游戏加载失败</p>';
            // };
            // document.head.appendChild(script);
            window.loadGameScript = null;
            LOAD_GAME_SCRIPT("game.js", "utf8")
        }

        // 启动游戏
        console.log('🎮 启动微信小游戏环境...');
        // __devtoolssubcontext = undefined
        window.loadGameScript = loadGameScript
    </script>
</body>

</html>

<script>
    const orgLocalStorage = localStorage;
    var orgDocument = document;
    var orgCreateElement = document.createElement;
    var orgXMLHttpRequest = window.XMLHttpRequest
    var orgImage = window.Image
    var orgWebsocket = window.WebSocket
    var orgAudio = window.Audio
    var cus = {}
    window.wx = {
        env: {
            USER_DATA_PATH: "src/test/remote",
        },
        showShareMenu: function () { },
        onShareAppMessage: function () { },
        onAddToFavorites: function () { },
        onShareTimeline: function () { },
        shareAppMessage: function () { },
        createGameClubButton: function () { },
        setKeepScreenOn: function () { },
        setPreferredFramesPerSecond: function () { },
        setStorage: function (options) {
            const { key, data, success, fail } = options
            orgLocalStorage.setItem(key, data)
            success && success()
        },
        setStorageSync: function (k, v) {
            orgLocalStorage.setItem(k, v)
        },
        getStorage: function (options) {
            const { key, success, fail } = options
            const data = orgLocalStorage.getItem(key)
            success && success({ data })
        },
        getStorageSync: function (k) {
            return orgLocalStorage.getItem(k)
        },
        removeStorage: function (options) {
            const { key, success, fail } = options
            orgLocalStorage.removeItem(key)
            success && success()
        },
        removeStorageSync: function (k) {
            orgLocalStorage.removeItem(k)
        },
        getSystemInfo: function (options) {
            const { success } = options
            if (success) {
                success(this.getSystemInfoSync())
            }
        },
        getSystemInfoSync: function () {
            return {
                "batteryLevel": 100,
                "benchmarkLevel": -1,
                "brand": "devtools",
                "memorySize": 2048,
                "model": "iPad Pro 10.5-inch",
                "system": "iOS 10.0.1",
                "platform": "devtools",
                "pixelRatio": 2,
                safeArea: {
                    left: 0,
                    top: 0,
                    right: 834,
                    width: 834,
                    bottom: 1112,
                    height: 1112
                },
                "screenWidth": 834,
                "screenHeight": 1112,
                "screenTop": 0,
                "windowWidth": 834,
                "windowHeight": 1112,
                "statusBarHeight": 0,
                "version": "8.0.5",
                "language": "zh_CN",
                "SDKVersion": "2.27.3",
                "enableDebug": false,
                "fontSizeScaleFactor": 1,
                "fontSizeSetting": 26,
                "mode": "default",
                "host": { "appId": "wx59d2f86ab224f8d4", "env": "devtools", "version": "1.06.2405010" },
                "bluetoothEnabled": true,
                "locationEnabled": true,
                "wifiEnabled": true,
                "locationReducedAccuracy": true,
                "albumAuthorized": true,
                "bluetoothAuthorized": true,
                "cameraAuthorized": true,
                "locationAuthorized": true,
                "microphoneAuthorized": true,
                "notificationAuthorized": true,
                "notificationAlertAuthorized": true,
                "notificationBadgeAuthorized": true,
                "notificationSoundAuthorized": true,
                "phoneCalendarAuthorized": true,
                "deviceOrientation": "portrait",
                "devicePixelRatio": 2
            };
        },

        onShow: function (callback) {
            console.log('[wx] 注册onShow回调');
            // callback({ query: {} })
        },

        onHide: function (callback) {
            console.log('[wx] 注册onHide回调');
        },

        request: function (options) {
            const {
                method = 'GET',
                url,
                data,
                timeout = 10000,
                success,
                fail,
                header = {}
            } = options;
            console.log('[wx] 网络请求:', url, method);
            const xhr = new orgXMLHttpRequest();
            xhr.timeout = timeout;
            xhr.open(method.toUpperCase(), url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            Object.keys(header).forEach(key => {
                xhr.setRequestHeader(key, header[key]);
            });
            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        let responseData;
                        // try {
                        //     responseData = JSON.parse(xhr.responseText);
                        // } catch (e) {
                        //     responseData = xhr.responseText;
                        // }
                        responseData = xhr.responseText;
                        success && success({
                            data: responseData,
                            statusCode: xhr.status,
                            header: xhr.getAllResponseHeaders()
                        });
                    } else {
                        fail && fail({
                            errMsg: `request:fail ${xhr.status} ${xhr.statusText}`,
                            statusCode: xhr.status
                        });
                    }
                }
            };
            xhr.ontimeout = function () {
                fail && fail({
                    errMsg: 'request:fail timeout',
                    statusCode: 0
                });
            };
            xhr.onerror = function () {
                fail && fail({
                    errMsg: 'request:fail network error',
                    statusCode: 0
                });
            };
            try {
                if (method.toUpperCase() === 'POST' && data) {
                    xhr.send(data);
                } else {
                    xhr.send();
                }
            } catch (error) {
                fail && fail({
                    errMsg: `request:fail ${error.message}`,
                    statusCode: 0
                });
            }
        },
        showToast: function (options) {
            console.log('[wx] 显示toast:', options.title);
        },

        getLaunchOptionsSync: function () {
            return {
                query: {},
                referrerInfo: {},
                scene: 1001,
                shareTicket: void 0
            }
        },

        createImage: function () {
            return new orgImage()
        },
        createCanvas: function () {
            return orgCreateElement.call(orgDocument, 'canvas')
        },
        connectSocket: function (options) {
            const { url, protocols } = options
            let v = new orgWebsocket(url, protocols)
            v.binaryType = "arraybuffer"
            v.onClose = function (fn) {
                v.onclose = fn
            }
            v.onMessage = function (fn) {
                v.onmessage = fn
            }
            v.onOpen = function (fn) {
                v.onopen = fn
            }
            v.onError = function (fn) {
                v.onerror = fn
            }
            var orgSend = v.send;
            v.send = function (arg) {
                orgSend.call(v, arg.data);
            }
            var orgClose = v.close;
            v.close = function (arg) {
                orgClose.call(v, 1000);
            }
            return v
        },
        loadFont: function () {
            return null
        },
        // 触摸事件回调存储
        _touchStartCallbacks: [],
        _touchMoveCallbacks: [],
        _touchEndCallbacks: [],

        // 键盘输入事件回调存储
        _keyboardInputCallbacks: [],
        _keyboardConfirmCallbacks: [],
        _keyboardCompleteCallbacks: [],
        _touchCancelCallbacks: [],

        onTouchStart: function (callback) {
            console.log('[wx] 注册onTouchStart回调');
            if (typeof callback === 'function') {
                this._touchStartCallbacks.push(callback);
            }
        },
        onTouchMove: function (callback) {
            console.log('[wx] 注册onTouchMove回调');
            if (typeof callback === 'function') {
                this._touchMoveCallbacks.push(callback);
            }
        },
        onTouchEnd: function (callback) {
            console.log('[wx] 注册onTouchEnd回调');
            if (typeof callback === 'function') {
                this._touchEndCallbacks.push(callback);
            }
        },
        onTouchCancel: function (callback) {
            console.log('[wx] 注册onTouchCancel回调');
            if (typeof callback === 'function') {
                this._touchCancelCallbacks.push(callback);
            }
        },

        // 键盘输入事件注册
        onKeyboardInput: function (callback) {
            console.log('[wx] 注册键盘输入事件');
            if (typeof callback === 'function') {
                this._keyboardInputCallbacks.push(callback);
            }
        },

        onKeyboardConfirm: function (callback) {
            console.log('[wx] 注册键盘确认事件');
            if (typeof callback === 'function') {
                this._keyboardConfirmCallbacks.push(callback);
            }
        },

        onKeyboardComplete: function (callback) {
            console.log('[wx] 注册键盘完成事件');
            if (typeof callback === 'function') {
                this._keyboardCompleteCallbacks.push(callback);
            }
        },

        // 初始化键盘事件监听
        _initKeyboardEvents: function() {
            console.log('[wx] 初始化键盘事件监听');

            let currentInput = '';

            // 监听键盘按下事件
            document.addEventListener('keydown', (e) => {
                // 忽略功能键
                if (e.ctrlKey || e.altKey || e.metaKey) return;

                if (e.key === 'Enter') {
                    console.log('[wx] 键盘确认:', currentInput);

                    // 触发确认回调
                    this._keyboardConfirmCallbacks.forEach(callback => {
                        try {
                            callback({ value: currentInput });
                        } catch (err) {
                            console.error('[wx] 键盘确认回调错误:', err);
                        }
                    });

                    // 触发完成回调
                    this._keyboardCompleteCallbacks.forEach(callback => {
                        try {
                            callback({ value: currentInput });
                        } catch (err) {
                            console.error('[wx] 键盘完成回调错误:', err);
                        }
                    });

                    currentInput = ''; // 清空输入
                } else if (e.key === 'Backspace') {
                    // 退格键
                    currentInput = currentInput.slice(0, -1);
                    console.log('[wx] 键盘输入:', currentInput);

                    // 触发输入回调
                    this._keyboardInputCallbacks.forEach(callback => {
                        try {
                            callback({ value: currentInput });
                        } catch (err) {
                            console.error('[wx] 键盘输入回调错误:', err);
                        }
                    });
                } else if (e.key.length === 1) {
                    // 普通字符输入
                    currentInput += e.key;
                    console.log('[wx] 键盘输入:', currentInput);

                    // 触发输入回调
                    this._keyboardInputCallbacks.forEach(callback => {
                        try {
                            callback({ value: currentInput });
                        } catch (err) {
                            console.error('[wx] 键盘输入回调错误:', err);
                        }
                    });
                }
            });
        },
        onDeviceOrientationChange: function () {
            console.warn('[wx] onDeviceOrientationChange未实现');
        },

        getAccountInfoSync: function () {
            return {
                miniProgram: {
                    appId: "wx0c747e2a0ec57737",
                    envVersion: "release",
                    version: ""
                }
            }
        },

        getFileSystemManager: function () {
            return {
                readFileSync: function (filePath, encoding) {
                    return READ_FILE_SYNC(filePath, encoding)
                },
                mkdirSync: function (filePath, recursive = true) {
                    return MAKE_DIR_SYNC(filePath, recursive)
                },
                writeFileSync: function (filePath, data, encoding = 'utf8') {
                    return WRITE_FILE_SYNC(filePath, data, encoding)
                },
                readFile: function (options) {
                    const { filePath, encoding, success, fail } = options
                    return new Promise((resolve, reject) => {
                        const data = READ_FILE_SYNC(filePath, encoding)
                        success && success({ data })
                    })
                },
                access: function (options) {
                    const { path, success, fail } = options
                    const is = ACCESS_FILE(path)
                    if (is) {
                        success && success()
                    }
                    else {
                        fail && fail()
                    }
                },
                copyFile: function (options) {
                    const { srcPath, destPath, success, fail } = options
                    const is = COPY_FILE(srcPath, destPath)
                    if (is) {
                        success && success()
                    }
                    else {
                        fail && fail("复制失败")
                    }
                }
            }
        },

        loadSubpackage: function (args) {
            console.log("加载分包....")
            const { name, success, fail } = args
            const gameJson = READ_FILE_SYNC("game.json", "utf8")
            const { subPackages } = JSON.parse(gameJson)
            const sub = subPackages.find(item => item.name == name)
            if (!sub) return
            const url = sub.root.substring(1)
            LOAD_GAME_SCRIPT(`${url}game.js`, "utf8")
            success()
            // const script = document.createElement('script');
            // script.src = `subpackages${sub.root}/game.js`;
            // script.onload = function () {
            //     console.log('分包脚本加载完成', name);
            //     success()
            // };
            // script.onerror = function () {
            //     console.error('分包脚本加载失败', name);
            //     fail()
            // };
            // document.head.appendChild(script);
        },

        downloadFile: function (options) {
            const { url, success, fail } = options
            return new Promise((resolve, reject) => {
                const filePath = DOWNLOAD_FILE(url)
                if (filePath != "") {
                    const res = { tempFilePath: filePath, statusCode: 200 }
                    if (success) {
                        success(res)
                    }
                    else {
                        resolve(res)
                    }
                }
                else {
                    if (fail) {
                        fail({ errMsg: "下载失败" })
                    }
                    else {
                        reject({ errMsg: "下载失败" })
                    }
                }
            })
        },
        createInnerAudioContext: function () {
            const audio = new orgAudio()
            audio.offCanplay = function () { }
            audio.offPlay = function () { }
            audio.offPause = function () { }
            audio.offStop = function () { }
            audio.offSeeked = function () { }
            audio.offSeeking = function () { }
            audio.offEnded = function () { }
            audio.onCanplay = function () { }
            audio.onError = function () { }
            audio.offError = function () { }
            audio.onSeeked = function () { }
            audio.onSeeking = function () { }
            audio.onEnded = function () { }
            audio.play = function () { }
            audio.pause = function () { }
            audio.stop = function () { }
            audio.seek = function () { }
            audio.destroy = function () { }
            return audio
        },
        offShow: function () { },
        offHide: function () { },
        getGameClubData: function () { return null },
        createRewardedVideoAd: function () { return null },
    };
</script>


<script>
    window.EventHandler = {
        ontouchstart: (evt) => {
            wx._touchStartCallbacks.forEach(callback => {
                callback(evt)
            })
        },
        ontouchmove: (evt) => {
            wx._touchMoveCallbacks.forEach(callback => {
                callback(evt)
            })
        },
        ontouchcancel: (evt) => {
            wx._touchCancelCallbacks.forEach(callback => {
                callback(evt)
            })
        },
        ontouchend: (evt) => {
            wx._touchEndCallbacks.forEach(callback => {
                callback(evt)
            })
        }
    }
    const toucheEventTransform = events => {
        return Array.prototype.map.call(events, e => {
            return {
                identifier: e.identifier,
                clientX: e.clientX,
                clientY: e.clientY,
                pageX: e.pageX,
                pageY: e.pageY
            };
        });
    };
    const ACTION_EVENTS = [
        "touchstart",
        "touchmove",
        "touchcancel",
        "touchend",
        "mousedown",
        "mousemove",
        "mouseup",
        "keydown",
        "keyup",
        'wheel',
        'pointerlockerror',
        'pointerlockchange',
    ]
    ACTION_EVENTS.forEach(eventName => {
        document.addEventListener(eventName, (e) => {
            const { type } = e
            if (type.startsWith('touch')) {
                callbackTouch(type, e)
            }

            if (type.startsWith('mouse')) {
                callbackMouse(type, e)
                // mouse 可以模拟 touch 的事件
                switch (type) {
                    case 'mousedown':
                        onTouchByMouse('touchstart', e)
                        break;
                    case 'mousemove':
                        onTouchByMouse('touchmove', e)
                        break;
                    case 'mouseup':
                        onTouchByMouse('touchend', e)
                        break;
                }
            }

            if (type.startsWith('key')) {
                callbackKeyboard(type, e)
            }

            if (type === 'wheel') {
                callbackWheel(type, e)
            }

            if (type.startsWith('pointerlock')) {
                callbackPointerlock(type, e)
            }
        });
    });

    function callbackTouch(type, e) {
        const listener = global.EventHandler["on" + type];
        if (typeof listener === "function") {
            const eventObj = {
                changedTouches: toucheEventTransform(e.changedTouches),
                touches: toucheEventTransform(e.touches),
                timeStamp: e.timeStamp,
                type,
            };
            listener.call(canvas, eventObj);
        }
    }
    function callbackMouse(type, e) {
        const listener = global.EventHandler["on" + type];
        if (typeof listener === "function") {
            const eventObj = {
                x: e.x,
                y: e.y,
                clientX: e.clientX,
                clientY: e.clientY,
                movementX: e.movementX,
                movementY: e.movementY,
                button: e.button,
                timeStamp: e.timeStamp,
                type
            };
            listener.call(canvas, eventObj);
        }
    }

    function callbackKeyboard(type, e) {
        const listener = global.EventHandler["on" + type];
        if (typeof listener === "function") {
            const eventObj = {
                key: e.key,
                code: e.code,
                timeStamp: e.timeStamp,
                type
            };
            listener.call(canvas, eventObj);
        }
    }

    function callbackWheel(type, e) {
        const listener = global.EventHandler["on" + type];
        if (typeof listener === "function") {
            const eventObj = {
                x: e.x,
                y: e.y,
                deltaX: e.deltaX,
                deltaY: e.deltaY,
                deltaZ: e.deltaZ,
                timeStamp: e.timeStamp,
                type,
            };
            listener.call(canvas, eventObj);
        }
    }

    function callbackPointerlock(type, e) {
        const listener = global.EventHandler["on" + type];
        if (typeof listener === "function") {
            const eventObj = {
                timeStamp: e.timeStamp,
                type,
            };
            listener.call(canvas, eventObj);
        }
    }

    function onTouchByMouse(touchType, ev) {
        // prevent mouse events (只处理左键)
        if (ev.which !== 1) {
            return;
        }

        // 获取canvas信息用于调试
        const rect = canvas.getBoundingClientRect();
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const scaleX = canvasWidth / rect.width;
        const scaleY = canvasHeight / rect.height;
        const pixelRatio = wx.getSystemInfoSync().pixelRatio || window.devicePixelRatio || 1;

        // 添加详细调试日志
        // console.log(`[触摸模拟] ${touchType} - 原始坐标: (${ev.clientX}, ${ev.clientY})`);
        // console.log(`[触摸模拟] Canvas显示尺寸: ${rect.width}x${rect.height}, 实际尺寸: ${canvasWidth}x${canvasHeight}`);
        // console.log(`[触摸模拟] 缩放比例: ${scaleX.toFixed(2)}x${scaleY.toFixed(2)}, pixelRatio: ${pixelRatio}`);

        // changedTouches 一直都是有值
        const changedTouches = getActiveTouches(ev)
        let touches = changedTouches
        // 如果是 mouseup 触发的是 touchend，则 touches 为空
        if (ev.type === 'mouseup') {
            touches = emptyTouchList();
        }

        // 添加转换后坐标的调试日志
        if (changedTouches.length > 0) {
            // console.log(`[触摸模拟] ${touchType} - 转换后坐标: (${changedTouches[0].clientX}, ${changedTouches[0].clientY})`);
        }

        callbackTouch(touchType, {
            changedTouches: toucheEventTransform(changedTouches),
            touches: toucheEventTransform(touches),
            timeStamp: ev.timeStamp,
            type: touchType,
        });
    }

    function Touch(target, identifier, pos, deltaX, deltaY) {
        deltaX = deltaX || 0;
        deltaY = deltaY || 0;

        // 获取canvas的显示尺寸和实际尺寸
        const rect = canvas.getBoundingClientRect();
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;

        // 计算显示尺寸与实际尺寸的比例
        const scaleX = canvasWidth / rect.width;
        const scaleY = canvasHeight / rect.height;

        // 获取像素比
        const pixelRatio = wx.getSystemInfoSync().pixelRatio || window.devicePixelRatio || 1;

        this.identifier = identifier;
        this.target = target;

        // 坐标转换：先转为canvas相对坐标，再按比例缩放，最后考虑像素比
        const relativeX = (pos.clientX - rect.left) * scaleX / pixelRatio;
        const relativeY = (pos.clientY - rect.top) * scaleY / pixelRatio;

        this.clientX = relativeX + deltaX;
        this.clientY = relativeY + deltaY;
        this.screenX = relativeX + deltaX;
        this.screenY = relativeY + deltaY;
        this.pageX = relativeX + deltaX;
        this.pageY = relativeY + deltaY;
    }

    // 声明eventTarget变量
    let eventTarget = null;

    function createTouchList(mouseEv) {
        if (
            mouseEv.type === "mousedown" ||
            !eventTarget ||
            (eventTarget && !eventTarget.dispatchEvent)
        ) {
            eventTarget = mouseEv.target;
        }

        const touchList = emptyTouchList()
        touchList.push(new Touch(eventTarget, 1, mouseEv, 0, 0))
        return touchList
    }

    function emptyTouchList() {
        const touchList = [];

        touchList['item'] = function (index) {
            return this[index] || null;
        };
        return touchList;
    }

    function getActiveTouches(mouseEv) {
        return createTouchList(mouseEv);
    }

</script>