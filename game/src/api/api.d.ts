import { DialogApiInjection, DialogReactive } from "naive-ui/es/dialog/src/DialogProvider";
import { MessageApiInjection } from "naive-ui/es/message/src/MessageProvider";
import { Ref, VNode } from "vue";

export { }
declare global {
    interface Window {
        $message: MessageApiInjection;
        $dialog: DialogApiInjection;
        $dialogReactive: Ref<readonly DialogReactive[]>
        Events: {}
        ON_UNKNOWN_KEY: (key: string) => void,
        DASHBOARD_EXEC: (jsCode: string) => any,
        request: (url: string) => Promise<any>
        _world: {
            isEnterGame: () => boolean
            useItemByName: (item: any, cnt: number, stopCall?: any) => void
            addRubbishName: (name: string) => number
            removeRubbishName: (name: string) => number
            rubbishNameArray: string[]
            toBattle: (id: number) => void
            OpenRewardBox: (stopCall?: any) => void
        }
        copy: (str: string, $el?: any) => void
        Evt: {
            on: (key: string, call: Function) => void
            emit: (key: string, ...args: any[]) => void
            off: (key: string) => void
        }
        REQ_JAVA_LOAD_FUNCTION: () => Promise<String>

        X_SCALE: number
        dlg: any
        VER_STR: string
        DEBUG: boolean
        LOCAL_HTTP: boolean
        LOCAL_LOGIC: boolean
        sampleNotify: (arg: string) => void;
        base64Encode: (str: string) => string
        base64Decode: (str: string) => string
        showTip: (content: VNode) => void
        hideTip: () => void
        $daily: any[]
        xself: any
        OneKeyTaskData: any[]
        showTipDailyMission: (code: number) => void
        groupCfg:any[]
        missionCfg:any[]
        monsterCfg:any[]
        npcCfg:any[]
        mapCfg:any[]
    }
}
