<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>触摸事件测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #gameCanvas {
            border: 2px solid #333;
            background-color: #f0f0f0;
            display: block;
            margin: 20px auto;
        }
        #log {
            max-height: 300px;
            overflow-y: auto;
            background: #000;
            color: #0f0;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>微信小游戏触摸事件测试</h1>
    <canvas id="gameCanvas" width="400" height="300"></canvas>
    <div id="log"></div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        const logDiv = document.getElementById('log');
        
        function log(message) {
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        // 清空画布并绘制提示
        function drawCanvas() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#333';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('点击或触摸这里测试事件', canvas.width/2, canvas.height/2);
        }
        
        drawCanvas();
        
        // 模拟微信小游戏的wx对象
        window.wx = {
            _touchStartCallbacks: [],
            _touchMoveCallbacks: [],
            _touchEndCallbacks: [],
            _touchCancelCallbacks: [],

            onTouchStart: function (callback) {
                log('注册onTouchStart回调');
                if (typeof callback === 'function') {
                    this._touchStartCallbacks.push(callback);
                }
            },
            onTouchMove: function (callback) {
                log('注册onTouchMove回调');
                if (typeof callback === 'function') {
                    this._touchMoveCallbacks.push(callback);
                }
            },
            onTouchEnd: function (callback) {
                log('注册onTouchEnd回调');
                if (typeof callback === 'function') {
                    this._touchEndCallbacks.push(callback);
                }
            },
            onTouchCancel: function (callback) {
                log('注册onTouchCancel回调');
                if (typeof callback === 'function') {
                    this._touchCancelCallbacks.push(callback);
                }
            }
        };

        // 触摸事件转换函数
        function convertTouchEvent(e) {
            const rect = canvas.getBoundingClientRect();
            const touches = [];
            const changedTouches = [];
            
            // 处理触摸点
            if (e.touches) {
                for (let i = 0; i < e.touches.length; i++) {
                    const touch = e.touches[i];
                    touches.push({
                        identifier: touch.identifier,
                        pageX: touch.pageX,
                        pageY: touch.pageY,
                        clientX: touch.clientX,
                        clientY: touch.clientY,
                        x: touch.clientX - rect.left,
                        y: touch.clientY - rect.top
                    });
                }
            }
            
            if (e.changedTouches) {
                for (let i = 0; i < e.changedTouches.length; i++) {
                    const touch = e.changedTouches[i];
                    changedTouches.push({
                        identifier: touch.identifier,
                        pageX: touch.pageX,
                        pageY: touch.pageY,
                        clientX: touch.clientX,
                        clientY: touch.clientY,
                        x: touch.clientX - rect.left,
                        y: touch.clientY - rect.top
                    });
                }
            }
            
            // 如果没有触摸事件，处理鼠标事件（模拟触摸）
            if (!e.touches && e.type.startsWith('mouse')) {
                const mouseTouch = {
                    identifier: 0,
                    pageX: e.pageX || e.clientX,
                    pageY: e.pageY || e.clientY,
                    clientX: e.clientX,
                    clientY: e.clientY,
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
                
                if (e.type === 'mousedown' || e.type === 'mousemove') {
                    touches.push(mouseTouch);
                }
                changedTouches.push(mouseTouch);
            }
            
            // 创建微信小游戏格式的事件对象
            return {
                touches: touches,
                changedTouches: changedTouches,
                timeStamp: e.timeStamp || Date.now()
            };
        }

        // 添加事件监听器
        let isMouseDown = false;
        
        // 触摸事件
        canvas.addEventListener('touchstart', function(e) {
            e.preventDefault();
            const wxEvent = convertTouchEvent(e);
            log(`touchstart: ${wxEvent.touches.length} 个触摸点`);
            
            wx._touchStartCallbacks.forEach(callback => {
                try {
                    callback(wxEvent);
                } catch (err) {
                    log(`touchstart回调错误: ${err.message}`);
                }
            });
        }, { passive: false });
        
        canvas.addEventListener('touchmove', function(e) {
            e.preventDefault();
            const wxEvent = convertTouchEvent(e);
            
            wx._touchMoveCallbacks.forEach(callback => {
                try {
                    callback(wxEvent);
                } catch (err) {
                    log(`touchmove回调错误: ${err.message}`);
                }
            });
        }, { passive: false });
        
        canvas.addEventListener('touchend', function(e) {
            e.preventDefault();
            const wxEvent = convertTouchEvent(e);
            log(`touchend: ${wxEvent.changedTouches.length} 个触摸点结束`);
            
            wx._touchEndCallbacks.forEach(callback => {
                try {
                    callback(wxEvent);
                } catch (err) {
                    log(`touchend回调错误: ${err.message}`);
                }
            });
        }, { passive: false });
        
        // 鼠标事件（模拟触摸）
        canvas.addEventListener('mousedown', function(e) {
            isMouseDown = true;
            const wxEvent = convertTouchEvent(e);
            log(`mousedown转touchstart: (${wxEvent.touches[0].clientX}, ${wxEvent.touches[0].clientY})`);
            
            wx._touchStartCallbacks.forEach(callback => {
                try {
                    callback(wxEvent);
                } catch (err) {
                    log(`mousedown转touchstart回调错误: ${err.message}`);
                }
            });
        });
        
        canvas.addEventListener('mousemove', function(e) {
            if (!isMouseDown) return;
            const wxEvent = convertTouchEvent(e);
            
            wx._touchMoveCallbacks.forEach(callback => {
                try {
                    callback(wxEvent);
                } catch (err) {
                    log(`mousemove转touchmove回调错误: ${err.message}`);
                }
            });
        });
        
        canvas.addEventListener('mouseup', function(e) {
            if (!isMouseDown) return;
            isMouseDown = false;
            const wxEvent = convertTouchEvent(e);
            log(`mouseup转touchend: (${wxEvent.changedTouches[0].clientX}, ${wxEvent.changedTouches[0].clientY})`);
            
            wx._touchEndCallbacks.forEach(callback => {
                try {
                    callback(wxEvent);
                } catch (err) {
                    log(`mouseup转touchend回调错误: ${err.message}`);
                }
            });
        });

        // 注册测试回调
        wx.onTouchStart(function(e) {
            log(`游戏收到touchstart: ${e.touches.length} 个触摸点, 坐标: (${e.touches[0].clientX}, ${e.touches[0].clientY})`);
            
            // 在触摸点绘制一个圆
            ctx.fillStyle = 'red';
            ctx.beginPath();
            ctx.arc(e.touches[0].x, e.touches[0].y, 10, 0, 2 * Math.PI);
            ctx.fill();
        });
        
        wx.onTouchMove(function(e) {
            // 清空画布并重新绘制
            drawCanvas();
            
            // 绘制当前触摸点
            ctx.fillStyle = 'blue';
            ctx.beginPath();
            ctx.arc(e.touches[0].x, e.touches[0].y, 8, 0, 2 * Math.PI);
            ctx.fill();
        });
        
        wx.onTouchEnd(function(e) {
            log(`游戏收到touchend: ${e.changedTouches.length} 个触摸点结束`);
            drawCanvas();
        });
        
        log('触摸事件测试页面已加载');
    </script>
</body>
</html>
